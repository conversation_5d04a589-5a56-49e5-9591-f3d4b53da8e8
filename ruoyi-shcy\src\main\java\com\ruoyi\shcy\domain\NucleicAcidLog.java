package com.ruoyi.shcy.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * 员工核酸情况对象 shcy_nucleic_acid_log
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
public class NucleicAcidLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 检查日志id */
    @Excel(name = "检查日志id")
    private Long checkLogId;

    /** 店铺id */
    @Excel(name = "店铺id")
    private Long shopId;

    /** 店铺名称*/
    @Excel(name="店铺名称")
    private String shopName;

    /** 核酸情况 */
    @Excel(name = "核酸情况")
    private String nucleicAcid;

    /** 员工id */
    @Excel(name = "员工id")
    private Long emplyeeId;

    /** 员工名称**/
    @Excel(name="员工名称")
    private String name;

    /** 所属居委会*/
    @Excel(name="所属居委会")
    private String deptName;

    /** 检查日期 */
    @Excel(name ="检查日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkDate;

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    /** 所属居委会id*/
    private Long deptId;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public Long getCheckLogId() {
        return checkLogId;
    }

    public void setCheckLogId(Long checkLogId) {
        this.checkLogId = checkLogId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getEmplyeeId() {
        return emplyeeId;
    }

    public void setEmplyeeId(Long emplyeeId) {
        this.emplyeeId = emplyeeId;
    }

    public void setNucleicAcid(String nucleicAcid)
    {
        this.nucleicAcid = nucleicAcid;
    }

    public String getNucleicAcid()
    {
        return nucleicAcid;
    }


    @Override
    public String toString() {
        return "NucleicAcidLog{" +
                "id=" + id +
                ", checkLogId=" + checkLogId +
                ", shopId=" + shopId +
                ", shopName='" + shopName + '\'' +
                ", nucleicAcid='" + nucleicAcid + '\'' +
                ", emplyeeId=" + emplyeeId +
                ", name='" + name + '\'' +
                ", deptName='" + deptName + '\'' +
                ", checkDate=" + checkDate +
                ", deptId=" + deptId +
                '}';
    }
}
