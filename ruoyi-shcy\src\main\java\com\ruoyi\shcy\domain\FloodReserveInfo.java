package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 防汛物资储备信息对象 shcy_flood_reserve_info
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public class FloodReserveInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 物业名称 */
    @Excel(name = "物业名称")
    private String propertyName;

    /** 储备点地址 */
    @Excel(name = "储备点地址")
    private String reservePointAddress;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactNumber;

    /** 辐射区域 */
    @Excel(name = "辐射区域")
    private String radiationArea;

    /** 编织袋数量 */
    private Integer wovenBag;

    /** 草包数量 */
    private Integer strawBag;

    /** 阻水板数量 */
    private Integer waterStopBoard;

    /** 防汛阻水袋数量 */
    private Integer floodStopBag;

    /** 黄沙数量 */
    private Integer yellowSand;

    /** 水泥数量 */
    private Integer cement;

    /** 便携式工作灯数量 */
    private Integer portableWorkLight;

    /** 水泵数量 */
    private Integer waterPump;

    /** 雨衣数量 */
    private Integer raincoat;

    /** 雨鞋数量 */
    private Integer rainboots;

    /** 图形类型 */
    private String type;

    /** 坐标 */
    private String coordinate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPropertyName(String propertyName) 
    {
        this.propertyName = propertyName;
    }

    public String getPropertyName() 
    {
        return propertyName;
    }
    public void setReservePointAddress(String reservePointAddress) 
    {
        this.reservePointAddress = reservePointAddress;
    }

    public String getReservePointAddress() 
    {
        return reservePointAddress;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactNumber(String contactNumber) 
    {
        this.contactNumber = contactNumber;
    }

    public String getContactNumber() 
    {
        return contactNumber;
    }
    public void setRadiationArea(String radiationArea) 
    {
        this.radiationArea = radiationArea;
    }

    public String getRadiationArea() 
    {
        return radiationArea;
    }
    public void setWovenBag(Integer wovenBag) 
    {
        this.wovenBag = wovenBag;
    }

    public Integer getWovenBag() 
    {
        return wovenBag;
    }
    public void setStrawBag(Integer strawBag) 
    {
        this.strawBag = strawBag;
    }

    public Integer getStrawBag() 
    {
        return strawBag;
    }
    public void setWaterStopBoard(Integer waterStopBoard) 
    {
        this.waterStopBoard = waterStopBoard;
    }

    public Integer getWaterStopBoard() 
    {
        return waterStopBoard;
    }
    public void setFloodStopBag(Integer floodStopBag) 
    {
        this.floodStopBag = floodStopBag;
    }

    public Integer getFloodStopBag() 
    {
        return floodStopBag;
    }
    public void setYellowSand(Integer yellowSand) 
    {
        this.yellowSand = yellowSand;
    }

    public Integer getYellowSand() 
    {
        return yellowSand;
    }
    public void setCement(Integer cement) 
    {
        this.cement = cement;
    }

    public Integer getCement() 
    {
        return cement;
    }
    public void setPortableWorkLight(Integer portableWorkLight) 
    {
        this.portableWorkLight = portableWorkLight;
    }

    public Integer getPortableWorkLight() 
    {
        return portableWorkLight;
    }
    public void setWaterPump(Integer waterPump) 
    {
        this.waterPump = waterPump;
    }

    public Integer getWaterPump() 
    {
        return waterPump;
    }
    public void setRaincoat(Integer raincoat) 
    {
        this.raincoat = raincoat;
    }

    public Integer getRaincoat() 
    {
        return raincoat;
    }
    public void setRainboots(Integer rainboots) 
    {
        this.rainboots = rainboots;
    }

    public Integer getRainboots() 
    {
        return rainboots;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setCoordinate(String coordinate) 
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate() 
    {
        return coordinate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("propertyName", getPropertyName())
            .append("reservePointAddress", getReservePointAddress())
            .append("contactPerson", getContactPerson())
            .append("contactNumber", getContactNumber())
            .append("radiationArea", getRadiationArea())
            .append("wovenBag", getWovenBag())
            .append("strawBag", getStrawBag())
            .append("waterStopBoard", getWaterStopBoard())
            .append("floodStopBag", getFloodStopBag())
            .append("yellowSand", getYellowSand())
            .append("cement", getCement())
            .append("portableWorkLight", getPortableWorkLight())
            .append("waterPump", getWaterPump())
            .append("raincoat", getRaincoat())
            .append("rainboots", getRainboots())
            .append("type", getType())
            .append("coordinate", getCoordinate())
            .toString();
    }
}
