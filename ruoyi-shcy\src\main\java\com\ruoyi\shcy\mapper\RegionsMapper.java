package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.Regions;

/**
 * 区域信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-24
 */
public interface RegionsMapper 
{
    /**
     * 查询区域信息
     * 
     * @param id 区域信息主键
     * @return 区域信息
     */
    public Regions selectRegionsById(Long id);

    /**
     * 查询区域信息列表
     * 
     * @param regions 区域信息
     * @return 区域信息集合
     */
    public List<Regions> selectRegionsList(Regions regions);

    /**
     * 新增区域信息
     * 
     * @param regions 区域信息
     * @return 结果
     */
    public int insertRegions(Regions regions);

    /**
     * 修改区域信息
     * 
     * @param regions 区域信息
     * @return 结果
     */
    public int updateRegions(Regions regions);

    /**
     * 删除区域信息
     * 
     * @param id 区域信息主键
     * @return 结果
     */
    public int deleteRegionsById(Long id);

    /**
     * 批量删除区域信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRegionsByIds(Long[] ids);
}
