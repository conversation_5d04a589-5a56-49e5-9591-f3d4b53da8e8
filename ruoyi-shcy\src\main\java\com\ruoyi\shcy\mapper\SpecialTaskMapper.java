package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.SpecialTask;

import java.util.List;

/**
 * 专项任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
public interface SpecialTaskMapper 
{
    /**
     * 查询专项任务
     * 
     * @param id 专项任务主键
     * @return 专项任务
     */
    public SpecialTask selectSpecialTaskById(Long id);

    /**
     * 查询专项任务列表
     * 
     * @param specialTask 专项任务
     * @return 专项任务集合
     */
    public List<SpecialTask> selectSpecialTaskList(SpecialTask specialTask);

    /**
     * 新增专项任务
     * 
     * @param specialTask 专项任务
     * @return 结果
     */
    public int insertSpecialTask(SpecialTask specialTask);

    /**
     * 修改专项任务
     * 
     * @param specialTask 专项任务
     * @return 结果
     */
    public int updateSpecialTask(SpecialTask specialTask);

    /**
     * 删除专项任务
     * 
     * @param id 专项任务主键
     * @return 结果
     */
    public int deleteSpecialTaskById(Long id);

    /**
     * 批量删除专项任务
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSpecialTaskByIds(Long[] ids);

    public long getCaseCount(SpecialTask specialTask);
}
