package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthlyExtend;

/**
 * 12345热线分析拓展Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
public interface IShcyDbcenterRxMonthlyExtendService 
{
    /**
     * 查询12345热线分析拓展
     * 
     * @param id 12345热线分析拓展主键
     * @return 12345热线分析拓展
     */
    public ShcyDbcenterRxMonthlyExtend selectShcyDbcenterRxMonthlyExtendById(Long id);

    /**
     * 查询12345热线分析拓展列表
     * 
     * @param shcyDbcenterRxMonthlyExtend 12345热线分析拓展
     * @return 12345热线分析拓展集合
     */
    public List<ShcyDbcenterRxMonthlyExtend> selectShcyDbcenterRxMonthlyExtendList(ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend);

    /**
     * 新增12345热线分析拓展
     * 
     * @param shcyDbcenterRxMonthlyExtend 12345热线分析拓展
     * @return 结果
     */
    public int insertShcyDbcenterRxMonthlyExtend(ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend);

    /**
     * 修改12345热线分析拓展
     * 
     * @param shcyDbcenterRxMonthlyExtend 12345热线分析拓展
     * @return 结果
     */
    public int updateShcyDbcenterRxMonthlyExtend(ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend);

    /**
     * 批量删除12345热线分析拓展
     * 
     * @param ids 需要删除的12345热线分析拓展主键集合
     * @return 结果
     */
    public int deleteShcyDbcenterRxMonthlyExtendByIds(Long[] ids);

    /**
     * 删除12345热线分析拓展信息
     * 
     * @param id 12345热线分析拓展主键
     * @return 结果
     */
    public int deleteShcyDbcenterRxMonthlyExtendById(Long id);
}
