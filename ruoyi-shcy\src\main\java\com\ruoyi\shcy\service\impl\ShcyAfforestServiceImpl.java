package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyAfforestMapper;
import com.ruoyi.shcy.domain.ShcyAfforest;
import com.ruoyi.shcy.service.IShcyAfforestService;

/**
 * 绿化信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-05-19
 */
@Service
public class ShcyAfforestServiceImpl implements IShcyAfforestService 
{
    @Autowired
    private ShcyAfforestMapper shcyAfforestMapper;

    /**
     * 查询绿化信息
     * 
     * @param id 绿化信息主键
     * @return 绿化信息
     */
    @Override
    public ShcyAfforest selectShcyAfforestById(Long id)
    {
        return shcyAfforestMapper.selectShcyAfforestById(id);
    }

    /**
     * 查询绿化信息列表
     * 
     * @param shcyAfforest 绿化信息
     * @return 绿化信息
     */
    @Override
    public List<ShcyAfforest> selectShcyAfforestList(ShcyAfforest shcyAfforest)
    {
        return shcyAfforestMapper.selectShcyAfforestList(shcyAfforest);
    }

    /**
     * 新增绿化信息
     * 
     * @param shcyAfforest 绿化信息
     * @return 结果
     */
    @Override
    public int insertShcyAfforest(ShcyAfforest shcyAfforest)
    {
        shcyAfforest.setCreateTime(DateUtils.getNowDate());
        return shcyAfforestMapper.insertShcyAfforest(shcyAfforest);
    }

    /**
     * 修改绿化信息
     * 
     * @param shcyAfforest 绿化信息
     * @return 结果
     */
    @Override
    public int updateShcyAfforest(ShcyAfforest shcyAfforest)
    {
        shcyAfforest.setUpdateTime(DateUtils.getNowDate());
        return shcyAfforestMapper.updateShcyAfforest(shcyAfforest);
    }

    /**
     * 批量删除绿化信息
     * 
     * @param ids 需要删除的绿化信息主键
     * @return 结果
     */
    @Override
    public int deleteShcyAfforestByIds(Long[] ids)
    {
        return shcyAfforestMapper.deleteShcyAfforestByIds(ids);
    }

    /**
     * 删除绿化信息信息
     * 
     * @param id 绿化信息主键
     * @return 结果
     */
    @Override
    public int deleteShcyAfforestById(Long id)
    {
        return shcyAfforestMapper.deleteShcyAfforestById(id);
    }
}
