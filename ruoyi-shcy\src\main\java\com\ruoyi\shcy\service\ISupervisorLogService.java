package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.SupervisorLog;

/**
 * 重点监管列Service接口
 *
 * <AUTHOR>
 * @date 2022-11-17
 */
public interface ISupervisorLogService
{
    /**
     * 查询重点监管列
     *
     * @param id 重点监管列主键
     * @return 重点监管列
     */
    public SupervisorLog selectSupervisorLogById(Long id);


    /**
     * 根据id查询最近三次重点监管记录
     * **/
    public List<SupervisorLog> selectSupervisorLogByIdThree(Long id);

    /**
     * 查询重点监管列列表
     *
     * @param supervisorLog 重点监管列
     * @return 重点监管列集合
     */
    public List<SupervisorLog> selectSupervisorLogList(SupervisorLog supervisorLog);

    /**
     * 新增重点监管列
     *
     * @param supervisorLog 重点监管列
     * @return 结果
     */
    public int insertSupervisorLog(SupervisorLog supervisorLog);

    /**
     * 修改重点监管列
     *
     * @param supervisorLog 重点监管列
     * @return 结果
     */
    public int updateSupervisorLog(SupervisorLog supervisorLog);

    /**
     * 批量删除重点监管列
     *
     * @param ids 需要删除的重点监管列主键集合
     * @return 结果
     */
    public int deleteSupervisorLogByIds(Long[] ids);

    /**
     * 删除重点监管列信息
     *
     * @param id 重点监管列主键
     * @return 结果
     */
    public int deleteSupervisorLogById(Long id);
}
