package com.ruoyi.shcy.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.CheckRecord;
import com.ruoyi.shcy.mapper.CheckRecordMapper;
import com.ruoyi.shcy.service.ICheckRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 商铺巡查通过记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@Service
public class CheckRecordServiceImpl implements ICheckRecordService
{
    @Autowired
    private CheckRecordMapper checkRecordMapper;

    /**
     * 查询商铺巡查通过记录
     *
     * @param id 商铺巡查通过记录主键
     * @return 商铺巡查通过记录
     */
    @Override
    public CheckRecord selectCheckRecordById(Long id)
    {
        return checkRecordMapper.selectCheckRecordById(id);
    }

    /**
     * 查询商铺巡查通过记录列表
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 商铺巡查通过记录
     */
    @Override
    public List<CheckRecord> selectCheckRecordList(CheckRecord checkRecord)
    {
        return checkRecordMapper.selectCheckRecordList(checkRecord);
    }

    /**
     * 新增商铺巡查通过记录
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 结果
     */
    @Override
    public int insertCheckRecord(CheckRecord checkRecord)
    {
        checkRecord.setCreateTime(DateUtils.getNowDate());
        return checkRecordMapper.insertCheckRecord(checkRecord);
    }

    /**
     * 修改商铺巡查通过记录
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 结果
     */
    @Override
    public int updateCheckRecord(CheckRecord checkRecord)
    {
        checkRecord.setUpdateTime(DateUtils.getNowDate());
        return checkRecordMapper.updateCheckRecord(checkRecord);
    }

    /**
     * 批量删除商铺巡查通过记录
     *
     * @param ids 需要删除的商铺巡查通过记录主键
     * @return 结果
     */
    @Override
    public int deleteCheckRecordByIds(Long[] ids)
    {
        return checkRecordMapper.deleteCheckRecordByIds(ids);
    }

    /**
     * 删除商铺巡查通过记录信息
     *
     * @param id 商铺巡查通过记录主键
     * @return 结果
     */
    @Override
    public int deleteCheckRecordById(Long id)
    {
        return checkRecordMapper.deleteCheckRecordById(id);
    }


    @Override
    public CheckRecord selectCheckRecordByShopIdAndCheckDate(Long shopId, Date checkDate) {
        return  checkRecordMapper.selectCheckRecordByShopIdAndCheckDate(shopId,checkDate);
    }


    /**
     * 查询出 已巡查商铺数量
     *
     * @param checkRecord
     **/
    @Override
    public List<CheckRecord> selectCheckedShopNum(CheckRecord checkRecord) {
        return  checkRecordMapper.selectCheckedShopNum(checkRecord);
    }

    /**
     * 查询出检查通过商铺数量
     *
     * @param checkRecord
     */
    @Override
    public List<CheckRecord> selectCheckAndPassShop(CheckRecord checkRecord) {
        return  checkRecordMapper.selectCheckAndPassShop(checkRecord);
    }

    /**
     * 查询检查未通过商铺数量
     *
     * @param checkRecord
     **/
    @Override
    public List<CheckRecord> selectCheckAndNoPassShop(CheckRecord checkRecord) {
        return  checkRecordMapper.selectCheckAndNoPassShop(checkRecord);
    }

    /**
     * 查询出歇业商铺数量
     *
     * @param checkRecord
     **/
    @Override
    public List<CheckRecord> selectNoCheckShop(CheckRecord checkRecord) {

        return  checkRecordMapper.selectNoCheckShop(checkRecord);
    }

    @Override
    public List<CheckRecord> selectCheckRecordShopList() {
        return checkRecordMapper.selectCheckRecordShopList();
    }
}
