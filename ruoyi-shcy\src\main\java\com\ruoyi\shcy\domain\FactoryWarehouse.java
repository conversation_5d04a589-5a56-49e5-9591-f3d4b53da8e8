package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 厂房仓库对象 shcy_factory_warehouse
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
public class FactoryWarehouse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 单位名称 */
    @Excel(name = "单位名称")
    private String unitName;

    /** 建筑类型 */
    @Excel(name = "建筑类型")
    private String areaName;

    /** 产权单位 */
    @Excel(name = "产权单位")
    private String propertyUnit;

    /** 是否碳谷绿湾托管 */
    @Excel(name = "是否碳谷绿湾托管")
    private String isManagedByTglw;

    /** 建筑区划 */
    @Excel(name = "建筑区划")
    private String buildingPartition;

    /** 建筑地址 */
    @Excel(name = "建筑地址")
    private String buildingAddress;

    /** 建筑结构 */
    @Excel(name = "建筑结构")
    private String buildingStructure;

    /** 建筑耐火等级 */
    @Excel(name = "建筑耐火等级")
    private String fireResistanceLevel;

    /** 使用状态 */
    @Excel(name = "使用状态")
    private String usageStatus;

    /** 建筑类型标签 */
    @Excel(name = "建筑类型标签")
    private String buildingTypeTag;

    /** 建筑使用功能标签1 */
    @Excel(name = "建筑使用功能标签1")
    private String functionTag1;

    /** 建筑使用功能标签2 */
    @Excel(name = "建筑使用功能标签2")
    private String functionTag2;

    /** 建筑面积 */
    @Excel(name = "建筑面积")
    private String buildingArea;

    /** 建筑高度 */
    @Excel(name = "建筑高度")
    private String buildingHeight;

    /** 建筑层数 */
    @Excel(name = "建筑层数")
    private String floorCount;

    /** 最大层高 */
    @Excel(name = "最大层高")
    private String maxFloorHeight;

    /** 夹（插）层面积 */
    @Excel(name = "夹（插）层面积")
    private String mezzanineArea;

    /** 雨棚等建（构）筑物面积 */
    @Excel(name = "雨棚等建（构）筑物面积")
    private String attachmentArea;

    /** 建筑消防设施 */
    @Excel(name = "建筑消防设施")
    private String fireFacilities;

    /** 主要原料 */
    @Excel(name = "主要原料")
    private String mainMaterials;

    /** 主要原料与产品的火灾危险性 */
    @Excel(name = "主要原料与产品的火灾危险性")
    private String fireHazardLevel;

    /** 风险等级 */
    @Excel(name = "风险等级")
    private String riskLevel;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUnitName(String unitName) 
    {
        this.unitName = unitName;
    }

    public String getUnitName() 
    {
        return unitName;
    }

    public void setAreaName(String areaName) 
    {
        this.areaName = areaName;
    }

    public String getAreaName() 
    {
        return areaName;
    }

    public void setPropertyUnit(String propertyUnit) 
    {
        this.propertyUnit = propertyUnit;
    }

    public String getPropertyUnit() 
    {
        return propertyUnit;
    }
    public void setIsManagedByTglw(String isManagedByTglw) 
    {
        this.isManagedByTglw = isManagedByTglw;
    }

    public String getIsManagedByTglw() 
    {
        return isManagedByTglw;
    }
    public void setBuildingPartition(String buildingPartition) 
    {
        this.buildingPartition = buildingPartition;
    }

    public String getBuildingPartition() 
    {
        return buildingPartition;
    }
    public void setBuildingAddress(String buildingAddress) 
    {
        this.buildingAddress = buildingAddress;
    }

    public String getBuildingAddress() 
    {
        return buildingAddress;
    }
    public void setBuildingStructure(String buildingStructure) 
    {
        this.buildingStructure = buildingStructure;
    }

    public String getBuildingStructure() 
    {
        return buildingStructure;
    }
    public void setFireResistanceLevel(String fireResistanceLevel) 
    {
        this.fireResistanceLevel = fireResistanceLevel;
    }

    public String getFireResistanceLevel() 
    {
        return fireResistanceLevel;
    }
    public void setUsageStatus(String usageStatus) 
    {
        this.usageStatus = usageStatus;
    }

    public String getUsageStatus() 
    {
        return usageStatus;
    }
    public void setBuildingTypeTag(String buildingTypeTag) 
    {
        this.buildingTypeTag = buildingTypeTag;
    }

    public String getBuildingTypeTag() 
    {
        return buildingTypeTag;
    }
    public void setFunctionTag1(String functionTag1) 
    {
        this.functionTag1 = functionTag1;
    }

    public String getFunctionTag1() 
    {
        return functionTag1;
    }
    public void setFunctionTag2(String functionTag2) 
    {
        this.functionTag2 = functionTag2;
    }

    public String getFunctionTag2() 
    {
        return functionTag2;
    }
    public void setBuildingArea(String buildingArea) 
    {
        this.buildingArea = buildingArea;
    }

    public String getBuildingArea() 
    {
        return buildingArea;
    }
    public void setBuildingHeight(String buildingHeight) 
    {
        this.buildingHeight = buildingHeight;
    }

    public String getBuildingHeight() 
    {
        return buildingHeight;
    }
    public void setFloorCount(String floorCount) 
    {
        this.floorCount = floorCount;
    }

    public String getFloorCount() 
    {
        return floorCount;
    }
    public void setMaxFloorHeight(String maxFloorHeight) 
    {
        this.maxFloorHeight = maxFloorHeight;
    }

    public String getMaxFloorHeight() 
    {
        return maxFloorHeight;
    }
    public void setMezzanineArea(String mezzanineArea) 
    {
        this.mezzanineArea = mezzanineArea;
    }

    public String getMezzanineArea() 
    {
        return mezzanineArea;
    }
    public void setAttachmentArea(String attachmentArea) 
    {
        this.attachmentArea = attachmentArea;
    }

    public String getAttachmentArea() 
    {
        return attachmentArea;
    }
    public void setFireFacilities(String fireFacilities) 
    {
        this.fireFacilities = fireFacilities;
    }

    public String getFireFacilities() 
    {
        return fireFacilities;
    }
    public void setMainMaterials(String mainMaterials) 
    {
        this.mainMaterials = mainMaterials;
    }

    public String getMainMaterials() 
    {
        return mainMaterials;
    }
    public void setFireHazardLevel(String fireHazardLevel) 
    {
        this.fireHazardLevel = fireHazardLevel;
    }

    public String getFireHazardLevel() 
    {
        return fireHazardLevel;
    }
    public void setRiskLevel(String riskLevel) 
    {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() 
    {
        return riskLevel;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("unitName", getUnitName())
            .append("areaName", getAreaName())
            .append("propertyUnit", getPropertyUnit())
            .append("isManagedByTglw", getIsManagedByTglw())
            .append("buildingPartition", getBuildingPartition())
            .append("buildingAddress", getBuildingAddress())
            .append("buildingStructure", getBuildingStructure())
            .append("fireResistanceLevel", getFireResistanceLevel())
            .append("usageStatus", getUsageStatus())
            .append("buildingTypeTag", getBuildingTypeTag())
            .append("functionTag1", getFunctionTag1())
            .append("functionTag2", getFunctionTag2())
            .append("buildingArea", getBuildingArea())
            .append("buildingHeight", getBuildingHeight())
            .append("floorCount", getFloorCount())
            .append("maxFloorHeight", getMaxFloorHeight())
            .append("mezzanineArea", getMezzanineArea())
            .append("attachmentArea", getAttachmentArea())
            .append("fireFacilities", getFireFacilities())
            .append("mainMaterials", getMainMaterials())
            .append("fireHazardLevel", getFireHazardLevel())
            .append("riskLevel", getRiskLevel())
            .toString();
    }
}
