package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.Cover;

/**
 * 井盖信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-21
 */
public interface CoverMapper 
{
    /**
     * 查询井盖信息
     * 
     * @param id 井盖信息主键
     * @return 井盖信息
     */
    public Cover selectCoverById(Long id);

    /**
     * 查询井盖信息列表
     * 
     * @param cover 井盖信息
     * @return 井盖信息集合
     */
    public List<Cover> selectCoverList(Cover cover);

    /**
     * 新增井盖信息
     * 
     * @param cover 井盖信息
     * @return 结果
     */
    public int insertCover(Cover cover);

    /**
     * 修改井盖信息
     * 
     * @param cover 井盖信息
     * @return 结果
     */
    public int updateCover(Cover cover);

    /**
     * 删除井盖信息
     * 
     * @param id 井盖信息主键
     * @return 结果
     */
    public int deleteCoverById(Long id);

    /**
     * 批量删除井盖信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCoverByIds(Long[] ids);
}
