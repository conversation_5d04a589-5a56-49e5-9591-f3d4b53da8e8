package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.Cameras;
import com.ruoyi.shcy.dto.CameraDTO;
import com.ruoyi.shcy.dto.GroupCameraDTO;

import java.util.List;

/**
 * 监控资源Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-24
 */
public interface ICamerasService 
{
    /**
     * 查询监控资源
     * 
     * @param id 监控资源主键
     * @return 监控资源
     */
    public Cameras selectCamerasById(Long id);

    public Cameras selectCamerasByCameraIndexCode(String cameraIndexCode);

    /**
     * 查询监控资源列表
     * 
     * @param cameras 监控资源
     * @return 监控资源集合
     */
    public List<Cameras> selectCamerasList(Cameras cameras);

    /**
     * 新增监控资源
     * 
     * @param cameras 监控资源
     * @return 结果
     */
    public int insertCameras(Cameras cameras);

    int batchInsertCameras(List<Cameras> camerasList);

    /**
     * 修改监控资源
     * 
     * @param cameras 监控资源
     * @return 结果
     */
    public int updateCameras(Cameras cameras);

    /**
     * 批量删除监控资源
     * 
     * @param ids 需要删除的监控资源主键集合
     * @return 结果
     */
    public int deleteCamerasByIds(Long[] ids);

    /**
     * 删除监控资源信息
     * 
     * @param id 监控资源主键
     * @return 结果
     */
    public int deleteCamerasById(Long id);

    List<CameraDTO> selectGroupCameras(String groupType) throws Exception;

    List<GroupCameraDTO> getGroupCamera();

    List<CameraDTO> selectLinkCameras(String cameras) throws Exception;
}
