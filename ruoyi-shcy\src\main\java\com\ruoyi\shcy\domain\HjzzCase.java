package com.ruoyi.shcy.domain;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.DateUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;

/**
 * 环境整治案事件对象 shcy_hjzz_case
 *
 * <AUTHOR>
 * @date 2023-11-08
 */
public class HjzzCase extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 事件名称
     */
    @Excel(name = "事件名称")
    private String caseName;

    /**
     * 事件类型(1:偷倒垃圾 2:违规生产入侵)
     */
    @Excel(name = "事件类型")
    private String caseType;

    /**
     * 事件描述
     */
    @Excel(name = "事件描述")
    private String caseDescription;

    /**
     * 现场处置人
     */
    @Excel(name = "现场处置人")
    private String caseDealBy;

    /**
     * 处理照片信息
     */
    @Excel(name = "处理照片信息")
    private String caseDealPhoto;

    /**
     * 处置状态  0:已完成 1:待处理 2:处理中 3:作废 4:退回
     */
    @Excel(name = "处置状态")
    private String circulationState;

    /**
     * 事件地点
     */
    @Excel(name = "事件地点")
    private String address;

    /**
     * 报警记录id
     */
    private Long alarmRecordId;

    /**
     * 现场处置截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseEndTime;

    /**
     * 现场处置时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseFinishTime;

    /**
     * 现场处置状态(0:正常 1:超时)
     */
    private String dealInTimeState;

    /**
     * 查处截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date investigationDeadline;

    /**
     * 查处完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date investigationCompleteTime;

    /**
     * 查处完成状态(0:正常 1:超时)
     */
    private String investigationCompleteState;

    /**
     * 事件建议
     */
    private String checkOthers;

    /**
     * 事件照片
     */
    private String checkPhoto;

    /**
     * 查处人
     */
    private String checkUpdateBy;

    /**
     * 查处时间
     */
    private Date checkUpdateTime;

    /**
     * 事件编号
     */
    @Excel(name = "事件编号")
    private String caseNumber;

    /**
     * 牌照
     */
    @Excel(name = "车牌")
    private String licensePlate;

    /**
     * 车型
     */
    private String vehicleType;

    /**
     * 责任人
     */
    private String responsiblePerson;

    /**
     * 照片URL
     */
    private List<String> photoUrls;

    /**
     * 偷倒垃圾类型
     */
    @Excel(name = "偷倒垃圾类型")
    private String dumpingGarbageType;

    /**
     * 是否个人行为
     */
    @Excel(name = "是否个人行为")
    private String isPersonalBehavior;

    /**
     * 是否立案
     */
    @Excel(name = "是否立案")
    private String isFiling;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 公司地址
     */
    @Excel(name = "公司地址")
    private String companyAddress;

    /**
     * 法人姓名
     */
    @Excel(name = "法人姓名")
    private String legalName;

    /**
     * 姓名
     */
    @Excel(name = "姓名")
    private String personnelInfo;

    /**
     * 身份证号码
     */
    @Excel(name = "身份证号码")
    private String idCardNo;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式")
    private String contactInfo;

    /**
     * 处罚案件号
     */
    @Excel(name = "处罚案件号")
    private String penaltyCaseNo;

    /**
     * 处罚金额
     */
    private BigDecimal penaltyAmount;

    /** 报警发生时间 */
    @Excel(name = "报警发生时间")
    private String alarmDate;

    private String group;

    /**
     * 附件
     */
    private String attachment;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setCaseName(String caseName) {
        this.caseName = caseName;
    }

    public String getCaseName() {
        return caseName;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getCaseType() {
        return caseType;
    }

    public void setCaseDescription(String caseDescription) {
        this.caseDescription = caseDescription;
    }

    public String getCaseDescription() {
        return caseDescription;
    }

    public void setCaseDealBy(String caseDealBy) {
        this.caseDealBy = caseDealBy;
    }

    public String getCaseDealBy() {
        return caseDealBy;
    }

    public void setCaseDealPhoto(String caseDealPhoto) {
        this.caseDealPhoto = caseDealPhoto;
    }

    public String getCaseDealPhoto() {
        return caseDealPhoto;
    }

    public void setCirculationState(String circulationState) {
        this.circulationState = circulationState;
    }

    public String getCirculationState() {
        return circulationState;
    }

    public void setDealInTimeState(String dealInTimeState) {
        this.dealInTimeState = dealInTimeState;
    }

    public String getDealInTimeState() {
        return dealInTimeState;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setAlarmRecordId(Long alarmRecordId) {
        this.alarmRecordId = alarmRecordId;
    }

    public Long getAlarmRecordId() {
        return alarmRecordId;
    }

    public void setCaseEndTime(Date caseEndTime) {
        this.caseEndTime = caseEndTime;
    }

    public Date getCaseEndTime() {
        return caseEndTime;
    }

    public void setCaseFinishTime(Date caseFinishTime) {
        this.caseFinishTime = caseFinishTime;
    }

    public Date getCaseFinishTime() {
        return caseFinishTime;
    }

    public void setCheckOthers(String checkOthers) {
        this.checkOthers = checkOthers;
    }

    public String getCheckOthers() {
        return checkOthers;
    }

    public void setCheckPhoto(String checkPhoto) {
        this.checkPhoto = checkPhoto;
    }

    public String getCheckPhoto() {
        return checkPhoto;
    }

    public void setCheckUpdateBy(String checkUpdateBy) {
        this.checkUpdateBy = checkUpdateBy;
    }

    public String getCheckUpdateBy() {
        return checkUpdateBy;
    }

    public void setCheckUpdateTime(Date checkUpdateTime) {
        this.checkUpdateTime = checkUpdateTime;
    }

    public Date getCheckUpdateTime() {
        return checkUpdateTime;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getResponsiblePerson() {
        return responsiblePerson;
    }

    public void setResponsiblePerson(String responsiblePerson) {
        this.responsiblePerson = responsiblePerson;
    }

    public List<String> getPhotoUrls() {
        return photoUrls;
    }

    public void setPhotoUrls(List<String> photoUrls) {
        this.photoUrls = photoUrls;
    }

    public String getDumpingGarbageType() {
        return dumpingGarbageType;
    }

    public void setDumpingGarbageType(String dumpingGarbageType) {
        this.dumpingGarbageType = dumpingGarbageType;
    }

    public String getIsPersonalBehavior() {
        return isPersonalBehavior;
    }

    public void setIsPersonalBehavior(String isPersonalBehavior) {
        this.isPersonalBehavior = isPersonalBehavior;
    }

    public String getIsFiling() {
        return isFiling;
    }

    public void setIsFiling(String isFiling) {
        this.isFiling = isFiling;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getPersonnelInfo() {
        return personnelInfo;
    }

    public void setPersonnelInfo(String personnelInfo) {
        this.personnelInfo = personnelInfo;
    }

    public String getIdCardNo() {
        return idCardNo;
    }

    public void setIdCardNo(String idCardNo) {
        this.idCardNo = idCardNo;
    }

    public String getPenaltyCaseNo() {
        return penaltyCaseNo;
    }

    public void setPenaltyCaseNo(String penaltyCaseNo) {
        this.penaltyCaseNo = penaltyCaseNo;
    }

    public BigDecimal getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public String getAlarmDate() {
        return alarmDate;
    }

    public void setAlarmDate(String alarmDate) {
        this.alarmDate = alarmDate;
    }

    public Date getInvestigationDeadline() {
        return investigationDeadline;
    }

    public void setInvestigationDeadline(Date investigationDeadline) {
        this.investigationDeadline = investigationDeadline;
    }

    public Date getInvestigationCompleteTime() {
        return investigationCompleteTime;
    }

    public void setInvestigationCompleteTime(Date investigationCompleteTime) {
        this.investigationCompleteTime = investigationCompleteTime;
    }

    public String getInvestigationCompleteState() {
        return investigationCompleteState;
    }

    public void setInvestigationCompleteState(String investigationCompleteState) {
        this.investigationCompleteState = investigationCompleteState;
    }

    public String getLegalName() {
        return legalName;
    }

    public void setLegalName(String legalName) {
        this.legalName = legalName;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public String getGroup() {
        // 根据this.getCreateTime()判断是哪个组, 小于14:30的是城管机动班, 否则是中班
        if (this.getCreateTime() != null) {
            // 给定的日期时间
            LocalDateTime givenDateTime = LocalDateTimeUtil.of(this.getCreateTime());
            // 指定下午14:30:00的时间
            LocalDateTime targetDateTime = LocalDateTime.of(givenDateTime.toLocalDate(), LocalTime.of(14, 30));
            // 比较给定时间的时分秒是否在下午14:30:00之前
            if (givenDateTime.isBefore(targetDateTime)) {
                group = "城管机动班";
            } else {
                group = "中班";
            }
        }
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getAttachment() {
        return attachment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("caseName", getCaseName())
                .append("caseType", getCaseType())
                .append("caseDescription", getCaseDescription())
                .append("caseDealBy", getCaseDealBy())
                .append("caseDealPhoto", getCaseDealPhoto())
                .append("circulationState", getCirculationState())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("dealInTimeState", getDealInTimeState())
                .append("address", getAddress())
                .append("alarmRecordId", getAlarmRecordId())
                .append("caseEndTime", getCaseEndTime())
                .append("caseFinishTime", getCaseFinishTime())
                .append("checkOthers", getCheckOthers())
                .append("checkPhoto", getCheckPhoto())
                .append("checkUpdateBy", getCheckUpdateBy())
                .append("checkUpdateTime", getCheckUpdateTime())
                .append("caseNumber", getCaseNumber())
                .append("licensePlate", getLicensePlate())
                .append("dumpingGarbageType", getDumpingGarbageType())
                .append("isPersonalBehavior", getIsPersonalBehavior())
                .append("isFiling", getIsFiling())
                .append("companyName", getCompanyName())
                .append("companyAddress", getCompanyAddress())
                .append("legalName", getLegalName())
                .append("personnelInfo", getPersonnelInfo())
                .append("idCardNo", getIdCardNo())
                .append("contactInfo", getContactInfo())
                .append("penaltyCaseNo", getPenaltyCaseNo())
                .append("penaltyAmount", getPenaltyAmount())
                .append("alarmDate", getAlarmDate())
                .append("investigationDeadline", getInvestigationDeadline())
                .append("investigationCompleteTime", getInvestigationCompleteTime())
                .append("investigationCompleteState", getInvestigationCompleteState())
                .append("group", getGroup())
                .append("attachment", getAttachment())
                .toString();
    }
}
