package com.ruoyi.web.controller.screen;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.shcy.constant.HjzzConstants;
import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.service.IHjzzCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Api("大屏环境整治接口")
@RestController
@RequestMapping("/screen/hjzz")
public class ScreenHjzzController {

    @Autowired
    private IHjzzCaseService hjzzCaseService;

    /**
     * 偷倒垃圾案件数据统计
     */
    @ApiOperation("偷倒垃圾案件数据统计")
    @PostMapping("/tdljCaseNumber")
    public AjaxResult tdljCaseNumber(@RequestBody HjzzCase hjzzCase) {
        if (StrUtil.isEmpty(hjzzCase.getAddress())) {
            return AjaxResult.error("事件地点不能为空");
        }
        hjzzCase.getParams().put("beginTime", DateUtil.format(DateUtil.beginOfMonth(new Date()), DatePattern.NORM_DATE_PATTERN));
        hjzzCase.getParams().put("endTime", DateUtil.format(DateUtil.endOfMonth(new Date()), DatePattern.NORM_DATE_PATTERN));
        hjzzCase.setCaseType("1");
        List<HjzzCase> monthHjzzCaseList = hjzzCaseService.selectHjzzCaseCount(hjzzCase);
        int monthTotal = (int) monthHjzzCaseList.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).count();
        // 作废数量
        int monthDiscarded = (int) monthHjzzCaseList.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_INVALID.equals(hjzzCase1.getCirculationState())).count();
        int monthInProcess = (int) monthHjzzCaseList.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_UNPROCESSED.equals(hjzzCase1.getCirculationState()) || HjzzConstants.CIRCULATION_STATE_PROCESSING.equals(hjzzCase1.getCirculationState())).count();
        int monthCompleted = (int) monthHjzzCaseList.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState())).count();
        double monthClosureRate = calculateClosureRate(monthTotal, monthDiscarded, monthCompleted);
        // 从monthHjzzCaseList中过滤出alarmDate是当天的数据
        int dayCount = (int) monthHjzzCaseList.stream().filter(hjzzCase1 -> DateUtil.isSameDay(DateUtil.parseDate(hjzzCase1.getAlarmDate()), DateUtil.parseDate(DateUtil.today())) && !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).count();;

        Map<String, Object> result = new HashMap<String, Object>() {{
            put("address", hjzzCase.getAddress());
            put("dayCount", dayCount);
            put("monthTotal", monthTotal);
            put("monthCompleted", monthCompleted);
            put("monthInProcess", monthInProcess);
            put("monthClosureRate", monthClosureRate);
        }};

        return AjaxResult.success(result);
    }

    /**
     * 违规生产入侵案件数据统计
     */
    @ApiOperation("违规生产入侵案件数据统计")
    @PostMapping("/wgscrqCaseNumber")
    public AjaxResult wgscrqCaseNumber(@RequestBody HjzzCase hjzzCase) {
        if (StrUtil.isEmpty(hjzzCase.getAddress())) {
            return AjaxResult.error("事件地点不能为空");
        }
        hjzzCase.getParams().put("beginTime", DateUtil.today());
        hjzzCase.getParams().put("endTime", DateUtil.today());
        hjzzCase.setCaseType("2");
        List<HjzzCase> list = hjzzCaseService.selectHjzzCaseCount(hjzzCase);
        int dayCount = list.size();
        // list中过滤出vehicleType是渣土车的数据
        int ztcCount = (int) list.stream().filter(hjzzCase1 -> "渣土车".equals(hjzzCase1.getVehicleType())).count();
        int hntjbcCount = (int) list.stream().filter(hjzzCase1 -> "混凝土搅拌车".equals(hjzzCase1.getVehicleType())).count();
        Map<String, Object> result = new HashMap<String, Object>() {{
            put("address", hjzzCase.getAddress());
            put("dayCount", dayCount);
            put("ztcCount", ztcCount);
            put("hntjbcCount", hntjbcCount);
        }};
        return AjaxResult.success(result);
    }

    /**
     * 计算结案率
     *
     * @param total     总数
     * @param discarded 作废数量
     * @param completed 已完成数量
     * @return 结案率
     */
    public double calculateClosureRate(int total, int discarded, int completed) {
        return (total - discarded) == 0 ? 0.0D : (double) completed / (total - discarded);
    }

}
