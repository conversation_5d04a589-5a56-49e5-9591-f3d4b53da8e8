package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ResidentialsMapper;
import com.ruoyi.shcy.domain.Residentials;
import com.ruoyi.shcy.service.IResidentialsService;

/**
 * 小区信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class ResidentialsServiceImpl implements IResidentialsService 
{
    @Autowired
    private ResidentialsMapper residentialsMapper;

    /**
     * 查询小区信息
     * 
     * @param id 小区信息主键
     * @return 小区信息
     */
    @Override
    public Residentials selectResidentialsById(Long id)
    {
        return residentialsMapper.selectResidentialsById(id);
    }

    /**
     * 查询小区信息列表
     * 
     * @param residentials 小区信息
     * @return 小区信息
     */
    @Override
    public List<Residentials> selectResidentialsList(Residentials residentials)
    {
        return residentialsMapper.selectResidentialsList(residentials);
    }

    /**
     * 新增小区信息
     * 
     * @param residentials 小区信息
     * @return 结果
     */
    @Override
    public int insertResidentials(Residentials residentials)
    {
        residentials.setCreateTime(DateUtils.getNowDate());
        return residentialsMapper.insertResidentials(residentials);
    }

    /**
     * 修改小区信息
     * 
     * @param residentials 小区信息
     * @return 结果
     */
    @Override
    public int updateResidentials(Residentials residentials)
    {
        residentials.setUpdateTime(DateUtils.getNowDate());
        return residentialsMapper.updateResidentials(residentials);
    }

    /**
     * 批量删除小区信息
     * 
     * @param ids 需要删除的小区信息主键
     * @return 结果
     */
    @Override
    public int deleteResidentialsByIds(Long[] ids)
    {
        return residentialsMapper.deleteResidentialsByIds(ids);
    }

    /**
     * 删除小区信息信息
     * 
     * @param id 小区信息主键
     * @return 结果
     */
    @Override
    public int deleteResidentialsById(Long id)
    {
        return residentialsMapper.deleteResidentialsById(id);
    }
}
