package com.ruoyi.shcy.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2023/2/24 13:50
 * @Version 1.0
 */
public class ShopCommitteeCheckStatusVo {

    /** 商铺名称 */
    private String shopName;

    /** 巡检日期**/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date checkDate;

    /** 巡检人员 **/
    private String checkPerson;

    /*** 巡检距离状态 */
    private String distanceStatus;

    /** 巡检状态*/
    private String checkStatus;

    /** 居委会**/
    private String  committee;


    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public String getCheckPerson() {
        return checkPerson;
    }

    public void setCheckPerson(String checkPerson) {
        this.checkPerson = checkPerson;
    }

    public String getDistanceStatus() {
        return distanceStatus;
    }

    public void setDistanceStatus(String distanceStatus) {
        this.distanceStatus = distanceStatus;
    }

    public String getCheckStatus() {
        return checkStatus;
    }

    public void setCheckStatus(String checkStatus) {
        this.checkStatus = checkStatus;
    }

    public String getCommittee() {
        return committee;
    }

    public void setCommittee(String committee) {
        this.committee = committee;
    }
}
