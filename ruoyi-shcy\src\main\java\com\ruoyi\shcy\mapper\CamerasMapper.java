package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.Cameras;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监控资源Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-24
 */
public interface CamerasMapper 
{
    /**
     * 查询监控资源
     * 
     * @param id 监控资源主键
     * @return 监控资源
     */
    public Cameras selectCamerasById(Long id);

    /**
     * 查询监控资源列表
     * 
     * @param cameras 监控资源
     * @return 监控资源集合
     */
    public List<Cameras> selectCamerasList(Cameras cameras);

    /**
     * 新增监控资源
     * 
     * @param cameras 监控资源
     * @return 结果
     */
    public int insertCameras(Cameras cameras);

    /**
     * 修改监控资源
     * 
     * @param cameras 监控资源
     * @return 结果
     */
    public int updateCameras(Cameras cameras);

    /**
     * 删除监控资源
     * 
     * @param id 监控资源主键
     * @return 结果
     */
    public int deleteCamerasById(Long id);

    /**
     * 批量删除监控资源
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCamerasByIds(Long[] ids);

    int batchInsertCameras(@Param("camerasList") List<Cameras> camerasList);

    Cameras selectCamerasByCameraIndexCode(@Param("cameraIndexCode") String cameraIndexCode);

    List<Cameras> selectCameraListByIds(Long[] ids);
}
