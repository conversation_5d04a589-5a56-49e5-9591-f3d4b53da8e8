package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 网格化案事件对象 shcy_grid_case
 * 
 * <AUTHOR>
 * @date 2023-11-21
 */
public class GridCase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 任务编号 */
    @Excel(name = "任务编号")
    private String taskId;

    /** 案件编号 */
    @Excel(name = "案件编号")
    private String caseId;

    /** 问题来源 */
    @Excel(name = "问题来源")
    private String problemSource;

    /** 发现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发现时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date discoveryTime;

    /** 当前状态 */
    @Excel(name = "当前状态")
    private String currentStatus;

    /** 热线录音 */
    private String hotlineRecording;

    /** 主责部门 */
    @Excel(name = "主责部门")
    private String mainDepartment;

    /** 案件类别 */
    @Excel(name = "案件类别")
    private String caseCategory;

    /** 诉求联系人 */
    @Excel(name = "诉求联系人")
    private String complainant;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 街道 */
    private String street;

    /** 居村 */
    private String village;

    /** 网格 */
    private String grid;

    /** 坐标 */
    private String coordinates;

    /** 结案评判 */
    private String caseEvaluation;

    /** 发生地址 */
    private String occurrenceAddress;

    /** 区域 */
    private String region;

    /** 问题描述 */
    private String problemDescription;

    /** 受理人员 */
    private String acceptPerson;

    /** 受理时间 */
    private Date acceptTime;

    /** 受理意见 */
    private String acceptOpinion;

    /** 立案人员 */
    private String filingPerson;

    /** 立案时间 */
    private Date filingTime;

    /** 立案意见 */
    private String filingOpinion;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTaskId(String taskId) 
    {
        this.taskId = taskId;
    }

    public String getTaskId() 
    {
        return taskId;
    }
    public void setCaseId(String caseId) 
    {
        this.caseId = caseId;
    }

    public String getCaseId() 
    {
        return caseId;
    }
    public void setProblemSource(String problemSource) 
    {
        this.problemSource = problemSource;
    }

    public String getProblemSource() 
    {
        return problemSource;
    }
    public void setDiscoveryTime(Date discoveryTime) 
    {
        this.discoveryTime = discoveryTime;
    }

    public Date getDiscoveryTime() 
    {
        return discoveryTime;
    }
    public void setCurrentStatus(String currentStatus) 
    {
        this.currentStatus = currentStatus;
    }

    public String getCurrentStatus() 
    {
        return currentStatus;
    }
    public void setHotlineRecording(String hotlineRecording) 
    {
        this.hotlineRecording = hotlineRecording;
    }

    public String getHotlineRecording() 
    {
        return hotlineRecording;
    }
    public void setMainDepartment(String mainDepartment) 
    {
        this.mainDepartment = mainDepartment;
    }

    public String getMainDepartment() 
    {
        return mainDepartment;
    }
    public void setCaseCategory(String caseCategory) 
    {
        this.caseCategory = caseCategory;
    }

    public String getCaseCategory() 
    {
        return caseCategory;
    }
    public void setComplainant(String complainant) 
    {
        this.complainant = complainant;
    }

    public String getComplainant() 
    {
        return complainant;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactPhone(String contactPhone) 
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() 
    {
        return contactPhone;
    }
    public void setStreet(String street) 
    {
        this.street = street;
    }

    public String getStreet() 
    {
        return street;
    }
    public void setVillage(String village) 
    {
        this.village = village;
    }

    public String getVillage() 
    {
        return village;
    }
    public void setGrid(String grid) 
    {
        this.grid = grid;
    }

    public String getGrid() 
    {
        return grid;
    }
    public void setCoordinates(String coordinates) 
    {
        this.coordinates = coordinates;
    }

    public String getCoordinates() 
    {
        return coordinates;
    }
    public void setCaseEvaluation(String caseEvaluation) 
    {
        this.caseEvaluation = caseEvaluation;
    }

    public String getCaseEvaluation() 
    {
        return caseEvaluation;
    }
    public void setOccurrenceAddress(String occurrenceAddress) 
    {
        this.occurrenceAddress = occurrenceAddress;
    }

    public String getOccurrenceAddress() 
    {
        return occurrenceAddress;
    }
    public void setRegion(String region) 
    {
        this.region = region;
    }

    public String getRegion() 
    {
        return region;
    }
    public void setProblemDescription(String problemDescription) 
    {
        this.problemDescription = problemDescription;
    }

    public String getProblemDescription() 
    {
        return problemDescription;
    }
    public void setAcceptPerson(String acceptPerson) 
    {
        this.acceptPerson = acceptPerson;
    }

    public String getAcceptPerson() 
    {
        return acceptPerson;
    }
    public void setAcceptTime(Date acceptTime) 
    {
        this.acceptTime = acceptTime;
    }

    public Date getAcceptTime() 
    {
        return acceptTime;
    }
    public void setAcceptOpinion(String acceptOpinion) 
    {
        this.acceptOpinion = acceptOpinion;
    }

    public String getAcceptOpinion() 
    {
        return acceptOpinion;
    }
    public void setFilingPerson(String filingPerson) 
    {
        this.filingPerson = filingPerson;
    }

    public String getFilingPerson() 
    {
        return filingPerson;
    }
    public void setFilingTime(Date filingTime) 
    {
        this.filingTime = filingTime;
    }

    public Date getFilingTime() 
    {
        return filingTime;
    }
    public void setFilingOpinion(String filingOpinion) 
    {
        this.filingOpinion = filingOpinion;
    }

    public String getFilingOpinion() 
    {
        return filingOpinion;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("taskId", getTaskId())
            .append("caseId", getCaseId())
            .append("problemSource", getProblemSource())
            .append("discoveryTime", getDiscoveryTime())
            .append("currentStatus", getCurrentStatus())
            .append("hotlineRecording", getHotlineRecording())
            .append("mainDepartment", getMainDepartment())
            .append("caseCategory", getCaseCategory())
            .append("complainant", getComplainant())
            .append("contactPerson", getContactPerson())
            .append("contactPhone", getContactPhone())
            .append("street", getStreet())
            .append("village", getVillage())
            .append("grid", getGrid())
            .append("coordinates", getCoordinates())
            .append("caseEvaluation", getCaseEvaluation())
            .append("occurrenceAddress", getOccurrenceAddress())
            .append("region", getRegion())
            .append("problemDescription", getProblemDescription())
            .append("acceptPerson", getAcceptPerson())
            .append("acceptTime", getAcceptTime())
            .append("acceptOpinion", getAcceptOpinion())
            .append("filingPerson", getFilingPerson())
            .append("filingTime", getFilingTime())
            .append("filingOpinion", getFilingOpinion())
            .toString();
    }
}
