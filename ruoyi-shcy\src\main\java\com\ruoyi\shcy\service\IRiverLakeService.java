package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.RiverLake;

/**
 * 河湖水体Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-03
 */
public interface IRiverLakeService 
{
    /**
     * 查询河湖水体
     * 
     * @param id 河湖水体主键
     * @return 河湖水体
     */
    public RiverLake selectRiverLakeById(Long id);

    /**
     * 查询河湖水体列表
     * 
     * @param riverLake 河湖水体
     * @return 河湖水体集合
     */
    public List<RiverLake> selectRiverLakeList(RiverLake riverLake);

    /**
     * 新增河湖水体
     * 
     * @param riverLake 河湖水体
     * @return 结果
     */
    public int insertRiverLake(RiverLake riverLake);

    /**
     * 修改河湖水体
     * 
     * @param riverLake 河湖水体
     * @return 结果
     */
    public int updateRiverLake(RiverLake riverLake);

    /**
     * 批量删除河湖水体
     * 
     * @param ids 需要删除的河湖水体主键集合
     * @return 结果
     */
    public int deleteRiverLakeByIds(Long[] ids);

    /**
     * 删除河湖水体信息
     * 
     * @param id 河湖水体主键
     * @return 结果
     */
    public int deleteRiverLakeById(Long id);
}
