package com.ruoyi.web.controller.screen;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.screen.vo.CaseNumberVO;
import com.ruoyi.screen.vo.CaseVO;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.Month;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 城市运行库 Controller
 *
 * @date 2023-03-01
 */
@Api("大屏城市运行库接口")
@RestController
@RequestMapping("/screen/csxyk")
public class ScreenCsxykController extends BaseController {

    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    /**
     * 案件数量统计
     */
    @ApiOperation("案件数量统计")
    @GetMapping("/caseNumber")
    public AjaxResult caseNumber() {
        TaskDbcenter taskDbcenter = new TaskDbcenter();
        Map<String, Object> params = taskDbcenter.getParams();
        params.put("beginTime", DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), DatePattern.NORM_DATE_PATTERN));
        params.put("endTime", DateUtil.format(DateUtil.endOfMonth(DateUtil.date()), DatePattern.NORM_DATE_PATTERN));
        params.put("typeName", "12345上报");
        params.put("typeName1", "兼职监督员上报");
        params.put("infobcname", "小区事务");
        taskDbcenter.setParams(params);
        List<TaskDbcenter> list = taskDbcenterService.selectSimpleTaskDbcenterList(taskDbcenter);
        // 本月标准案件总量 list.size()
        long monthCaseCount = list.size();
        // 当日标准案件量
        // 使用stream从list集合中过滤出discovertime为今天的案件总数
        long todayCaseCount = list.stream().filter(task -> DateUtil.isSameDay(task.getDiscovertime(), DateUtil.date())).count();
        // 本月突发事件总量 案件大类=突发事件
        // 使用stream从list集合中过滤出infobcname为突发事件并且infoscname不为架空线坠落、乱设或者description包含安全隐患的案件总数
        long monthEmergentCount = list.stream().filter(task -> "突发事件".equals(task.getInfobcname()) && !"架空线坠落、乱设".equals(task.getInfoscname()) || task.getDescription().contains("安全隐患")).count();
        // 使用stream从list集合中过滤出infobcname为突发事件并且infoscname不为架空线坠落、乱设或者description包含安全隐患并且statusname为已结案的案件总数
        long monthEmergentCloseCount = list.stream().filter(task -> "突发事件".equals(task.getInfobcname()) && !"架空线坠落、乱设".equals(task.getInfoscname()) || task.getDescription().contains("安全隐患")).filter(task -> "已结案".equals(task.getStatusname())).count();
        // 本月突发事件结案率 = 本月突发事件结案数 / 本月突发事件总数 * 100%
        double monthEmergentCloseRate = monthEmergentCount == 0 ? 0 : (double) monthEmergentCloseCount / monthEmergentCount * 100;

        // 保留两位小数
        monthEmergentCloseRate = (double) Math.round(monthEmergentCloseRate * 100) / 100;
        CaseNumberVO caseNumberVO = new CaseNumberVO();
        caseNumberVO.setMonthCaseCount(monthCaseCount);
        caseNumberVO.setTodayCaseCount(todayCaseCount);
        caseNumberVO.setMonthEmergentCount(monthEmergentCount);
        caseNumberVO.setMonthEmergentCloseRate(monthEmergentCloseRate);
        return AjaxResult.success(caseNumberVO);
    }

    /**
     * 案件列表
     * @return AjaxResult
     */
    @ApiOperation("案件列表")
    @GetMapping("/caseList")
    public AjaxResult caseList() {
        TaskDbcenter taskDbcenter = new TaskDbcenter();
        // 近15天已结案的案子
        Map<String, Object> params = taskDbcenter.getParams();
        // params.put("beginTime", DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -15), DatePattern.NORM_DATE_PATTERN));
        // params.put("endTime", DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN));
        params.put("beginTime", DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), DatePattern.NORM_DATE_PATTERN));
        params.put("endTime", DateUtil.format(DateUtil.endOfMonth(DateUtil.date()), DatePattern.NORM_DATE_PATTERN));
        params.put("typeName", "12345上报");
        params.put("typeName1", "兼职监督员上报");
        params.put("infobcname", "小区事务");
        // taskDbcenter.setStatusname("已结案");
        taskDbcenter.setParams(params);
        List<TaskDbcenter> list = taskDbcenterService.selectSimpleTaskDbcenterList(taskDbcenter);
        List<TaskDbcenter> collect = list.stream().filter(task -> "突发事件".equals(task.getInfobcname()) && !"架空线坠落、乱设".equals(task.getInfoscname()) || task.getDescription().contains("安全隐患")).collect(Collectors.toList());
        // 将list中的TaskDbcenter对象转换为CaseVO对象
        List<CaseVO> caseVOList = BeanUtil.copyToList(collect, CaseVO.class);
        return AjaxResult.success(caseVOList);
    }

    /**
     * 12345热线工单数量统计
     */
    @ApiOperation("12345热线工单数量统计")
    @GetMapping("/hotlineCaseNumber")
    public AjaxResult hotlineCaseNumber() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        Month currentMonth = currentDate.getMonth();
        String beginTime;
        String endTime;
        // 判断当前月份
        if (currentMonth == Month.NOVEMBER || currentMonth == Month.DECEMBER) {
            // 当前月份是11或12月，取下一个周期（今年11月到明年10月）
            int nextYear = currentYear + 1;
            LocalDate startDate = LocalDate.of(nextYear - 1, Month.NOVEMBER, 1);
            LocalDate endDate = LocalDate.of(nextYear, Month.OCTOBER, 31);
            beginTime = startDate.toString();
            endTime = endDate.toString();
        } else {
            // 当前月份不是11或12月，取上一个周期（上年11月到今年10月）
            LocalDate startDate = LocalDate.of(currentYear - 1, Month.NOVEMBER, 1);
            LocalDate endDate = LocalDate.of(currentYear, Month.OCTOBER, 31);
            beginTime = startDate.toString();
            endTime = endDate.toString();
        }

        TaskDbcenter taskDbcenter = new TaskDbcenter();
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("beginTime", beginTime);
        taskDbcenter.getParams().put("endTime", endTime);
        List<TaskDbcenter> list = taskDbcenterService.selectSimpleTaskDbcenterList(taskDbcenter);

        // 根据list集合中的discovertime字段进行年月分组beginTime到endTime的所有年月，然后统计每组的数量
        // 使用stream从list集合中过滤出discovertime在beginTime和endTime之间的案件，然后根据discovertime字段进行年月分组，统计每组的数量
        Map<String, Long> map = list.stream().filter(task -> DateUtil.isIn(task.getDiscovertime(), DateUtil.parse(beginTime), DateUtil.parse(endTime))).collect(Collectors.groupingBy(task -> DateUtil.format(task.getDiscovertime(), "yyyy-MM"), Collectors.counting()));

        // 使用stream从list集合中过滤出discovertime在beginTime和endTime之间的案件且satisfaction为不满意，然后根据discovertime字段进行年月分组，统计每组的数量
        Map<String, Long> satisfactionMap = list.stream().filter(task -> DateUtil.isIn(task.getDiscovertime(), DateUtil.parse(beginTime), DateUtil.parse(endTime)) && "不满意".equals(task.getSatisfaction())).collect(Collectors.groupingBy(task -> DateUtil.format(task.getDiscovertime(), "yyyy-MM"), Collectors.counting()));

        // 根据beginTime和endTime列出所有的年月，yyyy-MM格式返回字符串数组
        List<String > monthList = DateUtils.listMonths(beginTime, endTime);

        HashMap<String, Object> result = new HashMap<String, Object>() {{
            put("monthList", monthList);
            put("allData", map);
            put("satisfactionData", satisfactionMap);
        }};
        return AjaxResult.success(result);
    }

    /**
     * 获取本月突发事件列表
     */
    @ApiOperation("获取本月突发事件列表")
    @GetMapping("/emergentList")
    public AjaxResult emergentList() {
        TaskDbcenter taskDbcenter = new TaskDbcenter();
        Map<String, Object> params = taskDbcenter.getParams();
        params.put("beginTime", DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), DatePattern.NORM_DATE_PATTERN));
        params.put("endTime", DateUtil.format(DateUtil.endOfMonth(DateUtil.date()), DatePattern.NORM_DATE_PATTERN));
        params.put("typeName", "12345上报");
        params.put("typeName1", "兼职监督员上报");
        params.put("infobcname", "小区事务");
        taskDbcenter.setParams(params);
        List<TaskDbcenter> list = taskDbcenterService.selectSimpleTaskDbcenterList(taskDbcenter);
        List<TaskDbcenter> collect = list.stream().filter(task -> "突发事件".equals(task.getInfobcname()) && !"架空线坠落、乱设".equals(task.getInfoscname()) || task.getDescription().contains("安全隐患")).collect(Collectors.toList());
        return AjaxResult.success(collect);
    }



}

