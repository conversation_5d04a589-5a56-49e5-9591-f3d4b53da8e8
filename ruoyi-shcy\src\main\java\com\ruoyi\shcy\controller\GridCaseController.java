package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.GridCase;
import com.ruoyi.shcy.service.IGridCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 网格化案事件Controller
 * 
 * <AUTHOR>
 * @date 2023-11-21
 */
@RestController
@RequestMapping("/shcy/gridCase")
public class GridCaseController extends BaseController
{
    @Autowired
    private IGridCaseService gridCaseService;

    /**
     * 查询网格化案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridCase:list')")
    @GetMapping("/list")
    public TableDataInfo list(GridCase gridCase)
    {
        startPage();
        List<GridCase> list = gridCaseService.selectGridCaseList(gridCase);
        return getDataTable(list);
    }

    /**
     * 导出网格化案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridCase:export')")
    @Log(title = "网格化案事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GridCase gridCase)
    {
        List<GridCase> list = gridCaseService.selectGridCaseList(gridCase);
        ExcelUtil<GridCase> util = new ExcelUtil<GridCase>(GridCase.class);
        util.exportExcel(response, list, "网格化案事件数据");
    }

    /**
     * 获取网格化案事件详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridCase:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(gridCaseService.selectGridCaseById(id));
    }

    /**
     * 新增网格化案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridCase:add')")
    @Log(title = "网格化案事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GridCase gridCase)
    {
        return toAjax(gridCaseService.insertGridCase(gridCase));
    }

    /**
     * 修改网格化案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridCase:edit')")
    @Log(title = "网格化案事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GridCase gridCase)
    {
        return toAjax(gridCaseService.updateGridCase(gridCase));
    }

    /**
     * 删除网格化案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridCase:remove')")
    @Log(title = "网格化案事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(gridCaseService.deleteGridCaseByIds(ids));
    }
}
