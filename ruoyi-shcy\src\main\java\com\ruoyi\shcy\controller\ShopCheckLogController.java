package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.ShcyCase;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.ShopCheckLog;
import com.ruoyi.shcy.dto.JzzDto;
import com.ruoyi.shcy.service.IShcyCaseService;
import com.ruoyi.shcy.service.IShopCheckLogService;
import com.ruoyi.shcy.service.IShopService;
import com.ruoyi.system.service.ISysDeptService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 检查日志Controller
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@RestController
@RequestMapping("/shcy/shopCheckLog")
public class ShopCheckLogController extends BaseController
{
    @Autowired
    private IShopCheckLogService shopCheckLogService;

    @Autowired
    private ISysDeptService iSysDeptService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private IShopService shopService;

    @Autowired
    private IShcyCaseService shcyCaseService;

    /**
     * 查询检查日志列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:shopCheckLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShopCheckLog shopCheckLog) throws ParseException {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();
        String deptName = user.getDept().getDeptName();
        if(Objects.equals(user.getUserName(), "admin") || "营商办".equals(getUsername()) || "城运中心".equals(deptName)){
            // 获取当前的用户名称
            startPage();
            List<ShopCheckLog> list = shopCheckLogService.selectShopCheckLogList(shopCheckLog);
            return getDataTable(list);
        }

        if(deptId!=null&& !Objects.equals(user.getUserName(), "admin")){
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if(StringUtils.isNotEmpty(sysDept.getIsCommittee())&&sysDept.getIsCommittee().equals("1")) {     //获取居委会对应查看的检查日志列表
                startPage();
                shopCheckLog.setDeptId(sysDept.getDeptId());
                List<ShopCheckLog> list = shopCheckLogService.selectShopCheckLogList(shopCheckLog);
                return getDataTable(list);
            }else if(StringUtils.isNotEmpty(sysDept.getIsCommittee())&&sysDept.getIsCommittee().equals("2")){                                                                                 //获取网格长查看多个居委会数据

                TableDataInfo tableDataInfo = selectWanggezhangShopCheckLogList(user, shopCheckLog, shopCheckLogService);
                return tableDataInfo;
            }
        }
        // 获取当前的用户名称
        startPage();
//        List<ShopCheckLog> list = shopCheckLogService.selectShopCheckLogList(shopCheckLog);
        List<ShopCheckLog> list = new ArrayList<>();
        return getDataTable(list);
    }

    public static TableDataInfo  selectWanggezhangShopCheckLogList(SysUser user, ShopCheckLog shopCheckLog, IShopCheckLogService shopCheckLogService){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();


        List<ShopCheckLog> shopCheckLogList = shopCheckLogService.selectShopCheckLogList(shopCheckLog).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getDeptId())).collect(Collectors.toList());
        int num = shopCheckLogList.size();
        shopCheckLogList = shopCheckLogList.stream()
                .skip((pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(shopCheckLogList);
        rspData.setTotal(num);
        return rspData;
    }

    /**
     * 导出检查日志列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:shopCheckLog:export')")
    @Log(title = "检查日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShopCheckLog shopCheckLog)
    {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();

        if(user.getUserName().equals("admin") ){
            // 获取当前的用户名称

            List<ShopCheckLog> list = shopCheckLogService.selectShopCheckLogList(shopCheckLog);
            ExcelUtil<ShopCheckLog> util = new ExcelUtil<ShopCheckLog>(ShopCheckLog.class);
            util.exportExcel(response, list, "检查日志数据");
        }

        if(deptId!=null && !Objects.equals(user.getUserName(), "admin")){
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if(StringUtils.isNotEmpty(sysDept.getIsCommittee())&&sysDept.getIsCommittee().equals("1")) {     //获取居委会对应查看的店铺列表
                shopCheckLog.setDeptId(deptId);
                List<ShopCheckLog> list = shopCheckLogService.selectShopCheckLogList(shopCheckLog);
                ExcelUtil<ShopCheckLog> util = new ExcelUtil<ShopCheckLog>(ShopCheckLog.class);
                util.exportExcel(response, list, "检查日志数据");

            }else if(StringUtils.isNotEmpty(sysDept.getIsCommittee()) &&sysDept.getIsCommittee().equals("2")){ //获取网格长查看多个居委会数据
                List<ShopCheckLog> shopCheckLogList = shopCheckLogService.selectShopCheckLogList(shopCheckLog).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getDeptId())).collect(Collectors.toList());
                ExcelUtil<ShopCheckLog> util = new ExcelUtil<ShopCheckLog>(ShopCheckLog.class);
                util.exportExcel(response, shopCheckLogList, "检查日志数据");
            }
        }

    }


    /**
     * 查询检查日志列表
     * */
    @GetMapping(value="/threeList")
    public AjaxResult getCheckLogThreeList(ShopCheckLog shopCheckLog){
        List<ShopCheckLog> shopCheckLogs = shopCheckLogService.selectShopCheckLogListScreen(shopCheckLog);
        return AjaxResult.success(shopCheckLogs);
    }

    /**
     * 获取检查日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:shopCheckLog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shopCheckLogService.selectShopCheckLogById(id));
    }

    /**
     * 获取检查日志详细信息(最近三天)
     */
    @GetMapping(value = "/three/{id}")
    public AjaxResult getInfoThreeDays(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shopCheckLogService.selectShopCheckLogByIdThree(id));
    }

    /**
     * 新增检查日志
     */
    @PreAuthorize("@ss.hasPermi('shcy:shopCheckLog:add')")
    @Log(title = "检查日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShopCheckLog shopCheckLog)
    {
        shopCheckLog.setCreateBy(getUsername());
        return toAjax(shopCheckLogService.insertShopCheckLog(shopCheckLog));
    }

    /**
     * 修改检查日志
     */
    @PreAuthorize("@ss.hasPermi('shcy:shopCheckLog:edit')")
    @Log(title = "检查日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShopCheckLog shopCheckLog)
    {
        shopCheckLog.setUpdateBy(getUsername());
        int i = shopCheckLogService.updateShopCheckLog(shopCheckLog);
        return AjaxResult.success("成功！");
    }

    /**
     * 城管修改数据
     *
     * **/
    @PutMapping("/cg")
    public AjaxResult checkLogEditCg(@RequestBody ShopCheckLog shopCheckLog)
    {
        shopCheckLog.setCheckUpdateByCg(getUsername());
        return toAjax(shopCheckLogService.updateShopCheckLogByCg(shopCheckLog));
    }

    /**
     * 删除检查日志
     */
    @PreAuthorize("@ss.hasPermi('shcy:shopCheckLog:remove')")
    @Log(title = "检查日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shopCheckLogService.deleteShopCheckLogByIds(ids));
    }

    /**
     * “一街三长”汇总表导出
     */
    @PostMapping("/export1")
    public void export1(HttpServletResponse response, ShopCheckLog shopCheckLog)
    {
        List<SysDept> deptList = deptService.selectAllCommittee(null);

        List<Shop> shops = shopService.selectAllYyShops();

        List<Shop> allShutdownShops = shopService.selectAllShutdownShops();

        List<ShopCheckLog> shopCheckLogs = shopCheckLogService.selectShopCheckLogList1(shopCheckLog);

        // 过滤掉shopCheckLogs中的shopId在allShutdownShops中的ShopId，重新赋值给shopCheckLogs
        shopCheckLogs = shopCheckLogs.stream().filter(x -> allShutdownShops.stream().noneMatch(y -> y.getId().equals(x.getShopId()))).collect(Collectors.toList());

        List<JzzDto> exportList = new ArrayList<>();
        int i = 1;
        for (SysDept sysDept : deptList) {
            JzzDto jzzDto = new JzzDto();
            jzzDto.setSerialNumber(i++);
            jzzDto.setCommittee(sysDept.getDeptName());

            List<Shop> collect = shops.stream().filter(x -> x.getShopDeparmentId().equals(sysDept.getDeptId())).collect(Collectors.toList());

            // 商铺数量
            jzzDto.setShopCount(collect.size());

            // 社区街长数量 collect中的communityStreetChief去重计算总数
            long communityStreetCount = collect.stream().map(Shop::getCommunityStreetChief).distinct().count();
            jzzDto.setCommunityStreetCount((int) communityStreetCount);

            // 巡查户次
            List<ShopCheckLog> collect1 = shopCheckLogs.stream().filter(x -> x.getDeptId().equals(sysDept.getDeptId())).collect(Collectors.toList());
            jzzDto.setInspectionTimes(collect1.size());

            // collect1中shopId去重计算总数
            long shopCount = collect1.stream().map(ShopCheckLog::getShopId).distinct().count();

            // 覆盖率 jzzDto.getShopCount() 为0时，inspectionCompletionRate = 0 否则为 jzzDto.getInspectionTimes() / jzzDto.getShopCount() 取2位小数
            if (jzzDto.getShopCount() == 0) {
                jzzDto.setInspectionCompletionRate(0.0);
            } else {
                jzzDto.setInspectionCompletionRate((double) Math.round((double) shopCount / jzzDto.getShopCount() * 100) / 100);
            }



            // collect1中shopchecklogid 转成Long数组
            Long[] shopCheckLogIds = collect1.stream().map(ShopCheckLog::getId).toArray(Long[]::new);

            // 判断shopCheckLogIds是否为空
            if (shopCheckLogIds.length == 0) {
                jzzDto.setTotalReportCount(0);
                jzzDto.setTotalDisposalCount(0);
                jzzDto.setThreeInOneDisposalCount(0);
                jzzDto.setThreeInOneReportCount(0);
                jzzDto.setAdSafetyHazardDisposalCount(0);
                jzzDto.setAdSafetyHazardReportCount(0);
                jzzDto.setLicensePublicDisplayDisposalCount(0);
                jzzDto.setLicensePublicDisplayReportCount(0);
                jzzDto.setStreetShopConstructionSafetyDisposalCount(0);
                jzzDto.setStreetShopConstructionSafetyReportCount(0);
                jzzDto.setStreetShopCivilizedConstructionDisposalCount(0);
                jzzDto.setStreetShopCivilizedConstructionReportCount(0);
                exportList.add(jzzDto);
                continue;
            }

            List<ShcyCase> shcyCaseList =  shcyCaseService.selectShcyCaseListByShopCheckLogIds(shopCheckLogIds);

            // 合计上报数
            jzzDto.setTotalReportCount(shcyCaseList.size());

            // 合计处置数
            // shcyCaseList中circulationState为 "0" 的数量
            long count = shcyCaseList.stream().filter(x -> "0".equals(x.getCirculationState())).count();
            jzzDto.setTotalDisposalCount((int) count);

            // 是否存在“三合一”住人现象处置数
            // shcyCaseList中circulationState为 "0" 且caseType为 "1" 的数量
            long count1 = shcyCaseList.stream().filter(x -> "0".equals(x.getCirculationState()) && "1".equals(x.getCaseType())).count();

            // 是否存在“三合一”住人现象上报数
            // shcyCaseList中caseType为 "1" 的数量
            long count2 = shcyCaseList.stream().filter(x -> "1".equals(x.getCaseType())).count();

            // 广告牌存在安全隐患处置数
            // shcyCaseList中circulationState为 "0" 且caseType为 "2" 的数量
            long count3 = shcyCaseList.stream().filter(x -> "0".equals(x.getCirculationState()) && "2".equals(x.getCaseType())).count();

            // 广告牌存在安全隐患上报数
            // shcyCaseList中caseType为 "2" 的数量
            long count4 = shcyCaseList.stream().filter(x -> "2".equals(x.getCaseType())).count();

            // 证照公示处置数
            // shcyCaseList中circulationState为 "0" 且caseType为 "3" 的数量
            long count5 = shcyCaseList.stream().filter(x -> "0".equals(x.getCirculationState()) && "3".equals(x.getCaseType())).count();

            // 证照公示上报数
            // shcyCaseList中caseType为 "3" 的数量
            long count6 = shcyCaseList.stream().filter(x -> "3".equals(x.getCaseType())).count();

            // 沿街商铺施工安全处置数
            // shcyCaseList中circulationState为 "0" 且caseType为 "4" 的数量
            long count7 = shcyCaseList.stream().filter(x -> "0".equals(x.getCirculationState()) && "4".equals(x.getCaseType())).count();

            // 沿街商铺施工安全上报数
            // shcyCaseList中caseType为 "4" 的数量
            long count8 = shcyCaseList.stream().filter(x -> "4".equals(x.getCaseType())).count();

            // 沿街商铺文明施工处置数
            // shcyCaseList中circulationState为 "0" 且caseType为 "5" 的数量
            long count9 = shcyCaseList.stream().filter(x -> "0".equals(x.getCirculationState()) && "5".equals(x.getCaseType())).count();

            // 沿街商铺文明施工上报数
            // shcyCaseList中caseType为 "5" 的数量
            long count10 = shcyCaseList.stream().filter(x -> "5".equals(x.getCaseType())).count();

            jzzDto.setThreeInOneDisposalCount((int) count1);
            jzzDto.setThreeInOneReportCount((int) count2);

            jzzDto.setAdSafetyHazardDisposalCount((int) count3);
            jzzDto.setAdSafetyHazardReportCount((int) count4);

            jzzDto.setLicensePublicDisplayDisposalCount((int) count5);
            jzzDto.setLicensePublicDisplayReportCount((int) count6);

            jzzDto.setStreetShopConstructionSafetyDisposalCount((int) count7);
            jzzDto.setStreetShopConstructionSafetyReportCount((int) count8);

            jzzDto.setStreetShopCivilizedConstructionDisposalCount((int) count9);
            jzzDto.setStreetShopCivilizedConstructionReportCount((int) count10);

            exportList.add(jzzDto);
        }

        ExcelUtil<JzzDto> util = new ExcelUtil<JzzDto>(JzzDto.class);
        util.exportExcel(response, exportList, "汇总", "“一街三长”汇总表");
    }


}
