package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 绿化网格对象 shcy_green_grid
 * 
 * <AUTHOR>
 * @date 2023-02-09
 */
public class GreenGrid extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 居委会名称 */
    @Excel(name = "居委会名称")
    private String committeeName;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 经纬度（面） */
    @Excel(name = "经纬度", readConverterExp = "面=")
    private String siteRange;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 填充颜色 */
    @Excel(name = "填充颜色")
    private String fillColor;

    /** 边框色 */
    @Excel(name = "边框色")
    private String outlineColor;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCommitteeName(String committeeName) 
    {
        this.committeeName = committeeName;
    }

    public String getCommitteeName() 
    {
        return committeeName;
    }
    public void setLongitude(String longitude) 
    {
        this.longitude = longitude;
    }

    public String getLongitude() 
    {
        return longitude;
    }
    public void setLatitude(String latitude) 
    {
        this.latitude = latitude;
    }

    public String getLatitude() 
    {
        return latitude;
    }
    public void setSiteRange(String siteRange) 
    {
        this.siteRange = siteRange;
    }

    public String getSiteRange() 
    {
        return siteRange;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setCoordinate(String coordinate) 
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate() 
    {
        return coordinate;
    }
    public void setFillColor(String fillColor) 
    {
        this.fillColor = fillColor;
    }

    public String getFillColor() 
    {
        return fillColor;
    }
    public void setOutlineColor(String outlineColor) 
    {
        this.outlineColor = outlineColor;
    }

    public String getOutlineColor() 
    {
        return outlineColor;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("committeeName", getCommitteeName())
            .append("longitude", getLongitude())
            .append("latitude", getLatitude())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("siteRange", getSiteRange())
            .append("type", getType())
            .append("coordinate", getCoordinate())
            .append("fillColor", getFillColor())
            .append("outlineColor", getOutlineColor())
            .toString();
    }
}
