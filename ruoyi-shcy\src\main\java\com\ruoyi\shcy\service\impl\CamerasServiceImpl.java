package com.ruoyi.shcy.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.hikvision.ArtemisService;
import com.ruoyi.shcy.domain.Cameras;
import com.ruoyi.shcy.dto.CameraDTO;
import com.ruoyi.shcy.dto.GroupCameraDTO;
import com.ruoyi.shcy.mapper.CamerasMapper;
import com.ruoyi.shcy.service.ICamerasService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 监控资源Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-24
 */
@Service
public class CamerasServiceImpl implements ICamerasService {
    @Autowired
    private CamerasMapper camerasMapper;

    @Autowired
    private ArtemisService artemisService;

    /**
     * 查询监控资源
     *
     * @param id 监控资源主键
     * @return 监控资源
     */
    @Override
    public Cameras selectCamerasById(Long id) {
        return camerasMapper.selectCamerasById(id);
    }

    @Override
    public Cameras selectCamerasByCameraIndexCode(String cameraIndexCode) {
        return camerasMapper.selectCamerasByCameraIndexCode(cameraIndexCode);
    }

    /**
     * 查询监控资源列表
     *
     * @param cameras 监控资源
     * @return 监控资源
     */
    @Override
    public List<Cameras> selectCamerasList(Cameras cameras) {
        return camerasMapper.selectCamerasList(cameras);
    }

    /**
     * 新增监控资源
     *
     * @param cameras 监控资源
     * @return 结果
     */
    @Override
    public int insertCameras(Cameras cameras) {
        return camerasMapper.insertCameras(cameras);
    }

    @Override
    public int batchInsertCameras(List<Cameras> camerasList) {
        return camerasMapper.batchInsertCameras(camerasList);
    }

    /**
     * 修改监控资源
     *
     * @param cameras 监控资源
     * @return 结果
     */
    @Override
    public int updateCameras(Cameras cameras) {
        return camerasMapper.updateCameras(cameras);
    }

    /**
     * 批量删除监控资源
     *
     * @param ids 需要删除的监控资源主键
     * @return 结果
     */
    @Override
    public int deleteCamerasByIds(Long[] ids) {
        return camerasMapper.deleteCamerasByIds(ids);
    }

    /**
     * 删除监控资源信息
     *
     * @param id 监控资源主键
     * @return 结果
     */
    @Override
    public int deleteCamerasById(Long id) {
        return camerasMapper.deleteCamerasById(id);
    }

    @Override
    public List<CameraDTO> selectGroupCameras(String groupType) throws Exception {
        List<Cameras> camerasList = getCameras(groupType);
        List<CameraDTO> cameraDTOList = new ArrayList<>();
        for (Cameras camera : camerasList) {
            CameraDTO cameraDTO = new CameraDTO();
            cameraDTO.setId(camera.getId());
            cameraDTO.setCameraIndexCode(camera.getCameraIndexCode());
            cameraDTO.setName(camera.getName());
            String json = artemisService.callPostApiPreviewURLsWs(camera.getCameraIndexCode());
            JSONObject jsonObject = JSON.parseObject(json);
            JSONObject data = jsonObject.getJSONObject("data");
            // 判断data是否为null
            if (data != null) {
                String url = data.get("url").toString();
                cameraDTO.setUrl(url);
            } else {
                cameraDTO.setUrl("");
            }
            cameraDTOList.add(cameraDTO);
        }
        return cameraDTOList;
    }

    private List<Cameras> getCameras(String groupType) {
        Long[] ids = new Long[0];
        switch (groupType) {
            case "1":
                ids = new Long[]{
                        1864L,
                        1863L,
                        1793L,
                        1751L,
                        1575L,
                        1571L,
                        1557L,
                        1538L,
                        1460L,
                        1325L,
                        1323L,
                        1322L,
                        1321L,
                        1304L,
                        1158L,
                        1153L
                };
                break;
            case "2":
                ids = new Long[]{1871L, 1877L, 1888L, 1895L, 1917L, 1926L};
                break;
            case "3":
                ids = new Long[]{
                        1096L,
                        1154L,
                        1174L,
                        1215L,
                        1257L,
                        1259L,
                        1317L,
                        1318L,
                        1345L,
                        1512L,
                        1576L,
                        1689L,
                        1694L,
                        1701L,
                        1843L,
                        1851L};
                break;
            case "4":
                ids = new Long[]{1193L, 1371L, 1532L, 1258L, 2088L, 2096L, 1843L,1257L};
                break;
            case "5":
                ids = new Long[]{
                        1428L,
                        1510L,
                        1528L,
                        1532L,
                        1547L,
                        1582L,
                        1583L,
                        1599L,
                        1644L,
                        1647L,
                        1657L,
                        1759L,
                        1828L,
                        1829L,
                        1865L,
                };
                break;
            case "6":
                ids = new Long[]{
                        1520L,
                        914L,
                        1140L,
                        1440L,
                        1454L,
                        1505L,
                        1520L,
                        1521L,
                        1564L,
                        1567L,
                        1668L,
                        1669L,
                        1710L,
                        1765L,
                        1819L
                };
                break;
            case "7":
                ids = new Long[]{
                        1847L,
                        2088L,
                        2096L,
                        1532L,
                        1722L,
                        1851L,
                        1843L,
                        1729L,
                        1446L,
                        1512L,
                        2854L,
                        2855L,
                        2856L,
                        2857L,
                        2858L
                };
                break;
            case "8":
                ids = new Long[]{
                        1424L,
                        1433L,
                        1438L,
                        1439L,
                        1441L,
                        1533L,
                        1534L,
                        1581L,
                        1582L,
                        1583L,
                        1645L,
                        1663L,
                        1755L,
                        1765L,
                        2823L,
                        2824L,
                        2825L,
                        2844L
                };
                break;
            case "9":
                ids = new Long[]{
                        1097L,  // 31011622001180023016
                        953L,   // 31011622001180010003
                        1203L,  // 31011622001180023012
                        955L,   // 31011622001180031003
                        1235L,  // 31011622001180031002
                        952L,   // 31011622001180010001
                        951L,   // 31011622001180025016
                        954L,   // 31011622001180010002
                        1148L,  // 31011622001180009016
                        1206L,  // 31011622001180023015
                        1205L,  // 31011622001180009015
                        957L,   // 31011622001180024001
                        1200L,  // 31011622001180009014
                        1201L   // 31011622001180009013
                };
                break;
            case "10":
                ids = new Long[]{
                        1326L,
                        1325L,
                        1323L,
                        1322L,
                        1321L,
                        53L,
                        1039L
                };
                break;
        }
        List<Cameras> camerasList = camerasMapper.selectCameraListByIds(ids);
        return camerasList;
    }

    private String getGroupName(String groupType) {
        switch (groupType) {
            case "1":
                return "防汛重点区域";
            case "2":
                return "低洼积水";
            case "3":
                return "创城创卫";
            case "4":
                return "农贸市场";
            case "5":
                return "海岸线秩序";
            case "6":
                return "公园绿地广场";
            case "7":
                return "隆安路整治";
            case "8":
                return "烟花集中燃放区域";
            case "9":
                return "卫五北路";
            case "10":
                return "金卫三组巡检";
        }
        return "";
    }

    @Override
    public List<GroupCameraDTO> getGroupCamera() {
        ArrayList<GroupCameraDTO> result = new ArrayList<>();
        for (int i = 1; i < 11; i++) {  // 修改循环上限为11，包含新增的第10组
            String groupType = String.valueOf(i);
            GroupCameraDTO groupCameraDTO = new GroupCameraDTO();
            groupCameraDTO.setGroupName(getGroupName(groupType));
            groupCameraDTO.setGroupType(groupType);
            groupCameraDTO.setCameraDTOList(BeanUtil.copyToList(getCameras(groupType), CameraDTO.class));
            result.add(groupCameraDTO);
        }
        return result;
    }

    @Override
    public List<CameraDTO> selectLinkCameras(String cameras) throws Exception {
        List<CameraDTO> cameraDTOList = new ArrayList<>();
        if (StrUtil.isEmpty(cameras)) {
            return cameraDTOList;
        }
        String[] cameraIndexCodes = cameras.split(",");
        for (String cameraIndexCode : cameraIndexCodes) {
            CameraDTO cameraDTO = new CameraDTO();
            cameraDTO.setCameraIndexCode(cameraIndexCode);
            String json = artemisService.callPostApiPreviewURLsWs(cameraIndexCode);
            JSONObject jsonObject = JSON.parseObject(json);
            JSONObject data = jsonObject.getJSONObject("data");
            String url = data.get("url").toString();
            cameraDTO.setUrl(url);
            cameraDTOList.add(cameraDTO);
        }
        return cameraDTOList;
    }


}
