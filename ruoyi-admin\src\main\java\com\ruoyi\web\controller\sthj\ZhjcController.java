package com.ruoyi.web.controller.sthj;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.shcy.domain.ComprehensiveInspection;
import com.ruoyi.shcy.service.IComprehensiveInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 综合检查Controller
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/sthj/zhjc")
public class ZhjcController extends BaseController {

    @Autowired
    private IComprehensiveInspectionService comprehensiveInspectionService;

    /**
     * 上报
     */
    @PostMapping("/sb")
    public AjaxResult sb(@RequestBody ComprehensiveInspection comprehensiveInspection) {
        // circulationState 待处理
        comprehensiveInspection.setCirculationState("1");
        // createBy
        comprehensiveInspection.setCreateBy(getUsername());
        return toAjax(comprehensiveInspectionService.insertComprehensiveInspection(comprehensiveInspection));
    }

    /**
     * 查询列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ComprehensiveInspection comprehensiveInspection, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            comprehensiveInspection.setParams(params);
        }
        startPage();
        List<ComprehensiveInspection> list = comprehensiveInspectionService.selectComprehensiveInspectionList(comprehensiveInspection);
        return getDataTable(list);
    }

    /**
     * 获取案件数量
     */
    @GetMapping("/getCaseCount")
    public AjaxResult getCaseCount(ComprehensiveInspection comprehensiveInspection, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            comprehensiveInspection.setParams(params);
        }
        return AjaxResult.success(comprehensiveInspectionService.getCaseCount(comprehensiveInspection));
    }

    /**
     * 根据id获取案件
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getCase(@PathVariable("id") Long id) {
        return AjaxResult.success(comprehensiveInspectionService.selectComprehensiveInspectionById(id));
    }

    /**
     * 处理
     */
    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody ComprehensiveInspection comprehensiveInspection) {
        comprehensiveInspection.setCirculationState("0");
        comprehensiveInspection.setUpdateBy(getUsername());
        return toAjax(comprehensiveInspectionService.updateComprehensiveInspection(comprehensiveInspection));
    }

}
