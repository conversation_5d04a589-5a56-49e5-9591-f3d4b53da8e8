package com.ruoyi.shcy;

import com.ruoyi.shcy.domain.WaterLevelRealtime;
import com.ruoyi.shcy.service.IWaterLevelRealtimeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class WaterLevelRealtimeTest {

    @Autowired
    private IWaterLevelRealtimeService waterLevelRealtimeService;

    @Test
    public void testWaterLevelRealtime() {
        System.out.println("测试水位实时数据");

        WaterLevelRealtime res = waterLevelRealtimeService.selectWaterLevelRealtimeByWaterLevelId(12);

        System.out.println("====>>>>> " + res);
    }
}
