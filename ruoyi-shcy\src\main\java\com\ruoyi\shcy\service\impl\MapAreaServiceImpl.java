package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.MapAreaMapper;
import com.ruoyi.shcy.domain.MapArea;
import com.ruoyi.shcy.service.IMapAreaService;

/**
 * 地图区域Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-07-08
 */
@Service
public class MapAreaServiceImpl implements IMapAreaService 
{
    @Autowired
    private MapAreaMapper mapAreaMapper;

    /**
     * 查询地图区域
     * 
     * @param areaId 地图区域主键
     * @return 地图区域
     */
    @Override
    public MapArea selectMapAreaByAreaId(Long areaId)
    {
        return mapAreaMapper.selectMapAreaByAreaId(areaId);
    }

    /**
     * 查询地图区域列表
     * 
     * @param mapArea 地图区域
     * @return 地图区域
     */
    @Override
    public List<MapArea> selectMapAreaList(MapArea mapArea)
    {
        return mapAreaMapper.selectMapAreaList(mapArea);
    }

    /**
     * 新增地图区域
     * 
     * @param mapArea 地图区域
     * @return 结果
     */
    @Override
    public int insertMapArea(MapArea mapArea)
    {
        return mapAreaMapper.insertMapArea(mapArea);
    }

    /**
     * 修改地图区域
     * 
     * @param mapArea 地图区域
     * @return 结果
     */
    @Override
    public int updateMapArea(MapArea mapArea)
    {
        return mapAreaMapper.updateMapArea(mapArea);
    }

    /**
     * 批量删除地图区域
     * 
     * @param areaIds 需要删除的地图区域主键
     * @return 结果
     */
    @Override
    public int deleteMapAreaByAreaIds(Long[] areaIds)
    {
        return mapAreaMapper.deleteMapAreaByAreaIds(areaIds);
    }

    /**
     * 删除地图区域信息
     * 
     * @param areaId 地图区域主键
     * @return 结果
     */
    @Override
    public int deleteMapAreaByAreaId(Long areaId)
    {
        return mapAreaMapper.deleteMapAreaByAreaId(areaId);
    }
}
