package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.Committee;
import com.ruoyi.shcy.service.ICommitteeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 居委会信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/committee")
public class CommitteeController extends BaseController
{
    @Autowired
    private ICommitteeService committeeService;

    /**
     * 查询居委会信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:committee:list')")
    @GetMapping("/list")
    public TableDataInfo list(Committee committee)
    {
        startPage();
        List<Committee> list = committeeService.selectCommitteeList(committee);
        return getDataTable(list);
    }

    /**
     * 查询居委会信息列表（不分页）
     * **/
    @GetMapping("/committeelist")
    public AjaxResult committeeList(Committee committee)
    {
        List<Committee> list = committeeService.selectCommitteeList(committee);
        return AjaxResult.success(list);
    }

    /**
     * 导出居委会信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:committee:export')")
    @Log(title = "居委会信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Committee committee)
    {
        List<Committee> list = committeeService.selectCommitteeList(committee);
        ExcelUtil<Committee> util = new ExcelUtil<Committee>(Committee.class);
        util.exportExcel(response, list, "居委会信息数据");
    }

    /**
     * 获取居委会信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:committee:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(committeeService.selectCommitteeById(id));
    }

    /**
     * 新增居委会信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:committee:add')")
    @Log(title = "居委会信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Committee committee)
    {
        return toAjax(committeeService.insertCommittee(committee));
    }

    /**
     * 修改居委会信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:committee:edit')")
    @Log(title = "居委会信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Committee committee)
    {
        return toAjax(committeeService.updateCommittee(committee));
    }

    /**
     * 删除居委会信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:committee:remove')")
    @Log(title = "居委会信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(committeeService.deleteCommitteeByIds(ids));
    }
}
