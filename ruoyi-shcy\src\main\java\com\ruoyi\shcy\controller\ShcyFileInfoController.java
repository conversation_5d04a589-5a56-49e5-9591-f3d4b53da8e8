package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.service.IShcyFileInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@RestController
@RequestMapping("/shcy/file")
public class ShcyFileInfoController extends BaseController
{
    @Autowired
    private IShcyFileInfoService shcyFileInfoService;


    @Autowired
    private ServerConfig serverConfig;

    private static final String FILE_DELIMETER = ",";


    /**
     * 查询文件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:file:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyFileInfo shcyFileInfo)
    {
        startPage();
        List<ShcyFileInfo> list = shcyFileInfoService.selectShcyFileInfoList(shcyFileInfo);
        return getDataTable(list);
    }

    /**
     * 导出文件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:file:export')")
    @Log(title = "文件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyFileInfo shcyFileInfo)
    {
        List<ShcyFileInfo> list = shcyFileInfoService.selectShcyFileInfoList(shcyFileInfo);
        ExcelUtil<ShcyFileInfo> util = new ExcelUtil<ShcyFileInfo>(ShcyFileInfo.class);
        util.exportExcel(response, list, "文件信息数据");
    }

    /**
     * 获取文件信息详细信息
     */
    // @PreAuthorize("@ss.hasPermi('shcy:file:query')")
    @GetMapping(value = "/{fileId}")
    public AjaxResult getInfo(@PathVariable("fileId") Long fileId)
    {
        return AjaxResult.success(shcyFileInfoService.selectShcyFileInfoByFileId(fileId));
    }

    /**
     * 新增文件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:file:add')")
    @Log(title = "文件信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyFileInfo shcyFileInfo)
    {
        int i = shcyFileInfoService.insertShcyFileInfo(shcyFileInfo);
        return AjaxResult.success(shcyFileInfo.getFileId());
    }

    /**
     * 上传你文件信息
     * */
    @PostMapping("/uploads")
    public AjaxResult uploadFiles(List<MultipartFile> files) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            List<String> urls = new ArrayList<String>();
            List<String> fileNames = new ArrayList<String>();
            List<String> newFileNames = new ArrayList<String>();
            List<String> originalFilenames = new ArrayList<String>();
            List<String> filesId = new ArrayList<String>();
            for (MultipartFile file : files)
            {
                ShcyFileInfo shcyFileInfo = new ShcyFileInfo();
                // 上传并返回新文件名称

                String fileName = file.getOriginalFilename();
                String url = FileUploadUtils.upload(filePath, file);
//                String url = serverConfig.getUrl() + fileName;

                shcyFileInfo.setFilePath(url);
                shcyFileInfo.setFileName(fileName);
                shcyFileInfoService.insertShcyFileInfo(shcyFileInfo);
                filesId.add(String.valueOf(shcyFileInfo.getFileId())) ;
                urls.add(url);
            }
            AjaxResult ajax = AjaxResult.success();
            ajax.put("urls", StringUtils.join(urls, FILE_DELIMETER));
            ajax.put("filesId",StringUtils.join(filesId,FILE_DELIMETER));
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改文件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:file:edit')")
    @Log(title = "文件信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyFileInfo shcyFileInfo)
    {
        return toAjax(shcyFileInfoService.updateShcyFileInfo(shcyFileInfo));
    }

    /**
     * 删除文件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:file:remove')")
    @Log(title = "文件信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{fileIds}")
    public AjaxResult remove(@PathVariable Long[] fileIds)
    {
        return toAjax(shcyFileInfoService.deleteShcyFileInfoByFileIds(fileIds));
    }
}
