package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.ShopCheckLog;
import com.ruoyi.shcy.domain.vo.ShopCommitteeCheckStatusVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查日志Mapper接口
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
public interface ShopCheckLogMapper
{
    /**
     * 查询检查日志
     *
     * @param id 检查日志主键
     * @return 检查日志
     */
    public ShopCheckLog selectShopCheckLogById(Long id);

    /**
     * 查询最近三条检查日志
     *
     * @param id 检查日志主键
     * @return 检查日志
     */
    public List<ShopCheckLog> selectShopCheckLogByIdThree(Long id);

    /**
     * 查询检查日志列表
     *
     * @param shopCheckLog 检查日志
     * @return 检查日志集合
     */
    public List<ShopCheckLog> selectShopCheckLogList(ShopCheckLog shopCheckLog);

    /**
     *查询检查日志列表（给大屏使用）
     * **/
    public List<ShopCheckLog> selectShopCheckLogListScreen(ShopCheckLog shopCheckLog);


    /**
     * 查询大屏检查日志列表
     *
     * @param shopCheckLog 检查日志
     * @return 检查日志集合
     */
    public List<ShopCheckLog> selectShopCheckLogScreenList(ShopCheckLog shopCheckLog);

    /**
     * 新增检查日志
     *
     * @param shopCheckLog 检查日志
     * @return 结果
     */
    public int insertShopCheckLog(ShopCheckLog shopCheckLog);

    /**
     * 修改检查日志
     *
     * @param shopCheckLog 检查日志
     * @return 结果
     */
    public int updateShopCheckLog(ShopCheckLog shopCheckLog);

    /**
     * 删除检查日志
     *
     * @param id 检查日志主键
     * @return 结果
     */
    public int deleteShopCheckLogById(Long id);

    /**
     * 批量删除检查日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShopCheckLogByIds(Long[] ids);

    ShopCheckLog selectShopCheckLogByShopIdAndCheckDate(@Param("shopId") Long shopId, @Param("checkDate") String checkDate);


    /**
     *
     * 根据居委会id查询巡检状态，巡检时间、巡检距离状态等信息（当前月份的数据）
     * **/
    List<ShopCommitteeCheckStatusVo> selectShopCheckStatusByDeptId(Long deptId);

    /**
     * 查询居委会巡查最新记录的所属居委会id
     */
    Long getDeptIdOfNewthCheckRecord();

    List<ShopCheckLog> selectShopCheckLogList1(ShopCheckLog shopCheckLog);
}
