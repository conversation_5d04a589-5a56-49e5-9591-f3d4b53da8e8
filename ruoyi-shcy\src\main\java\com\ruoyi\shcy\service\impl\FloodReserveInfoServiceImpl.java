package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.FloodReserveInfo;
import com.ruoyi.shcy.mapper.FloodReserveInfoMapper;
import com.ruoyi.shcy.service.IFloodReserveInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 防汛物资储备信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
@Service
public class FloodReserveInfoServiceImpl implements IFloodReserveInfoService 
{
    @Autowired
    private FloodReserveInfoMapper floodReserveInfoMapper;

    /**
     * 查询防汛物资储备信息
     * 
     * @param id 防汛物资储备信息主键
     * @return 防汛物资储备信息
     */
    @Override
    public FloodReserveInfo selectFloodReserveInfoById(Long id)
    {
        return floodReserveInfoMapper.selectFloodReserveInfoById(id);
    }

    /**
     * 查询防汛物资储备信息列表
     * 
     * @param floodReserveInfo 防汛物资储备信息
     * @return 防汛物资储备信息
     */
    @Override
    public List<FloodReserveInfo> selectFloodReserveInfoList(FloodReserveInfo floodReserveInfo)
    {
        return floodReserveInfoMapper.selectFloodReserveInfoList(floodReserveInfo);
    }

    /**
     * 新增防汛物资储备信息
     * 
     * @param floodReserveInfo 防汛物资储备信息
     * @return 结果
     */
    @Override
    public int insertFloodReserveInfo(FloodReserveInfo floodReserveInfo)
    {
        return floodReserveInfoMapper.insertFloodReserveInfo(floodReserveInfo);
    }

    /**
     * 修改防汛物资储备信息
     * 
     * @param floodReserveInfo 防汛物资储备信息
     * @return 结果
     */
    @Override
    public int updateFloodReserveInfo(FloodReserveInfo floodReserveInfo)
    {
        return floodReserveInfoMapper.updateFloodReserveInfo(floodReserveInfo);
    }

    /**
     * 批量删除防汛物资储备信息
     * 
     * @param ids 需要删除的防汛物资储备信息主键
     * @return 结果
     */
    @Override
    public int deleteFloodReserveInfoByIds(Long[] ids)
    {
        return floodReserveInfoMapper.deleteFloodReserveInfoByIds(ids);
    }

    /**
     * 删除防汛物资储备信息信息
     * 
     * @param id 防汛物资储备信息主键
     * @return 结果
     */
    @Override
    public int deleteFloodReserveInfoById(Long id)
    {
        return floodReserveInfoMapper.deleteFloodReserveInfoById(id);
    }
}
