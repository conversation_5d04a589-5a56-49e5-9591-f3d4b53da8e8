package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.InstitutionsMapper;
import com.ruoyi.shcy.domain.Institutions;
import com.ruoyi.shcy.service.IInstitutionsService;

/**
 * 企事业单位Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class InstitutionsServiceImpl implements IInstitutionsService 
{
    @Autowired
    private InstitutionsMapper institutionsMapper;

    /**
     * 查询企事业单位
     * 
     * @param id 企事业单位主键
     * @return 企事业单位
     */
    @Override
    public Institutions selectInstitutionsById(Long id)
    {
        return institutionsMapper.selectInstitutionsById(id);
    }

    /**
     * 查询企事业单位列表
     * 
     * @param institutions 企事业单位
     * @return 企事业单位
     */
    @Override
    public List<Institutions> selectInstitutionsList(Institutions institutions)
    {
        return institutionsMapper.selectInstitutionsList(institutions);
    }

    /**
     * 新增企事业单位
     * 
     * @param institutions 企事业单位
     * @return 结果
     */
    @Override
    public int insertInstitutions(Institutions institutions)
    {
        institutions.setCreateTime(DateUtils.getNowDate());
        return institutionsMapper.insertInstitutions(institutions);
    }

    /**
     * 修改企事业单位
     * 
     * @param institutions 企事业单位
     * @return 结果
     */
    @Override
    public int updateInstitutions(Institutions institutions)
    {
        institutions.setUpdateTime(DateUtils.getNowDate());
        return institutionsMapper.updateInstitutions(institutions);
    }

    /**
     * 批量删除企事业单位
     * 
     * @param ids 需要删除的企事业单位主键
     * @return 结果
     */
    @Override
    public int deleteInstitutionsByIds(Long[] ids)
    {
        return institutionsMapper.deleteInstitutionsByIds(ids);
    }

    /**
     * 删除企事业单位信息
     * 
     * @param id 企事业单位主键
     * @return 结果
     */
    @Override
    public int deleteInstitutionsById(Long id)
    {
        return institutionsMapper.deleteInstitutionsById(id);
    }
}
