package com.ruoyi.common.utils;


import com.ruoyi.common.utils.http.HttpUtils;

/**
 * gis 相关工具类
 *
 * <AUTHOR>
 * @date 2023/05/31
 */
public class GisUtils {

    /**
     * 高德转城地
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return {@link String}
     */
    public static String convert(String longitude, String latitude) {
        String url = "http://31.8.16.103:9198/lnglat/convertcc/gd_to_cd?lng=" + longitude + "&lat=" + latitude;
        return HttpUtils.sendGet(url);
    }

    /**
     * 百度转城地
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return {@link String}
     */
    public static String convertcc(String longitude, String latitude) {
        String url = "http://31.8.16.103:9198/lnglat/convertcc/bd_to_cd?lng=" + longitude + "&lat=" + latitude;
        return HttpUtils.sendGet(url);
    }

}
