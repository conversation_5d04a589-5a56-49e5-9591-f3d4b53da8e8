package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyWxAccessToken;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-18
 */
public interface ShcyWxAccessTokenMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ShcyWxAccessToken selectShcyWxAccessTokenById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param shcyWxAccessToken 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ShcyWxAccessToken> selectShcyWxAccessTokenList(ShcyWxAccessToken shcyWxAccessToken);

    /**
     * 新增【请填写功能名称】
     * 
     * @param shcyWxAccessToken 【请填写功能名称】
     * @return 结果
     */
    public int insertShcyWxAccessToken(ShcyWxAccessToken shcyWxAccessToken);

    /**
     * 修改【请填写功能名称】
     * 
     * @param shcyWxAccessToken 【请填写功能名称】
     * @return 结果
     */
    public int updateShcyWxAccessToken(ShcyWxAccessToken shcyWxAccessToken);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteShcyWxAccessTokenById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyWxAccessTokenByIds(Long[] ids);
}
