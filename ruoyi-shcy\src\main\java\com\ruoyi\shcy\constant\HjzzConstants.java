package com.ruoyi.shcy.constant;

/**
 * 环境整治常量
 *
 * <AUTHOR>
 * @date 2023/11/02
 */
public class HjzzConstants {

    // 已处理
    public static final String PROCESSED = "0";
    // 待处理
    public static final String UNPROCESSED = "1";
    // 误报
    public static final String FALSE_ALARM = "2";

    // 超时状态 正常
    public static final String OVERTIME_STATE_NORMAL = "0";
    // 超时状态 超时
    public static final String OVERTIME_STATE_OVERTIME = "1";

    // 报警状态 待处理
    public static final String ALARM_STATE_UNPROCESSED = "0";
    // 报警状态 处理中
    public static final String ALARM_STATE_PROCESSING = "1";
    // 报警状态 已处理
    public static final String ALARM_STATE_PROCESSED = "2";
    // 报警待处理、已处理
    public static final String ALARM_STATE_UNPROCESSED_PROCESSED = "01";

    // 处置状态 0:已完成 1:待处理 2:处理中 3:作废 4:退回

    /**
     * 已完成
     */
    public static final String CIRCULATION_STATE_FINISHED = "0";

    /**
     * 待处理
     */
    public static final String CIRCULATION_STATE_UNPROCESSED = "1";

    /**
     * 处理中
     */
    public static final String CIRCULATION_STATE_PROCESSING = "2";

    /**
     * 作废
     */
    public static final String CIRCULATION_STATE_INVALID = "3";

    /**
     * 退回
     */
    public static final String CIRCULATION_STATE_RETURN = "4";

    // 事件类型 1:偷倒垃圾 2:违规生产入侵

    /**
     * 偷倒垃圾
     */
    public static final String CASE_TYPE_TDLJ = "1";

    /**
     * 违规生产入侵
     */
    public static final String CASE_TYPE_WGSC = "2";

    // 事件类型名称:偷倒垃圾、违规生产入侵

    /**
     * 偷倒垃圾
     */
    public static final String ALARM_TYPE_NAME_TDLJ = "偷倒垃圾";


    /**
     * 违规生产入侵
     */
    public static final String ALARM_TYPE_NAME_WGSC = "违规生产入侵";


}
