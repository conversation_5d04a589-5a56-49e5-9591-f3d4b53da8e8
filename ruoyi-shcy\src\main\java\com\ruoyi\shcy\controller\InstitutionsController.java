package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.Institutions;
import com.ruoyi.shcy.service.IInstitutionsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 企事业单位Controller
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/institutions")
public class InstitutionsController extends BaseController
{
    @Autowired
    private IInstitutionsService institutionsService;

    /**
     * 查询企事业单位列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:institutions:list')")
    @GetMapping("/list")
    public TableDataInfo list(Institutions institutions)
    {
        startPage();
        List<Institutions> list = institutionsService.selectInstitutionsList(institutions);
        return getDataTable(list);
    }

    /**
     * 导出企事业单位列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:institutions:export')")
    @Log(title = "企事业单位", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Institutions institutions)
    {
        List<Institutions> list = institutionsService.selectInstitutionsList(institutions);
        ExcelUtil<Institutions> util = new ExcelUtil<Institutions>(Institutions.class);
        util.exportExcel(response, list, "企事业单位数据");
    }

    /**
     * 获取企事业单位详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:institutions:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(institutionsService.selectInstitutionsById(id));
    }

    /**
     * 新增企事业单位
     */
    @PreAuthorize("@ss.hasPermi('shcy:institutions:add')")
    @Log(title = "企事业单位", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Institutions institutions)
    {
        return toAjax(institutionsService.insertInstitutions(institutions));
    }

    /**
     * 修改企事业单位
     */
    @PreAuthorize("@ss.hasPermi('shcy:institutions:edit')")
    @Log(title = "企事业单位", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Institutions institutions)
    {
        return toAjax(institutionsService.updateInstitutions(institutions));
    }

    /**
     * 删除企事业单位
     */
    @PreAuthorize("@ss.hasPermi('shcy:institutions:remove')")
    @Log(title = "企事业单位", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(institutionsService.deleteInstitutionsByIds(ids));
    }
}
