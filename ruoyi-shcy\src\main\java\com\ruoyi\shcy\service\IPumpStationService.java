package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.PumpStation;

import java.util.List;

/**
 * 泵站Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-28
 */
public interface IPumpStationService 
{
    /**
     * 查询泵站
     * 
     * @param id 泵站主键
     * @return 泵站
     */
    public PumpStation selectPumpStationById(Long id);

    /**
     * 查询泵站列表
     * 
     * @param pumpStation 泵站
     * @return 泵站集合
     */
    public List<PumpStation> selectPumpStationList(PumpStation pumpStation);

    /**
     * 新增泵站
     * 
     * @param pumpStation 泵站
     * @return 结果
     */
    public int insertPumpStation(PumpStation pumpStation);

    /**
     * 修改泵站
     * 
     * @param pumpStation 泵站
     * @return 结果
     */
    public int updatePumpStation(PumpStation pumpStation);

    /**
     * 批量删除泵站
     * 
     * @param ids 需要删除的泵站主键集合
     * @return 结果
     */
    public int deletePumpStationByIds(Long[] ids);

    /**
     * 删除泵站信息
     * 
     * @param id 泵站主键
     * @return 结果
     */
    public int deletePumpStationById(Long id);

    List<PumpStation> selectPumpStationListByIds(Long[] ids);
}
