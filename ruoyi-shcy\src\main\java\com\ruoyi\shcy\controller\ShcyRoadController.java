package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.ShcyRoad;
import com.ruoyi.shcy.service.IShcyRoadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 路段名Controller
 *
 * <AUTHOR>
 * @date 2023-02-09
 */
@RestController
@RequestMapping("/shcy/road")
public class ShcyRoadController extends BaseController
{
    @Autowired
    private IShcyRoadService shcyRoadService;

    /**
     * 查询路段名列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:road:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyRoad shcyRoad)
    {
        startPage();
        List<ShcyRoad> list = shcyRoadService.selectShcyRoadList(shcyRoad);
        return getDataTable(list);
    }


    @GetMapping("/detail")
    public AjaxResult roadList(ShcyRoad shcyRoad){
        List<ShcyRoad> list = shcyRoadService.selectShcyRoadList(shcyRoad);
        return AjaxResult.success(list);
    }


    /**
     * 导出路段名列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:road:export')")
    @Log(title = "路段名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyRoad shcyRoad)
    {
        List<ShcyRoad> list = shcyRoadService.selectShcyRoadList(shcyRoad);
        ExcelUtil<ShcyRoad> util = new ExcelUtil<ShcyRoad>(ShcyRoad.class);
        util.exportExcel(response, list, "路段名数据");
    }

    /**
     * 获取路段名详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:road:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyRoadService.selectShcyRoadById(id));
    }

    /**
     * 新增路段名
     */
    @PreAuthorize("@ss.hasPermi('shcy:road:add')")
    @Log(title = "路段名", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyRoad shcyRoad)
    {
        return toAjax(shcyRoadService.insertShcyRoad(shcyRoad));
    }

    /**
     * 修改路段名
     */
    @PreAuthorize("@ss.hasPermi('shcy:road:edit')")
    @Log(title = "路段名", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyRoad shcyRoad)
    {
        return toAjax(shcyRoadService.updateShcyRoad(shcyRoad));
    }

    /**
     * 删除路段名
     */
    @PreAuthorize("@ss.hasPermi('shcy:road:remove')")
    @Log(title = "路段名", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyRoadService.deleteShcyRoadByIds(ids));
    }
}
