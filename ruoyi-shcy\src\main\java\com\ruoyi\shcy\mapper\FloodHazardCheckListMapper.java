package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.FloodHazardCheckList;

import java.util.List;

/**
 * 汛期隐患排查Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface FloodHazardCheckListMapper 
{
    /**
     * 查询汛期隐患排查
     * 
     * @param id 汛期隐患排查主键
     * @return 汛期隐患排查
     */
    public FloodHazardCheckList selectFloodHazardCheckListById(Long id);

    /**
     * 查询汛期隐患排查列表
     * 
     * @param floodHazardCheckList 汛期隐患排查
     * @return 汛期隐患排查集合
     */
    public List<FloodHazardCheckList> selectFloodHazardCheckListList(FloodHazardCheckList floodHazardCheckList);

    /**
     * 新增汛期隐患排查
     * 
     * @param floodHazardCheckList 汛期隐患排查
     * @return 结果
     */
    public int insertFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList);

    /**
     * 修改汛期隐患排查
     * 
     * @param floodHazardCheckList 汛期隐患排查
     * @return 结果
     */
    public int updateFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList);

    /**
     * 删除汛期隐患排查
     * 
     * @param id 汛期隐患排查主键
     * @return 结果
     */
    public int deleteFloodHazardCheckListById(Long id);

    /**
     * 批量删除汛期隐患排查
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFloodHazardCheckListByIds(Long[] ids);

    public long getCaseCount(FloodHazardCheckList floodHazardCheckList);
}
