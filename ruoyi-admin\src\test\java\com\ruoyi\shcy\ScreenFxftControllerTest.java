package com.ruoyi.shcy;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.FxftCase;
import com.ruoyi.shcy.service.IFxftCaseService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest
public class ScreenFxftControllerTest {

    @Autowired
    private IFxftCaseService fxftCaseService;

    @Test
    public void contextLoads() {
        // 这里没有任何代码，只是确保应用程序上下文能够成功加载
    }


    @Test
    public void testIotEvent() {
        LocalDate currentDate = LocalDate.now(); // 获取当前日期
        LocalDate startDate = currentDate.minusMonths(11); // 计算开始日期，当前日期的前11个月

        List<String> monthList = new ArrayList<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (LocalDate date = startDate; !date.isAfter(currentDate); date = date.plusMonths(1)) {
            String month = date.format(formatter);
            monthList.add(month);
        }

        FxftCase fxftCase = new FxftCase();
        LocalDate beginTime = LocalDate.of(startDate.getYear(), startDate.getMonth(), 1);
        LocalDate endTime = LocalDate.of(currentDate.getYear(), currentDate.getMonth(), 1).plusMonths(1).minusDays(1);
        fxftCase.getParams().put("beginTime", beginTime.toString());
        fxftCase.getParams().put("endTime", endTime.toString());
        List<FxftCase> list = fxftCaseService.selectFxftCaseList(fxftCase);
        // 使用stream从list集合中过滤出createTime在beginTime和endTime之间的案件，然后根据createTime字段进行年月分组，统计每组的数量
        Map<String, Long> map = list.stream().collect(Collectors.groupingBy(item -> DateUtil.format(item.getCreateTime(), "yyyy-MM"), Collectors.counting()));
        // 使用stream从list集合中过滤出circulationState为0的案件，然后根据createTime字段进行年月分组，统计每组的数量
        Map<String, Long> processedMap = list.stream().filter(item -> FxftConstants.PROCESSED.equals(item.getCirculationState())).collect(Collectors.groupingBy(item -> DateUtil.format(item.getCreateTime(), "yyyy-MM"), Collectors.counting()));

        Map<String, Object> result = new HashMap<String, Object>() {{
            put("monthList", monthList);
            put("allList", map);
            put("processedList", processedMap);
        }};

        System.out.println(result);
    }
}
