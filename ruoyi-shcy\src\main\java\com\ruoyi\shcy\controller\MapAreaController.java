package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.MapArea;
import com.ruoyi.shcy.service.IMapAreaService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 地图区域Controller
 * 
 * <AUTHOR>
 * @date 2022-07-08
 */
@RestController
@RequestMapping("/shcy/mapArea")
public class MapAreaController extends BaseController
{
    @Autowired
    private IMapAreaService mapAreaService;

    /**
     * 查询地图区域列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:mapArea:list')")
    @GetMapping("/list")
    public TableDataInfo list(MapArea mapArea)
    {
        startPage();
        List<MapArea> list = mapAreaService.selectMapAreaList(mapArea);
        return getDataTable(list);
    }

    /**
     * 导出地图区域列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:mapArea:export')")
    @Log(title = "地图区域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MapArea mapArea)
    {
        List<MapArea> list = mapAreaService.selectMapAreaList(mapArea);
        ExcelUtil<MapArea> util = new ExcelUtil<MapArea>(MapArea.class);
        util.exportExcel(response, list, "地图区域数据");
    }

    /**
     * 获取地图区域详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:mapArea:query')")
    @GetMapping(value = "/{areaId}")
    public AjaxResult getInfo(@PathVariable("areaId") Long areaId)
    {
        return AjaxResult.success(mapAreaService.selectMapAreaByAreaId(areaId));
    }

    /**
     * 新增地图区域
     */
    @PreAuthorize("@ss.hasPermi('shcy:mapArea:add')")
    @Log(title = "地图区域", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MapArea mapArea)
    {
        return toAjax(mapAreaService.insertMapArea(mapArea));
    }

    /**
     * 修改地图区域
     */
    @PreAuthorize("@ss.hasPermi('shcy:mapArea:edit')")
    @Log(title = "地图区域", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MapArea mapArea)
    {
        return toAjax(mapAreaService.updateMapArea(mapArea));
    }

    /**
     * 删除地图区域
     */
    @PreAuthorize("@ss.hasPermi('shcy:mapArea:remove')")
    @Log(title = "地图区域", businessType = BusinessType.DELETE)
	@DeleteMapping("/{areaIds}")
    public AjaxResult remove(@PathVariable Long[] areaIds)
    {
        return toAjax(mapAreaService.deleteMapAreaByAreaIds(areaIds));
    }
}
