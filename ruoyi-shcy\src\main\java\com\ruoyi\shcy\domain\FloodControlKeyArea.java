package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.ibatis.type.Alias;

/**
 * 防汛防台重点区域对象 shcy_flood_control_key_area
 *
 * <AUTHOR>
 * @date 2023-09-22
 */
@Alias("FloodControlKeyArea")
public class FloodControlKeyArea extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 风险部位
     */
    @Excel(name = "风险部位")
    private String riskSite;

    /**
     * 风险等级
     */
    @Excel(name = "风险等级")
    private String riskLevel;

    /**
     * 风险类型
     */
    @Excel(name = "风险类型")
    private String riskType;

    /**
     * 脆弱性区域
     */
    @Excel(name = "脆弱性区域")
    private String vulnerableArea;

    /**
     * 区域面积
     */
    @Excel(name = "区域面积")
    private String areaSize;

    /**
     * 风险概况
     */
    @Excel(name = "风险概况")
    private String riskOverview;

    /**
     * 受影响范围
     */
    @Excel(name = "受影响范围")
    private String affectedRange;

    /**
     * 防控期
     */
    @Excel(name = "防控期")
    private String controlPeriod;

    /**
     * 风险评级
     */
    @Excel(name = "风险评级")
    private String riskRating;

    /**
     * 应对措施1
     */
    @Excel(name = "应对措施1")
    private String countermeasure1;

    /**
     * 启动条件
     */
    @Excel(name = "启动条件")
    private String startCondition1;

    /**
     * 责任部门
     */
    @Excel(name = "责任部门")
    private String responsibleDepartment1;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contactPerson1;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String contactPhone1;

    /**
     * 应对措施2
     */
    @Excel(name = "应对措施2")
    private String countermeasure2;

    /**
     * 启动条件
     */
    @Excel(name = "启动条件")
    private String startCondition2;

    /**
     * 责任部门
     */
    @Excel(name = "责任部门")
    private String responsibleDepartment2;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contactPerson2;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String contactPhone2;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 坐标
     */
    @Excel(name = "坐标")
    private String coordinate;

    /**
     * 填充色
     */
    @Excel(name = "填充色")
    private String fillColor;

    /**
     * 边框色
     */
    @Excel(name = "边框色")
    private String outlineColor;

    private String cameras;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setRiskSite(String riskSite) {
        this.riskSite = riskSite;
    }

    public String getRiskSite() {
        return riskSite;
    }

    public void setRiskLevel(String riskLevel) {
        this.riskLevel = riskLevel;
    }

    public String getRiskLevel() {
        return riskLevel;
    }

    public void setRiskType(String riskType) {
        this.riskType = riskType;
    }

    public String getRiskType() {
        return riskType;
    }

    public void setVulnerableArea(String vulnerableArea) {
        this.vulnerableArea = vulnerableArea;
    }

    public String getVulnerableArea() {
        return vulnerableArea;
    }

    public void setAreaSize(String areaSize) {
        this.areaSize = areaSize;
    }

    public String getAreaSize() {
        return areaSize;
    }

    public void setRiskOverview(String riskOverview) {
        this.riskOverview = riskOverview;
    }

    public String getRiskOverview() {
        return riskOverview;
    }

    public void setAffectedRange(String affectedRange) {
        this.affectedRange = affectedRange;
    }

    public String getAffectedRange() {
        return affectedRange;
    }

    public void setControlPeriod(String controlPeriod) {
        this.controlPeriod = controlPeriod;
    }

    public String getControlPeriod() {
        return controlPeriod;
    }

    public void setRiskRating(String riskRating) {
        this.riskRating = riskRating;
    }

    public String getRiskRating() {
        return riskRating;
    }

    public void setCountermeasure1(String countermeasure1) {
        this.countermeasure1 = countermeasure1;
    }

    public String getCountermeasure1() {
        return countermeasure1;
    }

    public void setStartCondition1(String startCondition1) {
        this.startCondition1 = startCondition1;
    }

    public String getStartCondition1() {
        return startCondition1;
    }

    public void setResponsibleDepartment1(String responsibleDepartment1) {
        this.responsibleDepartment1 = responsibleDepartment1;
    }

    public String getResponsibleDepartment1() {
        return responsibleDepartment1;
    }

    public void setContactPerson1(String contactPerson1) {
        this.contactPerson1 = contactPerson1;
    }

    public String getContactPerson1() {
        return contactPerson1;
    }

    public void setContactPhone1(String contactPhone1) {
        this.contactPhone1 = contactPhone1;
    }

    public String getContactPhone1() {
        return contactPhone1;
    }

    public void setCountermeasure2(String countermeasure2) {
        this.countermeasure2 = countermeasure2;
    }

    public String getCountermeasure2() {
        return countermeasure2;
    }

    public void setStartCondition2(String startCondition2) {
        this.startCondition2 = startCondition2;
    }

    public String getStartCondition2() {
        return startCondition2;
    }

    public void setResponsibleDepartment2(String responsibleDepartment2) {
        this.responsibleDepartment2 = responsibleDepartment2;
    }

    public String getResponsibleDepartment2() {
        return responsibleDepartment2;
    }

    public void setContactPerson2(String contactPerson2) {
        this.contactPerson2 = contactPerson2;
    }

    public String getContactPerson2() {
        return contactPerson2;
    }

    public void setContactPhone2(String contactPhone2) {
        this.contactPhone2 = contactPhone2;
    }

    public String getContactPhone2() {
        return contactPhone2;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public String getFillColor() {
        return fillColor;
    }

    public void setFillColor(String fillColor) {
        this.fillColor = fillColor;
    }

    public String getOutlineColor() {
        return outlineColor;
    }

    public void setOutlineColor(String outlineColor) {
        this.outlineColor = outlineColor;
    }

    public String getCameras() {
        return cameras;
    }

    public void setCameras(String cameras) {
        this.cameras = cameras;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("riskSite", getRiskSite())
                .append("riskLevel", getRiskLevel())
                .append("riskType", getRiskType())
                .append("vulnerableArea", getVulnerableArea())
                .append("areaSize", getAreaSize())
                .append("riskOverview", getRiskOverview())
                .append("affectedRange", getAffectedRange())
                .append("controlPeriod", getControlPeriod())
                .append("riskRating", getRiskRating())
                .append("countermeasure1", getCountermeasure1())
                .append("startCondition1", getStartCondition1())
                .append("responsibleDepartment1", getResponsibleDepartment1())
                .append("contactPerson1", getContactPerson1())
                .append("contactPhone1", getContactPhone1())
                .append("countermeasure2", getCountermeasure2())
                .append("startCondition2", getStartCondition2())
                .append("responsibleDepartment2", getResponsibleDepartment2())
                .append("contactPerson2", getContactPerson2())
                .append("contactPhone2", getContactPhone2())
                .append("remark", getRemark())
                .append("type", getType())
                .append("coordinate", getCoordinate())
                .append("fillColor", getFillColor())
                .append("outlineColor", getOutlineColor())
                .append("cameras", getCameras())
                .toString();
    }
}
