package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.RainwaterSewagePipe;
import com.ruoyi.shcy.service.IRainwaterSewagePipeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 雨污管道信息Controller
 * 
 * <AUTHOR>
 * @date 2022-12-21
 */
@RestController
@RequestMapping("/shcy/RainwaterSewagePipe")
public class RainwaterSewagePipeController extends BaseController
{
    @Autowired
    private IRainwaterSewagePipeService rainwaterSewagePipeService;

    /**
     * 查询雨污管道信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:RainwaterSewagePipe:list')")
    @GetMapping("/list")
    public TableDataInfo list(RainwaterSewagePipe rainwaterSewagePipe)
    {
        startPage();
        List<RainwaterSewagePipe> list = rainwaterSewagePipeService.selectRainwaterSewagePipeList(rainwaterSewagePipe);
        return getDataTable(list);
    }

    /**
     * 导出雨污管道信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:RainwaterSewagePipe:export')")
    @Log(title = "雨污管道信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RainwaterSewagePipe rainwaterSewagePipe)
    {
        List<RainwaterSewagePipe> list = rainwaterSewagePipeService.selectRainwaterSewagePipeList(rainwaterSewagePipe);
        ExcelUtil<RainwaterSewagePipe> util = new ExcelUtil<RainwaterSewagePipe>(RainwaterSewagePipe.class);
        util.exportExcel(response, list, "雨污管道信息数据");
    }

    /**
     * 获取雨污管道信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:RainwaterSewagePipe:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(rainwaterSewagePipeService.selectRainwaterSewagePipeById(id));
    }

    /**
     * 新增雨污管道信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:RainwaterSewagePipe:add')")
    @Log(title = "雨污管道信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RainwaterSewagePipe rainwaterSewagePipe)
    {
        return toAjax(rainwaterSewagePipeService.insertRainwaterSewagePipe(rainwaterSewagePipe));
    }

    /**
     * 修改雨污管道信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:RainwaterSewagePipe:edit')")
    @Log(title = "雨污管道信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RainwaterSewagePipe rainwaterSewagePipe)
    {
        return toAjax(rainwaterSewagePipeService.updateRainwaterSewagePipe(rainwaterSewagePipe));
    }

    /**
     * 删除雨污管道信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:RainwaterSewagePipe:remove')")
    @Log(title = "雨污管道信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rainwaterSewagePipeService.deleteRainwaterSewagePipeByIds(ids));
    }
}
