package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.GridCase;

import java.util.List;

/**
 * 网格化案事件Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-21
 */
public interface IGridCaseService 
{
    /**
     * 查询网格化案事件
     * 
     * @param id 网格化案事件主键
     * @return 网格化案事件
     */
    public GridCase selectGridCaseById(Long id);

    /**
     * 查询网格化案事件列表
     * 
     * @param gridCase 网格化案事件
     * @return 网格化案事件集合
     */
    public List<GridCase> selectGridCaseList(GridCase gridCase);

    /**
     * 新增网格化案事件
     * 
     * @param gridCase 网格化案事件
     * @return 结果
     */
    public int insertGridCase(GridCase gridCase);

    /**
     * 修改网格化案事件
     * 
     * @param gridCase 网格化案事件
     * @return 结果
     */
    public int updateGridCase(GridCase gridCase);

    /**
     * 批量删除网格化案事件
     * 
     * @param ids 需要删除的网格化案事件主键集合
     * @return 结果
     */
    public int deleteGridCaseByIds(Long[] ids);

    /**
     * 删除网格化案事件信息
     * 
     * @param id 网格化案事件主键
     * @return 结果
     */
    public int deleteGridCaseById(Long id);
}
