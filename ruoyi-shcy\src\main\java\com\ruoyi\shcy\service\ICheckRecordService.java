package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.CheckRecord;

import java.util.Date;
import java.util.List;

/**
 * 商铺巡查通过记录Service接口
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface ICheckRecordService
{
    /**
     * 查询商铺巡查通过记录
     *
     * @param id 商铺巡查通过记录主键
     * @return 商铺巡查通过记录
     */
    public CheckRecord selectCheckRecordById(Long id);

    /**
     * 查询商铺巡查通过记录列表
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 商铺巡查通过记录集合
     */
    public List<CheckRecord> selectCheckRecordList(CheckRecord checkRecord);

    /**
     * 新增商铺巡查通过记录
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 结果
     */
    public int insertCheckRecord(CheckRecord checkRecord);

    /**
     * 修改商铺巡查通过记录
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 结果
     */
    public int updateCheckRecord(CheckRecord checkRecord);

    /**
     * 批量删除商铺巡查通过记录
     *
     * @param ids 需要删除的商铺巡查通过记录主键集合
     * @return 结果
     */
    public int deleteCheckRecordByIds(Long[] ids);

    /**
     * 删除商铺巡查通过记录信息
     *
     * @param id 商铺巡查通过记录主键
     * @return 结果
     */
    public int deleteCheckRecordById(Long id);


    //根据shopid 和 日期 查询checkRecord记录表中是都有记录
    CheckRecord selectCheckRecordByShopIdAndCheckDate(Long shopId, Date checkDate);



    //********************************************************店铺巡查记录******************************************************

    /**
     * 查询出 已巡查商铺数量
     * **/
    public List<CheckRecord> selectCheckedShopNum(CheckRecord checkRecord);


    /**
     *查询出检查通过商铺数量
     *
     */
    public List<CheckRecord> selectCheckAndPassShop(CheckRecord checkRecord);

    /**
     * 查询检查未通过商铺数量
     *
     * **/
    public List<CheckRecord> selectCheckAndNoPassShop(CheckRecord checkRecord);

    /**
     * 查询出歇业商铺数量
     *
     * **/
    public List<CheckRecord> selectNoCheckShop(CheckRecord checkRecord);


    List<CheckRecord> selectCheckRecordShopList();
}
