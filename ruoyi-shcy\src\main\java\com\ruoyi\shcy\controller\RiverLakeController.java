package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.RiverLake;
import com.ruoyi.shcy.service.IRiverLakeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 河湖水体Controller
 * 
 * <AUTHOR>
 * @date 2023-02-03
 */
@RestController
@RequestMapping("/shcy/river")
public class RiverLakeController extends BaseController
{
    @Autowired
    private IRiverLakeService riverLakeService;

    /**
     * 查询河湖水体列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:river:list')")
    @GetMapping("/list")
    public TableDataInfo list(RiverLake riverLake)
    {
        startPage();
        List<RiverLake> list = riverLakeService.selectRiverLakeList(riverLake);
        return getDataTable(list);
    }

    /**
     * 导出河湖水体列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:river:export')")
    @Log(title = "河湖水体", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RiverLake riverLake)
    {
        List<RiverLake> list = riverLakeService.selectRiverLakeList(riverLake);
        ExcelUtil<RiverLake> util = new ExcelUtil<RiverLake>(RiverLake.class);
        util.exportExcel(response, list, "河湖水体数据");
    }

    /**
     * 获取河湖水体详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:river:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(riverLakeService.selectRiverLakeById(id));
    }

    /**
     * 新增河湖水体
     */
    @PreAuthorize("@ss.hasPermi('shcy:river:add')")
    @Log(title = "河湖水体", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RiverLake riverLake)
    {
        return toAjax(riverLakeService.insertRiverLake(riverLake));
    }

    /**
     * 修改河湖水体
     */
    @PreAuthorize("@ss.hasPermi('shcy:river:edit')")
    @Log(title = "河湖水体", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RiverLake riverLake)
    {
        return toAjax(riverLakeService.updateRiverLake(riverLake));
    }

    /**
     * 删除河湖水体
     */
    @PreAuthorize("@ss.hasPermi('shcy:river:remove')")
    @Log(title = "河湖水体", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(riverLakeService.deleteRiverLakeByIds(ids));
    }
}
