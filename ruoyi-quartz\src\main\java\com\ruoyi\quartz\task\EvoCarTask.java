package com.ruoyi.quartz.task;

import com.dahuatech.icc.exception.ClientException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.icc.dto.CarRecordDTO;
import com.ruoyi.icc.service.EvoCarService;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.icc.vo.CarPageVO;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;

/**
 * 车辆卡口定时任务
 *
 * <AUTHOR>
 * @date 2024/01/31
 */
@Component("evoCarTask")
public class EvoCarTask {

    @Autowired
    private IccService iccService;

    @Autowired
    private EvoCarService evoCarService;

    /**
     * 初始化
     */
    public void init() throws ClientException {
        CarPageVO carPageVO = new CarPageVO();
        carPageVO.setPage(1);
        carPageVO.setRows(99999);
        carPageVO.setAutoCount(1);
        LocalDate currentDate = LocalDate.of(2023, 8, 15);
        // 获取当天的开始时间，即凌晨 00:00:00
        LocalDateTime startOfDay = currentDate.atStartOfDay();
        carPageVO.setStartDate(String.valueOf(startOfDay.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond()));
        carPageVO.setEndDate(DateUtils.getEndTimestamp());
        // 卫九路 1000009$1$0$0
        carPageVO.setChannelIds(Arrays.asList("1000009$1$0$0"));
        carPageVO.setPlateNo("");
        carPageVO.setMinSpeed(0);
        carPageVO.setMaxSpeed(220);
        List<CarRecordDTO> picRecords = evoCarService.getPicRecords(carPageVO, iccService.getAccessToken());
        System.out.println("---------------" + picRecords.size());
        for (CarRecordDTO carRecordDTO : picRecords) {
            evoCarService.syncCarRecord(carRecordDTO);
        }

    }

    /**
     * 同步
     */
    public void sync() throws ClientException {
        CarPageVO carPageVO = new CarPageVO();
        carPageVO.setPage(1);
        carPageVO.setRows(999);
        carPageVO.setAutoCount(1);
        carPageVO.setStartDate(DateUtils.getStartTimestamp());
        carPageVO.setEndDate(DateUtils.getEndTimestamp());
        // 卫九路 1000009$1$0$0
        carPageVO.setChannelIds(Arrays.asList("1000009$1$0$0"));
        carPageVO.setPlateNo("");
        carPageVO.setMinSpeed(0);
        carPageVO.setMaxSpeed(220);
        List<CarRecordDTO> picRecords = evoCarService.getPicRecords(carPageVO, iccService.getAccessToken());
        for (CarRecordDTO carRecordDTO : picRecords) {
            evoCarService.syncCarRecord(carRecordDTO);
        }

    }

}
