package com.ruoyi.shcy.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.EmergencyAlertEventInfo;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.mapper.EmergencyAlertEventInfoMapper;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.service.IEmergencyAlertEventInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 突发告警事件派遣信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
@Service
public class EmergencyAlertEventInfoServiceImpl implements IEmergencyAlertEventInfoService
{
    @Autowired
    private EmergencyAlertEventInfoMapper emergencyAlertEventInfoMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    /**
     * 查询突发告警事件派遣信息
     *
     * @param id 突发告警事件派遣信息主键
     * @return 突发告警事件派遣信息
     */
    @Override
    public EmergencyAlertEventInfo selectEmergencyAlertEventInfoById(Long id)
    {
        return handlePhoto(emergencyAlertEventInfoMapper.selectEmergencyAlertEventInfoById(id));
    }

    /**
     * 查询突发告警事件派遣信息列表
     *
     * @param emergencyAlertEventInfo 突发告警事件派遣信息
     * @return 突发告警事件派遣信息
     */
    @Override
    public List<EmergencyAlertEventInfo> selectEmergencyAlertEventInfoList(EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        return emergencyAlertEventInfoMapper.selectEmergencyAlertEventInfoList(emergencyAlertEventInfo);
    }

    /**
     * 新增突发告警事件派遣信息
     *
     * @param emergencyAlertEventInfo 突发告警事件派遣信息
     * @return 结果
     */
    @Override
    public int insertEmergencyAlertEventInfo(EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        emergencyAlertEventInfo.setCreateTime(DateUtils.getNowDate());
        return emergencyAlertEventInfoMapper.insertEmergencyAlertEventInfo(emergencyAlertEventInfo);
    }

    /**
     * 修改突发告警事件派遣信息
     *
     * @param emergencyAlertEventInfo 突发告警事件派遣信息
     * @return 结果
     */
    @Override
    public int updateEmergencyAlertEventInfo(EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        return emergencyAlertEventInfoMapper.updateEmergencyAlertEventInfo(emergencyAlertEventInfo);
    }

    /**
     * 批量删除突发告警事件派遣信息
     *
     * @param ids 需要删除的突发告警事件派遣信息主键
     * @return 结果
     */
    @Override
    public int deleteEmergencyAlertEventInfoByIds(Long[] ids)
    {
        return emergencyAlertEventInfoMapper.deleteEmergencyAlertEventInfoByIds(ids);
    }

    /**
     * 删除突发告警事件派遣信息信息
     *
     * @param id 突发告警事件派遣信息主键
     * @return 结果
     */
    @Override
    public int deleteEmergencyAlertEventInfoById(Long id)
    {
        return emergencyAlertEventInfoMapper.deleteEmergencyAlertEventInfoById(id);
    }

    @Override
    public int handleEmergencyAlertEventInfo(EmergencyAlertEventInfo emergencyAlertEventInfo) {
        // 更新案件状态
        emergencyAlertEventInfo.setCirculationState(FxftConstants.PROCESSED);
        emergencyAlertEventInfo.setCaseFinishTime(DateUtils.getNowDate());
        // 判断当前时间是否小于emergencyAlertEventInfo的DisposalDeadline
        if (DateUtils.getNowDate().compareTo(emergencyAlertEventInfo.getDisposalDeadline()) < 0) {
            emergencyAlertEventInfo.setDealInTimeState(FxftConstants.OVERTIME_STATE_NORMAL);
        } else {
            emergencyAlertEventInfo.setDealInTimeState(FxftConstants.OVERTIME_STATE_OVERTIME);
        }
        return emergencyAlertEventInfoMapper.updateEmergencyAlertEventInfo(emergencyAlertEventInfo);
    }

    @Override
    public int handleEmergency(EmergencyAlertEventInfo emergencyAlertEventInfo) {
        emergencyAlertEventInfo.setEventNo(DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(10));
        emergencyAlertEventInfo.setDisposalPerson("锦石市政");
        emergencyAlertEventInfo.setCirculationState(FxftConstants.UNPROCESSED);

        // 截至日期 =》 当前时间 + 1小时
        emergencyAlertEventInfo.setDisposalDeadline(DateUtils.addHours(new Date(), 1));
        emergencyAlertEventInfo.setCreateTime(new Date());
        return emergencyAlertEventInfoMapper.insertEmergencyAlertEventInfo(emergencyAlertEventInfo);
    }

    @Override
    public long getCaseCount(EmergencyAlertEventInfo emergencyAlertEventInfo) {
        return emergencyAlertEventInfoMapper.getCaseCount(emergencyAlertEventInfo);
    }

    public EmergencyAlertEventInfo handlePhoto(EmergencyAlertEventInfo emergencyAlertEventInfo) {
        if (StrUtil.isNotEmpty(emergencyAlertEventInfo.getRescueOperationPhoto())) {
            List<String> photoUrls = new ArrayList<>();
            String[] fileIds = emergencyAlertEventInfo.getRescueOperationPhoto().split(",");
            for (String fileId : fileIds) {
                ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
                photoUrls.add(shcyFileInfo.getFilePath());
            }
            emergencyAlertEventInfo.setPhotoUrls(photoUrls);
        }

        // if (StrUtil.isNotEmpty(emergencyAlertEventInfo.getFloodMaterialPhoto())) {
        //     List<String> matUrls = new ArrayList<>();
        //     String[] fileIds = emergencyAlertEventInfo.getFloodMaterialPhoto().split(",");
        //     for (String fileId : fileIds) {
        //         ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
        //         matUrls.add(shcyFileInfo.getFilePath());
        //     }
        //     emergencyAlertEventInfo.setFloodMaterialPhotoUrls(matUrls);
        // }
        //
        // if (StrUtil.isNotEmpty(emergencyAlertEventInfo.getFloodPersonPhoto())) {
        //     List<String> perUrls = new ArrayList<>();
        //     String[] fileIds = emergencyAlertEventInfo.getFloodPersonPhoto().split(",");
        //     for (String fileId : fileIds) {
        //         ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
        //         perUrls.add(shcyFileInfo.getFilePath());
        //     }
        //     emergencyAlertEventInfo.setFloodPersonPhotolUrls(perUrls);
        // }
        return emergencyAlertEventInfo;
    }

}
