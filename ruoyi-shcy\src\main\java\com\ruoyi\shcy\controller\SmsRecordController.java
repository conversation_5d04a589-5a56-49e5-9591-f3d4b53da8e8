package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.SmsRecord;
import com.ruoyi.shcy.service.ISmsRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 短信发送记录Controller
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@RestController
@RequestMapping("/shcy/smsRecord")
public class SmsRecordController extends BaseController
{
    @Autowired
    private ISmsRecordService smsRecordService;

    /**
     * 查询短信发送记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:smsRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(SmsRecord smsRecord)
    {
        startPage();
        List<SmsRecord> list = smsRecordService.selectSmsRecordList(smsRecord);
        return getDataTable(list);
    }

    /**
     * 导出短信发送记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:smsRecord:export')")
    @Log(title = "短信发送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SmsRecord smsRecord)
    {
        List<SmsRecord> list = smsRecordService.selectSmsRecordList(smsRecord);
        ExcelUtil<SmsRecord> util = new ExcelUtil<SmsRecord>(SmsRecord.class);
        util.exportExcel(response, list, "短信发送记录数据");
    }

    /**
     * 获取短信发送记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:smsRecord:query')")
    @GetMapping(value = "/{smsId}")
    public AjaxResult getInfo(@PathVariable("smsId") Long smsId)
    {
        return AjaxResult.success(smsRecordService.selectSmsRecordBySmsId(smsId));
    }

    /**
     * 新增短信发送记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:smsRecord:add')")
    @Log(title = "短信发送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SmsRecord smsRecord)
    {
        return toAjax(smsRecordService.insertSmsRecord(smsRecord));
    }

    /**
     * 修改短信发送记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:smsRecord:edit')")
    @Log(title = "短信发送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SmsRecord smsRecord)
    {
        return toAjax(smsRecordService.updateSmsRecord(smsRecord));
    }

    /**
     * 删除短信发送记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:smsRecord:remove')")
    @Log(title = "短信发送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{smsIds}")
    public AjaxResult remove(@PathVariable Long[] smsIds)
    {
        return toAjax(smsRecordService.deleteSmsRecordBySmsIds(smsIds));
    }
}
