package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 案事件对象 shcy_case
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
public class ShcyCase extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺名称")
    private String shopName;

    /**
     * 事件类型
     */
    @Excel(name = "事件类型")
    private String caseType;

    /**
     * 事件类型
     */
    @Excel(name = "事件类型名称")
    private String caseTypeName;

    /**
     * 事件描述
     */
    @Excel(name = "事件描述")
    private String caseDescription;

    /**
     * 事件处理人
     */
    @Excel(name = "事件处理人")
    private String caseDealBy;

    /**
     * 处理照片信息
     */
    @Excel(name = "处理照片信息")
    private String caseDealPhoto;

    /**
     * 流转状态
     */
    @Excel(name = "流转状态")
    private String circulationState;

    /**
     * 按时完成状态
     */
    @Excel(name = "按时完成状态")
    private String dealInTimeState;

    /**
     * 店铺地址
     */
    @Excel(name = "店铺地址")
    private String shopAddress;

    /**
     * 巡检日志id
     */
    @Excel(name = "巡检日志id")
    private Long shopCheckLogId;

    /**
     * 事件截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date caseEndTime;

    /**
     * 事件完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date caseFinishTime;

    /**
     * 照片 url
     **/
    private List<String> photoUrls;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setCaseType(String caseType) {
        this.caseType = caseType;
    }

    public String getCaseType() {
        return caseType;
    }

    public String getCaseTypeName() {
        return caseTypeName;
    }

    public void setCaseTypeName(String caseTypeName) {
        this.caseTypeName = caseTypeName;
    }

    public void setCaseDescription(String caseDescription) {
        this.caseDescription = caseDescription;
    }

    public String getCaseDescription() {
        return caseDescription;
    }

    public void setCaseDealBy(String caseDealBy) {
        this.caseDealBy = caseDealBy;
    }

    public String getCaseDealBy() {
        return caseDealBy;
    }

    public void setCaseDealPhoto(String caseDealPhoto) {
        this.caseDealPhoto = caseDealPhoto;
    }

    public String getCaseDealPhoto() {
        return caseDealPhoto;
    }

    public void setCirculationState(String circulationState) {
        this.circulationState = circulationState;
    }

    public String getCirculationState() {
        return circulationState;
    }

    public void setDealInTimeState(String dealInTimeState) {
        this.dealInTimeState = dealInTimeState;
    }

    public String getDealInTimeState() {
        // 待处理
        if ("1".equals(circulationState)) {
            if (caseEndTime == null) {
                return dealInTimeState;
            }
            // 当前时间小于截止时间
            if (new Date().before(caseEndTime)) {
                // 正常
                return "0";
            } else {
                // 超时
                return "1";
            }
        } else if ("0".equals(circulationState)) {
            if (caseFinishTime == null || caseEndTime == null) {
                return dealInTimeState;
            }
            // 完成时间小于截止时间
            if (caseFinishTime.before(caseEndTime)) {
                // 正常
                return "0";
            } else {
                // 超时
                return "1";
            }
        }
        return dealInTimeState;
    }

    public void setShopAddress(String shopAddress) {
        this.shopAddress = shopAddress;
    }

    public String getShopAddress() {
        return shopAddress;
    }

    public void setShopCheckLogId(Long shopCheckLogId) {
        this.shopCheckLogId = shopCheckLogId;
    }

    public Long getShopCheckLogId() {
        return shopCheckLogId;
    }

    public void setCaseEndTime(Date caseEndTime) {
        this.caseEndTime = caseEndTime;
    }

    public Date getCaseEndTime() {
        return caseEndTime;
    }

    public void setCaseFinishTime(Date caseFinishTime) {
        this.caseFinishTime = caseFinishTime;
    }

    public Date getCaseFinishTime() {
        return caseFinishTime;
    }

    public List<String> getPhotoUrls() {
        return photoUrls;
    }

    public void setPhotoUrls(List<String> photoUrls) {
        this.photoUrls = photoUrls;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("shopName", getShopName())
                .append("caseType", getCaseType())
                .append("caseTypeName", getCaseTypeName())
                .append("caseDescription", getCaseDescription())
                .append("caseDealBy", getCaseDealBy())
                .append("caseDealPhoto", getCaseDealPhoto())
                .append("circulationState", getCirculationState())
                .append("createBy", getCreateBy())
                .append("updateBy", getUpdateBy())
                .append("dealInTimeState", getDealInTimeState())
                .append("shopAddress", getShopAddress())
                .append("shopCheckLogId", getShopCheckLogId())
                .append("createTime", getCreateTime())
                .append("caseEndTime", getCaseEndTime())
                .append("updateTime", getUpdateTime())
                .append("caseFinishTime", getCaseFinishTime())
                .toString();
    }

}
