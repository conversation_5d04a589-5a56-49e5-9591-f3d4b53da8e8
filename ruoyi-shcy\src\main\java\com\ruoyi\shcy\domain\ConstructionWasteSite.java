package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小区建筑垃圾堆放点对象 shcy_construction_waste_site
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public class ConstructionWasteSite extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属小区 */
    @Excel(name = "所属小区")
    private String residential;

    /** 所属小区id */
    @Excel(name = "所属小区id")
    private String residentialId;

    /** 所属居委会 */
    @Excel(name = "所属居委会")
    private String committee;

    /** 所属居委会id */
    @Excel(name = "所属居委会id")
    private Long committeeId;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setResidential(String residential)
    {
        this.residential = residential;
    }

    public String getResidential()
    {
        return residential;
    }
    public void setResidentialId(String residentialId)
    {
        this.residentialId = residentialId;
    }

    public String getResidentialId()
    {
        return residentialId;
    }
    public void setCommittee(String committee)
    {
        this.committee = committee;
    }

    public String getCommittee()
    {
        return committee;
    }
    public void setCommitteeId(Long committeeId)
    {
        this.committeeId = committeeId;
    }

    public Long getCommitteeId()
    {
        return committeeId;
    }
    public void setLongitude(String longitude)
    {
        this.longitude = longitude;
    }

    public String getLongitude()
    {
        return longitude;
    }
    public void setLatitude(String latitude)
    {
        this.latitude = latitude;
    }

    public String getLatitude()
    {
        return latitude;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("committee", getCommittee())
                .append("committeeId", getCommitteeId())
                .append("residential", getResidential())
                .append("residentialId", getResidentialId())
                .append("longitude", getLongitude())
                .append("latitude", getLatitude())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("type", getType())
                .append("coordinate", getCoordinate())
                .append("name", getName())
                .toString();
    }

}
