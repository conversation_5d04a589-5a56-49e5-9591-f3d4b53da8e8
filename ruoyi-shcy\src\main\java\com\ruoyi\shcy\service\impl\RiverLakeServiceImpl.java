package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.RiverLakeMapper;
import com.ruoyi.shcy.domain.RiverLake;
import com.ruoyi.shcy.service.IRiverLakeService;

/**
 * 河湖水体Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-02-03
 */
@Service
public class RiverLakeServiceImpl implements IRiverLakeService 
{
    @Autowired
    private RiverLakeMapper riverLakeMapper;

    /**
     * 查询河湖水体
     * 
     * @param id 河湖水体主键
     * @return 河湖水体
     */
    @Override
    public RiverLake selectRiverLakeById(Long id)
    {
        return riverLakeMapper.selectRiverLakeById(id);
    }

    /**
     * 查询河湖水体列表
     * 
     * @param riverLake 河湖水体
     * @return 河湖水体
     */
    @Override
    public List<RiverLake> selectRiverLakeList(RiverLake riverLake)
    {
        return riverLakeMapper.selectRiverLakeList(riverLake);
    }

    /**
     * 新增河湖水体
     * 
     * @param riverLake 河湖水体
     * @return 结果
     */
    @Override
    public int insertRiverLake(RiverLake riverLake)
    {
        riverLake.setCreateTime(DateUtils.getNowDate());
        return riverLakeMapper.insertRiverLake(riverLake);
    }

    /**
     * 修改河湖水体
     * 
     * @param riverLake 河湖水体
     * @return 结果
     */
    @Override
    public int updateRiverLake(RiverLake riverLake)
    {
        riverLake.setUpdateTime(DateUtils.getNowDate());
        return riverLakeMapper.updateRiverLake(riverLake);
    }

    /**
     * 批量删除河湖水体
     * 
     * @param ids 需要删除的河湖水体主键
     * @return 结果
     */
    @Override
    public int deleteRiverLakeByIds(Long[] ids)
    {
        return riverLakeMapper.deleteRiverLakeByIds(ids);
    }

    /**
     * 删除河湖水体信息
     * 
     * @param id 河湖水体主键
     * @return 结果
     */
    @Override
    public int deleteRiverLakeById(Long id)
    {
        return riverLakeMapper.deleteRiverLakeById(id);
    }
}
