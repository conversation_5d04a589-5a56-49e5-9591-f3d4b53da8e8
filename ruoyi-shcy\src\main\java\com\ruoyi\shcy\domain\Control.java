package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 监控点对象 shcy_control
 * 
 * <AUTHOR>
 * @date 2022-07-26
 */
public class Control extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 监控点ID */
    private Long controlId;

    /** 监控点名称 */
    @Excel(name = "监控点名称")
    private String controlName;

    /** 监控点代码 */
    @Excel(name = "监控点代码")
    private String controlCode;

    /** 监控点经度 */
    @Excel(name = "监控点经度")
    private String controlLon;

    /** 监控点纬度 */
    @Excel(name = "监控点纬度")
    private String controlLat;

    /** 监控点类型 */
    @Excel(name = "监控点类型")
    private String controlType;

    public void setControlId(Long controlId) 
    {
        this.controlId = controlId;
    }

    public Long getControlId() 
    {
        return controlId;
    }
    public void setControlName(String controlName) 
    {
        this.controlName = controlName;
    }

    public String getControlName() 
    {
        return controlName;
    }
    public void setControlCode(String controlCode) 
    {
        this.controlCode = controlCode;
    }

    public String getControlCode() 
    {
        return controlCode;
    }
    public void setControlLon(String controlLon) 
    {
        this.controlLon = controlLon;
    }

    public String getControlLon() 
    {
        return controlLon;
    }
    public void setControlLat(String controlLat) 
    {
        this.controlLat = controlLat;
    }

    public String getControlLat() 
    {
        return controlLat;
    }
    public void setControlType(String controlType) 
    {
        this.controlType = controlType;
    }

    public String getControlType() 
    {
        return controlType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("controlId", getControlId())
            .append("controlName", getControlName())
            .append("controlCode", getControlCode())
            .append("controlLon", getControlLon())
            .append("controlLat", getControlLat())
            .append("controlType", getControlType())
            .toString();
    }
}
