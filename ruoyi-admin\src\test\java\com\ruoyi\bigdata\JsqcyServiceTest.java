package com.ruoyi.bigdata;

import com.ruoyi.bigdata.service.JsqcyService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class JsqcyServiceTest {

    @Autowired
    private JsqcyService jsqcyService;

    @Test
    public void contextLoads() {
        // 这里没有任何代码，只是确保应用程序上下文能够成功加载
    }

    @Test
    public void testGetToken() {
        // System.out.println("token: " + jsqcyService.getToken());
        // System.out.println("api-token: " + jsqcyService.getApiToken());
        // System.out.println("获取城市预警防汛数据: " + jsqcyService.getCsyjfxData());
        // System.out.println("获取城市预警(气象)数据: " + jsqcyService.getCsyjqxData());
        // System.out.println("获取防汛防台(预警)数据: " + jsqcyService.getFxftyjData());
        // System.out.println("获取天气预报(当天)数据: " + jsqcyService.getTqybData());
        // System.out.println("获取防汛防台(综合汛情积水)数据: " + jsqcyService.getFxftzhxqjsData());
        // System.out.println("获取防汛防台(综合汛情风速风向)数据: " + jsqcyService.getFxftzhxqfsfxData());
        // System.out.println("获取防汛防台(综合汛情水位)数据: " + jsqcyService.getFxftzhxqswData());
        // System.out.println("获取防汛防台(综合汛情雨量)数据: " + jsqcyService.getFxftzhxqylData());
        // System.out.println("获取防汛防台(综合汛情泵闸)数据: " + jsqcyService.getFxftzhxqbzData());
    }
}
