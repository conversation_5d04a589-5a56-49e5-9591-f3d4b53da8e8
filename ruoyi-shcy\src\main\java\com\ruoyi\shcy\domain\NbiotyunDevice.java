package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 设备管理对象 nbiotyun_device
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
public class NbiotyunDevice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备imei
     */
    @Excel(name = "设备imei")
    private String deviceImei;

    /**
     * 设备imsi
     */
    @Excel(name = "设备imsi")
    private String deviceImsi;

    /**
     * 设备iccid
     */
    @Excel(name = "设备iccid")
    private String iccid;

    /**
     * 设备类型名称
     */
    @Excel(name = "设备类型名称")
    private String deviceTypeName;

    /**
     * 设备型号名称
     */
    @Excel(name = "设备型号名称")
    private String deviceModelName;

    /**
     * 入网注册标志 0 未注册 1 注册
     */
    @Excel(name = "入网注册标志")
    private int registerSign;

    /**
     * 设备状态 0:正常 1:故障 2:报警 3:手动报警 4:离线 5:待删除 6:停用 7:未激活
     */
    @Excel(name = "设备状态")
    private int state;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private double latitude;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private double longitude;

    /**
     * 设备详细安装地址
     */
    @Excel(name = "设备详细安装地址")
    private String installAddress;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contact;

    /**
     * 电话号码
     */
    @Excel(name = "电话号码")
    private String phonenumber;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 安装点名称
     */
    @Excel(name = "安装点名称")
    private String roomName;

    /**
     * 归属地区
     */
    @Excel(name = "归属地区")
    private String region;

    /**
     * 负责人
     */
    @Excel(name = "负责人")
    private String responsiblePerson;

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceImei(String deviceImei) {
        this.deviceImei = deviceImei;
    }

    public String getDeviceImei() {
        return deviceImei;
    }

    public void setDeviceImsi(String deviceImsi) {
        this.deviceImsi = deviceImsi;
    }

    public String getDeviceImsi() {
        return deviceImsi;
    }

    public void setIccid(String iccid) {
        this.iccid = iccid;
    }

    public String getIccid() {
        return iccid;
    }

    public void setDeviceTypeName(String deviceTypeName) {
        this.deviceTypeName = deviceTypeName;
    }

    public String getDeviceTypeName() {
        return deviceTypeName;
    }

    public void setDeviceModelName(String deviceModelName) {
        this.deviceModelName = deviceModelName;
    }

    public String getDeviceModelName() {
        return deviceModelName;
    }

    public void setRegisterSign(int registerSign) {
        this.registerSign = registerSign;
    }

    public int getRegisterSign() {
        return registerSign;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getState() {
        return state;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setInstallAddress(String installAddress) {
        this.installAddress = installAddress;
    }

    public String getInstallAddress() {
        return installAddress;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getContact() {
        return contact;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setRoomName(String roomName) {
        this.roomName = roomName;
    }

    public String getRoomName() {
        return roomName;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getRegion() {
        return region;
    }

    public String getResponsiblePerson() {
        return responsiblePerson;
    }

    public void setResponsiblePerson(String responsiblePerson) {
        this.responsiblePerson = responsiblePerson;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("deviceId", getDeviceId())
                .append("deviceImei", getDeviceImei())
                .append("deviceImsi", getDeviceImsi())
                .append("iccid", getIccid())
                .append("deviceTypeName", getDeviceTypeName())
                .append("deviceModelName", getDeviceModelName())
                .append("registerSign", getRegisterSign())
                .append("state", getState())
                .append("latitude", getLatitude())
                .append("longitude", getLongitude())
                .append("installAddress", getInstallAddress())
                .append("contact", getContact())
                .append("phonenumber", getPhonenumber())
                .append("companyName", getCompanyName())
                .append("roomName", getRoomName())
                .append("region", getRegion())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("responsiblePerson", getResponsiblePerson())
                .toString();
    }
}
