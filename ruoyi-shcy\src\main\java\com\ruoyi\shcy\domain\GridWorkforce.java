package com.ruoyi.shcy.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 网格工作力量对象 shcy_grid_workforce
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public class GridWorkforce extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 关联的网格id */
    @Excel(name = "关联的网格id")
    private Long gridId;

    /** 类别(常驻力量、辅助力量) */
    @Excel(name = "类别(常驻力量、辅助力量)")
    private String category;

    /** 身份(公安民警、综合执法队员等) */
    @Excel(name = "身份(公安民警、综合执法队员等)")
    private String identity;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 出生年月 */
    @Excel(name = "出生年月")
    private String birthDate;

    /** 政治面貌 */
    @Excel(name = "政治面貌")
    private String politicalStatus;

    /** 单位职务 */
    @Excel(name = "单位职务")
    private String position;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contact;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer orderNum;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setGridId(Long gridId) 
    {
        this.gridId = gridId;
    }

    public Long getGridId() 
    {
        return gridId;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setIdentity(String identity) 
    {
        this.identity = identity;
    }

    public String getIdentity() 
    {
        return identity;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setBirthDate(String birthDate)
    {
        this.birthDate = birthDate;
    }

    public String getBirthDate()
    {
        return birthDate;
    }
    public void setPoliticalStatus(String politicalStatus) 
    {
        this.politicalStatus = politicalStatus;
    }

    public String getPoliticalStatus() 
    {
        return politicalStatus;
    }
    public void setPosition(String position) 
    {
        this.position = position;
    }

    public String getPosition() 
    {
        return position;
    }
    public void setContact(String contact) 
    {
        this.contact = contact;
    }

    public String getContact() 
    {
        return contact;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("gridId", getGridId())
            .append("category", getCategory())
            .append("identity", getIdentity())
            .append("name", getName())
            .append("birthDate", getBirthDate())
            .append("politicalStatus", getPoliticalStatus())
            .append("position", getPosition())
            .append("contact", getContact())
            .append("orderNum", getOrderNum())
            .toString();
    }
}
