package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 突发告警事件派遣信息对象 shcy_emergency_alert_event_info
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
public class EmergencyAlertEventInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 事件编号 */
    @Excel(name = "事件编号")
    private String eventNo;

    /** 告警地点 */
    @Excel(name = "告警地点")
    private String alarmLocation;

    /** 处置人员 */
    @Excel(name = "处置人员")
    private String disposalPerson;

    /** 处置截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处置截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date disposalDeadline;

    /** 防汛物资情况照片 */
    private String floodMaterialPhoto;

    /** 抢险人员是否到位情况照片 */
    private String floodPersonPhoto;

    /** 抢险作业情况照片 */
    private String rescueOperationPhoto;

    /** 临时管制措施 */
    private String temporaryControlMeasure;

    /** 其他隐患 */
    private String otherHazard;

    /** 流转状态 */
    @Excel(name = "流转状态")
    private String circulationState;

    /** 事件完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseFinishTime;

    /** 按时完成状态 */
    private String dealInTimeState;

    private List<String> photoUrls;

    private List<String> floodPersonPhotolUrls;

    private List<String> floodMaterialPhotoUrls;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setEventNo(String eventNo)
    {
        this.eventNo = eventNo;
    }

    public String getEventNo()
    {
        return eventNo;
    }
    public void setAlarmLocation(String alarmLocation)
    {
        this.alarmLocation = alarmLocation;
    }

    public String getAlarmLocation()
    {
        return alarmLocation;
    }
    public void setDisposalPerson(String disposalPerson)
    {
        this.disposalPerson = disposalPerson;
    }

    public String getDisposalPerson()
    {
        return disposalPerson;
    }
    public void setDisposalDeadline(Date disposalDeadline)
    {
        this.disposalDeadline = disposalDeadline;
    }

    public Date getDisposalDeadline()
    {
        return disposalDeadline;
    }
    public void setFloodMaterialPhoto(String floodMaterialPhoto)
    {
        this.floodMaterialPhoto = floodMaterialPhoto;
    }

    public String getFloodMaterialPhoto()
    {
        return floodMaterialPhoto;
    }
    public void setFloodPersonPhoto(String floodPersonPhoto)
    {
        this.floodPersonPhoto = floodPersonPhoto;
    }

    public String getFloodPersonPhoto()
    {
        return floodPersonPhoto;
    }
    public void setRescueOperationPhoto(String rescueOperationPhoto)
    {
        this.rescueOperationPhoto = rescueOperationPhoto;
    }

    public String getRescueOperationPhoto()
    {
        return rescueOperationPhoto;
    }
    public void setTemporaryControlMeasure(String temporaryControlMeasure)
    {
        this.temporaryControlMeasure = temporaryControlMeasure;
    }

    public String getTemporaryControlMeasure()
    {
        return temporaryControlMeasure;
    }
    public void setOtherHazard(String otherHazard)
    {
        this.otherHazard = otherHazard;
    }

    public String getOtherHazard()
    {
        return otherHazard;
    }

    public String getCirculationState() {
        return circulationState;
    }

    public void setCirculationState(String circulationState) {
        this.circulationState = circulationState;
    }

    public Date getCaseFinishTime() {
        return caseFinishTime;
    }

    public void setCaseFinishTime(Date caseFinishTime) {
        this.caseFinishTime = caseFinishTime;
    }

    public String getDealInTimeState() {
        return dealInTimeState;
    }

    public void setDealInTimeState(String dealInTimeState) {
        this.dealInTimeState = dealInTimeState;
    }

    public List<String> getPhotoUrls() {
        return photoUrls;
    }

    public void setPhotoUrls(List<String> photoUrls) {
        this.photoUrls = photoUrls;
    }

    public List<String> getFloodPersonPhotolUrls() {
        return floodPersonPhotolUrls;
    }

    public void setFloodPersonPhotolUrls(List<String> floodPersonPhotolUrls) {
        this.floodPersonPhotolUrls = floodPersonPhotolUrls;
    }

    public List<String> getFloodMaterialPhotoUrls() {
        return floodMaterialPhotoUrls;
    }

    public void setFloodMaterialPhotoUrls(List<String> floodMaterialPhotoUrls) {
        this.floodMaterialPhotoUrls = floodMaterialPhotoUrls;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventNo", getEventNo())
            .append("alarmLocation", getAlarmLocation())
            .append("disposalPerson", getDisposalPerson())
            .append("createTime", getCreateTime())
            .append("disposalDeadline", getDisposalDeadline())
            .append("floodMaterialPhoto", getFloodMaterialPhoto())
            .append("floodPersonPhoto", getFloodPersonPhoto())
            .append("rescueOperationPhoto", getRescueOperationPhoto())
            .append("temporaryControlMeasure", getTemporaryControlMeasure())
            .append("otherHazard", getOtherHazard())
            .append("circulationState", getCirculationState())
            .append("caseFinishTime", getCaseFinishTime())
            .append("dealInTimeState", getDealInTimeState())
            .toString();
    }
}
