package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.Control;
import com.ruoyi.shcy.service.IControlService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 监控点Controller
 * 
 * <AUTHOR>
 * @date 2022-07-26
 */
@RestController
@RequestMapping("/shcy/control")
public class ControlController extends BaseController
{
    @Autowired
    private IControlService controlService;

    /**
     * 查询监控点列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:control:list')")
    @GetMapping("/list")
    public TableDataInfo list(Control control)
    {
        startPage();
        List<Control> list = controlService.selectControlList(control);
        return getDataTable(list);
    }

    /**
     * 导出监控点列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:control:export')")
    @Log(title = "监控点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Control control)
    {
        List<Control> list = controlService.selectControlList(control);
        ExcelUtil<Control> util = new ExcelUtil<Control>(Control.class);
        util.exportExcel(response, list, "监控点数据");
    }

    /**
     * 获取监控点详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:control:query')")
    @GetMapping(value = "/{controlId}")
    public AjaxResult getInfo(@PathVariable("controlId") Long controlId)
    {
        return AjaxResult.success(controlService.selectControlByControlId(controlId));
    }

    /**
     * 新增监控点
     */
    @PreAuthorize("@ss.hasPermi('shcy:control:add')")
    @Log(title = "监控点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Control control)
    {
        return toAjax(controlService.insertControl(control));
    }

    /**
     * 修改监控点
     */
    @PreAuthorize("@ss.hasPermi('shcy:control:edit')")
    @Log(title = "监控点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Control control)
    {
        return toAjax(controlService.updateControl(control));
    }

    /**
     * 删除监控点
     */
    @PreAuthorize("@ss.hasPermi('shcy:control:remove')")
    @Log(title = "监控点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{controlIds}")
    public AjaxResult remove(@PathVariable Long[] controlIds)
    {
        return toAjax(controlService.deleteControlByControlIds(controlIds));
    }
}
