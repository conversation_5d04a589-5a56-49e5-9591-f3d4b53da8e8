package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.SpecialTask;
import com.ruoyi.shcy.service.ISpecialTaskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 专项任务Controller
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
@RestController
@RequestMapping("/shcy/specialTask")
public class SpecialTaskController extends BaseController
{
    @Autowired
    private ISpecialTaskService specialTaskService;

    /**
     * 查询专项任务列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTask:list')")
    @GetMapping("/list")
    public TableDataInfo list(SpecialTask specialTask)
    {
        startPage();
        List<SpecialTask> list = specialTaskService.selectSpecialTaskList(specialTask);
        return getDataTable(list);
    }

    /**
     * 导出专项任务列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTask:export')")
    @Log(title = "专项任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SpecialTask specialTask)
    {
        List<SpecialTask> list = specialTaskService.selectSpecialTaskList(specialTask);
        ExcelUtil<SpecialTask> util = new ExcelUtil<SpecialTask>(SpecialTask.class);
        util.exportExcel(response, list, "专项任务数据");
    }

    /**
     * 获取专项任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTask:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(specialTaskService.selectSpecialTaskById(id));
    }

    /**
     * 新增专项任务
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTask:add')")
    @Log(title = "专项任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SpecialTask specialTask)
    {
        return toAjax(specialTaskService.insertSpecialTask(specialTask));
    }

    /**
     * 修改专项任务
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTask:edit')")
    @Log(title = "专项任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SpecialTask specialTask)
    {
        return toAjax(specialTaskService.updateSpecialTask(specialTask));
    }

    /**
     * 删除专项任务
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTask:remove')")
    @Log(title = "专项任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(specialTaskService.deleteSpecialTaskByIds(ids));
    }
}
