package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 居委会信息对象 shcy_committee
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public class Committee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 居委会名称 */
    @Excel(name = "居委会名称")
    private String committeeName;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 经纬度（面） */
    @Excel(name = "经纬度", readConverterExp = "面=")
    private String siteRange;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 填充色*/
    @Excel(name="填充色")
    private String fillColor;

    /** 边框色 */
    @Excel(name="边框色")
    private String outlineColor;

    /** 街长填充颜色 */
    @Excel(name = "街长填充颜色")
    private String streetFillColor;

    /** 街长边框色 */
    @Excel(name = "街长边框色")
    private String streetOutlineColor;


    public String getFillColor() {
        return fillColor;
    }

    public void setFillColor(String fillColor) {
        this.fillColor = fillColor;
    }

    public String getOutlineColor() {
        return outlineColor;
    }

    public void setOutlineColor(String outlineColor) {
        this.outlineColor = outlineColor;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCommitteeName(String committeeName)
    {
        this.committeeName = committeeName;
    }

    public String getCommitteeName()
    {
        return committeeName;
    }
    public void setLongitude(String longitude)
    {
        this.longitude = longitude;
    }

    public String getLongitude()
    {
        return longitude;
    }
    public void setLatitude(String latitude)
    {
        this.latitude = latitude;
    }

    public String getLatitude()
    {
        return latitude;
    }
    public void setSiteRange(String siteRange)
    {
        this.siteRange = siteRange;
    }

    public String getSiteRange()
    {
        return siteRange;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getStreetFillColor() {
        return streetFillColor;
    }

    public void setStreetFillColor(String streetFillColor) {
        this.streetFillColor = streetFillColor;
    }

    public String getStreetOutlineColor() {
        return streetOutlineColor;
    }

    public void setStreetOutlineColor(String streetOutlineColor) {
        this.streetOutlineColor = streetOutlineColor;
    }

    @Override
    public String toString() {
        return "Committee{" +
                "id=" + id +
                ", committeeName='" + committeeName + '\'' +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", siteRange='" + siteRange + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", fillColor='" + fillColor + '\'' +
                ", outlineColor='" + outlineColor + '\'' +
                '}';
    }
}
