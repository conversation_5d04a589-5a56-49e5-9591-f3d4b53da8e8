package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 防汛安置点信息对象 shcy_flood_shelter_info
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public class FloodShelterInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 安置点名称 */
    @Excel(name = "安置点名称")
    private String shelterName;

    /** 地址 */
    @Excel(name = "地址")
    private String address;

    /** 房间数 */
    @Excel(name = "房间数")
    private Integer roomCount;

    /** 可容纳人数 */
    @Excel(name = "可容纳人数")
    private Integer capacity;

    /** 具体房间位置 */
    @Excel(name = "具体房间位置")
    private String roomLocation;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactNumber;

    /** 图形类型 */
    private String type;

    /** 坐标 */
    private String coordinate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setShelterName(String shelterName) 
    {
        this.shelterName = shelterName;
    }

    public String getShelterName() 
    {
        return shelterName;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setRoomCount(Integer roomCount) 
    {
        this.roomCount = roomCount;
    }

    public Integer getRoomCount() 
    {
        return roomCount;
    }
    public void setCapacity(Integer capacity) 
    {
        this.capacity = capacity;
    }

    public Integer getCapacity() 
    {
        return capacity;
    }
    public void setRoomLocation(String roomLocation) 
    {
        this.roomLocation = roomLocation;
    }

    public String getRoomLocation() 
    {
        return roomLocation;
    }
    public void setContactPerson(String contactPerson) 
    {
        this.contactPerson = contactPerson;
    }

    public String getContactPerson() 
    {
        return contactPerson;
    }
    public void setContactNumber(String contactNumber) 
    {
        this.contactNumber = contactNumber;
    }

    public String getContactNumber() 
    {
        return contactNumber;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setCoordinate(String coordinate) 
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate() 
    {
        return coordinate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("shelterName", getShelterName())
            .append("address", getAddress())
            .append("roomCount", getRoomCount())
            .append("capacity", getCapacity())
            .append("roomLocation", getRoomLocation())
            .append("contactPerson", getContactPerson())
            .append("contactNumber", getContactNumber())
            .append("type", getType())
            .append("coordinate", getCoordinate())
            .toString();
    }
}
