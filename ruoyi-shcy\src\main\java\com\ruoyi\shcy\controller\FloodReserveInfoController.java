package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.FloodReserveInfo;
import com.ruoyi.shcy.service.IFloodReserveInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 防汛物资储备信息Controller
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
@RestController
@RequestMapping("/shcy/floodReserveInfo")
public class FloodReserveInfoController extends BaseController
{
    @Autowired
    private IFloodReserveInfoService floodReserveInfoService;

    /**
     * 查询防汛物资储备信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodReserveInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(FloodReserveInfo floodReserveInfo)
    {
        startPage();
        List<FloodReserveInfo> list = floodReserveInfoService.selectFloodReserveInfoList(floodReserveInfo);
        return getDataTable(list);
    }

    /**
     * 导出防汛物资储备信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodReserveInfo:export')")
    @Log(title = "防汛物资储备信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FloodReserveInfo floodReserveInfo)
    {
        List<FloodReserveInfo> list = floodReserveInfoService.selectFloodReserveInfoList(floodReserveInfo);
        ExcelUtil<FloodReserveInfo> util = new ExcelUtil<FloodReserveInfo>(FloodReserveInfo.class);
        util.exportExcel(response, list, "防汛物资储备信息数据");
    }

    /**
     * 获取防汛物资储备信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodReserveInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(floodReserveInfoService.selectFloodReserveInfoById(id));
    }

    /**
     * 新增防汛物资储备信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodReserveInfo:add')")
    @Log(title = "防汛物资储备信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FloodReserveInfo floodReserveInfo)
    {
        return toAjax(floodReserveInfoService.insertFloodReserveInfo(floodReserveInfo));
    }

    /**
     * 修改防汛物资储备信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodReserveInfo:edit')")
    @Log(title = "防汛物资储备信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FloodReserveInfo floodReserveInfo)
    {
        return toAjax(floodReserveInfoService.updateFloodReserveInfo(floodReserveInfo));
    }

    /**
     * 删除防汛物资储备信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodReserveInfo:remove')")
    @Log(title = "防汛物资储备信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(floodReserveInfoService.deleteFloodReserveInfoByIds(ids));
    }
}
