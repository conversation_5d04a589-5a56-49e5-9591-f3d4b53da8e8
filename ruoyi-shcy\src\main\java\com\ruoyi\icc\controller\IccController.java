package com.ruoyi.icc.controller;

import cn.hutool.core.collection.CollUtil;
import com.dahuatech.icc.exception.ClientException;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.icc.video.VideoService;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@RequestMapping("/icc")
@RestController
@Slf4j
public class IccController {

    private VideoService videoService;

    @Autowired
    private IccService iccService;

    private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    {
        try {
            videoService = new VideoService();
        } catch (ClientException e) {
            log.error("初始化客户端失败", e);
            videoService = null;
        }
    }

    @GetMapping("/getAccessToken")
    public AjaxResult getAccessToken() throws ClientException {
        String accessToken = iccService.getAccessToken();
        return AjaxResult.success(accessToken);
    }

    @GetMapping("/getAlarmRecords")
    public AjaxResult getAlarmRecords() throws ClientException {
        JsonNode alarmRecords = videoService.getAlarmRecords("{C7D035FE-4CC7-AC4E-B71D-4DA2FC86CF51}");
        return AjaxResult.success(alarmRecords);
    }

    @GetMapping("/replay_HLS")
    public AjaxResult replay_HLS() throws ClientException {

        String channelCode = "1000010$1$0$0";

        JsonNode records = videoService.getAlarmRecords("{C7D035FE-4CC7-AC4E-B71D-4DA2FC86CF51}");
        JsonNode videoRecord = records.get(0);
        String streamType = videoRecord.get("streamType").asText();
        String recordSource = videoRecord.get("recordSource").asText();
        String recordType = videoRecord.get("recordType").asText();
        String startTime = videoRecord.get("startTime").asText();
        String endTime = videoRecord.get("endTime").asText();
        String fileName = videoRecord.get("recordName").asText();
        String ssId = videoRecord.get("ssId").asText();
        String streamId = videoRecord.get("streamId").asText();
        String diskId = videoRecord.get("diskId").asText();
        startTime = simpleDateFormat.format(new Date(Long.parseLong(startTime) * 1000));
        endTime = simpleDateFormat.format(new Date(Long.parseLong(endTime) * 1000));
        log.info("----步骤三----{}------", "请求‘HLS、RTMP录像回放（FLV不支持）’接口");
        String url = videoService.replay(channelCode, "1", "hls", recordSource, "2", startTime, endTime);
        log.info("录像回放，hls流地址=[{}]", url);
        return AjaxResult.success("操作成功", url);
    }

    @GetMapping("/getVideoUrl")
    public AjaxResult getVideoUrl(IccAlarmRecord alarmRecord) throws ClientException {
        // JsonNode records = videoService.getAlarmRecords(alarmRecord.getAlarmCode());
        // JsonNode videoRecord = records.get(0);
        // String channelId = videoRecord.get("channelId").asText();
        // String recordSource = videoRecord.get("recordSource").asText();
        // String startTime = videoRecord.get("startTime").asText();
        // String endTime = videoRecord.get("endTime").asText();
        // startTime = simpleDateFormat.format(new Date(Long.parseLong(startTime) * 1000));
        // endTime = simpleDateFormat.format(new Date(Long.parseLong(endTime) * 1000));
        // String url = videoService.replay(channelId, "1", "hls", recordSource, "2", startTime, endTime);
        String url = videoService.getAlarmVideoPlayback(alarmRecord.getAlarmCode());
        String accessToken = iccService.getAccessToken();
        return AjaxResult.success("操作成功", url + "?token=" + accessToken);
    }

    @GetMapping("/getRealTimeUrl")
    public AjaxResult getRealTimeUrl(String channelCode) throws ClientException {
        String url = videoService.realtime(channelCode, "1", "flv");
        String accessToken = iccService.getAccessToken();
        return AjaxResult.success("操作成功", url + "?token=" + accessToken);
    }

    @GetMapping("/getVideoUrls")
    public AjaxResult getVideoUrls(IccAlarmRecord alarmRecord) throws ClientException {
        JsonNode records = videoService.getAlarmRecords(alarmRecord.getAlarmCode());
        if (records.size() == 0) {
            return AjaxResult.error("无录像");
        }
        List<String> list = new ArrayList<>();
        String accessToken = iccService.getAccessToken();
        for (int i = 0; i < records.size(); i++) {
            JsonNode videoRecord = records.get(i);
            String channelId = videoRecord.get("channelId").asText();
            String recordSource = videoRecord.get("recordSource").asText();
            String recordType = videoRecord.get("videoRecordType").asText();
            String startTime = videoRecord.get("startTime").asText();
            String endTime = videoRecord.get("endTime").asText();
            startTime = simpleDateFormat.format(new Date(Long.parseLong(startTime) * 1000));
            endTime = simpleDateFormat.format(new Date(Long.parseLong(endTime) * 1000));
            String url = videoService.replay(channelId, "1", "hls", recordSource, recordType, startTime, endTime);
            list.add(url + "?token=" + accessToken);
        }
        // 将list元素顺序反转
        return AjaxResult.success("操作成功", CollUtil.reverse(list));
    }

}
