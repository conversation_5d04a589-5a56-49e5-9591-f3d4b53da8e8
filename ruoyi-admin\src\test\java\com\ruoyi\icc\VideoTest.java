package com.ruoyi.icc;

import com.dahuatech.icc.exception.ClientException;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.icc.video.VideoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.text.SimpleDateFormat;

@SpringBootTest
@Slf4j
public class VideoTest {

    private VideoService videoService;

    @Autowired
    private IccService iccService;

    private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    {
        try {
            videoService = new VideoService();
        } catch (ClientException e) {
            log.error("初始化客户端失败", e);
            videoService = null;
        }
    }

    @Test
    public void contextLoads() {
    }

    /**
     * 测试视频回放（前2后4）
     *
     * @throws ClientException 客户端异常
     */
    @Test
    public void testVideoReplay() throws ClientException {
        // JsonNode records = videoService.getAlarmRecords("{C9D3513A-DCA5-9146-868D-B576AE5954AF}");
        // JsonNode videoRecord = records.get(0);
        // String channelId = videoRecord.get("channelId").asText();
        // String recordSource = videoRecord.get("recordSource").asText();
        // String recordType = videoRecord.get("videoRecordType").asText();
        // String startTime = videoRecord.get("startTime").asText();
        // String endTime = videoRecord.get("endTime").asText();
        // startTime = simpleDateFormat.format(new Date(Long.parseLong(startTime) * 1000));
        // endTime = simpleDateFormat.format(new Date(Long.parseLong(endTime) * 1000));
        // String url = videoService.replay(channelId, "1", "hls", "2", "1", startTime, endTime);
        // String accessToken = iccService.getAccessToken();
        // String hlsUrl = url + "?token=" + accessToken;
        // log.info("hlsUrl: {}", hlsUrl);

        // 报警后录像
        /*JsonNode videoRecord = records.get(0);
        String channelId = videoRecord.get("channelId").asText();
        String recordSource = videoRecord.get("recordSource").asText();
        String recordType = videoRecord.get("videoRecordType").asText();
        String startTime = videoRecord.get("startTime").asText();
        String endTime = videoRecord.get("endTime").asText();
        String formatStartTime = simpleDateFormat.format(new Date(Long.parseLong(startTime) * 1000));
        String formatEndTime = simpleDateFormat.format(new Date(Long.parseLong(endTime) * 1000));
        // 中心获取录像
        // String originalUrl = videoService.replay(channelId, "1", "hls", recordSource, recordType, formatStartTime, formatEndTime);
        // 设备获取录像
        String originalUrl = videoService.replay(channelId, "1", "hls", "2", "1", formatStartTime, formatEndTime);
        System.out.println(originalUrl);
        // 报警前录像
        JsonNode beforeVideoRecord = records.get(1);
        String beforeStartTime = beforeVideoRecord.get("startTime").asText();
        // 构建正则表达式
        String patternString = "totallength/(\\d+)/begintime/(\\d+)/endtime/(\\d+)";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(originalUrl);
        // 计算endTime - beforeStartTime
        // 转换为long类型进行计算
        long startTimestamp = Long.parseLong(beforeStartTime);
        long endTimestamp = Long.parseLong(endTime);
        long difference = endTimestamp - startTimestamp;
        // 将时间差转换为字符串
        String differenceAsString = Long.toString(difference);
        // 替换匹配的部分
        String modifiedUrl = matcher.replaceAll("totallength/" + differenceAsString + "/begintime/" + beforeStartTime + "/endtime/" + endTime);*/

        String modifiedUrl = videoService.getAlarmVideoPlayback("{37B142CF-224A-B544-A92B-26CBF557D7AC}");

        String accessToken = iccService.getAccessToken();
        String hlsUrl = modifiedUrl + "?token=" + accessToken;
        log.info("hlsUrl: {}", hlsUrl);
    }

    @Test
    public void testRtsp()  throws ClientException {
        JsonNode records = videoService.getAlarmRecords("{17E2E79C-70B4-214F-8EA4-40A032E0197A}");
        JsonNode videoRecord = records.get(0);
        String channelId = videoRecord.get("channelId").asText();
        String recordSource = videoRecord.get("recordSource").asText();
        String startTime = videoRecord.get("startTime").asText();
        String endTime = videoRecord.get("endTime").asText();
        String url = iccService.replayByTime(channelId, "1", "2", "1", startTime, endTime);
        String accessToken = iccService.getAccessToken();
        String rtspUrl = url + "?token=" + accessToken;
        log.info("rtspUrl: {}", rtspUrl);
    }

}
