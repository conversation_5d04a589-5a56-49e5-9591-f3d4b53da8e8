package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.FloodControlKeyArea;
import com.ruoyi.shcy.mapper.FloodControlKeyAreaMapper;
import com.ruoyi.shcy.service.IFloodControlKeyAreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 防汛防台重点区域Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-22
 */
@Service
public class FloodControlKeyAreaServiceImpl implements IFloodControlKeyAreaService 
{
    @Autowired
    private FloodControlKeyAreaMapper floodControlKeyAreaMapper;

    /**
     * 查询防汛防台重点区域
     * 
     * @param id 防汛防台重点区域主键
     * @return 防汛防台重点区域
     */
    @Override
    public FloodControlKeyArea selectFloodControlKeyAreaById(Long id)
    {
        return floodControlKeyAreaMapper.selectFloodControlKeyAreaById(id);
    }

    /**
     * 查询防汛防台重点区域列表
     * 
     * @param floodControlKeyArea 防汛防台重点区域
     * @return 防汛防台重点区域
     */
    @Override
    public List<FloodControlKeyArea> selectFloodControlKeyAreaList(FloodControlKeyArea floodControlKeyArea)
    {
        return floodControlKeyAreaMapper.selectFloodControlKeyAreaList(floodControlKeyArea);
    }

    /**
     * 新增防汛防台重点区域
     * 
     * @param floodControlKeyArea 防汛防台重点区域
     * @return 结果
     */
    @Override
    public int insertFloodControlKeyArea(FloodControlKeyArea floodControlKeyArea)
    {
        return floodControlKeyAreaMapper.insertFloodControlKeyArea(floodControlKeyArea);
    }

    /**
     * 修改防汛防台重点区域
     * 
     * @param floodControlKeyArea 防汛防台重点区域
     * @return 结果
     */
    @Override
    public int updateFloodControlKeyArea(FloodControlKeyArea floodControlKeyArea)
    {
        return floodControlKeyAreaMapper.updateFloodControlKeyArea(floodControlKeyArea);
    }

    /**
     * 批量删除防汛防台重点区域
     * 
     * @param ids 需要删除的防汛防台重点区域主键
     * @return 结果
     */
    @Override
    public int deleteFloodControlKeyAreaByIds(Long[] ids)
    {
        return floodControlKeyAreaMapper.deleteFloodControlKeyAreaByIds(ids);
    }

    /**
     * 删除防汛防台重点区域信息
     * 
     * @param id 防汛防台重点区域主键
     * @return 结果
     */
    @Override
    public int deleteFloodControlKeyAreaById(Long id)
    {
        return floodControlKeyAreaMapper.deleteFloodControlKeyAreaById(id);
    }
}
