package com.ruoyi.shcy.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.util.Date;

/**
 * 12345热线案件信息DTO
 *
 * @date 2024-01-26
 */
@Data
public class TaskDbcenterRxDTO {

    @Excel(name = "热线12345工单编号")
    private String hotlinesn;

    @Excel(name = "紧急程度", readConverterExp = "0=一般,1=紧急,2=次紧急")
    private String urgentdegree;

    @Excel(name = "任务号")
    private String taskid;

    @Excel(name = "诉求联系人")
    private String reporter;

    @Excel(name = "联系方式")
    private String contactinfo;

    @Excel(name = "发现时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date discovertime;

    @Excel(name = "业务类型")
    private String servicetypename;

    @Excel(name = "发生地址", align = HorizontalAlignment.LEFT)
    private String address;

    @Excel(name = "所属小区")
    private String community;

    @Excel(name = "所属居委会")
    private String residentialarea;

    @Excel(name = "所属网格")
    private String streetarea;

    @Excel(name = "所属物业")
    private String property;

    @Excel(name = "问题描述", align = HorizontalAlignment.LEFT)
    private String description;

    @Excel(name = "三级主责部门", align = HorizontalAlignment.LEFT)
    private String subexecutedeptnameMh;

    @Excel(name = "反馈结论", align = HorizontalAlignment.LEFT)
    private String description12345;

    @Excel(name = "工单来源")
    private String infosourcename;

    @Excel(name = "重点专项")
    private String specialtopic;

    @Excel(name = "测评月份")
    private String evaluationmonth;

    @Excel(name = "满意度")
    private String satisfaction;

    @Excel(name = "是否存电")
    private String powerstorage;

    @Excel(name = "跟踪类型")
    private String trackingtype;

    @Excel(name = "是否重复")
    private String isduplicate;

    @Excel(name = "是否客观件")
    private String isobjective;

    @Excel(name = "是否超期")
    private String overdue;

    @Excel(name = "诉求大类")
    private String parentappealclassification;

    @Excel(name = "诉求小类")
    private String appealclassification;

    @Excel(name = "到期日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date overdueDate;

    @Excel(name = "备注")
    private String remark;
}
