package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthly;

/**
 * 12345热线分析Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
public interface ShcyDbcenterRxMonthlyMapper 
{
    /**
     * 查询12345热线分析
     * 
     * @param id 12345热线分析主键
     * @return 12345热线分析
     */
    public ShcyDbcenterRxMonthly selectShcyDbcenterRxMonthlyById(Long id);

    /**
     * 查询12345热线分析列表
     * 
     * @param shcyDbcenterRxMonthly 12345热线分析
     * @return 12345热线分析集合
     */
    public List<ShcyDbcenterRxMonthly> selectShcyDbcenterRxMonthlyList(ShcyDbcenterRxMonthly shcyDbcenterRxMonthly);

    /**
     * 新增12345热线分析
     * 
     * @param shcyDbcenterRxMonthly 12345热线分析
     * @return 结果
     */
    public int insertShcyDbcenterRxMonthly(ShcyDbcenterRxMonthly shcyDbcenterRxMonthly);

    /**
     * 修改12345热线分析
     * 
     * @param shcyDbcenterRxMonthly 12345热线分析
     * @return 结果
     */
    public int updateShcyDbcenterRxMonthly(ShcyDbcenterRxMonthly shcyDbcenterRxMonthly);

    /**
     * 删除12345热线分析
     * 
     * @param id 12345热线分析主键
     * @return 结果
     */
    public int deleteShcyDbcenterRxMonthlyById(Long id);

    /**
     * 批量删除12345热线分析
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyDbcenterRxMonthlyByIds(Long[] ids);
}
