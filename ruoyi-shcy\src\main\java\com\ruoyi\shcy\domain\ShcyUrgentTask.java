package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 台风登陆前紧急任务对象 shcy_urgent_task
 *
 * <AUTHOR>
 * @date 2023-12-22
 */
public class ShcyUrgentTask extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 事件编号 */
    @Excel(name = "事件编号")
    private String eventNo;

    /** 风险部位 */
    @Excel(name = "风险部位")
    private String alarmLocation;

    /** 脆弱性区域 */
    @Excel(name = "脆弱性区域")
    private String region;

    /** 防汛沙袋到位情况 */
    @Excel(name = "防汛沙袋到位情况")
    private String sandbagPhoto;

    /** 值班人员是否到位 */
    @Excel(name = "值班人员是否到位")
    private String dutyPersonnelPhoto;

    /** 是否存在其他隐患 */
    @Excel(name = "是否存在其他隐患")
    private String otherHazardStatus;

    /** 备注 */
    @Excel(name = "备注")
    private String otherHazard;

    /** 处置截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处置截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date disposalDeadline;

    /** 处置状态 */
    @Excel(name = "处置状态")
    private String circulationState;

    /** 事件完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date caseFinishTime;

    /** 按时完成状态 */
    @Excel(name = "按时完成状态")
    private String dealInTimeState;

    @Excel(name = "处置人")
    private String caseDealBy;

    private List<String> sandbagPhotoUrls;

    private  List<String> dutyPersonnelPhotoUrls;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setEventNo(String eventNo)
    {
        this.eventNo = eventNo;
    }

    public String getEventNo()
    {
        return eventNo;
    }
    public void setAlarmLocation(String alarmLocation)
    {
        this.alarmLocation = alarmLocation;
    }

    public String getAlarmLocation()
    {
        return alarmLocation;
    }
    public void setRegion(String region)
    {
        this.region = region;
    }

    public String getRegion()
    {
        return region;
    }
    public void setSandbagPhoto(String sandbagPhoto)
    {
        this.sandbagPhoto = sandbagPhoto;
    }

    public String getSandbagPhoto()
    {
        return sandbagPhoto;
    }
    public void setDutyPersonnelPhoto(String dutyPersonnelPhoto)
    {
        this.dutyPersonnelPhoto = dutyPersonnelPhoto;
    }

    public String getDutyPersonnelPhoto()
    {
        return dutyPersonnelPhoto;
    }
    public void setOtherHazardStatus(String otherHazardStatus)
    {
        this.otherHazardStatus = otherHazardStatus;
    }

    public String getOtherHazardStatus()
    {
        return otherHazardStatus;
    }
    public void setOtherHazard(String otherHazard)
    {
        this.otherHazard = otherHazard;
    }

    public String getOtherHazard()
    {
        return otherHazard;
    }
    public void setDisposalDeadline(Date disposalDeadline)
    {
        this.disposalDeadline = disposalDeadline;
    }

    public Date getDisposalDeadline()
    {
        return disposalDeadline;
    }
    public void setCirculationState(String circulationState)
    {
        this.circulationState = circulationState;
    }

    public String getCirculationState()
    {
        return circulationState;
    }
    public void setCaseFinishTime(Date caseFinishTime)
    {
        this.caseFinishTime = caseFinishTime;
    }

    public Date getCaseFinishTime()
    {
        return caseFinishTime;
    }
    public void setDealInTimeState(String dealInTimeState)
    {
        this.dealInTimeState = dealInTimeState;
    }

    public String getDealInTimeState()
    {
        return dealInTimeState;
    }

    public  List<String> getSandbagPhotoUrls() {
        return sandbagPhotoUrls;
    }

    public void setSandbagPhotoUrls( List<String> sandbagPhotoUrls) {
        this.sandbagPhotoUrls = sandbagPhotoUrls;
    }

    public  List<String> getDutyPersonnelPhotoUrls() {
        return dutyPersonnelPhotoUrls;
    }

    public void setDutyPersonnelPhotoUrls( List<String> dutyPersonnelPhotoUrls) {
        this.dutyPersonnelPhotoUrls = dutyPersonnelPhotoUrls;
    }

    public String getCaseDealBy() {
        return caseDealBy;
    }

    public void setCaseDealBy(String caseDealBy) {
        this.caseDealBy = caseDealBy;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("eventNo", getEventNo())
            .append("alarmLocation", getAlarmLocation())
            .append("region", getRegion())
            .append("sandbagPhoto", getSandbagPhoto())
            .append("dutyPersonnelPhoto", getDutyPersonnelPhoto())
            .append("otherHazardStatus", getOtherHazardStatus())
            .append("otherHazard", getOtherHazard())
            .append("createTime", getCreateTime())
            .append("disposalDeadline", getDisposalDeadline())
            .append("circulationState", getCirculationState())
            .append("caseFinishTime", getCaseFinishTime())
            .append("dealInTimeState", getDealInTimeState())
            .append("caseDealBy", getCaseDealBy())
            .toString();
    }
}
