package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 娱乐场所对象 shcy_entertainment_places
 *
 * <AUTHOR>
 * @date 2023-01-31
 */
public class EntertainmentPlaces extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 营业执照名称 */
    @Excel(name = "营业执照名称")
    private String licenseName;

    /** 对外经营名称 */
    @Excel(name = "对外经营名称")
    private String operateName;

    /** 法人 */
    @Excel(name = "法人")
    private String legalPerson;

    /** 经营地址 */
    @Excel(name = "经营地址")
    private String operateAddress;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactPhone;


    /** 撒点类型 */
    @Excel(name = "撒点类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setLicenseName(String licenseName)
    {
        this.licenseName = licenseName;
    }

    public String getLicenseName()
    {
        return licenseName;
    }
    public void setOperateName(String operateName)
    {
        this.operateName = operateName;
    }

    public String getOperateName()
    {
        return operateName;
    }
    public void setLegalPerson(String legalPerson)
    {
        this.legalPerson = legalPerson;
    }

    public String getLegalPerson()
    {
        return legalPerson;
    }
    public void setOperateAddress(String operateAddress)
    {
        this.operateAddress = operateAddress;
    }

    public String getOperateAddress()
    {
        return operateAddress;
    }
    public void setContactPhone(String contactPhone)
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone()
    {
        return contactPhone;
    }

    @Override
    public String toString() {
        return "EntertainmentPlaces{" +
                "id=" + id +
                ", licenseName='" + licenseName + '\'' +
                ", operateName='" + operateName + '\'' +
                ", legalPerson='" + legalPerson + '\'' +
                ", operateAddress='" + operateAddress + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                '}';
    }
}
