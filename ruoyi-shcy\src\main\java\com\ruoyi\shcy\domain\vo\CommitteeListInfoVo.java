package com.ruoyi.shcy.domain.vo;

import com.ruoyi.common.core.domain.entity.SysDept;

import java.util.List;

public class CommitteeListInfoVo {

    //有巡查记录的居委id
    private Long deptId;

    private List<SysDept> depts;

    private List<ShopCommitteeCheckStatusVo> checkRecords;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public List<SysDept> getDepts() {
        return depts;
    }

    public void setDepts(List<SysDept> depts) {
        this.depts = depts;
    }

    public List<ShopCommitteeCheckStatusVo> getCheckRecords() {
        return checkRecords;
    }

    public void setCheckRecords(List<ShopCommitteeCheckStatusVo> checkRecords) {
        this.checkRecords = checkRecords;
    }
}
