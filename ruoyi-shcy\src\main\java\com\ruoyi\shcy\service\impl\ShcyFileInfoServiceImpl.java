package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.service.IShcyFileInfoService;

/**
 * 文件信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-05-19
 */
@Service
public class ShcyFileInfoServiceImpl implements IShcyFileInfoService 
{
    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    /**
     * 查询文件信息
     * 
     * @param fileId 文件信息主键
     * @return 文件信息
     */
    @Override
    public ShcyFileInfo selectShcyFileInfoByFileId(Long fileId)
    {
        return shcyFileInfoMapper.selectShcyFileInfoByFileId(fileId);
    }

    /**
     * 查询文件信息列表
     * 
     * @param shcyFileInfo 文件信息
     * @return 文件信息
     */
    @Override
    public List<ShcyFileInfo> selectShcyFileInfoList(ShcyFileInfo shcyFileInfo)
    {
        return shcyFileInfoMapper.selectShcyFileInfoList(shcyFileInfo);
    }

    /**
     * 新增文件信息
     * 
     * @param shcyFileInfo 文件信息
     * @return 结果
     */
    @Override
    public int insertShcyFileInfo(ShcyFileInfo shcyFileInfo)
    {
        return shcyFileInfoMapper.insertShcyFileInfo(shcyFileInfo);
    }

    /**
     * 修改文件信息
     * 
     * @param shcyFileInfo 文件信息
     * @return 结果
     */
    @Override
    public int updateShcyFileInfo(ShcyFileInfo shcyFileInfo)
    {
        return shcyFileInfoMapper.updateShcyFileInfo(shcyFileInfo);
    }

    /**
     * 批量删除文件信息
     * 
     * @param fileIds 需要删除的文件信息主键
     * @return 结果
     */
    @Override
    public int deleteShcyFileInfoByFileIds(Long[] fileIds)
    {
        return shcyFileInfoMapper.deleteShcyFileInfoByFileIds(fileIds);
    }

    /**
     * 删除文件信息信息
     * 
     * @param fileId 文件信息主键
     * @return 结果
     */
    @Override
    public int deleteShcyFileInfoByFileId(Long fileId)
    {
        return shcyFileInfoMapper.deleteShcyFileInfoByFileId(fileId);
    }
}
