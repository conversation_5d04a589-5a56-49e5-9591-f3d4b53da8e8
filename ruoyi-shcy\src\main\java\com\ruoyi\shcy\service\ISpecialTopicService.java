package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.SpecialTopic;

import java.util.List;

/**
 * 重点专项Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
public interface ISpecialTopicService 
{
    /**
     * 查询重点专项
     * 
     * @param id 重点专项主键
     * @return 重点专项
     */
    public SpecialTopic selectSpecialTopicById(Long id);

    /**
     * 查询重点专项列表
     * 
     * @param specialTopic 重点专项
     * @return 重点专项集合
     */
    public List<SpecialTopic> selectSpecialTopicList(SpecialTopic specialTopic);

    /**
     * 新增重点专项
     * 
     * @param specialTopic 重点专项
     * @return 结果
     */
    public int insertSpecialTopic(SpecialTopic specialTopic);

    /**
     * 修改重点专项
     * 
     * @param specialTopic 重点专项
     * @return 结果
     */
    public int updateSpecialTopic(SpecialTopic specialTopic);

    /**
     * 批量删除重点专项
     * 
     * @param ids 需要删除的重点专项主键集合
     * @return 结果
     */
    public int deleteSpecialTopicByIds(Long[] ids);

    /**
     * 删除重点专项信息
     * 
     * @param id 重点专项主键
     * @return 结果
     */
    public int deleteSpecialTopicById(Long id);
}
