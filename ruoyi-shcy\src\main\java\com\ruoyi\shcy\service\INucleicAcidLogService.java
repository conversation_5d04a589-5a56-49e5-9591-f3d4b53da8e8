package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.NucleicAcidLog;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.vo.EmployeesDetailVo;
import com.ruoyi.shcy.domain.vo.ShopDetailVo;

import java.util.List;

/**
 * 员工核酸情况Service接口
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
public interface INucleicAcidLogService
{
    /**
     * 查询员工核酸情况
     *
     * @param id 员工核酸情况主键
     * @return 员工核酸情况
     */
    public NucleicAcidLog selectNucleicAcidLogById(Long id);

    /**
     * 查询员工核酸情况列表
     *
     * @param nucleicAcidLog 员工核酸情况
     * @return 员工核酸情况集合
     */
    public List<NucleicAcidLog> selectNucleicAcidLogList(NucleicAcidLog nucleicAcidLog);

    /**
     * 新增员工核酸情况
     *
     * @param nucleicAcidLog 员工核酸情况
     * @return 结果
     */
    public int insertNucleicAcidLog(NucleicAcidLog nucleicAcidLog);

    /**
     * 修改员工核酸情况
     *
     * @param nucleicAcidLog 员工核酸情况
     * @return 结果
     */
    public int updateNucleicAcidLog(NucleicAcidLog nucleicAcidLog);

    /**
     * 批量删除员工核酸情况
     *
     * @param ids 需要删除的员工核酸情况主键集合
     * @return 结果
     */
    public int deleteNucleicAcidLogByIds(Long[] ids);

    /**
     * 删除员工核酸情况信息
     *
     * @param id 员工核酸情况主键
     * @return 结果
     */
    public int deleteNucleicAcidLogById(Long id);

    NucleicAcidLog selectNucleicAcidLogByEmployeeIdAndCheckDate(Long employeeId, String checkDate);



    //************************************************************日报-从业人员相关数据***************************************
    //************************************************************日报-从业人员相关数据***************************************

    /**
     * 查询出来 所有从业人员
     * */
    List<ShopDetailVo> selectEmployeesAll(Shop shop);

    /**
     * 今日巡查从业人员总数
     * **/
    List<ShopDetailVo>  selectCheckEmployeesTotal(NucleicAcidLog nucleicAcidLog);
    /**
     * 今日核酸正常人员总数
     * **/
    List<ShopDetailVo>  selectCheckNormalEmployeesNum(NucleicAcidLog nucleicAcidLog);
    /**
     *  今日核酸异常人员总数
     *  **/
    List<ShopDetailVo>  selectCheckAbnormalEmployeesNum(NucleicAcidLog nucleicAcidLog);
    /**
     * 今日离岗人员总数
     * **/
    List<ShopDetailVo>  selectCheckRetiredEmployeesNum(NucleicAcidLog nucleicAcidLog);
}
