package com.ruoyi.shcy.domain;

import java.util.Date;

public class WaterLevelRealtime {

    private static final long serialVersionUID = 1L;

    /**
     * 水位ID
     */
    private Integer waterLevelId;

    /**
     * 水位
     */
    private Double waterLevel;

    /**
     * 创建时间
     */
    private Date cjsj;

    /**
     * 位置
     */
    private String location;

    /**
     * 警戒线
     */
    private Double warn;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 下立交ID
     */
    private String xljId;

    /**
     * 下立交名称
     */
    private String xljName;

    public Integer getWaterLevelId() {
        return waterLevelId;
    }

    public void setWaterLevelId(Integer waterLevelId) {
        this.waterLevelId = waterLevelId;
    }

    public Double getWaterLevel() {
        return waterLevel;
    }

    public void setWaterLevel(Double waterLevel) {
        this.waterLevel = waterLevel;
    }

    public Date getCjsj() {
        return cjsj;
    }

    public void setCjsj(Date cjsj) {
        this.cjsj = cjsj;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Double getWarn() {
        return warn;
    }

    public void setWarn(Double warn) {
        this.warn = warn;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getXljId() {
        return xljId;
    }

    public void setXljId(String xljId) {
        this.xljId = xljId;
    }

    public String getXljName() {
        return xljName;
    }

    public void setXljName(String xljName) {
        this.xljName = xljName;
    }

    @Override
    public String toString() {
        return "WaterLevelRealtime{" +
                "waterLevelId=" + waterLevelId +
                ", waterLevel=" + waterLevel +
                ", cjsj=" + cjsj +
                ", location='" + location + '\'' +
                ", warn=" + warn +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", xljId='" + xljId + '\'' +
                ", xljName='" + xljName + '\'' +
                '}';
    }
}
