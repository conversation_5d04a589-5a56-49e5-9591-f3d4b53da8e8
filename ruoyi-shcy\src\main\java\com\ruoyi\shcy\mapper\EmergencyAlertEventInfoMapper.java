package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.EmergencyAlertEventInfo;

import java.util.List;

/**
 * 突发告警事件派遣信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface EmergencyAlertEventInfoMapper 
{
    /**
     * 查询突发告警事件派遣信息
     * 
     * @param id 突发告警事件派遣信息主键
     * @return 突发告警事件派遣信息
     */
    public EmergencyAlertEventInfo selectEmergencyAlertEventInfoById(Long id);

    /**
     * 查询突发告警事件派遣信息列表
     * 
     * @param emergencyAlertEventInfo 突发告警事件派遣信息
     * @return 突发告警事件派遣信息集合
     */
    public List<EmergencyAlertEventInfo> selectEmergencyAlertEventInfoList(EmergencyAlertEventInfo emergencyAlertEventInfo);

    /**
     * 新增突发告警事件派遣信息
     * 
     * @param emergencyAlertEventInfo 突发告警事件派遣信息
     * @return 结果
     */
    public int insertEmergencyAlertEventInfo(EmergencyAlertEventInfo emergencyAlertEventInfo);

    /**
     * 修改突发告警事件派遣信息
     * 
     * @param emergencyAlertEventInfo 突发告警事件派遣信息
     * @return 结果
     */
    public int updateEmergencyAlertEventInfo(EmergencyAlertEventInfo emergencyAlertEventInfo);

    /**
     * 删除突发告警事件派遣信息
     * 
     * @param id 突发告警事件派遣信息主键
     * @return 结果
     */
    public int deleteEmergencyAlertEventInfoById(Long id);

    /**
     * 批量删除突发告警事件派遣信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmergencyAlertEventInfoByIds(Long[] ids);

    public long getCaseCount(EmergencyAlertEventInfo emergencyAlertEventInfo);
}
