package com.ruoyi.shcy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.domain.ShopCheckLog;
import com.ruoyi.shcy.domain.vo.ShopCommitteeCheckStatusVo;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.mapper.ShopCheckLogMapper;
import com.ruoyi.shcy.service.IShopCheckLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 检查日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@Service
public class ShopCheckLogServiceImpl implements IShopCheckLogService


{
    @Autowired
    private ShopCheckLogMapper shopCheckLogMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    /**
     * 查询检查日志
     *
     * @param id 检查日志主键
     * @return 检查日志
     */
    @Override
    public ShopCheckLog selectShopCheckLogById(Long id)
    {
        return shopCheckLogMapper.selectShopCheckLogById(id);
    }


    /**
     * 查询最近三条检查日志
     *
     * @param id 检查日志主键
     * @return 检查日志
     */
    @Override
    public List<ShopCheckLog> selectShopCheckLogByIdThree(Long id) {
        return  shopCheckLogMapper.selectShopCheckLogByIdThree(id);
    }

    /**
     * 查询检查日志列表
     *
     * @param shopCheckLog 检查日志
     * @return 检查日志
     */
    @Override
    public List<ShopCheckLog> selectShopCheckLogList(ShopCheckLog shopCheckLog)
    {
        return shopCheckLogMapper.selectShopCheckLogList(shopCheckLog);
    }

    /**
     * 查询检查日志列表（给大屏使用）
     *
     * @param shopCheckLog
     **/
    @Override
    public List<ShopCheckLog> selectShopCheckLogListScreen(ShopCheckLog shopCheckLog) {
        return  shopCheckLogMapper.selectShopCheckLogListScreen(shopCheckLog);
    }

    /**
     * 查询大屏检查日志列表
     *
     * @param shopCheckLog 检查日志
     * @return 检查日志集合
     */
    @Override
    public List<ShopCheckLog> selectShopCheckLogScreenList(ShopCheckLog shopCheckLog) {
        return shopCheckLogMapper.selectShopCheckLogScreenList(shopCheckLog);
    }

    /**
     * 新增检查日志
     *
     * @param shopCheckLog 检查日志
     * @return 结果
     */
    @Override
    public int insertShopCheckLog(ShopCheckLog shopCheckLog)
    {
        shopCheckLog.setCreateTime(DateUtils.getNowDate());
        return shopCheckLogMapper.insertShopCheckLog(shopCheckLog);
    }

    /**
     * 修改检查日志
     *
     * @param shopCheckLog 检查日志
     * @return 结果
     */
    @Override
    public int updateShopCheckLog(ShopCheckLog shopCheckLog)
    {
        shopCheckLog.setUpdateTime(DateUtils.getNowDate());
        return shopCheckLogMapper.updateShopCheckLog(shopCheckLog);
    }

    /**
     * 批量删除检查日志
     *
     * @param ids 需要删除的检查日志主键
     * @return 结果
     */
    @Override
    public int deleteShopCheckLogByIds(Long[] ids)
    {
        return shopCheckLogMapper.deleteShopCheckLogByIds(ids);
    }

    /**
     * 删除检查日志信息
     *
     * @param id 检查日志主键
     * @return 结果
     */
    @Override
    public int deleteShopCheckLogById(Long id)
    {
        return shopCheckLogMapper.deleteShopCheckLogById(id);
    }

    @Override
    public ShopCheckLog selectShopCheckLogByShopIdAndCheckDate(Long shopId, String checkDate) {
        return shopCheckLogMapper.selectShopCheckLogByShopIdAndCheckDate(shopId, checkDate);
    }

    /**
     * 根据居委会id查询巡检状态，巡检时间、巡检距离状态等信息（当前月份的数据）
     *
     * @param deptId
     **/
    @Override
    public List<ShopCommitteeCheckStatusVo> selectShopCheckStatusByDeptId(Long deptId) {
        return  shopCheckLogMapper.selectShopCheckStatusByDeptId(deptId);
    }

    /**
     * 查询居委会巡查最新记录的所属居委会id
     */
    @Override
    public Long getDeptIdOfNewthCheckRecord() {
        return shopCheckLogMapper.getDeptIdOfNewthCheckRecord();
    }


    @Override
    public int updateShopCheckLogByCg(ShopCheckLog shopCheckLog) {
        shopCheckLog.setUpdateTimeCg(DateUtils.getNowDate());
        return shopCheckLogMapper.updateShopCheckLog(shopCheckLog);
    }

    @Override
    public ShopCheckLog getShopCheckLog(Long id) {
        return handlePhoto(shopCheckLogMapper.selectShopCheckLogById(id));
    }

    @Override
    public List<ShopCheckLog> selectShopCheckLogList1(ShopCheckLog shopCheckLog) {
        return shopCheckLogMapper.selectShopCheckLogList1(shopCheckLog);
    }

    public ShopCheckLog handlePhoto(ShopCheckLog shopCheckLog) {
        if (StrUtil.isNotEmpty(shopCheckLog.getJzCheckPhoto())) {
            List<String> photoUrls = new ArrayList<>();
            String[] fileIds = shopCheckLog.getJzCheckPhoto().split(",");
            for (String fileId : fileIds) {
                ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
                // 判断shcyFileInfo是否为空
                if (shcyFileInfo != null) {
                    photoUrls.add(shcyFileInfo.getFilePath());
                }
            }
            shopCheckLog.setPhotoUrls(photoUrls);
        }
        return shopCheckLog;
    }

}
