package com.ruoyi.dahuatech;

import com.dahuatech.hutool.http.Method;
import com.dahuatech.hutool.json.JSONUtil;
import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.http.DefaultClient;
import com.dahuatech.icc.oauth.http.IClient;
import com.dahuatech.icc.oauth.model.v202010.GeneralRequest;
import com.dahuatech.icc.oauth.model.v202010.GeneralResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest
public class YanTest {

    /**
     * 使用GeneralRequest 调用示例
     *
     * @throws ClientException
     */
    @Test
    public void evo_brm_dept_page_general() throws ClientException {
        log.info("----开始执行----{}------", "用户部门-分页查询");
        IClient iClient = new DefaultClient();
        GeneralRequest generalRequest =
                new GeneralRequest("/evo-apigw/evo-brm/1.0.0/department/page", Method.POST);
        // 设置参数
        Map<String, Object> param = new HashMap<>();
        param.put("pageSize", 20);
        generalRequest.body(JSONUtil.toJsonStr(param));
        generalRequest.header("userId","1");
        GeneralResponse response = iClient.doAction(generalRequest, generalRequest.getResponseClass());
        System.out.println(response);
        log.info("----结束执行----{}------", "用户部门-分页查询");
    }

    /**
     * 事件查询
     *
     * @throws ClientException 客户端异常
     */
    @Test
    public void eventQuery() throws ClientException {
        IClient iClient = new DefaultClient();
        GeneralRequest generalRequest =
                new GeneralRequest("/evo-apigw/evo-event/1.2.0/alarm-record/page", Method.POST);
        Map<String,String> map = new HashMap<>();
        map.put("pageNum","1");
        map.put("pageSize","10");
        // 设置参数
        generalRequest.body(JSONUtil.toJsonStr(map));
        GeneralResponse response = iClient.doAction(generalRequest, generalRequest.getResponseClass());
        System.out.println(JSONUtil.toJsonStr(response));
    }



}
