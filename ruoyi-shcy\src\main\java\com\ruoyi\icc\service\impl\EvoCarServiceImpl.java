package com.ruoyi.icc.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.icc.dto.CarRecordDTO;
import com.ruoyi.icc.service.EvoCarService;
import com.ruoyi.icc.vo.CarPageVO;
import com.ruoyi.shcy.constant.HjzzConstants;
import com.ruoyi.shcy.constant.SmsConstants;
import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import com.ruoyi.shcy.mapper.HjzzCaseMapper;
import com.ruoyi.shcy.mapper.IccAlarmRecordMapper;
import com.ruoyi.system.service.ISysConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 车辆卡口接口实现
 *
 * <AUTHOR>
 * @date 2024/01/30
 */
@Service
@AllArgsConstructor
@Slf4j
public class EvoCarServiceImpl implements EvoCarService {

    public static final String EVO_CAR_URL = "https://172.16.33.100/evo-apigw/evo-car/1.0.0/carCommonQuery/getPicRecords";

    public static final String EVO_CAR_TOTAL_URL = "https://172.16.33.100/evo-apigw/evo-car/1.0.0/carCommonQuery/getPicRecordTotal";

    private final IccAlarmRecordMapper iccAlarmRecordMapper;

    private final HjzzCaseMapper hjzzCaseMapper;

    private final ISysConfigService configService;

    /**
     * 获取过车记录
     *
     * @param pageVO 分页参数
     * @param token 授权令牌
     * @return {@link List}<{@link CarRecordDTO}>
     */
    @Override
    public List<CarRecordDTO> getPicRecords(CarPageVO pageVO, String token) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("data", pageVO);
        String jsonStr = JSON.toJSONString(map);
        String responseStr = HttpRequest.post(EVO_CAR_URL)
                .header("Authorization", "bearer " + token)
                .body(jsonStr)
                .execute().body();
        JSONObject jsonObject = JSON.parseObject(responseStr);
        JSONArray dataArray = jsonObject.getJSONObject("data").getJSONArray("list");
        List<CarRecordDTO> records = dataArray.toJavaList(CarRecordDTO.class);
        return records;
    }

    /**
     * 获取过车记录总数
     *
     * @param pageVO 分页参数
     * @param token  授权令牌
     * @return int 总数
     */
    @Override
    public int getPicRecordTotal(CarPageVO pageVO, String token) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("data", pageVO);
        String jsonStr = JSON.toJSONString(map);
        String responseStr = HttpRequest.post(EVO_CAR_TOTAL_URL)
                .header("Authorization", "bearer " + token)
                .body(jsonStr)
                .execute().body();
        JSONObject jsonObject = JSON.parseObject(responseStr);
        // 获取jsonObject中的data属性值，类型为int
        int total = jsonObject.getIntValue("data");
        return total;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncCarRecord(CarRecordDTO carRecordDTO) {
        // 只同步车型为渣土车或者混凝土搅拌车的记录
        if (!"渣土车".equals(carRecordDTO.getCarTypeStr()) && !"混凝土搅拌车".equals(carRecordDTO.getCarTypeStr())) {
            return;
        }
        if ("未识别".equals(carRecordDTO.getCarNum())) {
            return;
        }
        IccAlarmRecord alarmRecord = iccAlarmRecordMapper.selectIccAlarmRecordByCarRecordId(carRecordDTO.getId());
        boolean exist = alarmRecord != null;
        if (exist) {
        } else {
            // 1.carRecordDTO转换为IccAlarmRecord
            IccAlarmRecord iccAlarmRecord = new IccAlarmRecord();
            iccAlarmRecord.setCarRecordId(carRecordDTO.getId());
            iccAlarmRecord.setAlarmDate(carRecordDTO.getCapDateStr());
            iccAlarmRecord.setAlarmPosition(carRecordDTO.getDevChnname());
            if (iccAlarmRecord.getAlarmPosition().contains("卫九路车辆抓拍前端")) {
                iccAlarmRecord.setAlarmPosition("卫九路11号");
            }
            iccAlarmRecord.setAlarmType(2L);
            iccAlarmRecord.setAlarmTypeName("违规生产入侵");
            iccAlarmRecord.setNodeCode(carRecordDTO.getDevChnid());
            iccAlarmRecord.setLicensePlate(carRecordDTO.getCarNum());
            iccAlarmRecord.setAlarmPicture(carRecordDTO.getCarImgUrl());
            iccAlarmRecord.setVehicleType(carRecordDTO.getCarTypeStr());
            iccAlarmRecord.setStatus(HjzzConstants.PROCESSED);
            iccAlarmRecordMapper.insertIccAlarmRecord(iccAlarmRecord);

            // 2.自动流转到HjzzCase
            HjzzCase hjzzCase = new HjzzCase();
            hjzzCase.setAlarmDate(iccAlarmRecord.getAlarmDate());
            hjzzCase.setCaseName(iccAlarmRecord.getAlarmTypeName());
            hjzzCase.setCaseType(HjzzConstants.CASE_TYPE_WGSC);
            hjzzCase.setCaseDescription("通过布控摄像头抓拍违规生产入侵行为");
            // 临时状态：已完成
            hjzzCase.setCirculationState(HjzzConstants.CIRCULATION_STATE_FINISHED);
            hjzzCase.setDealInTimeState("0");
            hjzzCase.setAddress(iccAlarmRecord.getAlarmPosition());
            hjzzCase.setAlarmRecordId(iccAlarmRecord.getId());
            // 截至日期 =》 当前时间 + 1小时
            String timeLimit = configService.selectConfigByKey("shcy.hjzz.timeLimit");
            hjzzCase.setCaseEndTime(DateUtils.addHours(new Date(), Integer.parseInt(timeLimit)));
            String investigationTImeLimit = configService.selectConfigByKey("shcy.hjzz.investigationTImeLimit");
            hjzzCase.setInvestigationDeadline(DateUtils.addHours(new Date(), Integer.parseInt(investigationTImeLimit)));
            // 事件编号 =》 日期 + 10位随机数
            hjzzCase.setCaseNumber(DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(10));
            hjzzCase.setLicensePlate(iccAlarmRecord.getLicensePlate());
            hjzzCase.setVehicleType(iccAlarmRecord.getVehicleType());
            hjzzCase.setCreateTime(new Date());
            hjzzCaseMapper.insertHjzzCase(hjzzCase);

            // 是否发送短信
            String sendSms = configService.selectConfigByKey("shcy.wgscrq.sendSms");
            boolean isSendSms = Convert.toBool(sendSms);
            // 发送短信
            if (isSendSms) {
                LinkedHashMap<String, String> map = new LinkedHashMap<>(3);
                map.put("time", hjzzCase.getAlarmDate());
                map.put("type", hjzzCase.getVehicleType());
                map.put("code", hjzzCase.getLicensePlate());
                String phones = configService.selectConfigByKey("shcy.wgscrq.phone");
                if (StrUtil.isNotEmpty(phones)) {
                    String[] split = phones.split(",");
                    // split转list
                    List<String> phoneList = Arrays.asList(split);
                    SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
                    phoneList.forEach(phone1 -> {
                        smsBlend.sendMessageAsync(phone1, SmsConstants.TEMPLATE_CODE_HJZZ, map);
                    });
                }
            }

        }
    }
}
