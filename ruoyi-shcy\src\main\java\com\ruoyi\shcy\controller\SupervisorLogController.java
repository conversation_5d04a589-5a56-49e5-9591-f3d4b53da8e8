package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.SupervisorLog;
import com.ruoyi.shcy.service.ISupervisorLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 重点监管列Controller
 *
 * <AUTHOR>
 * @date 2022-11-17
 */
@RestController
@RequestMapping("/shcy/supervisorLog")
public class SupervisorLogController extends BaseController
{
    @Autowired
    private ISupervisorLogService supervisorLogService;

    /**
     * 查询重点监管列列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:supervisorLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(SupervisorLog supervisorLog)
    {
        startPage();
        List<SupervisorLog> list = supervisorLogService.selectSupervisorLogList(supervisorLog);
        return getDataTable(list);
    }

    /**
     * 导出重点监管列列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:supervisorLog:export')")
    @Log(title = "重点监管列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SupervisorLog supervisorLog)
    {
        List<SupervisorLog> list = supervisorLogService.selectSupervisorLogList(supervisorLog);
        ExcelUtil<SupervisorLog> util = new ExcelUtil<SupervisorLog>(SupervisorLog.class);
        util.exportExcel(response, list, "重点监管列数据");
    }

    /**
     * 获取重点监管列详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:supervisorLog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(supervisorLogService.selectSupervisorLogById(id));
    }


    /** 根据id获取对应商铺最近三次重点监管记录**/
    @GetMapping(value = "/three/{id}")
    public AjaxResult getSupervisorThree(@PathVariable("id") Long id)
    {

        return AjaxResult.success(supervisorLogService.selectSupervisorLogByIdThree(id));
    }

    /**
     * 新增重点监管列
     */
    @PreAuthorize("@ss.hasPermi('shcy:supervisorLog:add')")
    @Log(title = "重点监管列", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SupervisorLog supervisorLog)
    {
        return toAjax(supervisorLogService.insertSupervisorLog(supervisorLog));
    }

    /**
     * 修改重点监管列
     */
    @PreAuthorize("@ss.hasPermi('shcy:supervisorLog:edit')")
    @Log(title = "重点监管列", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SupervisorLog supervisorLog)
    {
        return toAjax(supervisorLogService.updateSupervisorLog(supervisorLog));
    }

    /**
     * 删除重点监管列
     */
    @PreAuthorize("@ss.hasPermi('shcy:supervisorLog:remove')")
    @Log(title = "重点监管列", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(supervisorLogService.deleteSupervisorLogByIds(ids));
    }
}
