package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.FloodHazardCheckList;

import java.util.List;

/**
 * 汛期隐患排查Service接口
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface IFloodHazardCheckListService
{
    /**
     * 查询汛期隐患排查
     *
     * @param id 汛期隐患排查主键
     * @return 汛期隐患排查
     */
    public FloodHazardCheckList selectFloodHazardCheckListById(Long id);

    /**
     * 查询汛期隐患排查列表
     *
     * @param floodHazardCheckList 汛期隐患排查
     * @return 汛期隐患排查集合
     */
    public List<FloodHazardCheckList> selectFloodHazardCheckListList(FloodHazardCheckList floodHazardCheckList);

    /**
     * 新增汛期隐患排查
     *
     * @param floodHazardCheckList 汛期隐患排查
     * @return 结果
     */
    public int insertFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList);

    /**
     * 修改汛期隐患排查
     *
     * @param floodHazardCheckList 汛期隐患排查
     * @return 结果
     */
    public int updateFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList);

    /**
     * 批量删除汛期隐患排查
     *
     * @param ids 需要删除的汛期隐患排查主键集合
     * @return 结果
     */
    public int deleteFloodHazardCheckListByIds(Long[] ids);

    /**
     * 删除汛期隐患排查信息
     *
     * @param id 汛期隐患排查主键
     * @return 结果
     */
    public int deleteFloodHazardCheckListById(Long id);

    public int handleFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList);

    public int publishFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList);

    public long getCaseCount(FloodHazardCheckList floodHazardCheckList);
}
