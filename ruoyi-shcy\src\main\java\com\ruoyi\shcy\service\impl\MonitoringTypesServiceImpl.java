package com.ruoyi.shcy.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.MonitoringTypes;
import com.ruoyi.shcy.mapper.MonitoringTypesMapper;
import com.ruoyi.shcy.service.IMonitoringTypesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 监控类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-10-11
 */
@Service
public class MonitoringTypesServiceImpl implements IMonitoringTypesService 
{
    @Autowired
    private MonitoringTypesMapper monitoringTypesMapper;

    /**
     * 查询监控类型
     * 
     * @param monitoringTypeId 监控类型主键
     * @return 监控类型
     */
    @Override
    public MonitoringTypes selectMonitoringTypesByMonitoringTypeId(Long monitoringTypeId)
    {
        return monitoringTypesMapper.selectMonitoringTypesByMonitoringTypeId(monitoringTypeId);
    }

    /**
     * 查询监控类型列表
     * 
     * @param monitoringTypes 监控类型
     * @return 监控类型
     */
    @Override
    public List<MonitoringTypes> selectMonitoringTypesList(MonitoringTypes monitoringTypes)
    {
        return monitoringTypesMapper.selectMonitoringTypesList(monitoringTypes);
    }

    /**
     * 新增监控类型
     * 
     * @param monitoringTypes 监控类型
     * @return 结果
     */
    @Override
    public int insertMonitoringTypes(MonitoringTypes monitoringTypes)
    {
        monitoringTypes.setCreateTime(DateUtils.getNowDate());
        return monitoringTypesMapper.insertMonitoringTypes(monitoringTypes);
    }

    /**
     * 修改监控类型
     * 
     * @param monitoringTypes 监控类型
     * @return 结果
     */
    @Override
    public int updateMonitoringTypes(MonitoringTypes monitoringTypes)
    {
        monitoringTypes.setUpdateTime(DateUtils.getNowDate());
        return monitoringTypesMapper.updateMonitoringTypes(monitoringTypes);
    }

    /**
     * 批量删除监控类型
     * 
     * @param monitoringTypeIds 需要删除的监控类型主键
     * @return 结果
     */
    @Override
    public int deleteMonitoringTypesByMonitoringTypeIds(Long[] monitoringTypeIds)
    {
        return monitoringTypesMapper.deleteMonitoringTypesByMonitoringTypeIds(monitoringTypeIds);
    }

    /**
     * 删除监控类型信息
     * 
     * @param monitoringTypeId 监控类型主键
     * @return 结果
     */
    @Override
    public int deleteMonitoringTypesByMonitoringTypeId(Long monitoringTypeId)
    {
        return monitoringTypesMapper.deleteMonitoringTypesByMonitoringTypeId(monitoringTypeId);
    }

    @Override
    public List<MonitoringTypes> selectAllMonitoringTypes() {
        return monitoringTypesMapper.selectAllMonitoringTypes();
    }

    @Override
    public List<String> getAllMonitoringTypeName() {
        return monitoringTypesMapper.getAllMonitoringTypeName();
    }

}
