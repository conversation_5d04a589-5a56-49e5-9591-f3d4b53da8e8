package com.ruoyi.shcy.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.constant.SmsConstants;
import com.ruoyi.shcy.domain.EmergencyAlertEventInfo;
import com.ruoyi.shcy.domain.FxftCase;
import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.domain.ShcyUrgentTask;
import com.ruoyi.shcy.service.IEmergencyAlertEventInfoService;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 突发告警事件派遣信息Controller
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
@RestController
@RequestMapping("/shcy/emergencyAlertEventInfo")
public class EmergencyAlertEventInfoController extends BaseController
{
    @Autowired
    private IEmergencyAlertEventInfoService emergencyAlertEventInfoService;

    /**
     * 查询突发告警事件派遣信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:emergencyAlertEventInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        startPage();
        List<EmergencyAlertEventInfo> list = emergencyAlertEventInfoService.selectEmergencyAlertEventInfoList(emergencyAlertEventInfo);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:emergencyAlertEventInfo:list')")
    @GetMapping("/emergency/list")
    public TableDataInfo emergencyList(EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        startPage();
        // emergencyAlertEventInfo.setDisposalPerson(getUsername());
        List<EmergencyAlertEventInfo> list = emergencyAlertEventInfoService.selectEmergencyAlertEventInfoList(emergencyAlertEventInfo);
        return getDataTable(list);
    }

    /**
     * 导出突发告警事件派遣信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:emergencyAlertEventInfo:export')")
    @Log(title = "突发告警事件派遣信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        List<EmergencyAlertEventInfo> list = emergencyAlertEventInfoService.selectEmergencyAlertEventInfoList(emergencyAlertEventInfo);
        ExcelUtil<EmergencyAlertEventInfo> util = new ExcelUtil<EmergencyAlertEventInfo>(EmergencyAlertEventInfo.class);
        util.exportExcel(response, list, "突发告警事件派遣信息数据");
    }

    /**
     * 获取突发告警事件派遣信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:emergencyAlertEventInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(emergencyAlertEventInfoService.selectEmergencyAlertEventInfoById(id));
    }

    /**
     * 新增突发告警事件派遣信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:emergencyAlertEventInfo:add')")
    @Log(title = "突发告警事件派遣信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        return toAjax(emergencyAlertEventInfoService.insertEmergencyAlertEventInfo(emergencyAlertEventInfo));
    }

    /**
     * 修改突发告警事件派遣信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:emergencyAlertEventInfo:edit')")
    @Log(title = "突发告警事件派遣信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        return toAjax(emergencyAlertEventInfoService.updateEmergencyAlertEventInfo(emergencyAlertEventInfo));
    }

    /**
     * 删除突发告警事件派遣信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:emergencyAlertEventInfo:remove')")
    @Log(title = "突发告警事件派遣信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(emergencyAlertEventInfoService.deleteEmergencyAlertEventInfoByIds(ids));
    }

    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        return toAjax(emergencyAlertEventInfoService.handleEmergencyAlertEventInfo(emergencyAlertEventInfo));
    }

    @PostMapping("/handleEmergency")
    public AjaxResult handleEmergency(@RequestBody EmergencyAlertEventInfo emergencyAlertEventInfo)
    {
        int result = emergencyAlertEventInfoService.handleEmergency(emergencyAlertEventInfo);
        sendEmergencyNotifications();
        return toAjax(result);
    }

    /**
     * 发送突发事件通知
     */
    private void sendEmergencyNotifications() {
        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
        smsBlend.sendMessage(SmsConstants.PHONE_NUMBER_NIJUN, SmsConstants.TEMPLATE_CODE_TFSJ);

        List<String> phoneNumbers = Arrays.asList(
                SmsConstants.PHONE_NUMBER_ZHOUBING,
                SmsConstants.PHONE_NUMBER_YANGYUJI
        );
        phoneNumbers.forEach(phone1 -> {
            smsBlend.sendMessageAsync(phone1, SmsConstants.TEMPLATE_CODE_TFSJ_FXB, new LinkedHashMap<>());
        });
    }

    @PreAuthorize("@ss.hasPermi('shcy:emergencyAlertEventInfo:list')")
    @GetMapping("/getCaseCount")
    public AjaxResult getCaseCount(EmergencyAlertEventInfo emergencyAlertEventInfo, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            emergencyAlertEventInfo.setParams(params);
        }
        return AjaxResult.success("操作成功", emergencyAlertEventInfoService.getCaseCount(emergencyAlertEventInfo));
    }
}
