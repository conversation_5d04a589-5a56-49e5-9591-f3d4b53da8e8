package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.SensorDevice;
import com.ruoyi.shcy.service.ISensorDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 传感器设备Controller
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
@RestController
@RequestMapping("/shcy/sensorDevice")
public class SensorDeviceController extends BaseController
{
    @Autowired
    private ISensorDeviceService sensorDeviceService;

    /**
     * 查询传感器设备列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:sensorDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(SensorDevice sensorDevice)
    {
        startPage();
        List<SensorDevice> list = sensorDeviceService.selectSensorDeviceList(sensorDevice);
        return getDataTable(list);
    }

    /**
     * 导出传感器设备列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:sensorDevice:export')")
    @Log(title = "传感器设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SensorDevice sensorDevice)
    {
        List<SensorDevice> list = sensorDeviceService.selectSensorDeviceList(sensorDevice);
        ExcelUtil<SensorDevice> util = new ExcelUtil<SensorDevice>(SensorDevice.class);
        util.exportExcel(response, list, "传感器设备数据");
    }

    /**
     * 获取传感器设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:sensorDevice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(sensorDeviceService.selectSensorDeviceById(id));
    }

    /**
     * 新增传感器设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:sensorDevice:add')")
    @Log(title = "传感器设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SensorDevice sensorDevice)
    {
        return toAjax(sensorDeviceService.insertSensorDevice(sensorDevice));
    }

    /**
     * 修改传感器设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:sensorDevice:edit')")
    @Log(title = "传感器设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SensorDevice sensorDevice)
    {
        return toAjax(sensorDeviceService.updateSensorDevice(sensorDevice));
    }

    /**
     * 删除传感器设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:sensorDevice:remove')")
    @Log(title = "传感器设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sensorDeviceService.deleteSensorDeviceByIds(ids));
    }
}
