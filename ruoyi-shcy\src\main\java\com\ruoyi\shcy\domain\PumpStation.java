package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 泵站对象 shcy_pump_station
 * 
 * <AUTHOR>
 * @date 2023-03-28
 */
public class PumpStation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 泵站名称 */
    @Excel(name = "泵站名称")
    private String pumpStationName;

    /** 泵站类型 */
    @Excel(name = "泵站类型")
    private String pumpStationType;

    /** 宗地编号 */
    private String lotNumber;

    /** 父宗地编号 */
    private String parentLotNumber;

    /** 图形类型 */
    private String type;

    /** 坐标 */
    private String coordinate;

    /** 填充颜色 */
    private String fillColor;

    /** 边框色 */
    private String outlineColor;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPumpStationName(String pumpStationName) 
    {
        this.pumpStationName = pumpStationName;
    }

    public String getPumpStationName() 
    {
        return pumpStationName;
    }
    public void setPumpStationType(String pumpStationType) 
    {
        this.pumpStationType = pumpStationType;
    }

    public String getPumpStationType() 
    {
        return pumpStationType;
    }
    public void setLotNumber(String lotNumber) 
    {
        this.lotNumber = lotNumber;
    }

    public String getLotNumber() 
    {
        return lotNumber;
    }
    public void setParentLotNumber(String parentLotNumber) 
    {
        this.parentLotNumber = parentLotNumber;
    }

    public String getParentLotNumber() 
    {
        return parentLotNumber;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setCoordinate(String coordinate) 
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate() 
    {
        return coordinate;
    }
    public void setFillColor(String fillColor) 
    {
        this.fillColor = fillColor;
    }

    public String getFillColor() 
    {
        return fillColor;
    }
    public void setOutlineColor(String outlineColor) 
    {
        this.outlineColor = outlineColor;
    }

    public String getOutlineColor() 
    {
        return outlineColor;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pumpStationName", getPumpStationName())
            .append("pumpStationType", getPumpStationType())
            .append("lotNumber", getLotNumber())
            .append("parentLotNumber", getParentLotNumber())
            .append("type", getType())
            .append("coordinate", getCoordinate())
            .append("fillColor", getFillColor())
            .append("outlineColor", getOutlineColor())
            .toString();
    }
}
