package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyRoadMapper;
import com.ruoyi.shcy.domain.ShcyRoad;
import com.ruoyi.shcy.service.IShcyRoadService;

/**
 * 路段名Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-02-09
 */
@Service
public class ShcyRoadServiceImpl implements IShcyRoadService 
{
    @Autowired
    private ShcyRoadMapper shcyRoadMapper;

    /**
     * 查询路段名
     * 
     * @param id 路段名主键
     * @return 路段名
     */
    @Override
    public ShcyRoad selectShcyRoadById(Long id)
    {
        return shcyRoadMapper.selectShcyRoadById(id);
    }

    /**
     * 查询路段名列表
     * 
     * @param shcyRoad 路段名
     * @return 路段名
     */
    @Override
    public List<ShcyRoad> selectShcyRoadList(ShcyRoad shcyRoad)
    {
        return shcyRoadMapper.selectShcyRoadList(shcyRoad);
    }

    /**
     * 新增路段名
     * 
     * @param shcyRoad 路段名
     * @return 结果
     */
    @Override
    public int insertShcyRoad(ShcyRoad shcyRoad)
    {
        shcyRoad.setCreateTime(DateUtils.getNowDate());
        return shcyRoadMapper.insertShcyRoad(shcyRoad);
    }

    /**
     * 修改路段名
     * 
     * @param shcyRoad 路段名
     * @return 结果
     */
    @Override
    public int updateShcyRoad(ShcyRoad shcyRoad)
    {
        shcyRoad.setUpdateTime(DateUtils.getNowDate());
        return shcyRoadMapper.updateShcyRoad(shcyRoad);
    }

    /**
     * 批量删除路段名
     * 
     * @param ids 需要删除的路段名主键
     * @return 结果
     */
    @Override
    public int deleteShcyRoadByIds(Long[] ids)
    {
        return shcyRoadMapper.deleteShcyRoadByIds(ids);
    }

    /**
     * 删除路段名信息
     * 
     * @param id 路段名主键
     * @return 结果
     */
    @Override
    public int deleteShcyRoadById(Long id)
    {
        return shcyRoadMapper.deleteShcyRoadById(id);
    }
}
