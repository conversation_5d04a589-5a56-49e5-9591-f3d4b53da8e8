package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.FloodShelterInfo;

import java.util.List;

/**
 * 防汛安置点信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface IFloodShelterInfoService
{
    /**
     * 查询防汛安置点信息
     * 
     * @param id 防汛安置点信息主键
     * @return 防汛安置点信息
     */
    public FloodShelterInfo selectFloodShelterInfoById(Long id);

    /**
     * 查询防汛安置点信息列表
     * 
     * @param floodShelterInfo 防汛安置点信息
     * @return 防汛安置点信息集合
     */
    public List<FloodShelterInfo> selectFloodShelterInfoList(FloodShelterInfo floodShelterInfo);

    /**
     * 新增防汛安置点信息
     * 
     * @param floodShelterInfo 防汛安置点信息
     * @return 结果
     */
    public int insertFloodShelterInfo(FloodShelterInfo floodShelterInfo);

    /**
     * 修改防汛安置点信息
     * 
     * @param floodShelterInfo 防汛安置点信息
     * @return 结果
     */
    public int updateFloodShelterInfo(FloodShelterInfo floodShelterInfo);

    /**
     * 批量删除防汛安置点信息
     * 
     * @param ids 需要删除的防汛安置点信息主键集合
     * @return 结果
     */
    public int deleteFloodShelterInfoByIds(Long[] ids);

    /**
     * 删除防汛安置点信息信息
     * 
     * @param id 防汛安置点信息主键
     * @return 结果
     */
    public int deleteFloodShelterInfoById(Long id);
}
