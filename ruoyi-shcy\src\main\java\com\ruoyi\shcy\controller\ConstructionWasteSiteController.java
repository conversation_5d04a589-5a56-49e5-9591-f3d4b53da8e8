package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ConstructionWasteSite;
import com.ruoyi.shcy.service.IConstructionWasteSiteService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区建筑垃圾堆放点Controller
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/constructionWasteSite")
public class ConstructionWasteSiteController extends BaseController
{
    @Autowired
    private IConstructionWasteSiteService constructionWasteSiteService;

    /**
     * 查询小区建筑垃圾堆放点列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:constructionWasteSite:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConstructionWasteSite constructionWasteSite)
    {
        startPage();
        List<ConstructionWasteSite> list = constructionWasteSiteService.selectConstructionWasteSiteList(constructionWasteSite);
        return getDataTable(list);
    }

    /**
     * 导出小区建筑垃圾堆放点列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:constructionWasteSite:export')")
    @Log(title = "小区建筑垃圾堆放点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConstructionWasteSite constructionWasteSite)
    {
        List<ConstructionWasteSite> list = constructionWasteSiteService.selectConstructionWasteSiteList(constructionWasteSite);
        ExcelUtil<ConstructionWasteSite> util = new ExcelUtil<ConstructionWasteSite>(ConstructionWasteSite.class);
        util.exportExcel(response, list, "小区建筑垃圾堆放点数据");
    }

    /**
     * 获取小区建筑垃圾堆放点详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:constructionWasteSite:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(constructionWasteSiteService.selectConstructionWasteSiteById(id));
    }

    /**
     * 新增小区建筑垃圾堆放点
     */
    @PreAuthorize("@ss.hasPermi('shcy:constructionWasteSite:add')")
    @Log(title = "小区建筑垃圾堆放点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ConstructionWasteSite constructionWasteSite)
    {
        return toAjax(constructionWasteSiteService.insertConstructionWasteSite(constructionWasteSite));
    }

    /**
     * 修改小区建筑垃圾堆放点
     */
    @PreAuthorize("@ss.hasPermi('shcy:constructionWasteSite:edit')")
    @Log(title = "小区建筑垃圾堆放点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConstructionWasteSite constructionWasteSite)
    {
        return toAjax(constructionWasteSiteService.updateConstructionWasteSite(constructionWasteSite));
    }

    /**
     * 删除小区建筑垃圾堆放点
     */
    @PreAuthorize("@ss.hasPermi('shcy:constructionWasteSite:remove')")
    @Log(title = "小区建筑垃圾堆放点", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(constructionWasteSiteService.deleteConstructionWasteSiteByIds(ids));
    }
}
