package com.ruoyi.artemis;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ruoyi.common.utils.CoordinateTransform;
import com.ruoyi.common.utils.GisUtils;
import com.ruoyi.hikvision.ArtemisService;
import com.ruoyi.shcy.domain.Cameras;
import com.ruoyi.shcy.domain.Regions;
import com.ruoyi.shcy.service.ICamerasService;
import com.ruoyi.shcy.service.IRegionsService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 海康威视智能应用平台API测试
 *
 * <AUTHOR>
 * @date 2023/03/24
 */
@SpringBootTest
public class ArtemisTest {

    @Autowired
    private ArtemisService artemisService;

    @Autowired
    private IRegionsService regionsService;

    @Autowired
    private ICamerasService camerasService;

    @Test
    public void contextLoads() {

    }


    /**
     * 测试api
     *
     * @throws Exception 异常
     */
    @Test
    public void testApi() throws Exception {

        // String result = artemisService.callPostApiGetCameras();
        // 31011623601328000020 31011623601328000019    31011623601328000018  31011623601328000017
        // String result = artemisService.callPostApiPreviewURLs("31011623601328000018");
        // System.out.println(result);


        // 街镇 indexCode 310116002 treeCode 0 石化街道 indexCode 31011640000000000000000000
        String s = artemisService.callPostApiSubRegions("31011640000000000000000000", "0");
        JSONObject jsonObject = JSON.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray list = data.getJSONArray("list");
        List<Regions> regionsList = JSON.parseObject(list.toJSONString(), new TypeReference<List<Regions>>() {
        });

        for (Regions regions : regionsList) {
            // regionsService.insertRegions(regions);
            String str = artemisService.callPostApiGetCameras(1, 1000, regions.getIndexCode(), "0");
            JSONObject obj = JSON.parseObject(str);
            JSONObject camerasData = obj.getJSONObject("data");
            JSONArray camerasDataList = camerasData.getJSONArray("list");
            List<Cameras> camerasList = JSON.parseObject(camerasDataList.toJSONString(), new TypeReference<List<Cameras>>() {
            });
            for (Cameras cameras : camerasList) {
                // camerasService.insertCameras(cameras);
                if (StrUtil.isNotEmpty(cameras.getLongitude()) && StrUtil.isNotEmpty(cameras.getLatitude())) {
                    String res = GisUtils.convert(cameras.getLongitude(), cameras.getLatitude());
                    JSONObject resObject = JSONObject.parseObject(res);
                    JSONObject dataObject = resObject.getJSONObject("data");
                    String lng = dataObject.getString("lng");
                    String lat = dataObject.getString("lat");
                    cameras.setType("point");
                    cameras.setCoordinate(lng + "," + lat);
                }
                camerasService.insertCameras(cameras);
            }
            // camerasService.batchInsertCameras(camerasList);
        }

        // q: fastjson2 json字符串转对象
        // a: https://blog.csdn.net/xuforeverlove/article/details/80842148

    }

    /**
     * 导入蒙山、象州派出所监控资源
     *
     * 31011622000000000000000000
     * 31011623000000000000000000
     *
     * @throws Exception 异常
     */
    @Test
    public void importPcsCameras() throws Exception {
        String str = artemisService.callPostApiGetCameras(1, 1000, "31011623000000000000000000", "0");
        JSONObject obj = JSON.parseObject(str);
        JSONObject camerasData = obj.getJSONObject("data");
        JSONArray camerasDataList = camerasData.getJSONArray("list");
        List<Cameras> camerasList = JSON.parseObject(camerasDataList.toJSONString(), new TypeReference<List<Cameras>>() {
        });
        for (Cameras cameras : camerasList) {
            // camerasService.insertCameras(cameras);
            if (StrUtil.isNotEmpty(cameras.getLongitude()) && StrUtil.isNotEmpty(cameras.getLatitude())) {
                double[] lngLat_bd09 = CoordinateTransform.transformWGS84ToBD09(Double.parseDouble(cameras.getLongitude()), Double.parseDouble(cameras.getLatitude()));
                String res = GisUtils.convertcc(String.valueOf(lngLat_bd09[0]), String.valueOf(lngLat_bd09[1]));
                JSONObject jsonObject = JSONObject.parseObject(res);
                JSONObject data = jsonObject.getJSONObject("data");
                String lng = data.getString("lng");
                String lat = data.getString("lat");
                cameras.setType("point");
                cameras.setCoordinate(lng + "," + lat);
            }
            cameras.setCameraTypeName("市政所");
        }
        camerasService.batchInsertCameras(camerasList);
    }

    @Test
    public void updateCameras() {
        List<Cameras> camerasList = camerasService.selectCamerasList(new Cameras());
        for (Cameras cameras : camerasList) {
            if (StrUtil.isNotEmpty(cameras.getLongitude()) && StrUtil.isNotEmpty(cameras.getLatitude())) {
                double[] lngLat_bd09 = CoordinateTransform.transformWGS84ToBD09(Double.parseDouble(cameras.getLongitude()), Double.parseDouble(cameras.getLatitude()));
                String res = GisUtils.convertcc(String.valueOf(lngLat_bd09[0]), String.valueOf(lngLat_bd09[1]));
                JSONObject jsonObject = JSONObject.parseObject(res);
                JSONObject data = jsonObject.getJSONObject("data");
                String lng = data.getString("lng");
                String lat = data.getString("lat");
                cameras.setType("point");
                cameras.setCoordinate(lng + "," + lat);
            }
            cameras.setCameraTypeName("市政所");
            camerasService.updateCameras(cameras);
        }
    }

    /**
     * 测试调用后api预览url
     *
     * @throws Exception 例外
     */
    @Test
    public void testCallPostApiPreviewURLs() throws Exception {
        String result = artemisService.callPostApiPreviewURLs("31011623051198000015");
        System.out.println(result);
    }

    /**
     * 测试调用后api预览url
     *
     * @throws Exception 例外
     */
    @Test
    public void testCallPostApiPreviewURLsWs() throws Exception {
        String result = artemisService.callPostApiPreviewURLsWs("31011601041326000057");
        System.out.println(result);
    }


    /**
     * 同步街道监控资源，已存在则跳过，不存在则插入
     *
     * @throws Exception 异常
     */
    @Test
    public void testSync() throws Exception {
        // 街镇 indexCode 310116002 treeCode 0 石化街道 indexCode 31011640000000000000000000
        String s = artemisService.callPostApiSubRegions("31011640000000000000000000", "0");
        JSONObject jsonObject = JSON.parseObject(s);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray list = data.getJSONArray("list");
        List<Regions> regionsList = JSON.parseObject(list.toJSONString(), new TypeReference<List<Regions>>() {
        });

        for (Regions regions : regionsList) {
            String str = artemisService.callPostApiGetCameras(1, 1000, regions.getIndexCode(), "0");
            JSONObject obj = JSON.parseObject(str);
            JSONObject camerasData = obj.getJSONObject("data");
            JSONArray camerasDataList = camerasData.getJSONArray("list");
            List<Cameras> camerasList = JSON.parseObject(camerasDataList.toJSONString(), new TypeReference<List<Cameras>>() {
            });

            System.out.println(regions.getName() + "=========> " + camerasList.size());
            for (Cameras cameras : camerasList) {

                // 根据监控点编号查询监控点是否存在
                Cameras cam = camerasService.selectCamerasByCameraIndexCode(cameras.getCameraIndexCode());
                boolean exist = cam != null;
                if (exist) {
                    // continue;

                    //更新坐标
                    if (StrUtil.isNotEmpty(cameras.getLongitude()) && StrUtil.isNotEmpty(cameras.getLatitude())) {
                        String res = GisUtils.convert(cameras.getLongitude(), cameras.getLatitude());
                        JSONObject resObject = JSONObject.parseObject(res);
                        JSONObject dataObject = resObject.getJSONObject("data");
                        String lng = dataObject.getString("lng");
                        String lat = dataObject.getString("lat");
                        cameras.setType("point");
                        cameras.setCoordinate(lng + "," + lat);
                        camerasService.updateCameras(cameras);
                    }

                }

                System.out.println(cameras.getName() + "==============>" + cameras.getCameraIndexCode());

                // if (StrUtil.isNotEmpty(cameras.getLongitude()) && StrUtil.isNotEmpty(cameras.getLatitude())) {
                //     String res = GisUtils.convert(cameras.getLongitude(), cameras.getLatitude());
                //     JSONObject resObject = JSONObject.parseObject(res);
                //     JSONObject dataObject = resObject.getJSONObject("data");
                //     String lng = dataObject.getString("lng");
                //     String lat = dataObject.getString("lat");
                //     cameras.setType("point");
                //     cameras.setCoordinate(lng + "," + lat);
                // }
                // camerasService.insertCameras(cameras);
            }
        }
    }

}
