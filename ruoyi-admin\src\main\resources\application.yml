# 项目相关配置
ruoyi:
  # 名称
  name: shcy
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2022
  # 实例演示开关
  demoEnabled: true
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: @logging.level@
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认30分钟）
    lockTime: 30

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profiles.active@
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  50MB
       # 设置总上传的文件大小
       max-request-size:  200MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  rabbitmq:
    username: jinshan
    password: jinshan
    virtual-host: /
    host: ***************
    port: 5672

# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 480

# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.ruoyi.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# 短信服务
sms:
  # 标注从yml读取配置
  config-type: yaml
  blends:
    # 自定义的标识，也就是configId这里可以是任意值（最好不要是中文）
    ali1:
      #厂商标识，标定此配置是哪个厂商，详细请看厂商标识介绍部分
      supplier: alibaba
      #有些称为accessKey有些称之为apiKey，也有称为sdkKey或者appId。
      access-key-id: LTAI5tCcg5CXUTgxCT91iquP
      #称为accessSecret有些称之为apiSecret。
      access-key-secret: ******************************
      #您的短信签名
      signature: 上海金山区石化街道办事处
      #模板ID 如果不需要简化的sendMessage方法可以不配置
      template-id: SMS_489685827
      # 随机权重，负载均衡的权重值依赖于此，默认为1，如不需要负载均衡可不进行配置
      weight: 1
      #配置标识名称 如果你使用的yml进行配置，则blends下层配置的就是这个，可为空，如果你使用的接口类配置，则需要设置值
      #需要注意的是，不同的配置之间config-id不能重复，否则会发生配置丢失的问题
      config-id: sms1
      #短信自动重试间隔时间  默认五秒
      retry-interval: 5
      # 短信重试次数，默认0次不重试，如果你需要短信重试则根据自己的需求修改值即可
      max-retries: 0

artemis:
  host: *************
  appKey: 24125089
  appSecret: gnz3BY9eaxLWPMX9Gt8S

nbiotyun:
  host: https://webapi.nbiotyun.com
  appKey: 4LAtuvj8
  appSecret: 15f6f714f366f373f1914a72463c2abefe28319f





