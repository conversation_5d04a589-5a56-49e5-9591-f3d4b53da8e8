package com.ruoyi.shcy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.ComprehensiveInspection;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.mapper.ComprehensiveInspectionMapper;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.service.IComprehensiveInspectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 综合检查Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
@Service
public class ComprehensiveInspectionServiceImpl implements IComprehensiveInspectionService 
{
    @Autowired
    private ComprehensiveInspectionMapper comprehensiveInspectionMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    /**
     * 查询综合检查
     * 
     * @param id 综合检查主键
     * @return 综合检查
     */
    @Override
    public ComprehensiveInspection selectComprehensiveInspectionById(Long id)
    {
        return handlePhoto(comprehensiveInspectionMapper.selectComprehensiveInspectionById(id));
    }

    private ComprehensiveInspection handlePhoto(ComprehensiveInspection comprehensiveInspection) {
        // 收集所有的文件ID
        List<Long> allFileIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(comprehensiveInspection.getHighEmissionPhoto())) {
            allFileIds.addAll(StrUtil.split(comprehensiveInspection.getHighEmissionPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        if (StrUtil.isNotEmpty(comprehensiveInspection.getPurifierPhoto())) {
            allFileIds.addAll(StrUtil.split(comprehensiveInspection.getPurifierPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        if (StrUtil.isNotEmpty(comprehensiveInspection.getGreaseTrapPhoto())) {
            allFileIds.addAll(StrUtil.split(comprehensiveInspection.getGreaseTrapPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        if (StrUtil.isNotEmpty(comprehensiveInspection.getOtherIssuesPhoto())) {
            allFileIds.addAll(StrUtil.split(comprehensiveInspection.getOtherIssuesPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        if (StrUtil.isNotEmpty(comprehensiveInspection.getHighEmissionDisposalPhoto())) {
            allFileIds.addAll(StrUtil.split(comprehensiveInspection.getHighEmissionDisposalPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        if (StrUtil.isNotEmpty(comprehensiveInspection.getPurifierDisposalPhoto())) {
            allFileIds.addAll(StrUtil.split(comprehensiveInspection.getPurifierDisposalPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        if (StrUtil.isNotEmpty(comprehensiveInspection.getGreaseTrapDisposalPhoto())) {
            allFileIds.addAll(StrUtil.split(comprehensiveInspection.getGreaseTrapDisposalPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        if (StrUtil.isNotEmpty(comprehensiveInspection.getInspectionPhoto())) {
            allFileIds.addAll(StrUtil.split(comprehensiveInspection.getInspectionPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }

        // 如果有文件ID,则一次性查询所有文件信息
        if (!allFileIds.isEmpty()) {
            // 假设 shcyFileInfoMapper 中新增了批量查询方法
            List<ShcyFileInfo> allFileInfos = shcyFileInfoMapper.selectShcyFileInfoByFileIds(allFileIds);
            // 转换为Map方便查找
            Map<Long, String> filePathMap = allFileInfos.stream()
                    .collect(Collectors.toMap(ShcyFileInfo::getFileId, ShcyFileInfo::getFilePath));

            // 设置各字段的图片URL
            if (StrUtil.isNotEmpty(comprehensiveInspection.getHighEmissionPhoto())) {
                comprehensiveInspection.setHighEmissionPhotoUrls(
                        StrUtil.split(comprehensiveInspection.getHighEmissionPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
            if (StrUtil.isNotEmpty(comprehensiveInspection.getPurifierPhoto())) {
                comprehensiveInspection.setPurifierPhotoUrls(
                        StrUtil.split(comprehensiveInspection.getPurifierPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
            if (StrUtil.isNotEmpty(comprehensiveInspection.getGreaseTrapPhoto())) {
                comprehensiveInspection.setGreaseTrapPhotoUrls(
                        StrUtil.split(comprehensiveInspection.getGreaseTrapPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
            if (StrUtil.isNotEmpty(comprehensiveInspection.getOtherIssuesPhoto())) {
                comprehensiveInspection.setOtherIssuesPhotoUrls(
                        StrUtil.split(comprehensiveInspection.getOtherIssuesPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
            if (StrUtil.isNotEmpty(comprehensiveInspection.getHighEmissionDisposalPhoto())) {
                comprehensiveInspection.setHighEmissionDisposalPhotoUrls(
                        StrUtil.split(comprehensiveInspection.getHighEmissionDisposalPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
            if (StrUtil.isNotEmpty(comprehensiveInspection.getPurifierDisposalPhoto())) {
                comprehensiveInspection.setPurifierDisposalPhotoUrls(
                        StrUtil.split(comprehensiveInspection.getPurifierDisposalPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
            if (StrUtil.isNotEmpty(comprehensiveInspection.getGreaseTrapDisposalPhoto())) {
                comprehensiveInspection.setGreaseTrapDisposalPhotoUrls(
                        StrUtil.split(comprehensiveInspection.getGreaseTrapDisposalPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
            if (StrUtil.isNotEmpty(comprehensiveInspection.getInspectionPhoto())) {
                comprehensiveInspection.setInspectionPhotoUrls(
                        StrUtil.split(comprehensiveInspection.getInspectionPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
        }

        return comprehensiveInspection;
    }

    /**
     * 查询综合检查列表
     * 
     * @param comprehensiveInspection 综合检查
     * @return 综合检查
     */
    @Override
    public List<ComprehensiveInspection> selectComprehensiveInspectionList(ComprehensiveInspection comprehensiveInspection)
    {
        return comprehensiveInspectionMapper.selectComprehensiveInspectionList(comprehensiveInspection);
    }

    /**
     * 新增综合检查
     * 
     * @param comprehensiveInspection 综合检查
     * @return 结果
     */
    @Override
    public int insertComprehensiveInspection(ComprehensiveInspection comprehensiveInspection)
    {
        comprehensiveInspection.setCreateTime(DateUtils.getNowDate());
        return comprehensiveInspectionMapper.insertComprehensiveInspection(comprehensiveInspection);
    }

    /**
     * 修改综合检查
     * 
     * @param comprehensiveInspection 综合检查
     * @return 结果
     */
    @Override
    public int updateComprehensiveInspection(ComprehensiveInspection comprehensiveInspection)
    {
        comprehensiveInspection.setUpdateTime(DateUtils.getNowDate());
        return comprehensiveInspectionMapper.updateComprehensiveInspection(comprehensiveInspection);
    }

    /**
     * 批量删除综合检查
     * 
     * @param ids 需要删除的综合检查主键
     * @return 结果
     */
    @Override
    public int deleteComprehensiveInspectionByIds(Long[] ids)
    {
        return comprehensiveInspectionMapper.deleteComprehensiveInspectionByIds(ids);
    }

    /**
     * 删除综合检查信息
     * 
     * @param id 综合检查主键
     * @return 结果
     */
    @Override
    public int deleteComprehensiveInspectionById(Long id)
    {
        return comprehensiveInspectionMapper.deleteComprehensiveInspectionById(id);
    }

    @Override
    public long getCaseCount(ComprehensiveInspection comprehensiveInspection) {
        return comprehensiveInspectionMapper.getCaseCount(comprehensiveInspection);
    }
}
