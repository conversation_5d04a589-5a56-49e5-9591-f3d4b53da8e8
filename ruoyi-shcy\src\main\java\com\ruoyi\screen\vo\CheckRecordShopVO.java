package com.ruoyi.screen.vo;

import com.ruoyi.shcy.domain.CheckRecord;
import com.ruoyi.shcy.domain.Shop;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 巡检商铺 VO 对象
 *
 * <AUTHOR>
 * @since 2023/03/01
 */
@Data
@Builder
public class CheckRecordShopVO {

    /**
     * 今日巡查通过商铺
     */
    List<CheckRecord> todayPassShop;

    /**
     * 今日巡查未通过商铺
     */
    List<CheckRecord> todayNotPassShop;

    /**
     * 本月未巡查商铺
     */
    List<Shop> monthNotCheckShop;

    /**
     * 本月巡查通过商铺
     */
    List<CheckRecord> monthPassShop;

    /**
     * 本月巡查未通过商铺
     */
    List<CheckRecord> monthNotPassShop;


}
