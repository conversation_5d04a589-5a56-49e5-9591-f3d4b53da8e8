package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ComprehensiveGrid;

/**
 * 综合网格Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-11
 */
public interface ComprehensiveGridMapper 
{
    /**
     * 查询综合网格
     * 
     * @param id 综合网格主键
     * @return 综合网格
     */
    public ComprehensiveGrid selectComprehensiveGridById(Long id);

    /**
     * 查询综合网格列表
     * 
     * @param comprehensiveGrid 综合网格
     * @return 综合网格集合
     */
    public List<ComprehensiveGrid> selectComprehensiveGridList(ComprehensiveGrid comprehensiveGrid);

    /**
     * 新增综合网格
     * 
     * @param comprehensiveGrid 综合网格
     * @return 结果
     */
    public int insertComprehensiveGrid(ComprehensiveGrid comprehensiveGrid);

    /**
     * 修改综合网格
     * 
     * @param comprehensiveGrid 综合网格
     * @return 结果
     */
    public int updateComprehensiveGrid(ComprehensiveGrid comprehensiveGrid);

    /**
     * 删除综合网格
     * 
     * @param id 综合网格主键
     * @return 结果
     */
    public int deleteComprehensiveGridById(Long id);

    /**
     * 批量删除综合网格
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteComprehensiveGridByIds(Long[] ids);
}
