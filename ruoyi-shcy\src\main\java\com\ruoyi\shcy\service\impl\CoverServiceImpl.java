package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.CoverMapper;
import com.ruoyi.shcy.domain.Cover;
import com.ruoyi.shcy.service.ICoverService;

/**
 * 井盖信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-21
 */
@Service
public class CoverServiceImpl implements ICoverService 
{
    @Autowired
    private CoverMapper coverMapper;

    /**
     * 查询井盖信息
     * 
     * @param id 井盖信息主键
     * @return 井盖信息
     */
    @Override
    public Cover selectCoverById(Long id)
    {
        return coverMapper.selectCoverById(id);
    }

    /**
     * 查询井盖信息列表
     * 
     * @param cover 井盖信息
     * @return 井盖信息
     */
    @Override
    public List<Cover> selectCoverList(Cover cover)
    {
        return coverMapper.selectCoverList(cover);
    }

    /**
     * 新增井盖信息
     * 
     * @param cover 井盖信息
     * @return 结果
     */
    @Override
    public int insertCover(Cover cover)
    {
        cover.setCreateTime(DateUtils.getNowDate());
        return coverMapper.insertCover(cover);
    }

    /**
     * 修改井盖信息
     * 
     * @param cover 井盖信息
     * @return 结果
     */
    @Override
    public int updateCover(Cover cover)
    {
        cover.setUpdateTime(DateUtils.getNowDate());
        return coverMapper.updateCover(cover);
    }

    /**
     * 批量删除井盖信息
     * 
     * @param ids 需要删除的井盖信息主键
     * @return 结果
     */
    @Override
    public int deleteCoverByIds(Long[] ids)
    {
        return coverMapper.deleteCoverByIds(ids);
    }

    /**
     * 删除井盖信息信息
     * 
     * @param id 井盖信息主键
     * @return 结果
     */
    @Override
    public int deleteCoverById(Long id)
    {
        return coverMapper.deleteCoverById(id);
    }
}
