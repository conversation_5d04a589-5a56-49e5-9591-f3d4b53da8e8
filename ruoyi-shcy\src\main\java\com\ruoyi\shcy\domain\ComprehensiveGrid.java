package com.ruoyi.shcy.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 综合网格对象 shcy_comprehensive_grid
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
public class ComprehensiveGrid extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String gridName;

    /**
     * 编号
     */
    @Excel(name = "编号")
    private String gridNumber;

    /**
     * 图形类型
     */
    private String type;

    /**
     * 坐标
     */
    private String coordinate;

    /**
     * 填充颜色
     */
    private String fillColor;

    /**
     * 边框色
     */
    private String outlineColor;

    /**
     * 覆盖范围
     */
    @Excel(name = "覆盖范围")
    private String coverageArea;

    /**
     * 覆盖居民区数量
     */
    @Excel(name = "覆盖居民区数量")
    private Long residentialAreas;

    /**
     * 面积(平方千米)
     */
    @Excel(name = "面积(平方千米)")
    private BigDecimal areaSize;

    /**
     * 人口规模(万人)
     */
    @Excel(name = "人口规模(万人)")
    private BigDecimal population;

    /**
     * 市场主体数量
     */
    @Excel(name = "市场主体数量")
    private Long marketEntities;

    /**
     * 新就业群人数
     */
    @Excel(name = "新就业群人数")
    private Long newEmployment;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setGridName(String gridName) {
        this.gridName = gridName;
    }

    public String getGridName() {
        return gridName;
    }

    public void setGridNumber(String gridNumber) {
        this.gridNumber = gridNumber;
    }

    public String getGridNumber() {
        return gridNumber;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setFillColor(String fillColor) {
        this.fillColor = fillColor;
    }

    public String getFillColor() {
        return fillColor;
    }

    public void setOutlineColor(String outlineColor) {
        this.outlineColor = outlineColor;
    }

    public String getOutlineColor() {
        return outlineColor;
    }

    public String getCoverageArea() {
        return coverageArea;
    }

    public void setCoverageArea(String coverageArea) {
        this.coverageArea = coverageArea;
    }

    public Long getResidentialAreas() {
        return residentialAreas;
    }

    public void setResidentialAreas(Long residentialAreas) {
        this.residentialAreas = residentialAreas;
    }

    public BigDecimal getAreaSize() {
        return areaSize;
    }

    public void setAreaSize(BigDecimal areaSize) {
        this.areaSize = areaSize;
    }

    public BigDecimal getPopulation() {
        return population;
    }

    public void setPopulation(BigDecimal population) {
        this.population = population;
    }

    public Long getMarketEntities() {
        return marketEntities;
    }

    public void setMarketEntities(Long marketEntities) {
        this.marketEntities = marketEntities;
    }

    public Long getNewEmployment() {
        return newEmployment;
    }

    public void setNewEmployment(Long newEmployment) {
        this.newEmployment = newEmployment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("gridName", getGridName())
                .append("gridNumber", getGridNumber())
                .append("type", getType())
                .append("coordinate", getCoordinate())
                .append("fillColor", getFillColor())
                .append("outlineColor", getOutlineColor())
                .append("coverageArea", getCoverageArea())
                .append("residentialAreas", getResidentialAreas())
                .append("areaSize", getAreaSize())
                .append("population", getPopulation())
                .append("marketEntities", getMarketEntities())
                .append("newEmployment", getNewEmployment())
                .toString();
    }
}
