package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.EntertainmentPlaces;

/**
 * 娱乐场所Service接口
 * 
 * <AUTHOR>
 * @date 2023-01-31
 */
public interface IEntertainmentPlacesService 
{
    /**
     * 查询娱乐场所
     * 
     * @param id 娱乐场所主键
     * @return 娱乐场所
     */
    public EntertainmentPlaces selectEntertainmentPlacesById(Long id);

    /**
     * 查询娱乐场所列表
     * 
     * @param entertainmentPlaces 娱乐场所
     * @return 娱乐场所集合
     */
    public List<EntertainmentPlaces> selectEntertainmentPlacesList(EntertainmentPlaces entertainmentPlaces);

    /**
     * 新增娱乐场所
     * 
     * @param entertainmentPlaces 娱乐场所
     * @return 结果
     */
    public int insertEntertainmentPlaces(EntertainmentPlaces entertainmentPlaces);

    /**
     * 修改娱乐场所
     * 
     * @param entertainmentPlaces 娱乐场所
     * @return 结果
     */
    public int updateEntertainmentPlaces(EntertainmentPlaces entertainmentPlaces);

    /**
     * 批量删除娱乐场所
     * 
     * @param ids 需要删除的娱乐场所主键集合
     * @return 结果
     */
    public int deleteEntertainmentPlacesByIds(Long[] ids);

    /**
     * 删除娱乐场所信息
     * 
     * @param id 娱乐场所主键
     * @return 结果
     */
    public int deleteEntertainmentPlacesById(Long id);
}
