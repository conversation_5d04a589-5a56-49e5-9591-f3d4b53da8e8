package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.domain.vo.RxReportCfgdVO;
import com.ruoyi.shcy.domain.vo.RxReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网格化案件信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
public interface TaskDbcenterMapper 
{
    /**
     * 查询网格化案件信息
     * 
     * @param id 网格化案件信息主键
     * @return 网格化案件信息
     */
    public TaskDbcenter selectTaskDbcenterById(Long id);

    /**
     * 查询网格化案件信息列表
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 网格化案件信息集合
     */
    public List<TaskDbcenter> selectTaskDbcenterList(TaskDbcenter taskDbcenter);

    /**
     * 新增网格化案件信息
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 结果
     */
    public int insertTaskDbcenter(TaskDbcenter taskDbcenter);

    /**
     * 修改网格化案件信息
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 结果
     */
    public int updateTaskDbcenter(TaskDbcenter taskDbcenter);

    /**
     * 删除网格化案件信息
     * 
     * @param id 网格化案件信息主键
     * @return 结果
     */
    public int deleteTaskDbcenterById(Long id);

    /**
     * 批量删除网格化案件信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskDbcenterByIds(Long[] ids);

    public List<Long> selectTaskDbcenterIdList();

    public List<Long> selectTaskDbcenterIdListBySynctime(String synctime);

    public TaskDbcenter selectTaskDbcenterByHotlinesn(String hotlinesn);

    public TaskDbcenter selectTaskDbcenterByRecenthotlinesn(String recenthotlinesn);

    public List<TaskDbcenter> selectTaskDbcenterSyncList();

    public List<Long> selectTaskDbcenterSyncIdList();

    public int deleteTaskDbcenterNotShjd();

    public List<String> selectTaskDbcenterSyncTaskidList();

    public List<TaskDbcenter> selectTaskDbcenterRxList(TaskDbcenter taskDbcenter);

    List<TaskDbcenter> selectTaskDbcenterListDesc(TaskDbcenter taskDbcenter);

    List<TaskDbcenter> selectDeptList(TaskDbcenter taskDbcenter);

    public int deleteTaskDbcenterReturnOther();

    List<RxReportVO> selectListByParentappealclassification(TaskDbcenter taskDbcenter);

    List<RxReportVO> selectXlByParentappealclassification(TaskDbcenter taskDbcenter);

    List<RxReportVO> selectJmqByParentappealclassification(TaskDbcenter taskDbcenter);

    List<RxReportVO> selectTaskDbcenterListGroupByResidentialarea(TaskDbcenter taskDbcenter);

    List<String> selectTaskDbcenterHotlinesnList();

    List<TaskDbcenter> selectSimpleTaskDbcenterList(TaskDbcenter taskDbcenter);
}
