package com.ruoyi.shcy;

import com.ruoyi.shcy.constant.SmsConstants;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;

@SpringBootTest
public class SmsTest {


    @Test
    public void contextLoads() {
    }


    @Test
    public void testSmsSend() {

        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
        List<String> list = Arrays.asList("13524096143", "15026828254");
        list.forEach(phone1 -> {
            smsBlend.sendMessageAsync(phone1, SmsConstants.TEMPLATE_CODE_JJRW_FXB, new LinkedHashMap<>());
        });

    }

    @Test
    public void testSmsSend2() {
        String phone = "13524096143";
        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
        List<String> list = Arrays.asList(
                phone,
                "13917707676"
        );

        list.forEach(phone1 -> {
            smsBlend.sendMessageAsync(phone1, SmsConstants.TEMPLATE_CODE_YWGJ, new LinkedHashMap<>());
        });

    }

    @Test
    public void testSmsSend3() {
        String phone = "13524096143";
        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
        List<String> list = Arrays.asList(
                phone,
                "13917707676"
        );

        // 参数
        LinkedHashMap<String, String> params = new LinkedHashMap<>();
        params.put("road", "龙胜路垃圾房");
        params.put("date", "2025-03-24 15:54:00");
        params.put("car_num", "沪A10821");

        list.forEach(phone1 -> {
            smsBlend.sendMessageAsync(phone1, SmsConstants.TEMPLATE_CODE_TDLJ, params);
        });

    }

    @Test
    public void testSmsSend4() {
        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
        // String phone = "13524096143";
        // List<String> list = Arrays.asList(
        //         phone,
        //         "13816859266"
        // );

        // 参数
        // LinkedHashMap<String, String> params = new LinkedHashMap<>();
        // params.put("date", "2025-06-17");
        // params.put("date1", "22:00-08:00");
        // params.put("n12", "1");
        // params.put("n13", "2");
        // params.put("n19", "3");
        //
        // list.forEach(phone1 -> {
        //     smsBlend.sendMessageAsync(phone1, "SMS_489195204", params);
        // });

        // LinkedHashMap<String, String> params = new LinkedHashMap<>();
        // params.put("road", "龙胜路垃圾房");
        // params.put("date", "2025-07-01 08:00:00");
        // params.put("car_num", "沪AF72627");

        // list.forEach(phone1 -> {
        //     smsBlend.sendMessageAsync(phone1, "SMS_489750145", params);
        // });

        smsBlend.sendMessageAsync("13052257380", "SMS_489685827", new LinkedHashMap<>());

    }

}
