package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.GreenGrid;
import com.ruoyi.shcy.service.IGreenGridService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 绿化网格Controller
 * 
 * <AUTHOR>
 * @date 2023-02-09
 */
@RestController
@RequestMapping("/shcy/grid")
public class GreenGridController extends BaseController
{
    @Autowired
    private IGreenGridService greenGridService;

    /**
     * 查询绿化网格列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:grid:list')")
    @GetMapping("/list")
    public TableDataInfo list(GreenGrid greenGrid)
    {
        startPage();
        List<GreenGrid> list = greenGridService.selectGreenGridList(greenGrid);
        return getDataTable(list);
    }

    /**
     * 导出绿化网格列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:grid:export')")
    @Log(title = "绿化网格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GreenGrid greenGrid)
    {
        List<GreenGrid> list = greenGridService.selectGreenGridList(greenGrid);
        ExcelUtil<GreenGrid> util = new ExcelUtil<GreenGrid>(GreenGrid.class);
        util.exportExcel(response, list, "绿化网格数据");
    }

    /**
     * 获取绿化网格详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:grid:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(greenGridService.selectGreenGridById(id));
    }

    /**
     * 新增绿化网格
     */
    @PreAuthorize("@ss.hasPermi('shcy:grid:add')")
    @Log(title = "绿化网格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GreenGrid greenGrid)
    {
        return toAjax(greenGridService.insertGreenGrid(greenGrid));
    }

    /**
     * 修改绿化网格
     */
    @PreAuthorize("@ss.hasPermi('shcy:grid:edit')")
    @Log(title = "绿化网格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GreenGrid greenGrid)
    {
        return toAjax(greenGridService.updateGreenGrid(greenGrid));
    }

    /**
     * 删除绿化网格
     */
    @PreAuthorize("@ss.hasPermi('shcy:grid:remove')")
    @Log(title = "绿化网格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(greenGridService.deleteGreenGridByIds(ids));
    }
}
