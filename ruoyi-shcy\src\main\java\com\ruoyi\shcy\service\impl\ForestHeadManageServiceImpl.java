package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ForestHeadManageMapper;
import com.ruoyi.shcy.domain.ForestHeadManage;
import com.ruoyi.shcy.service.IForestHeadManageService;

/**
 * 林长管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-08-05
 */
@Service
public class ForestHeadManageServiceImpl implements IForestHeadManageService 
{
    @Autowired
    private ForestHeadManageMapper forestHeadManageMapper;

    /**
     * 查询林长管理
     * 
     * @param id 林长管理主键
     * @return 林长管理
     */
    @Override
    public ForestHeadManage selectForestHeadManageById(Long id)
    {
        return forestHeadManageMapper.selectForestHeadManageById(id);
    }

    /**
     * 查询林长管理列表
     * 
     * @param forestHeadManage 林长管理
     * @return 林长管理
     */
    @Override
    public List<ForestHeadManage> selectForestHeadManageList(ForestHeadManage forestHeadManage)
    {
        return forestHeadManageMapper.selectForestHeadManageList(forestHeadManage);
    }

    /**
     * 新增林长管理
     * 
     * @param forestHeadManage 林长管理
     * @return 结果
     */
    @Override
    public int insertForestHeadManage(ForestHeadManage forestHeadManage)
    {
        return forestHeadManageMapper.insertForestHeadManage(forestHeadManage);
    }

    /**
     * 修改林长管理
     * 
     * @param forestHeadManage 林长管理
     * @return 结果
     */
    @Override
    public int updateForestHeadManage(ForestHeadManage forestHeadManage)
    {
        return forestHeadManageMapper.updateForestHeadManage(forestHeadManage);
    }

    /**
     * 批量删除林长管理
     * 
     * @param ids 需要删除的林长管理主键
     * @return 结果
     */
    @Override
    public int deleteForestHeadManageByIds(Long[] ids)
    {
        return forestHeadManageMapper.deleteForestHeadManageByIds(ids);
    }

    /**
     * 删除林长管理信息
     * 
     * @param id 林长管理主键
     * @return 结果
     */
    @Override
    public int deleteForestHeadManageById(Long id)
    {
        return forestHeadManageMapper.deleteForestHeadManageById(id);
    }
}
