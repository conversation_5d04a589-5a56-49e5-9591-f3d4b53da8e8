package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.GridCase;

import java.util.List;

/**
 * 网格化案事件Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-21
 */
public interface GridCaseMapper 
{
    /**
     * 查询网格化案事件
     * 
     * @param id 网格化案事件主键
     * @return 网格化案事件
     */
    public GridCase selectGridCaseById(Long id);

    /**
     * 查询网格化案事件列表
     * 
     * @param gridCase 网格化案事件
     * @return 网格化案事件集合
     */
    public List<GridCase> selectGridCaseList(GridCase gridCase);

    /**
     * 新增网格化案事件
     * 
     * @param gridCase 网格化案事件
     * @return 结果
     */
    public int insertGridCase(GridCase gridCase);

    /**
     * 修改网格化案事件
     * 
     * @param gridCase 网格化案事件
     * @return 结果
     */
    public int updateGridCase(GridCase gridCase);

    /**
     * 删除网格化案事件
     * 
     * @param id 网格化案事件主键
     * @return 结果
     */
    public int deleteGridCaseById(Long id);

    /**
     * 批量删除网格化案事件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGridCaseByIds(Long[] ids);
}
