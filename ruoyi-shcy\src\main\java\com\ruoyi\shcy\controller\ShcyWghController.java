package com.ruoyi.shcy.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.shcy.constant.SmsConstants;
import com.ruoyi.shcy.domain.ShcyWgh;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.domain.vo.WghCountVo;
import com.ruoyi.shcy.service.IShcyWghService;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import com.ruoyi.system.service.ISysUserService;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Month;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 网格化案件信息Controller
 * 
 * <AUTHOR>
 * @date 2024-04-10
 */
@RestController
@RequestMapping("/shcy/wgh")
public class ShcyWghController extends BaseController
{
    @Autowired
    private IShcyWghService shcyWghService;
    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 查询网格化案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:wgh:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyWgh shcyWgh)
    {
        shcyWgh.getParams().put("czFlag",1);
        startPage();
        List<ShcyWgh> list = shcyWghService.selectShcyWghList(shcyWgh);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:wgh:listAll')")
    @GetMapping("/listAll")
    public TableDataInfo listAll(ShcyWgh shcyWgh)
    {
        startPage();
        List<ShcyWgh> list = shcyWghService.selectShcyWghList(shcyWgh);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:wgh:listReport')")
    @GetMapping("/listReport")
    public AjaxResult listReport(ShcyWgh shcyWgh)
    {
        SysUser theUser=new SysUser();
        theUser.setDeptId(Long.parseLong("233"));
        List<SysUser> users=sysUserService.selectUserList(theUser);
        List<WghCountVo> voList=new ArrayList<WghCountVo>();
        users.stream().forEach(item ->{
            //除退单状态的该用户的派遣总数
            shcyWgh.getParams().put("czFlag",null);
            WghCountVo theW=new WghCountVo();
            theW.setName(item.getUserName());
            shcyWgh.getParams().put("allFlag",1);
            shcyWgh.setDealBy(item.getUserName());
            List<ShcyWgh> list = shcyWghService.selectShcyWghListForReport(shcyWgh);
            theW.setNum(list.size());

            //结案件数
            shcyWgh.getParams().put("allFlag",6);
            List<ShcyWgh> list1 = shcyWghService.selectShcyWghListForReport(shcyWgh);
            theW.setJaNum(list1.size());

            //处置中件数
            shcyWgh.getParams().put("allFlag",null);
            shcyWgh.getParams().put("czFlag",1);
            List<ShcyWgh> list2 = shcyWghService.selectShcyWghListForReport(shcyWgh);
            theW.setCzNum(list2.size());
            voList.add(theW);
        });

        return AjaxResult.success(voList);
    }

    @PreAuthorize("@ss.hasPermi('shcy:wgh:listDja')")
    @GetMapping("/listDja")
    public TableDataInfo listDja(ShcyWgh shcyWgh)
    {
        shcyWgh.getParams().put("czFlag",2);
        startPage();
        List<ShcyWgh> list = shcyWghService.selectShcyWghList(shcyWgh);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:wgh:listTd')")
    @GetMapping("/listTd")
    public TableDataInfo listTd(ShcyWgh shcyWgh)
    {
        shcyWgh.getParams().put("czFlag",4);
        startPage();
        List<ShcyWgh> list = shcyWghService.selectShcyWghList(shcyWgh);
        return getDataTable(list);
    }

    /**
     * 已上报至三高平台列表
     *
     * @param shcyWgh 网格化案件信息
     * @return {@link TableDataInfo }
     */
    @PreAuthorize("@ss.hasPermi('shcy:wgh:listYsb')")
    @GetMapping("/listYsb")
    public TableDataInfo listYsb(ShcyWgh shcyWgh)
    {
        shcyWgh.getParams().put("czFlag", 3);
        startPage();
        List<ShcyWgh> list = shcyWghService.selectShcyWghList(shcyWgh);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:wgh:listYja')")
    @GetMapping("/listYja")
    public TableDataInfo listYja(ShcyWgh shcyWgh)
    {
        shcyWgh.getParams().put("czFlag", 6);
        startPage();
        List<ShcyWgh> list = shcyWghService.selectShcyWghList(shcyWgh);
        return getDataTable(list);
    }


    /**
     * 导出网格化案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:wgh:export')")
    @Log(title = "网格化案件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyWgh shcyWgh)
    {
        List<ShcyWgh> list = shcyWghService.selectShcyWghList(shcyWgh);
        ExcelUtil<ShcyWgh> util = new ExcelUtil<ShcyWgh>(ShcyWgh.class);
        util.exportExcel(response, list, "网格化案件信息数据");
    }

    /**
     * 获取网格化案件信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:wgh:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyWghService.selectShcyWghById(id));
    }

    @GetMapping(value = "/info/{wghId}")
    public AjaxResult getWghByWghId(@PathVariable("wghId") Long wghId)
    {
        ShcyWgh shcyWgh=new ShcyWgh();
        shcyWgh.setWghId(wghId);
        List<ShcyWgh> list=shcyWghService.selectShcyWghList(shcyWgh);
        if(list.size()>0)
        {
            return AjaxResult.success(list.get(0));
        }
        return AjaxResult.success(new ShcyWgh());
    }


    /**
     * 新增网格化案件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:wgh:add')")
    @Log(title = "网格化案件信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyWgh shcyWgh)
    {
        return toAjax(shcyWghService.insertShcyWgh(shcyWgh));
    }

    /**
     * 修改网格化案件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:wgh:edit')")
    @Log(title = "网格化案件信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyWgh shcyWgh)
    {
        return toAjax(shcyWghService.updateShcyWgh(shcyWgh));
    }

    @PreAuthorize("@ss.hasPermi('shcy:wgh:check')")
    @Log(title = "网格化案件信息", businessType = BusinessType.UPDATE)
    @PostMapping("/check")
    public AjaxResult check(@RequestBody ShcyWgh shcyWgh)
    {
        ShcyWgh theS=shcyWghService.selectShcyWghById(shcyWgh.getId());
        if(StringUtils.isNotEmpty(theS.getYqReason()) && "1".equals(shcyWgh.getCirculationState()))
        {
            shcyWgh.setCirculationState("2");
        }

        TaskDbcenter theTaskDbcenter=taskDbcenterService.selectTaskDbcenterById(theS.getWghId());
        if(theTaskDbcenter != null)
        {
            theTaskDbcenter.setCirculationState(shcyWgh.getCirculationState());
            taskDbcenterService.updateTaskDbcenter(theTaskDbcenter);
        }
        return toAjax(shcyWghService.updateShcyWgh(shcyWgh));
    }

    @PreAuthorize("@ss.hasPermi('shcy:wgh:zf')")
    @Log(title = "网格化案件信息", businessType = BusinessType.UPDATE)
    @PostMapping("/zf")
    public AjaxResult zf(@RequestBody ShcyWgh shcyWgh)
    {
        ShcyWgh theS=shcyWghService.selectShcyWghById(shcyWgh.getId());
        shcyWgh.setCirculationState("5");

        TaskDbcenter theTaskDbcenter=taskDbcenterService.selectTaskDbcenterById(theS.getWghId());
        if(theTaskDbcenter != null)
        {
            theTaskDbcenter.setCirculationState(shcyWgh.getCirculationState());
            taskDbcenterService.updateTaskDbcenter(theTaskDbcenter);
        }
        return toAjax(shcyWghService.updateShcyWgh(shcyWgh));
    }


    /**
     * 删除网格化案件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:wgh:remove')")
    @Log(title = "网格化案件信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyWghService.deleteShcyWghByIds(ids));
    }

    @Log(title = "选择处置人", businessType = BusinessType.UPDATE)
    @PostMapping(value="/chose")
    public AjaxResult choseDealPerson(@RequestBody ShcyWgh shcyWgh){
        //查询原始表数据信息
        if(shcyWgh.getWghId() != null)
        {
            TaskDbcenter theTaskDbcenter=taskDbcenterService.selectTaskDbcenterById(shcyWgh.getWghId());
            if(theTaskDbcenter != null)
            {
                ShcyWgh theShcy=new ShcyWgh();
                theShcy.setWghId(theTaskDbcenter.getId());
                List<ShcyWgh> list=shcyWghService.selectShcyWghList(theShcy);

                shcyWgh.setAddress(theTaskDbcenter.getAddress());
                shcyWgh.setInfobcName(theTaskDbcenter.getInfobcname());
                shcyWgh.setLianTime(theTaskDbcenter.getCreatetime());
                shcyWgh.setReporter(theTaskDbcenter.getReporter());
                shcyWgh.setInfoscName(theTaskDbcenter.getInfoscname());
                shcyWgh.setDescription(theTaskDbcenter.getDescription());
                shcyWgh.setCirculationState("1");
                shcyWgh.setCreateBy(getUsername());
                shcyWgh.setTaskId(theTaskDbcenter.getTaskid());
                shcyWgh.setDiscoverTime(theTaskDbcenter.getDiscovertime());
                shcyWgh.setImagefilename(theTaskDbcenter.getImagefilename());
                theTaskDbcenter.setCirculationState("1");
                taskDbcenterService.updateTaskDbcenter(theTaskDbcenter);

                // 发送短信
                // 查询处置人手机号
                String dealBy = shcyWgh.getDealBy();
                String dealByPhone = sysUserService.selectUserByUserName(dealBy).getPhonenumber();
                if (StringUtils.isNotEmpty(dealByPhone)) {
                    SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
                    smsBlend.sendMessageAsync(dealByPhone, SmsConstants.TEMPLATE_CODE_AJBJ_CG, new LinkedHashMap<>());
                }

                if(list.size()>0)
                {
                    shcyWgh.setId(list.get(0).getId());
                    return toAjax(shcyWghService.updateShcyWgh(shcyWgh));
                }
                else {
                    return toAjax(shcyWghService.insertShcyWgh(shcyWgh));
                }

            }
            else
            {
                return error("查询信息失败");
            }
        }
        return error("查询信息失败");
    }

    /**
     * 重新选择处置人
     */
    @PostMapping(value="/reChose")
    public AjaxResult reChoseDealPerson(@RequestBody ShcyWgh shcyWgh){
        // 判断shcyWgh.getWghId()是否为空
        if (shcyWgh.getWghId() == null) {
            return error("操作失败");
        }
        // 此处的shcyWgh.getWghId()其实是shcyWgh的id并非是wghId
        ShcyWgh wgh = shcyWghService.selectShcyWghById(shcyWgh.getWghId());
        // 1: 处置中
        wgh.setCirculationState("1");
        wgh.setDealBy(shcyWgh.getDealBy());
        wgh.setUpdateBy(getUsername());
        return toAjax(shcyWghService.updateShcyWgh(wgh));
    }

    @GetMapping("/getCaseCount")
    public AjaxResult getCaseCount(ShcyWgh shcyWgh, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            shcyWgh.setParams(params);
        }
        return AjaxResult.success("操作成功", shcyWghService.getCaseCount(shcyWgh));
    }

    @GetMapping("/handle/list")
    public TableDataInfo handleList(ShcyWgh shcyWgh, String keyword)
    {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            shcyWgh.setParams(params);
        }
        startPage();
        List<ShcyWgh> list = shcyWghService.selectHandleList(shcyWgh);
        return getDataTable(list);
    }

    @GetMapping("/history/list")
    public TableDataInfo historyList(ShcyWgh shcyWgh, String keyword)
    {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            shcyWgh.setParams(params);
        }
        startPage();
        List<ShcyWgh> list = shcyWghService.selectHistoryList(shcyWgh);
        return getDataTable(list);
    }

    @GetMapping(value = "/getDbCenter/{id}")
    public AjaxResult getDbCenter(@PathVariable("id") Long id)
    {
        return AjaxResult.success(taskDbcenterService.selectTaskDbcenterById(id));
    }

    @GetMapping(value = "/getWghInfo/{id}")
    public AjaxResult getWhgInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyWghService.selectShcyWghById(id));
    }

    @PostMapping("/uploadDealPhoto")
    public AjaxResult uploadDealPhoto(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }

    @PostMapping("/handle")
    @Log(title = "移动端选择处置", businessType = BusinessType.UPDATE)
    public AjaxResult handle(@RequestBody ShcyWgh shcyWgh)
    {
        TaskDbcenter theTaskDbcenter=taskDbcenterService.selectTaskDbcenterById(shcyWgh.getWghId());
        if(theTaskDbcenter != null)
        {
            theTaskDbcenter.setCirculationState(shcyWgh.getCirculationState());
            taskDbcenterService.updateTaskDbcenter(theTaskDbcenter);
        }

        return toAjax(shcyWghService.handleShcyWgh(shcyWgh));
    }


    @GetMapping("/czlList")
    public AjaxResult czlList(String beginTime, String endTime) throws ParseException {
        if (StrUtil.isEmpty(beginTime) || StrUtil.isEmpty(endTime)) {
            Map<String, String> timeRange = getTimeRange();
            beginTime = timeRange.get("beginTime");
            endTime = timeRange.get("endTime");
        }
        TaskDbcenter taskDbcenter = new TaskDbcenter();
        taskDbcenter.getParams().put("beginTime", beginTime);
        taskDbcenter.getParams().put("endTime", endTime);
        taskDbcenter.getParams().put("jaFlag",true);
        taskDbcenter.getParams().put("cfFlag",true);
        taskDbcenter.setSubexecutedeptnameMh("石化街道综合行政执法队");
        taskDbcenter.getParams().put("sourceFlag",false);
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);

        // 当前年度的数据
        Map<String, Long> map = list.stream().collect(Collectors.groupingBy(task -> DateUtil.format(task.getCreatetime(), "yyyy-MM"), Collectors.counting()));


//        taskDbcenter.getParams().put("jieanTime",getMonthlyDeadline(taskDbcenter.getParams().get("endTime").toString()));
//        List<TaskDbcenter> allRelateds1 = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
//        Map<String, Long> map1 = allRelateds1.stream().collect(Collectors.groupingBy(task -> DateUtil.format(task.getDiscovertime(), "yyyy-MM"), Collectors.counting()));
        Map<String, Double> map1=new HashMap<String,Double>();
        Map<String, Long> map2=new HashMap<String,Long>();
        map2.put("2023-11",396L);
        map2.put("2023-12",374L);
        map2.put("2024-01",467L);
        map2.put("2024-02",384L);
        map2.put("2024-03",454L);
        map2.put("2024-04",472L);
        map2.put("2024-05",410L);
        map2.put("2024-06",381L);
        map2.put("2024-07",403L);
        map2.put("2024-08",381L);
        map2.put("2024-09",345L);
        map2.put("2024-10",478L);

        map1.put("2023-11",82.8);
        map1.put("2023-12",78.8);
        map1.put("2024-01",77.4);
        map1.put("2024-02",81.2);
        map1.put("2024-03",77.8);
        map1.put("2024-04",79.2);
        map1.put("2024-05",76.2);
        map1.put("2024-06",88.3);
        map1.put("2024-07",94.8);
        map1.put("2024-08",98.5);
        map1.put("2024-09",99.4);
        map1.put("2024-10",99.7);
        // 根据beginTime和endTime列出所有的年月，yyyy-MM格式返回字符串数组
        LocalDate startDate = LocalDate.parse(beginTime, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endDate = LocalDate.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<String> months = getMonthsBetween(startDate, endDate);
//        for(int i=0;i<months.size();i++)
//        {
//            String t=months.get(i)+"-01";
//            //按照月份获取为已结案数
//            if(months.size() == 1 || i==months.size()-1)
//            {
//                taskDbcenter.getParams().put("yjieanTime",getMonthlyDeadline1(taskDbcenter.getParams().get("endTime").toString()));
//            }
//           else
//            {
//                String t1=getMonthlyDeadline(t);
//                System.out.println("@1111@@@"+t1);
//                taskDbcenter.getParams().put("yjieanTime",getMonthlyDeadline1(t1));
//            }
//
//            System.out.println("@@@@"+taskDbcenter.getParams().get("yjieanTime"));
//            List<TaskDbcenter> allRelateds1 = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
//            map1 = allRelateds1.stream().collect(Collectors.groupingBy(task -> DateUtil.format(task.getCreatetime(), "yyyy-MM"), Collectors.counting()));
//            System.out.println("!!!!!"+map1.get(months.get(i)));
//            map2.put(months.get(i),map1.get(months.get(i)));
//        }
        HashMap<String, Object> result = new HashMap<String, Object>() {{
            put("monthList", months);
            put("pdList", map2);
            put("wjaList", map1);
        }};
        return AjaxResult.success(result);
    }

    public static List<String> getMonthsBetween(LocalDate startDate, LocalDate endDate) {
        List<String> monthList = new ArrayList<>();
        YearMonth startYearMonth = YearMonth.from(startDate);
        YearMonth endYearMonth = YearMonth.from(endDate);

        YearMonth currentYearMonth = startYearMonth;
        while (!currentYearMonth.isAfter(endYearMonth)) {
            monthList.add(currentYearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            currentYearMonth = currentYearMonth.plusMonths(1);
        }

        return monthList;
    }

    private Map<String, String> getTimeRange() {
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        Month currentMonth = currentDate.getMonth();
        String beginTime;
        String endTime;

        if (currentMonth == Month.NOVEMBER || currentMonth == Month.DECEMBER) {
            int nextYear = currentYear + 1;
            LocalDate startDate = LocalDate.of(nextYear - 1, Month.NOVEMBER, 1);
            LocalDate endDate = LocalDate.of(nextYear, Month.OCTOBER, 31);
            beginTime = startDate.toString();
            endTime = endDate.toString();
        } else {
            LocalDate startDate = LocalDate.of(currentYear - 1, Month.NOVEMBER, 1);
            LocalDate endDate = LocalDate.of(currentYear, Month.OCTOBER, 31);
            beginTime = startDate.toString();
            endTime = endDate.toString();
        }

        Map<String, String> timeRange = new HashMap<>();
        timeRange.put("beginTime", beginTime);
        timeRange.put("endTime", endTime);
        return timeRange;
    }

    public static String getMonthlyDeadline(String date) throws ParseException {
        date=date+" 23:59:59";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatter.parse(date));
        //calendar.add(Calendar.DAY_OF_MONTH, 7);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return formatter.format(calendar.getTime());
    }
    public static String getMonthlyDeadline1(String date) throws ParseException {
        date=date+" 23:59:59";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatter.parse(date));
        calendar.add(Calendar.DAY_OF_MONTH, 7);
       // calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return formatter.format(calendar.getTime());
    }

    /**
     * 同步已上报至三高平台的案件
     *
     */
    @Log(title = "同步已上报至三高平台", businessType = BusinessType.UPDATE)
    @PostMapping("/syncYsbData")
    public AjaxResult syncYsbData()
    {
        return toAjax(shcyWghService.syncYsbData());
    }
}
