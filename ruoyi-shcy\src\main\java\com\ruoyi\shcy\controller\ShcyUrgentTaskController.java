package com.ruoyi.shcy.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.constant.SmsConstants;
import com.ruoyi.shcy.domain.ShcyUrgentTask;
import com.ruoyi.shcy.service.IShcyUrgentTaskService;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 台风登陆前紧急任务Controller
 *
 * <AUTHOR>
 * @date 2023-12-22
 */
@RestController
@RequestMapping("/shcy/urgentTask")
public class ShcyUrgentTaskController extends BaseController
{
    @Autowired
    private IShcyUrgentTaskService shcyUrgentTaskService;

    /**
     * 查询台风登陆前紧急任务列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:urgentTask:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyUrgentTask shcyUrgentTask)
    {
        startPage();
        List<ShcyUrgentTask> list = shcyUrgentTaskService.selectShcyUrgentTaskList(shcyUrgentTask);
        return getDataTable(list);
    }

    @PreAuthorize("@ss.hasPermi('shcy:urgentTask:list')")
    @GetMapping("/urgency/list")
    public TableDataInfo emergencyList(ShcyUrgentTask shcyUrgentTask, String keyword)
    {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            shcyUrgentTask.setParams(params);
        }
        startPage();
        // shcyUrgentTask.setCaseDealBy(getUsername());
        List<ShcyUrgentTask> list = shcyUrgentTaskService.selectShcyUrgentTaskList(shcyUrgentTask);
        return getDataTable(list);
    }


    /**
     * 导出台风登陆前紧急任务列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:urgentTask:export')")
    @Log(title = "台风登陆前紧急任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyUrgentTask shcyUrgentTask)
    {
        List<ShcyUrgentTask> list = shcyUrgentTaskService.selectShcyUrgentTaskList(shcyUrgentTask);
        ExcelUtil<ShcyUrgentTask> util = new ExcelUtil<ShcyUrgentTask>(ShcyUrgentTask.class);
        util.exportExcel(response, list, "台风登陆前紧急任务数据");
    }

    /**
     * 获取台风登陆前紧急任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:urgentTask:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyUrgentTaskService.selectShcyUrgentTaskById(id));
    }

    /**
     * 新增台风登陆前紧急任务
     */
    @PreAuthorize("@ss.hasPermi('shcy:urgentTask:add')")
    @Log(title = "台风登陆前紧急任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyUrgentTask shcyUrgentTask)
    {
        return toAjax(shcyUrgentTaskService.insertShcyUrgentTask(shcyUrgentTask));
    }

    /**
     * 修改台风登陆前紧急任务
     */
    @PreAuthorize("@ss.hasPermi('shcy:urgentTask:edit')")
    @Log(title = "台风登陆前紧急任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyUrgentTask shcyUrgentTask)
    {
        return toAjax(shcyUrgentTaskService.updateShcyUrgentTask(shcyUrgentTask));
    }

    /**
     * 删除台风登陆前紧急任务
     */
    @PreAuthorize("@ss.hasPermi('shcy:urgentTask:remove')")
    @Log(title = "台风登陆前紧急任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyUrgentTaskService.deleteShcyUrgentTaskByIds(ids));
    }

    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody ShcyUrgentTask shcyUrgentTask)
    {
        return toAjax(shcyUrgentTaskService.handleShcyUrgentTaskList(shcyUrgentTask));
    }

    @PostMapping("/handleUrgent")
    public AjaxResult handleUrgent(@RequestBody ShcyUrgentTask shcyUrgentTask)
    {
        int result = shcyUrgentTaskService.handleUrgent(shcyUrgentTask);
        sendUrgentTaskNotifications();
        return toAjax(result);
    }

    /**
     * 发送紧急任务通知
     */
    private void sendUrgentTaskNotifications() {
        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
        smsBlend.sendMessage(SmsConstants.PHONE_NUMBER_FANHUI, SmsConstants.TEMPLATE_CODE_JJRW, new LinkedHashMap<>());
        List<String> phoneNumbers = Arrays.asList(
                SmsConstants.PHONE_NUMBER_ZHOUBING,
                SmsConstants.PHONE_NUMBER_YANGYUJI
        );
        phoneNumbers.forEach(phone1 -> {
            smsBlend.sendMessageAsync(phone1, SmsConstants.TEMPLATE_CODE_JJRW_FXB, new LinkedHashMap<>());
        });
    }

    @PreAuthorize("@ss.hasPermi('shcy:urgentTask:list')")
    @GetMapping("/getCaseCount")
    public AjaxResult getCaseCount(ShcyUrgentTask shcyUrgentTask, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            shcyUrgentTask.setParams(params);
        }
        return AjaxResult.success("操作成功", shcyUrgentTaskService.getCaseCount(shcyUrgentTask));
    }

}
