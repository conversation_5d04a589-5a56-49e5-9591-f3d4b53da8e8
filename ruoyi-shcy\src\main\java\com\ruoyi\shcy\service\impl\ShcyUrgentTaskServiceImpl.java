package com.ruoyi.shcy.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.domain.ShcyUrgentTask;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.mapper.ShcyUrgentTaskMapper;
import com.ruoyi.shcy.service.IShcyUrgentTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 台风登陆前紧急任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-22
 */
@Service
public class ShcyUrgentTaskServiceImpl implements IShcyUrgentTaskService
{
    @Autowired
    private ShcyUrgentTaskMapper shcyUrgentTaskMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    /**
     * 查询台风登陆前紧急任务
     *
     * @param id 台风登陆前紧急任务主键
     * @return 台风登陆前紧急任务
     */
    @Override
    public ShcyUrgentTask selectShcyUrgentTaskById(Long id)
    {
        return  handlePhoto(shcyUrgentTaskMapper.selectShcyUrgentTaskById(id));
    }

    private ShcyUrgentTask handlePhoto(ShcyUrgentTask shcyUrgentTask) {
        if (StrUtil.isNotEmpty(shcyUrgentTask.getSandbagPhoto())) {
            List<String> photoUrls = new ArrayList<>();
            String[] fileIds = shcyUrgentTask.getSandbagPhoto().split(",");
            for (String fileId : fileIds) {
                ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
                photoUrls.add(shcyFileInfo.getFilePath());
            }
            shcyUrgentTask.setSandbagPhotoUrls(photoUrls);
        }
        // if (StrUtil.isNotEmpty(shcyUrgentTask.getDutyPersonnelPhoto())) {
        //     List<String> matUrls = new ArrayList<>();
        //     String[] fileIds = shcyUrgentTask.getDutyPersonnelPhoto().split(",");
        //     for (String fileId : fileIds) {
        //         ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
        //         matUrls.add(shcyFileInfo.getFilePath());
        //     }
        //     shcyUrgentTask.setDutyPersonnelPhotoUrls(matUrls);
        // }


        return shcyUrgentTask;
    }

    /**
     * 查询台风登陆前紧急任务列表
     *
     * @param shcyUrgentTask 台风登陆前紧急任务
     * @return 台风登陆前紧急任务
     */
    @Override
    public List<ShcyUrgentTask> selectShcyUrgentTaskList(ShcyUrgentTask shcyUrgentTask)
    {
        return shcyUrgentTaskMapper.selectShcyUrgentTaskList(shcyUrgentTask);
    }

    /**
     * 新增台风登陆前紧急任务
     *
     * @param shcyUrgentTask 台风登陆前紧急任务
     * @return 结果
     */
    @Override
    public int insertShcyUrgentTask(ShcyUrgentTask shcyUrgentTask)
    {
        shcyUrgentTask.setCreateTime(DateUtils.getNowDate());
        return shcyUrgentTaskMapper.insertShcyUrgentTask(shcyUrgentTask);
    }

    /**
     * 修改台风登陆前紧急任务
     *
     * @param shcyUrgentTask 台风登陆前紧急任务
     * @return 结果
     */
    @Override
    public int updateShcyUrgentTask(ShcyUrgentTask shcyUrgentTask)
    {
        return shcyUrgentTaskMapper.updateShcyUrgentTask(shcyUrgentTask);
    }

    /**
     * 批量删除台风登陆前紧急任务
     *
     * @param ids 需要删除的台风登陆前紧急任务主键
     * @return 结果
     */
    @Override
    public int deleteShcyUrgentTaskByIds(Long[] ids)
    {
        return shcyUrgentTaskMapper.deleteShcyUrgentTaskByIds(ids);
    }

    /**
     * 删除台风登陆前紧急任务信息
     *
     * @param id 台风登陆前紧急任务主键
     * @return 结果
     */
    @Override
    public int deleteShcyUrgentTaskById(Long id)
    {
        return shcyUrgentTaskMapper.deleteShcyUrgentTaskById(id);
    }

    @Override
    public int handleShcyUrgentTaskList(ShcyUrgentTask shcyUrgentTask) {
        // 更新案件状态
        shcyUrgentTask.setCirculationState(FxftConstants.PROCESSED);
        shcyUrgentTask.setCaseFinishTime(DateUtils.getNowDate());
        // 判断当前时间是否小于shcyUrgentTask的DisposalDeadline
        if (DateUtils.getNowDate().compareTo(shcyUrgentTask.getDisposalDeadline()) < 0) {
            shcyUrgentTask.setDealInTimeState(FxftConstants.OVERTIME_STATE_NORMAL);
        } else {
            shcyUrgentTask.setDealInTimeState(FxftConstants.OVERTIME_STATE_OVERTIME);
        }
        return shcyUrgentTaskMapper.updateShcyUrgentTask(shcyUrgentTask);
    }

    @Override
    public int handleUrgent(ShcyUrgentTask shcyUrgentTask) {
        shcyUrgentTask.setEventNo(DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(10));
        shcyUrgentTask.setAlarmLocation("石化一村");
        shcyUrgentTask.setCaseDealBy("建管中心");
        shcyUrgentTask.setRegion("雨水倒灌");
        shcyUrgentTask.setCirculationState(FxftConstants.UNPROCESSED);

        // 截至日期 =》 当前时间 + 2小时
        shcyUrgentTask.setDisposalDeadline(DateUtils.addHours(new Date(), 2));
        shcyUrgentTask.setCreateTime(new Date());
        return shcyUrgentTaskMapper.insertShcyUrgentTask(shcyUrgentTask);
    }

    @Override
    public long getCaseCount(ShcyUrgentTask shcyUrgentTask) {
        return shcyUrgentTaskMapper.getCaseCount(shcyUrgentTask);
    }
}
