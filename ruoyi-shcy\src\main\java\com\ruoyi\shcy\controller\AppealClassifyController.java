package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.AppealClassify;
import com.ruoyi.shcy.service.IAppealClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 诉求归类Controller
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
@RestController
@RequestMapping("/shcy/appealClassify")
public class AppealClassifyController extends BaseController
{
    @Autowired
    private IAppealClassifyService appealClassifyService;

    /**
     * 查询诉求归类列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:appealClassify:list')")
    @GetMapping("/list")
    public AjaxResult list(AppealClassify appealClassify)
    {
        List<AppealClassify> list = appealClassifyService.selectAppealClassifyList(appealClassify);
        return AjaxResult.success(list);
    }

    /**
     * 导出诉求归类列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:appealClassify:export')")
    @Log(title = "诉求归类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppealClassify appealClassify)
    {
        List<AppealClassify> list = appealClassifyService.selectAppealClassifyList(appealClassify);
        List<AppealClassify> appealClassifies = buildTree(list);
        List<AppealClassify> flatList = flattenTree(appealClassifies);
        ExcelUtil<AppealClassify> util = new ExcelUtil<AppealClassify>(AppealClassify.class);
        util.exportExcel(response, flatList, "诉求归类数据");
    }

    /**
     * 构建树形结构
     */
    private List<AppealClassify> buildTree(List<AppealClassify> list) {
        Map<Long, List<AppealClassify>> childrenMap = new HashMap<>();
        Map<Long, AppealClassify> nodeMap = new HashMap<>();
        List<AppealClassify> root = new ArrayList<>();

        // 构建映射关系
        for (AppealClassify node : list) {
            Long parentId = node.getParentId();
            childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(node);
            nodeMap.put(node.getId(), node);
        }

        // 找出根节点
        for (AppealClassify node : list) {
            if (!nodeMap.containsKey(node.getParentId())) {
                root.add(node);
            }
        }

        // 递归设置子节点
        for (AppealClassify node : root) {
            setChildren(node, childrenMap);
        }

        return root;
    }

    /**
     * 递归设置子节点
     */
    private void setChildren(AppealClassify node, Map<Long, List<AppealClassify>> childrenMap) {
        List<AppealClassify> children = childrenMap.get(node.getId());
        if (children != null) {
            node.setChildren(children);
            for (AppealClassify child : children) {
                setChildren(child, childrenMap);
            }
        }
    }

    private List<AppealClassify> flattenTree(List<AppealClassify> tree) {
        List<AppealClassify> flatList = new ArrayList<>();
        for (AppealClassify node : tree) {
            addNodeAndChildren(node, flatList);
        }
        return flatList;
    }

    private void addNodeAndChildren(AppealClassify node, List<AppealClassify> flatList) {
        flatList.add(node);
        for (AppealClassify child : node.getChildren()) {
            addNodeAndChildren(child, flatList);
        }
    }

    /**
     * 获取诉求归类详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:appealClassify:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(appealClassifyService.selectAppealClassifyById(id));
    }

    /**
     * 新增诉求归类
     */
    @PreAuthorize("@ss.hasPermi('shcy:appealClassify:add')")
    @Log(title = "诉求归类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppealClassify appealClassify)
    {
        return toAjax(appealClassifyService.insertAppealClassify(appealClassify));
    }

    /**
     * 修改诉求归类
     */
    @PreAuthorize("@ss.hasPermi('shcy:appealClassify:edit')")
    @Log(title = "诉求归类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppealClassify appealClassify)
    {
        return toAjax(appealClassifyService.updateAppealClassify(appealClassify));
    }

    /**
     * 删除诉求归类
     */
    @PreAuthorize("@ss.hasPermi('shcy:appealClassify:remove')")
    @Log(title = "诉求归类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(appealClassifyService.deleteAppealClassifyByIds(ids));
    }
}
