package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ApartmentRental;

/**
 * 公寓租赁房Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface ApartmentRentalMapper 
{
    /**
     * 查询公寓租赁房
     * 
     * @param id 公寓租赁房主键
     * @return 公寓租赁房
     */
    public ApartmentRental selectApartmentRentalById(Long id);

    /**
     * 查询公寓租赁房列表
     * 
     * @param apartmentRental 公寓租赁房
     * @return 公寓租赁房集合
     */
    public List<ApartmentRental> selectApartmentRentalList(ApartmentRental apartmentRental);

    /**
     * 新增公寓租赁房
     * 
     * @param apartmentRental 公寓租赁房
     * @return 结果
     */
    public int insertApartmentRental(ApartmentRental apartmentRental);

    /**
     * 修改公寓租赁房
     * 
     * @param apartmentRental 公寓租赁房
     * @return 结果
     */
    public int updateApartmentRental(ApartmentRental apartmentRental);

    /**
     * 删除公寓租赁房
     * 
     * @param id 公寓租赁房主键
     * @return 结果
     */
    public int deleteApartmentRentalById(Long id);

    /**
     * 批量删除公寓租赁房
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteApartmentRentalByIds(Long[] ids);
}
