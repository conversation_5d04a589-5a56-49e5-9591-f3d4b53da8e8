package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 综合检查对象 shcy_comprehensive_inspection
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
public class ComprehensiveInspection extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 任务名称
     */
    @Excel(name = "任务名称")
    private String taskName;

    /**
     * 所属网格
     */
    @Excel(name = "所属网格")
    private String gridArea;

    /**
     * 企业名称
     */
    @Excel(name = "企业名称")
    private String companyName;

    /**
     * 企业地址
     */
    @Excel(name = "企业地址")
    private String companyAddress;

    /**
     * 检查日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检查日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inspectionDate;

    /**
     * 烟道是否高空排放
     */
    @Excel(name = "烟道是否高空排放")
    private String isHighEmission;

    /**
     * 烟道是否高空排放照片
     */
    @Excel(name = "烟道是否高空排放照片")
    private String highEmissionPhoto;

    /**
     * 是否安装油烟净化器
     */
    @Excel(name = "是否安装油烟净化器")
    private String hasPurifier;

    /**
     * 是否安装油烟净化器照片
     */
    @Excel(name = "是否安装油烟净化器照片")
    private String purifierPhoto;

    /**
     * 是否符合距离要求
     */
    @Excel(name = "是否符合距离要求")
    private String meetDistanceReq;

    /**
     * 是否有隔油除渣设施
     */
    @Excel(name = "是否有隔油除渣设施")
    private String hasGreaseTrap;

    /**
     * 是否有隔油除渣设施照片
     */
    @Excel(name = "是否有隔油除渣设施照片")
    private String greaseTrapPhoto;

    /**
     * 污水是否正确纳管
     */
    @Excel(name = "污水是否正确纳管")
    private String sewageProperlyManaged;

    /**
     * 其他问题
     */
    @Excel(name = "其他问题")
    private String otherIssues;

    /**
     * 其他问题照片
     */
    @Excel(name = "其他问题照片")
    private String otherIssuesPhoto;

    /**
     * 检查人数
     */
    @Excel(name = "检查人数")
    private Long inspectorCount;

    /**
     * 现场检查照片
     */
    @Excel(name = "现场检查照片")
    private String inspectionPhoto;

    /**
     * 复查日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "复查日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reviewDate;

    /**
     * 烟道是否高空排放处置内容
     */
    @Excel(name = "烟道是否高空排放处置内容")
    private String highEmissionDisposal;

    /**
     * 烟道是否高空排放处置照片
     */
    @Excel(name = "烟道是否高空排放处置照片")
    private String highEmissionDisposalPhoto;

    /**
     * 是否安装油烟净化器处置内容
     */
    @Excel(name = "是否安装油烟净化器处置内容")
    private String purifierDisposal;

    /**
     * 是否安装油烟净化器处置照片
     */
    @Excel(name = "是否安装油烟净化器处置照片")
    private String purifierDisposalPhoto;

    /**
     * 是否有隔油除渣设施处置内容
     */
    @Excel(name = "是否有隔油除渣设施处置内容")
    private String greaseTrapDisposal;

    /**
     * 是否有隔油除渣设施处置照片
     */
    @Excel(name = "是否有隔油除渣设施处置照片")
    private String greaseTrapDisposalPhoto;

    /**
     * 污水是否正确纳管处置内容
     */
    @Excel(name = "污水是否正确纳管处置内容")
    private String sewageDisposal;

    /**
     * 处置状态
     */
    @Excel(name = "处置状态")
    private String circulationState;

    private List<String> highEmissionPhotoUrls;

    private List<String> purifierPhotoUrls;

    private List<String> greaseTrapPhotoUrls;

    private List<String> otherIssuesPhotoUrls;

    private List<String> highEmissionDisposalPhotoUrls;

    private List<String> purifierDisposalPhotoUrls;

    private List<String> greaseTrapDisposalPhotoUrls;

    private List<String> inspectionPhotoUrls;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setGridArea(String gridArea) {
        this.gridArea = gridArea;
    }

    public String getGridArea() {
        return gridArea;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setInspectionDate(Date inspectionDate) {
        this.inspectionDate = inspectionDate;
    }

    public Date getInspectionDate() {
        return inspectionDate;
    }

    public void setIsHighEmission(String isHighEmission) {
        this.isHighEmission = isHighEmission;
    }

    public String getIsHighEmission() {
        return isHighEmission;
    }

    public void setHighEmissionPhoto(String highEmissionPhoto) {
        this.highEmissionPhoto = highEmissionPhoto;
    }

    public String getHighEmissionPhoto() {
        return highEmissionPhoto;
    }

    public void setHasPurifier(String hasPurifier) {
        this.hasPurifier = hasPurifier;
    }

    public String getHasPurifier() {
        return hasPurifier;
    }

    public void setPurifierPhoto(String purifierPhoto) {
        this.purifierPhoto = purifierPhoto;
    }

    public String getPurifierPhoto() {
        return purifierPhoto;
    }

    public void setMeetDistanceReq(String meetDistanceReq) {
        this.meetDistanceReq = meetDistanceReq;
    }

    public String getMeetDistanceReq() {
        return meetDistanceReq;
    }

    public void setHasGreaseTrap(String hasGreaseTrap) {
        this.hasGreaseTrap = hasGreaseTrap;
    }

    public String getHasGreaseTrap() {
        return hasGreaseTrap;
    }

    public void setGreaseTrapPhoto(String greaseTrapPhoto) {
        this.greaseTrapPhoto = greaseTrapPhoto;
    }

    public String getGreaseTrapPhoto() {
        return greaseTrapPhoto;
    }

    public void setSewageProperlyManaged(String sewageProperlyManaged) {
        this.sewageProperlyManaged = sewageProperlyManaged;
    }

    public String getSewageProperlyManaged() {
        return sewageProperlyManaged;
    }

    public void setOtherIssues(String otherIssues) {
        this.otherIssues = otherIssues;
    }

    public String getOtherIssues() {
        return otherIssues;
    }

    public void setOtherIssuesPhoto(String otherIssuesPhoto) {
        this.otherIssuesPhoto = otherIssuesPhoto;
    }

    public String getOtherIssuesPhoto() {
        return otherIssuesPhoto;
    }

    public void setInspectorCount(Long inspectorCount) {
        this.inspectorCount = inspectorCount;
    }

    public Long getInspectorCount() {
        return inspectorCount;
    }

    public void setInspectionPhoto(String inspectionPhoto) {
        this.inspectionPhoto = inspectionPhoto;
    }

    public String getInspectionPhoto() {
        return inspectionPhoto;
    }

    public void setReviewDate(Date reviewDate) {
        this.reviewDate = reviewDate;
    }

    public Date getReviewDate() {
        return reviewDate;
    }

    public void setHighEmissionDisposal(String highEmissionDisposal) {
        this.highEmissionDisposal = highEmissionDisposal;
    }

    public String getHighEmissionDisposal() {
        return highEmissionDisposal;
    }

    public void setHighEmissionDisposalPhoto(String highEmissionDisposalPhoto) {
        this.highEmissionDisposalPhoto = highEmissionDisposalPhoto;
    }

    public String getHighEmissionDisposalPhoto() {
        return highEmissionDisposalPhoto;
    }

    public void setPurifierDisposal(String purifierDisposal) {
        this.purifierDisposal = purifierDisposal;
    }

    public String getPurifierDisposal() {
        return purifierDisposal;
    }

    public void setPurifierDisposalPhoto(String purifierDisposalPhoto) {
        this.purifierDisposalPhoto = purifierDisposalPhoto;
    }

    public String getPurifierDisposalPhoto() {
        return purifierDisposalPhoto;
    }

    public void setGreaseTrapDisposal(String greaseTrapDisposal) {
        this.greaseTrapDisposal = greaseTrapDisposal;
    }

    public String getGreaseTrapDisposal() {
        return greaseTrapDisposal;
    }

    public void setGreaseTrapDisposalPhoto(String greaseTrapDisposalPhoto) {
        this.greaseTrapDisposalPhoto = greaseTrapDisposalPhoto;
    }

    public String getGreaseTrapDisposalPhoto() {
        return greaseTrapDisposalPhoto;
    }

    public void setSewageDisposal(String sewageDisposal) {
        this.sewageDisposal = sewageDisposal;
    }

    public String getSewageDisposal() {
        return sewageDisposal;
    }

    public void setCirculationState(String circulationState) {
        this.circulationState = circulationState;
    }

    public String getCirculationState() {
        return circulationState;
    }

    public void setHighEmissionPhotoUrls(List<String> highEmissionPhotoUrls) {
        this.highEmissionPhotoUrls = highEmissionPhotoUrls;
    }

    public List<String> getHighEmissionPhotoUrls() {
        return highEmissionPhotoUrls;
    }

    public void setPurifierPhotoUrls(List<String> purifierPhotoUrls) {
        this.purifierPhotoUrls = purifierPhotoUrls;
    }

    public List<String> getPurifierPhotoUrls() {
        return purifierPhotoUrls;
    }

    public void setGreaseTrapPhotoUrls(List<String> greaseTrapPhotoUrls) {
        this.greaseTrapPhotoUrls = greaseTrapPhotoUrls;
    }

    public List<String> getGreaseTrapPhotoUrls() {
        return greaseTrapPhotoUrls;
    }

    public void setOtherIssuesPhotoUrls(List<String> otherIssuesPhotoUrls) {
        this.otherIssuesPhotoUrls = otherIssuesPhotoUrls;
    }

    public List<String> getOtherIssuesPhotoUrls() {
        return otherIssuesPhotoUrls;
    }

    public void setHighEmissionDisposalPhotoUrls(List<String> highEmissionDisposalPhotoUrls) {
        this.highEmissionDisposalPhotoUrls = highEmissionDisposalPhotoUrls;
    }

    public List<String> getHighEmissionDisposalPhotoUrls() {
        return highEmissionDisposalPhotoUrls;
    }

    public void setPurifierDisposalPhotoUrls(List<String> purifierDisposalPhotoUrls) {
        this.purifierDisposalPhotoUrls = purifierDisposalPhotoUrls;
    }

    public List<String> getPurifierDisposalPhotoUrls() {
        return purifierDisposalPhotoUrls;
    }

    public void setGreaseTrapDisposalPhotoUrls(List<String> greaseTrapDisposalPhotoUrls) {
        this.greaseTrapDisposalPhotoUrls = greaseTrapDisposalPhotoUrls;
    }

    public List<String> getGreaseTrapDisposalPhotoUrls() {
        return greaseTrapDisposalPhotoUrls;
    }

    public void setInspectionPhotoUrls(List<String> inspectionPhotoUrls) {
        this.inspectionPhotoUrls = inspectionPhotoUrls;
    }

    public List<String> getInspectionPhotoUrls() {
        return inspectionPhotoUrls;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("taskName", getTaskName())
                .append("gridArea", getGridArea())
                .append("companyName", getCompanyName())
                .append("companyAddress", getCompanyAddress())
                .append("inspectionDate", getInspectionDate())
                .append("isHighEmission", getIsHighEmission())
                .append("highEmissionPhoto", getHighEmissionPhoto())
                .append("hasPurifier", getHasPurifier())
                .append("purifierPhoto", getPurifierPhoto())
                .append("meetDistanceReq", getMeetDistanceReq())
                .append("hasGreaseTrap", getHasGreaseTrap())
                .append("greaseTrapPhoto", getGreaseTrapPhoto())
                .append("sewageProperlyManaged", getSewageProperlyManaged())
                .append("otherIssues", getOtherIssues())
                .append("otherIssuesPhoto", getOtherIssuesPhoto())
                .append("inspectorCount", getInspectorCount())
                .append("inspectionPhoto", getInspectionPhoto())
                .append("reviewDate", getReviewDate())
                .append("highEmissionDisposal", getHighEmissionDisposal())
                .append("highEmissionDisposalPhoto", getHighEmissionDisposalPhoto())
                .append("purifierDisposal", getPurifierDisposal())
                .append("purifierDisposalPhoto", getPurifierDisposalPhoto())
                .append("greaseTrapDisposal", getGreaseTrapDisposal())
                .append("greaseTrapDisposalPhoto", getGreaseTrapDisposalPhoto())
                .append("sewageDisposal", getSewageDisposal())
                .append("circulationState", getCirculationState())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
