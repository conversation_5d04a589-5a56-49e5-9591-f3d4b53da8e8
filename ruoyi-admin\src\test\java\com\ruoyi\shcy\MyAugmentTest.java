package com.ruoyi.shcy;

import cn.hutool.core.date.DateUtil;
import org.junit.jupiter.api.Test;

public class MyAugmentTest {


    @Test
    public void test() {

        System.out.println(DateUtil.today());
        System.out.println(DateUtil.yesterday().toDateStr());

        // String startTime = DateUtil.offsetMinute(DateUtil.date(), -5).toString();
        // String endTime = DateUtil.now();
        //
        // System.out.println(startTime);
        // System.out.println(endTime);


        // String timestamp = Convert.toStr(System.currentTimeMillis());
        // String signature = SecureUtil.md5("/api/v1/dev/alarmRecord" + timestamp + "15f6f714f366f373f1914a72463c2abefe28319f");
        //
        // System.out.println(timestamp);
        // System.out.println(signature);

    }

}
