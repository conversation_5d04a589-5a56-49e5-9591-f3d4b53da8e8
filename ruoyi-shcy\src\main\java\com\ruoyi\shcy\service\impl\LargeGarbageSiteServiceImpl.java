package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.LargeGarbageSiteMapper;
import com.ruoyi.shcy.domain.LargeGarbageSite;
import com.ruoyi.shcy.service.ILargeGarbageSiteService;

/**
 * 小区大件垃圾堆放点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class LargeGarbageSiteServiceImpl implements ILargeGarbageSiteService 
{
    @Autowired
    private LargeGarbageSiteMapper largeGarbageSiteMapper;

    /**
     * 查询小区大件垃圾堆放点
     * 
     * @param id 小区大件垃圾堆放点主键
     * @return 小区大件垃圾堆放点
     */
    @Override
    public LargeGarbageSite selectLargeGarbageSiteById(Long id)
    {
        return largeGarbageSiteMapper.selectLargeGarbageSiteById(id);
    }

    /**
     * 查询小区大件垃圾堆放点列表
     * 
     * @param largeGarbageSite 小区大件垃圾堆放点
     * @return 小区大件垃圾堆放点
     */
    @Override
    public List<LargeGarbageSite> selectLargeGarbageSiteList(LargeGarbageSite largeGarbageSite)
    {
        return largeGarbageSiteMapper.selectLargeGarbageSiteList(largeGarbageSite);
    }

    /**
     * 新增小区大件垃圾堆放点
     * 
     * @param largeGarbageSite 小区大件垃圾堆放点
     * @return 结果
     */
    @Override
    public int insertLargeGarbageSite(LargeGarbageSite largeGarbageSite)
    {
        largeGarbageSite.setCreateTime(DateUtils.getNowDate());
        return largeGarbageSiteMapper.insertLargeGarbageSite(largeGarbageSite);
    }

    /**
     * 修改小区大件垃圾堆放点
     * 
     * @param largeGarbageSite 小区大件垃圾堆放点
     * @return 结果
     */
    @Override
    public int updateLargeGarbageSite(LargeGarbageSite largeGarbageSite)
    {
        largeGarbageSite.setUpdateTime(DateUtils.getNowDate());
        return largeGarbageSiteMapper.updateLargeGarbageSite(largeGarbageSite);
    }

    /**
     * 批量删除小区大件垃圾堆放点
     * 
     * @param ids 需要删除的小区大件垃圾堆放点主键
     * @return 结果
     */
    @Override
    public int deleteLargeGarbageSiteByIds(Long[] ids)
    {
        return largeGarbageSiteMapper.deleteLargeGarbageSiteByIds(ids);
    }

    /**
     * 删除小区大件垃圾堆放点信息
     * 
     * @param id 小区大件垃圾堆放点主键
     * @return 结果
     */
    @Override
    public int deleteLargeGarbageSiteById(Long id)
    {
        return largeGarbageSiteMapper.deleteLargeGarbageSiteById(id);
    }
}
