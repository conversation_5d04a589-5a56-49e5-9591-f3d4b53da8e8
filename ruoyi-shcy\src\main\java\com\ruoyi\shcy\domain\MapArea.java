package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 地图区域对象 shcy_map_area
 * 
 * <AUTHOR>
 * @date 2022-07-08
 */
public class MapArea extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 地图区域ID */
    private Long areaId;

    /** 地图区域名称 */
    @Excel(name = "地图区域名称")
    private String areaName;

    /** 地图区域范围 */
    @Excel(name = "地图区域范围")
    private String areaRange;

    public void setAreaId(Long areaId) 
    {
        this.areaId = areaId;
    }

    public Long getAreaId() 
    {
        return areaId;
    }
    public void setAreaName(String areaName) 
    {
        this.areaName = areaName;
    }

    public String getAreaName() 
    {
        return areaName;
    }
    public void setAreaRange(String areaRange) 
    {
        this.areaRange = areaRange;
    }

    public String getAreaRange() 
    {
        return areaRange;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("areaId", getAreaId())
            .append("areaName", getAreaName())
            .append("areaRange", getAreaRange())
            .toString();
    }
}
