package com.ruoyi.shcy.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * @Description  记录商铺正常信息
 * <AUTHOR>
 * @Date 2023/2/15 16:21
 * @Version 1.0
 */

public class ShopCheckVo extends BaseEntity {

    /**  巡查店铺总数 */
    @Excel(name="巡查店铺总数")
    private String shopCheckedNum;

    /** 巡查未通过店铺数量 */
    @Excel(name="巡查店铺通过数量")
    private String shopCheckedPassNum;

    /** 巡查未通过店铺数量 */
    @Excel(name="巡查店铺未通过数量")
    private String shopCheckedNoPassNum;

    /** 未巡查店铺数量 */
    @Excel(name="未巡查店铺数量")
    private String shopNoCheckNum;

    /** 店铺总数量 */
    private String shopTotalNum;




    /**巡查日期*/
    @Excel(name = "巡查日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date curDate;


    public String getShopCheckedNum() {
        return shopCheckedNum;
    }

    public void setShopCheckedNum(String shopCheckedNum) {
        this.shopCheckedNum = shopCheckedNum;
    }

    public String getShopCheckedPassNum() {
        return shopCheckedPassNum;
    }

    public void setShopCheckedPassNum(String shopCheckedPassNum) {
        this.shopCheckedPassNum = shopCheckedPassNum;
    }

    public String getShopCheckedNoPassNum() {
        return shopCheckedNoPassNum;
    }

    public void setShopCheckedNoPassNum(String shopCheckedNoPassNum) {
        this.shopCheckedNoPassNum = shopCheckedNoPassNum;
    }

    public Date getCurDate() {
        return curDate;
    }

    public void setCurDate(Date curDate) {
        this.curDate = curDate;
    }

    public String getShopTotalNum() {
        return shopTotalNum;
    }

    public void setShopTotalNum(String shopTotalNum) {
        this.shopTotalNum = shopTotalNum;
    }

    public String getShopNoCheckNum() {
        return shopNoCheckNum;
    }

    public void setShopNoCheckNum(String shopNoCheckNum) {
        this.shopNoCheckNum = shopNoCheckNum;
    }


    @Override
    public String toString() {
        return "ShopCheckVo{" +
                "shopCheckedNum='" + shopCheckedNum + '\'' +
                ", shopCheckedPassNum='" + shopCheckedPassNum + '\'' +
                ", shopCheckedNoPassNum='" + shopCheckedNoPassNum + '\'' +
                ", shopNoCheckNum='" + shopNoCheckNum + '\'' +
                ", shopTotalNum='" + shopTotalNum + '\'' +
                ", curDate=" + curDate +
                '}';
    }
}
