package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.SmsRecordMapper;
import com.ruoyi.shcy.domain.SmsRecord;
import com.ruoyi.shcy.service.ISmsRecordService;

/**
 * 短信发送记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class SmsRecordServiceImpl implements ISmsRecordService 
{
    @Autowired
    private SmsRecordMapper smsRecordMapper;

    /**
     * 查询短信发送记录
     * 
     * @param smsId 短信发送记录主键
     * @return 短信发送记录
     */
    @Override
    public SmsRecord selectSmsRecordBySmsId(Long smsId)
    {
        return smsRecordMapper.selectSmsRecordBySmsId(smsId);
    }

    /**
     * 查询短信发送记录列表
     * 
     * @param smsRecord 短信发送记录
     * @return 短信发送记录
     */
    @Override
    public List<SmsRecord> selectSmsRecordList(SmsRecord smsRecord)
    {
        return smsRecordMapper.selectSmsRecordList(smsRecord);
    }

    /**
     * 新增短信发送记录
     * 
     * @param smsRecord 短信发送记录
     * @return 结果
     */
    @Override
    public int insertSmsRecord(SmsRecord smsRecord)
    {
        smsRecord.setCreateTime(DateUtils.getNowDate());
        return smsRecordMapper.insertSmsRecord(smsRecord);
    }

    /**
     * 修改短信发送记录
     * 
     * @param smsRecord 短信发送记录
     * @return 结果
     */
    @Override
    public int updateSmsRecord(SmsRecord smsRecord)
    {
        smsRecord.setUpdateTime(DateUtils.getNowDate());
        return smsRecordMapper.updateSmsRecord(smsRecord);
    }

    /**
     * 批量删除短信发送记录
     * 
     * @param smsIds 需要删除的短信发送记录主键
     * @return 结果
     */
    @Override
    public int deleteSmsRecordBySmsIds(Long[] smsIds)
    {
        return smsRecordMapper.deleteSmsRecordBySmsIds(smsIds);
    }

    /**
     * 删除短信发送记录信息
     * 
     * @param smsId 短信发送记录主键
     * @return 结果
     */
    @Override
    public int deleteSmsRecordBySmsId(Long smsId)
    {
        return smsRecordMapper.deleteSmsRecordBySmsId(smsId);
    }
}
