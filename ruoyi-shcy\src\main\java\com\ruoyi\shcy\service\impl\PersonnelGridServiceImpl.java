package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.PersonnelGridMapper;
import com.ruoyi.shcy.domain.PersonnelGrid;
import com.ruoyi.shcy.service.IPersonnelGridService;

/**
 * 两类人员网格化Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-18
 */
@Service
public class PersonnelGridServiceImpl implements IPersonnelGridService 
{
    @Autowired
    private PersonnelGridMapper personnelGridMapper;

    /**
     * 查询两类人员网格化
     * 
     * @param id 两类人员网格化主键
     * @return 两类人员网格化
     */
    @Override
    public PersonnelGrid selectPersonnelGridById(Long id)
    {
        return personnelGridMapper.selectPersonnelGridById(id);
    }

    /**
     * 查询两类人员网格化列表
     * 
     * @param personnelGrid 两类人员网格化
     * @return 两类人员网格化
     */
    @Override
    public List<PersonnelGrid> selectPersonnelGridList(PersonnelGrid personnelGrid)
    {
        return personnelGridMapper.selectPersonnelGridList(personnelGrid);
    }

    /**
     * 新增两类人员网格化
     * 
     * @param personnelGrid 两类人员网格化
     * @return 结果
     */
    @Override
    public int insertPersonnelGrid(PersonnelGrid personnelGrid)
    {
        personnelGrid.setCreateTime(DateUtils.getNowDate());
        return personnelGridMapper.insertPersonnelGrid(personnelGrid);
    }

    /**
     * 修改两类人员网格化
     * 
     * @param personnelGrid 两类人员网格化
     * @return 结果
     */
    @Override
    public int updatePersonnelGrid(PersonnelGrid personnelGrid)
    {
        personnelGrid.setUpdateTime(DateUtils.getNowDate());
        return personnelGridMapper.updatePersonnelGrid(personnelGrid);
    }

    /**
     * 批量删除两类人员网格化
     * 
     * @param ids 需要删除的两类人员网格化主键
     * @return 结果
     */
    @Override
    public int deletePersonnelGridByIds(Long[] ids)
    {
        return personnelGridMapper.deletePersonnelGridByIds(ids);
    }

    /**
     * 删除两类人员网格化信息
     * 
     * @param id 两类人员网格化主键
     * @return 结果
     */
    @Override
    public int deletePersonnelGridById(Long id)
    {
        return personnelGridMapper.deletePersonnelGridById(id);
    }
}
