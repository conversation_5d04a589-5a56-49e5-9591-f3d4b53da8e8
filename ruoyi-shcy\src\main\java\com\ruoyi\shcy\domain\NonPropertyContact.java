package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 非住宅物业联系表对象 shcy_non_property_contact
 *
 * <AUTHOR>
 * @date 2023-01-31
 */
public class NonPropertyContact extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 物业服务点 */
    @Excel(name = "物业服务点")
    private String propertyServicePosition;

    /** 物业服务公司 */
    @Excel(name = "物业服务公司")
    private String propertyCompany;


    /** 地址 */
    @Excel(name="地址")
    private String address;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contact;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 撒点类型 */
    @Excel(name = "撒点类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setPropertyServicePosition(String propertyServicePosition)
    {
        this.propertyServicePosition = propertyServicePosition;
    }

    public String getPropertyServicePosition()
    {
        return propertyServicePosition;
    }
    public void setPropertyCompany(String propertyCompany)
    {
        this.propertyCompany = propertyCompany;
    }

    public String getPropertyCompany()
    {
        return propertyCompany;
    }
    public void setContact(String contact)
    {
        this.contact = contact;
    }

    public String getContact()
    {
        return contact;
    }
    public void setContactPhone(String contactPhone)
    {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone()
    {
        return contactPhone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public String toString() {
        return "NonPropertyContact{" +
                "id=" + id +
                ", propertyServicePosition='" + propertyServicePosition + '\'' +
                ", propertyCompany='" + propertyCompany + '\'' +
                ", address='" + address + '\'' +
                ", contact='" + contact + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                '}';
    }
}
