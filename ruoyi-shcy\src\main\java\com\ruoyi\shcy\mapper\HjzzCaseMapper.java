package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.domain.vo.ViolationDataVO;

import java.util.List;

/**
 * 环境整治案事件Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-08
 */
public interface HjzzCaseMapper 
{
    /**
     * 查询环境整治案事件
     * 
     * @param id 环境整治案事件主键
     * @return 环境整治案事件
     */
    public HjzzCase selectHjzzCaseById(Long id);

    /**
     * 查询环境整治案事件列表
     * 
     * @param hjzzCase 环境整治案事件
     * @return 环境整治案事件集合
     */
    public List<HjzzCase> selectHjzzCaseList(HjzzCase hjzzCase);

    /**
     * 新增环境整治案事件
     * 
     * @param hjzzCase 环境整治案事件
     * @return 结果
     */
    public int insertHjzzCase(HjzzCase hjzzCase);

    /**
     * 修改环境整治案事件
     * 
     * @param hjzzCase 环境整治案事件
     * @return 结果
     */
    public int updateHjzzCase(HjzzCase hjzzCase);

    /**
     * 删除环境整治案事件
     * 
     * @param id 环境整治案事件主键
     * @return 结果
     */
    public int deleteHjzzCaseById(Long id);

    /**
     * 批量删除环境整治案事件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHjzzCaseByIds(Long[] ids);

    public List<HjzzCase> selectHandleList(HjzzCase hjzzCase);

    public List<HjzzCase> selectHistoryList(HjzzCase hjzzCase);

    public long getPendingCaseCount();

    public long getProcessingCaseCount();

    public long getCaseCount(HjzzCase hjzzCase);

    List<ViolationDataVO> getViolations(HjzzCase hjzzCase);

    public List<HjzzCase> selectHjzzCaseCount(HjzzCase hjzzCase);

    HjzzCase selectHjzzCaseByCaseNumber(String caseNumber);

    List<HjzzCase> selectHjzzCaseWgList(HjzzCase hjzzCase);

    List<HjzzCase> selectCompletedList(HjzzCase hjzzCase);
}
