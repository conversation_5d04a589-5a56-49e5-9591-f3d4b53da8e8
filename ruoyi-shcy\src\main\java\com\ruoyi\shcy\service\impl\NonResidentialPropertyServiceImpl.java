package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.NonResidentialPropertyMapper;
import com.ruoyi.shcy.domain.NonResidentialProperty;
import com.ruoyi.shcy.service.INonResidentialPropertyService;

/**
 * 物业管理处Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class NonResidentialPropertyServiceImpl implements INonResidentialPropertyService
{
    @Autowired
    private NonResidentialPropertyMapper nonResidentialPropertyMapper;

    /**
     * 查询物业管理处
     *
     * @param id 物业管理处主键
     * @return 物业管理处
     */
    @Override
    public NonResidentialProperty selectNonResidentialPropertyById(Long id)
    {
        return nonResidentialPropertyMapper.selectNonResidentialPropertyById(id);
    }

    /**
     * 查询物业管理处列表
     *
     * @param nonResidentialProperty 物业管理处
     * @return 物业管理处
     */
    @Override
    public List<NonResidentialProperty> selectNonResidentialPropertyList(NonResidentialProperty nonResidentialProperty)
    {
        return nonResidentialPropertyMapper.selectNonResidentialPropertyList(nonResidentialProperty);
    }

    /**
     * 新增物业管理处
     *
     * @param nonResidentialProperty 物业管理处
     * @return 结果
     */
    @Override
    public int insertNonResidentialProperty(NonResidentialProperty nonResidentialProperty)
    {
        nonResidentialProperty.setCreateTime(DateUtils.getNowDate());
        return nonResidentialPropertyMapper.insertNonResidentialProperty(nonResidentialProperty);
    }

    /**
     * 修改物业管理处
     *
     * @param nonResidentialProperty 物业管理处
     * @return 结果
     */
    @Override
    public int updateNonResidentialProperty(NonResidentialProperty nonResidentialProperty)
    {
        nonResidentialProperty.setUpdateTime(DateUtils.getNowDate());
        return nonResidentialPropertyMapper.updateNonResidentialProperty(nonResidentialProperty);
    }

    /**
     * 批量删除物业管理处
     *
     * @param ids 需要删除的物业管理处主键
     * @return 结果
     */
    @Override
    public int deleteNonResidentialPropertyByIds(Long[] ids)
    {
        return nonResidentialPropertyMapper.deleteNonResidentialPropertyByIds(ids);
    }

    /**
     * 删除物业管理处信息
     *
     * @param id 物业管理处主键
     * @return 结果
     */
    @Override
    public int deleteNonResidentialPropertyById(Long id)
    {
        return nonResidentialPropertyMapper.deleteNonResidentialPropertyById(id);
    }
}
