package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.SensorDevice;

import java.util.List;

/**
 * 传感器设备Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
public interface SensorDeviceMapper 
{
    /**
     * 查询传感器设备
     * 
     * @param id 传感器设备主键
     * @return 传感器设备
     */
    public SensorDevice selectSensorDeviceById(Long id);

    /**
     * 查询传感器设备列表
     * 
     * @param sensorDevice 传感器设备
     * @return 传感器设备集合
     */
    public List<SensorDevice> selectSensorDeviceList(SensorDevice sensorDevice);

    /**
     * 新增传感器设备
     * 
     * @param sensorDevice 传感器设备
     * @return 结果
     */
    public int insertSensorDevice(SensorDevice sensorDevice);

    /**
     * 修改传感器设备
     * 
     * @param sensorDevice 传感器设备
     * @return 结果
     */
    public int updateSensorDevice(SensorDevice sensorDevice);

    /**
     * 删除传感器设备
     * 
     * @param id 传感器设备主键
     * @return 结果
     */
    public int deleteSensorDeviceById(Long id);

    /**
     * 批量删除传感器设备
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSensorDeviceByIds(Long[] ids);

    public SensorDevice selectSensorDeviceByImei(String imei);
}
