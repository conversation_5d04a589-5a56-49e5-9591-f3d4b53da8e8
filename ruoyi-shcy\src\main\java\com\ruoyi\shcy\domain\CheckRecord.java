package com.ruoyi.shcy.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商铺巡查通过记录对象 shop_check_record
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
public class CheckRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 巡查表id */
    @Excel(name = "巡查表id")
    private Long shopCheckId;

    /** 商铺id */
    @Excel(name = "商铺id")
    private Long shopId;

    /** 商铺名称 */
    @Excel(name = "商铺名称")
    private String shopName;

    /** 巡查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "巡查时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date checkDate;

    /** 巡查结果 */
    @Excel(name = "巡查结果")
    private String checkStatus;

    private Shop shop;

    public Shop getShop() {
        return shop;
    }

    public void setShop(Shop shop) {
        this.shop = shop;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setShopCheckId(Long shopCheckId)
    {
        this.shopCheckId = shopCheckId;
    }

    public Long getShopCheckId()
    {
        return shopCheckId;
    }
    public void setShopId(Long shopId)
    {
        this.shopId = shopId;
    }

    public Long getShopId()
    {
        return shopId;
    }
    public void setShopName(String shopName)
    {
        this.shopName = shopName;
    }

    public String getShopName()
    {
        return shopName;
    }
    public void setCheckDate(Date checkDate)
    {
        this.checkDate = checkDate;
    }

    public Date getCheckDate()
    {
        return checkDate;
    }
    public void setCheckStatus(String checkStatus)
    {
        this.checkStatus = checkStatus;
    }

    public String getCheckStatus()
    {
        return checkStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("shopCheckId", getShopCheckId())
            .append("shopId", getShopId())
            .append("shopName", getShopName())
            .append("checkDate", getCheckDate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("checkStatus", getCheckStatus())
            .toString();
    }
}
