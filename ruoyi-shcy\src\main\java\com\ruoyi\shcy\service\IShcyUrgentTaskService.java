package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.ShcyUrgentTask;

import java.util.List;

/**
 * 台风登陆前紧急任务Service接口
 *
 * <AUTHOR>
 * @date 2023-12-22
 */
public interface IShcyUrgentTaskService
{
    /**
     * 查询台风登陆前紧急任务
     *
     * @param id 台风登陆前紧急任务主键
     * @return 台风登陆前紧急任务
     */
    public ShcyUrgentTask selectShcyUrgentTaskById(Long id);

    /**
     * 查询台风登陆前紧急任务列表
     *
     * @param shcyUrgentTask 台风登陆前紧急任务
     * @return 台风登陆前紧急任务集合
     */
    public List<ShcyUrgentTask> selectShcyUrgentTaskList(ShcyUrgentTask shcyUrgentTask);

    /**
     * 新增台风登陆前紧急任务
     *
     * @param shcyUrgentTask 台风登陆前紧急任务
     * @return 结果
     */
    public int insertShcyUrgentTask(ShcyUrgentTask shcyUrgentTask);

    /**
     * 修改台风登陆前紧急任务
     *
     * @param shcyUrgentTask 台风登陆前紧急任务
     * @return 结果
     */
    public int updateShcyUrgentTask(ShcyUrgentTask shcyUrgentTask);

    /**
     * 批量删除台风登陆前紧急任务
     *
     * @param ids 需要删除的台风登陆前紧急任务主键集合
     * @return 结果
     */
    public int deleteShcyUrgentTaskByIds(Long[] ids);

    /**
     * 删除台风登陆前紧急任务信息
     *
     * @param id 台风登陆前紧急任务主键
     * @return 结果
     */
    public int deleteShcyUrgentTaskById(Long id);

    public int handleShcyUrgentTaskList(ShcyUrgentTask shcyUrgentTask);

    public int handleUrgent(ShcyUrgentTask shcyUrgentTask);

    public long getCaseCount(ShcyUrgentTask shcyUrgentTask);
}
