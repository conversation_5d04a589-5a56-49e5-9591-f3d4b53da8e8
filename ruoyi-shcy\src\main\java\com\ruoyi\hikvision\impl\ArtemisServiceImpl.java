package com.ruoyi.hikvision.impl;

import com.alibaba.fastjson2.JSON;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.ruoyi.hikvision.ArtemisService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class ArtemisServiceImpl implements ArtemisService {

    @Value("${artemis.host}")
    private String host;

    @Value("${artemis.appKey}")
    private String appKey;

    @Value("${artemis.appSecret}")
    private String appSecret;

    /**
     * API网关的后端服务上下文为：/artemis
     */
    private static final String ARTEMIS_PATH = "/artemis";

    @Override
    public String callPostApiRegions(int pageNo, int pageSize, String treeCode) throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getApi = ARTEMIS_PATH + "/api/resource/v1/regions";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("pageNo", pageNo);
        paramMap.put("pageSize", pageSize);
        paramMap.put("treeCode", treeCode);
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getApi);
            }
        };
        return ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
    }

    @Override
    public String callPostApiSubRegions(String parentIndexCode, String treeCode) throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getApi = ARTEMIS_PATH + "/api/resource/v1/regions/subRegions";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("parentIndexCode", parentIndexCode);
        paramMap.put("treeCode", treeCode);
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getApi);
            }
        };
        return ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
    }

    @Override
    public String callPostApiPreviewURLs(String cameraIndexCode) throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getCamsApi = ARTEMIS_PATH + "/api/video/v1/cameras/previewURLs";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("cameraIndexCode", cameraIndexCode);
        paramMap.put("streamType", 0);
        paramMap.put("protocol", "hls");
        paramMap.put("transmode", 0);
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        return ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
    }

    @Override
    public String callPostApiPreviewURLsWs(String cameraIndexCode) throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getCamsApi = ARTEMIS_PATH + "/api/video/v1/cameras/previewURLs";
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("cameraIndexCode", cameraIndexCode);
        paramMap.put("streamType", 0);
        paramMap.put("protocol", "ws");
        paramMap.put("transmode", 1);
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        return ArtemisHttpUtil.doPostStringArtemis(config, path, body, null, null, "application/json");
    }

    public String callPostApiGetCameras(int pageNo, int pageSize, String regionIndexCode, String treeCode) throws Exception {
        ArtemisConfig config = new ArtemisConfig();
        config.setHost(host);
        config.setAppKey(appKey);
        config.setAppSecret(appSecret);
        final String getCamsApi = ARTEMIS_PATH + "/api/resource/v1/regions/regionIndexCode/cameras";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("pageNo", pageNo);
        paramMap.put("pageSize", pageSize);
        paramMap.put("regionIndexCode", regionIndexCode);
        paramMap.put("treeCode", treeCode);
        String body = JSON.toJSON(paramMap).toString();
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", getCamsApi);
            }
        };
        return ArtemisHttpUtil.doPostStringArtemis(config,path, body, null, null, "application/json");
    }

}
