package com.ruoyi.shcy.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.HjzzCaseMonthly;
import com.ruoyi.shcy.mapper.HjzzCaseMonthlyMapper;
import com.ruoyi.shcy.service.IHjzzCaseMonthlyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 每月案件情况分析Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-22
 */
@Service
public class HjzzCaseMonthlyServiceImpl implements IHjzzCaseMonthlyService 
{
    @Autowired
    private HjzzCaseMonthlyMapper hjzzCaseMonthlyMapper;

    /**
     * 查询每月案件情况分析
     * 
     * @param id 每月案件情况分析主键
     * @return 每月案件情况分析
     */
    @Override
    public HjzzCaseMonthly selectHjzzCaseMonthlyById(Long id)
    {
        return hjzzCaseMonthlyMapper.selectHjzzCaseMonthlyById(id);
    }

    /**
     * 查询每月案件情况分析列表
     * 
     * @param hjzzCaseMonthly 每月案件情况分析
     * @return 每月案件情况分析
     */
    @Override
    public List<HjzzCaseMonthly> selectHjzzCaseMonthlyList(HjzzCaseMonthly hjzzCaseMonthly)
    {
        return hjzzCaseMonthlyMapper.selectHjzzCaseMonthlyList(hjzzCaseMonthly);
    }

    /**
     * 新增每月案件情况分析
     * 
     * @param hjzzCaseMonthly 每月案件情况分析
     * @return 结果
     */
    @Override
    public int insertHjzzCaseMonthly(HjzzCaseMonthly hjzzCaseMonthly)
    {
        hjzzCaseMonthly.setCreateTime(DateUtils.getNowDate());
        return hjzzCaseMonthlyMapper.insertHjzzCaseMonthly(hjzzCaseMonthly);
    }

    /**
     * 修改每月案件情况分析
     * 
     * @param hjzzCaseMonthly 每月案件情况分析
     * @return 结果
     */
    @Override
    public int updateHjzzCaseMonthly(HjzzCaseMonthly hjzzCaseMonthly)
    {
        hjzzCaseMonthly.setUpdateTime(DateUtils.getNowDate());
        return hjzzCaseMonthlyMapper.updateHjzzCaseMonthly(hjzzCaseMonthly);
    }

    /**
     * 批量删除每月案件情况分析
     * 
     * @param ids 需要删除的每月案件情况分析主键
     * @return 结果
     */
    @Override
    public int deleteHjzzCaseMonthlyByIds(Long[] ids)
    {
        return hjzzCaseMonthlyMapper.deleteHjzzCaseMonthlyByIds(ids);
    }

    /**
     * 删除每月案件情况分析信息
     * 
     * @param id 每月案件情况分析主键
     * @return 结果
     */
    @Override
    public int deleteHjzzCaseMonthlyById(Long id)
    {
        return hjzzCaseMonthlyMapper.deleteHjzzCaseMonthlyById(id);
    }

    @Override
    public HjzzCaseMonthly selectHjzzCaseMonthlyByYearAndMonth(HjzzCaseMonthly hjzzCaseMonthly) {
        return hjzzCaseMonthlyMapper.selectHjzzCaseMonthlyByYearAndMonth(hjzzCaseMonthly);
    }
}
