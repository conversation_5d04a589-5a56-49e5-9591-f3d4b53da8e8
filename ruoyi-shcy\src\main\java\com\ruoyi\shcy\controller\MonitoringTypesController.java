package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.MonitoringTypes;
import com.ruoyi.shcy.service.IMonitoringTypesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监控类型Controller
 * 
 * <AUTHOR>
 * @date 2023-10-11
 */
@RestController
@RequestMapping("/shcy/monitoringTypes")
public class MonitoringTypesController extends BaseController
{
    @Autowired
    private IMonitoringTypesService monitoringTypesService;

    /**
     * 查询监控类型列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:monitoringTypes:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonitoringTypes monitoringTypes)
    {
        startPage();
        List<MonitoringTypes> list = monitoringTypesService.selectMonitoringTypesList(monitoringTypes);
        return getDataTable(list);
    }

    /**
     * 导出监控类型列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:monitoringTypes:export')")
    @Log(title = "监控类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonitoringTypes monitoringTypes)
    {
        List<MonitoringTypes> list = monitoringTypesService.selectMonitoringTypesList(monitoringTypes);
        ExcelUtil<MonitoringTypes> util = new ExcelUtil<MonitoringTypes>(MonitoringTypes.class);
        util.exportExcel(response, list, "监控类型数据");
    }

    /**
     * 获取监控类型详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:monitoringTypes:query')")
    @GetMapping(value = "/{monitoringTypeId}")
    public AjaxResult getInfo(@PathVariable("monitoringTypeId") Long monitoringTypeId)
    {
        return AjaxResult.success(monitoringTypesService.selectMonitoringTypesByMonitoringTypeId(monitoringTypeId));
    }

    /**
     * 新增监控类型
     */
    @PreAuthorize("@ss.hasPermi('shcy:monitoringTypes:add')")
    @Log(title = "监控类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonitoringTypes monitoringTypes)
    {
        return toAjax(monitoringTypesService.insertMonitoringTypes(monitoringTypes));
    }

    /**
     * 修改监控类型
     */
    @PreAuthorize("@ss.hasPermi('shcy:monitoringTypes:edit')")
    @Log(title = "监控类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonitoringTypes monitoringTypes)
    {
        return toAjax(monitoringTypesService.updateMonitoringTypes(monitoringTypes));
    }

    /**
     * 删除监控类型
     */
    @PreAuthorize("@ss.hasPermi('shcy:monitoringTypes:remove')")
    @Log(title = "监控类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{monitoringTypeIds}")
    public AjaxResult remove(@PathVariable Long[] monitoringTypeIds)
    {
        return toAjax(monitoringTypesService.deleteMonitoringTypesByMonitoringTypeIds(monitoringTypeIds));
    }
}
