package com.ruoyi.icc.vo;

import lombok.Data;

@Data
public class RecordVO {

    /**
     * 通道编码
     */
    private String channelId;

    /**
     * 录像来源：1=全部，2=设备，3=中心
     */
    private String recordSource;

    /**
     * 录像类型：0=全部，1=手动录像，2=报警录像，6=定时录像（普通录像）
     */
    private String recordType;

    /**
     * 起始时间(时间戳：单位秒)
     */
    private String startTime;

    /**
     * 结束时间(时间戳：单位秒)
     */
    private String endTime;

    /**
     * 录像名字(不同厂家对文件的标识不同)
     */
    private String recordName;

    /**
     * 文件长度，单位KB
     */
    private String fileLength;

    /**
     * 录像计划ID
     */
    private String planId;

    /**
     * 存储服务ID
     */
    private String ssId;

    /**
     * 磁盘ID
     */
    private String diskId;

    /**
     * 码流处理(StreamId)
     */
    private String streamId;

    /**
     * 是否淡忘
     */
    private String forgotten;

    private String streamType;

    private String videoRecordType;



}
