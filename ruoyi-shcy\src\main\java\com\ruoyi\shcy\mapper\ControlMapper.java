package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.Control;

/**
 * 监控点Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-07-26
 */
public interface ControlMapper 
{
    /**
     * 查询监控点
     * 
     * @param controlId 监控点主键
     * @return 监控点
     */
    public Control selectControlByControlId(Long controlId);

    /**
     * 查询监控点列表
     * 
     * @param control 监控点
     * @return 监控点集合
     */
    public List<Control> selectControlList(Control control);

    /**
     * 新增监控点
     * 
     * @param control 监控点
     * @return 结果
     */
    public int insertControl(Control control);

    /**
     * 修改监控点
     * 
     * @param control 监控点
     * @return 结果
     */
    public int updateControl(Control control);

    /**
     * 删除监控点
     * 
     * @param controlId 监控点主键
     * @return 结果
     */
    public int deleteControlByControlId(Long controlId);

    /**
     * 批量删除监控点
     * 
     * @param controlIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteControlByControlIds(Long[] controlIds);
}
