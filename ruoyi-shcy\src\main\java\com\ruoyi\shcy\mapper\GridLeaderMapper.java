package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.GridLeader;

/**
 * 网格联系领导Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface GridLeaderMapper 
{
    /**
     * 查询网格联系领导
     * 
     * @param id 网格联系领导主键
     * @return 网格联系领导
     */
    public GridLeader selectGridLeaderById(Long id);

    /**
     * 查询网格联系领导列表
     * 
     * @param gridLeader 网格联系领导
     * @return 网格联系领导集合
     */
    public List<GridLeader> selectGridLeaderList(GridLeader gridLeader);

    /**
     * 新增网格联系领导
     * 
     * @param gridLeader 网格联系领导
     * @return 结果
     */
    public int insertGridLeader(GridLeader gridLeader);

    /**
     * 修改网格联系领导
     * 
     * @param gridLeader 网格联系领导
     * @return 结果
     */
    public int updateGridLeader(GridLeader gridLeader);

    /**
     * 删除网格联系领导
     * 
     * @param id 网格联系领导主键
     * @return 结果
     */
    public int deleteGridLeaderById(Long id);

    /**
     * 批量删除网格联系领导
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGridLeaderByIds(Long[] ids);
}
