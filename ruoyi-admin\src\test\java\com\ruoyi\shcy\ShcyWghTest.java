package com.ruoyi.shcy;

import com.ruoyi.shcy.domain.ShcyWgh;
import com.ruoyi.shcy.service.IShcyWghService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class ShcyWghTest {

    @Autowired
    private IShcyWghService shcyWghService;

    @Test
    public void contextLoads() {
    }

    @Test
    public void testSelectShcyWghByTaskDbcenterId() {

        ShcyWgh shcyWgh = shcyWghService.selectShcyWghByTaskDbcenterId("2404G1079283");
        System.out.println("shcyWgh -------- " + shcyWgh);

    }

}
