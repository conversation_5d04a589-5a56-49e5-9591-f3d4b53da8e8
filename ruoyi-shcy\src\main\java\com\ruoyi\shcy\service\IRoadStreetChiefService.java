package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.RoadStreetChief;

/**
 * 街长路名Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-23
 */
public interface IRoadStreetChiefService 
{
    /**
     * 查询街长路名
     * 
     * @param id 街长路名主键
     * @return 街长路名
     */
    public RoadStreetChief selectRoadStreetChiefById(Long id);

    /**
     * 查询街长路名列表
     * 
     * @param roadStreetChief 街长路名
     * @return 街长路名集合
     */
    public List<RoadStreetChief> selectRoadStreetChiefList(RoadStreetChief roadStreetChief);

    /**
     * 新增街长路名
     * 
     * @param roadStreetChief 街长路名
     * @return 结果
     */
    public int insertRoadStreetChief(RoadStreetChief roadStreetChief);

    /**
     * 修改街长路名
     * 
     * @param roadStreetChief 街长路名
     * @return 结果
     */
    public int updateRoadStreetChief(RoadStreetChief roadStreetChief);

    /**
     * 批量删除街长路名
     * 
     * @param ids 需要删除的街长路名主键集合
     * @return 结果
     */
    public int deleteRoadStreetChiefByIds(Long[] ids);

    /**
     * 删除街长路名信息
     * 
     * @param id 街长路名主键
     * @return 结果
     */
    public int deleteRoadStreetChiefById(Long id);
}
