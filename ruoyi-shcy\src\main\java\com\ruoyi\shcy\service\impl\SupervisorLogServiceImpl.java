package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.SupervisorLogMapper;
import com.ruoyi.shcy.domain.SupervisorLog;
import com.ruoyi.shcy.service.ISupervisorLogService;

/**
 * 重点监管列Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-17
 */
@Service
public class SupervisorLogServiceImpl implements ISupervisorLogService
{
    @Autowired
    private SupervisorLogMapper supervisorLogMapper;

    /**
     * 查询重点监管列
     *
     * @param id 重点监管列主键
     * @return 重点监管列
     */
    @Override
    public SupervisorLog selectSupervisorLogById(Long id)
    {
        return supervisorLogMapper.selectSupervisorLogById(id);
    }


    /**
     * 根据id查询最近三次重点监管记录
     *
     * @param id
     **/
    @Override
    public List<SupervisorLog> selectSupervisorLogByIdThree(Long id) {
        return  supervisorLogMapper.selectSupervisorLogByIdThree(id);
    }

    /**
     * 查询重点监管列列表
     *
     * @param supervisorLog 重点监管列
     * @return 重点监管列
     */
    @Override
    public List<SupervisorLog> selectSupervisorLogList(SupervisorLog supervisorLog)
    {
        return supervisorLogMapper.selectSupervisorLogList(supervisorLog);
    }

    /**
     * 新增重点监管列
     *
     * @param supervisorLog 重点监管列
     * @return 结果
     */
    @Override
    public int insertSupervisorLog(SupervisorLog supervisorLog)
    {
        supervisorLog.setCreateTime(DateUtils.getNowDate());
        return supervisorLogMapper.insertSupervisorLog(supervisorLog);
    }

    /**
     * 修改重点监管列
     *
     * @param supervisorLog 重点监管列
     * @return 结果
     */
    @Override
    public int updateSupervisorLog(SupervisorLog supervisorLog)
    {
        supervisorLog.setUpdateTime(DateUtils.getNowDate());
        return supervisorLogMapper.updateSupervisorLog(supervisorLog);
    }

    /**
     * 批量删除重点监管列
     *
     * @param ids 需要删除的重点监管列主键
     * @return 结果
     */
    @Override
    public int deleteSupervisorLogByIds(Long[] ids)
    {
        return supervisorLogMapper.deleteSupervisorLogByIds(ids);
    }

    /**
     * 删除重点监管列信息
     *
     * @param id 重点监管列主键
     * @return 结果
     */
    @Override
    public int deleteSupervisorLogById(Long id)
    {
        return supervisorLogMapper.deleteSupervisorLogById(id);
    }
}
