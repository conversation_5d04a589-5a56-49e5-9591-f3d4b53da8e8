package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.SpecialTopic;
import com.ruoyi.shcy.service.ISpecialTopicService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 重点专项Controller
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@RestController
@RequestMapping("/shcy/specialTopic")
public class SpecialTopicController extends BaseController
{
    @Autowired
    private ISpecialTopicService specialTopicService;

    /**
     * 查询重点专项列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTopic:list')")
    @GetMapping("/list")
    public TableDataInfo list(SpecialTopic specialTopic)
    {
        startPage();
        List<SpecialTopic> list = specialTopicService.selectSpecialTopicList(specialTopic);
        return getDataTable(list);
    }

    /**
     * 导出重点专项列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTopic:export')")
    @Log(title = "重点专项", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SpecialTopic specialTopic)
    {
        List<SpecialTopic> list = specialTopicService.selectSpecialTopicList(specialTopic);
        ExcelUtil<SpecialTopic> util = new ExcelUtil<SpecialTopic>(SpecialTopic.class);
        util.exportExcel(response, list, "重点专项数据");
    }

    /**
     * 获取重点专项详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTopic:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(specialTopicService.selectSpecialTopicById(id));
    }

    /**
     * 新增重点专项
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTopic:add')")
    @Log(title = "重点专项", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SpecialTopic specialTopic)
    {
        return toAjax(specialTopicService.insertSpecialTopic(specialTopic));
    }

    /**
     * 修改重点专项
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTopic:edit')")
    @Log(title = "重点专项", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SpecialTopic specialTopic)
    {
        return toAjax(specialTopicService.updateSpecialTopic(specialTopic));
    }

    /**
     * 删除重点专项
     */
    @PreAuthorize("@ss.hasPermi('shcy:specialTopic:remove')")
    @Log(title = "重点专项", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(specialTopicService.deleteSpecialTopicByIds(ids));
    }
}
