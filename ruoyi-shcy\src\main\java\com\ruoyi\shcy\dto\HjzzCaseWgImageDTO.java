package com.ruoyi.shcy.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

@Data
public class HjzzCaseWgImageDTO {

    @Excel(name = "事件编号")
    private String caseNumber;

    @Excel(name = "事件地点")
    private String address;

    @Excel(name = "车牌信息")
    private String licensePlate;

    @Excel(name = "车辆类型")
    private String vehicleType;

    @Excel(name = "事件类型", readConverterExp = "1=偷倒垃圾,2=违规生产入侵")
    private String caseType;

    @Excel(name = "告警时间")
    private String alarmDate;

    @Excel(name = "抓拍照片", width = 25, height = 80, cellType = Excel.ColumnType.IMAGE)
    private String image;

    private Long alarmRecordId;

}
