package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 绿化信息对象 shcy_afforest
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
public class ShcyAfforest extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 绿化编号
     */
    @Excel(name = "绿化编号")
    private String afforestId;

    /**
     * 地图类型
     */
    @Excel(name = "地图类型")
    private String type;

    /**
     * 地图位置
     */
    @Excel(name = "地图位置")
    private String coordinate;

    /**
     * 责任区域
     */
    @Excel(name = "责任区域")
    private String responsibilityArea;

    /**
     * 街道
     */
    @Excel(name = "街道")
    private String street;

    /**
     * 管理单位
     */
    @Excel(name = "管理单位")
    private String managementDept;

    /**
     * 养护单位
     */
    @Excel(name = "养护单位")
    private String maintenanceDept;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String contactPhone;

    /**
     * 街道林长
     */
    @Excel(name = "街道林长")
    private String streetArborist;

    /**
     * 居民区林长
     */
    @Excel(name = "居民区林长")
    private String residentialArborist;

    /**
     * 绿化类型
     */
    @Excel(name = "绿化类型")
    private String greeneryType;

    /**
     * 小区名称
     */
    @Excel(name = "小区名称")
    private String communityName;

    /**
     * 小区经理姓名
     */
    @Excel(name = "小区经理姓名")
    private String managerName;

    /**
     * 物业办公电话
     */
    @Excel(name = "物业办公电话")
    private String officePhone;

    /**
     * 小区经理手机
     */
    @Excel(name = "小区经理手机")
    private String managerMobile;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setAfforestId(String afforestId) {
        this.afforestId = afforestId;
    }

    public String getAfforestId() {
        return afforestId;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getStreet() {
        return street;
    }

    public void setManagementDept(String managementDept) {
        this.managementDept = managementDept;
    }

    public String getManagementDept() {
        return managementDept;
    }

    public void setMaintenanceDept(String maintenanceDept) {
        this.maintenanceDept = maintenanceDept;
    }

    public String getMaintenanceDept() {
        return maintenanceDept;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public String getResponsibilityArea() {
        return responsibilityArea;
    }

    public void setResponsibilityArea(String responsibilityArea) {
        this.responsibilityArea = responsibilityArea;
    }

    public void setStreetArborist(String streetArborist) {
        this.streetArborist = streetArborist;
    }

    public String getStreetArborist() {
        return streetArborist;
    }

    public void setResidentialArborist(String residentialArborist) {
        this.residentialArborist = residentialArborist;
    }

    public String getResidentialArborist() {
        return residentialArborist;
    }

    public void setGreeneryType(String greeneryType) {
        this.greeneryType = greeneryType;
    }

    public String getGreeneryType() {
        return greeneryType;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setManagerName(String managerName) {
        this.managerName = managerName;
    }

    public String getManagerName() {
        return managerName;
    }

    public void setOfficePhone(String officePhone) {
        this.officePhone = officePhone;
    }

    public String getOfficePhone() {
        return officePhone;
    }

    public void setManagerMobile(String managerMobile) {
        this.managerMobile = managerMobile;
    }

    public String getManagerMobile() {
        return managerMobile;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("afforestId", getAfforestId())
                .append("type", getType())
                .append("coordinate", getCoordinate())
                .append("street", getStreet())
                .append("managementDept", getManagementDept())
                .append("maintenanceDept", getMaintenanceDept())
                .append("contactPhone", getContactPhone())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("streetArborist", getStreetArborist())
                .append("residentialArborist", getResidentialArborist())
                .append("greeneryType", getGreeneryType())
                .append("communityName", getCommunityName())
                .append("managerName", getManagerName())
                .append("officePhone", getOfficePhone())
                .append("managerMobile", getManagerMobile())
                .toString();
    }
}
