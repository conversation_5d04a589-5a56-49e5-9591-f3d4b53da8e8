package com.ruoyi.quartz.task;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.service.IShopService;
import com.ruoyi.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/11/1 8:10
 * @Version 1.0
 */

@Component("shopTask")
public class ShopTaskController {


    @Autowired
    private IShopService shopService;

    @Autowired
    private ISysDeptService sysDeptService;

    /** 居委会店铺数量小于 50  实行一天一检 */
     public void oneDayCheck(){
         SysDept sysDept = new SysDept();
         List<SysDept> sysDepts = sysDeptService.selectAllCommittee(sysDept);
         for (SysDept dept : sysDepts) {
             Shop shop = new Shop();
             shop.setShopDeparmentId(dept.getDeptId());
             List<Shop> committeeShop = shopService.selectCommitteeShopNum(shop);
             if(committeeShop.size()!=0){
                if(Integer.valueOf(committeeShop.get(0).getShopNum())<50){
                    Long shopDepartmentId = dept.getDeptId();
                    int i = shopService.updateShopCheckStatus(shopDepartmentId);
                    System.out.println("店铺的状态修改了");
                }
             }

         }

     }


     /**  月头修改状态   **/
     public void oneMonthCheck(){
         Shop shop = new Shop();
         List<Shop> shopList = shopService.selectShopList(shop);
         if(shopList.size()!=0){
             for (Shop shops : shopList) {
                 int i = shopService.updateCheckStatus(shops.getId());
                 System.out.println(i+"店铺的状态修改了");
             }

         }
     }

     /** 每月15号修改状态  **/
     public void MonthCenterCheck(){
         Shop shop = new Shop();
         List<Shop> shopList = shopService.selectShopList(shop);
         if(shopList.size()!=0){
             for (Shop shops : shopList) {
                 int i = shopService.updateCheckStatus(shops.getId());
                 System.out.println(i+"店铺的状态修改了");
             }

         }
     }


    /** 居委会店铺数量大于等于 50，小于100  实行两天一检 */
     public void twoDaysCheck(){
         SysDept sysDept = new SysDept();
         List<SysDept> sysDepts = sysDeptService.selectAllCommittee(sysDept);
         for (SysDept dept : sysDepts) {
             Shop shop = new Shop();
             shop.setShopDeparmentId(dept.getDeptId());
             List<Shop> committeeShop = shopService.selectCommitteeShopNum(shop);
             if(committeeShop.size()!=0){
                 Integer shopNum = Integer.valueOf(committeeShop.get(0).getShopNum());
                 if(50<=shopNum&&shopNum<100){
                     Long shopDepartmentId = dept.getDeptId();
                     int i = shopService.updateShopCheckStatus(shopDepartmentId);
                     System.out.println("店铺的状态修改了");
                 }
             }

         }
     }

    /** 居委会店铺数量大于等于100  实行三天一检 */
    public void threeDaysCheck(){
        SysDept sysDept = new SysDept();
        List<SysDept> sysDepts = sysDeptService.selectAllCommittee(sysDept);
        for (SysDept dept : sysDepts) {
            Shop shop = new Shop();
            shop.setShopDeparmentId(dept.getDeptId());
            List<Shop> committeeShop = shopService.selectCommitteeShopNum(shop);
            if(committeeShop.size()!=0){
                Integer shopNum = Integer.valueOf(committeeShop.get(0).getShopNum());
                if(shopNum>=100){
                    Long shopDepartmentId = dept.getDeptId();
                    int i = shopService.updateShopCheckStatus(shopDepartmentId);
                    System.out.println("店铺的状态修改了");
                }
            }

        }
    }

}
