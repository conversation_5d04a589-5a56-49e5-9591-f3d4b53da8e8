package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.ShcyAfforestCase;
import com.ruoyi.shcy.domain.ShcyCase;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.service.IShcyAfforestCaseService;
import com.ruoyi.shcy.service.IShcyFileInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 绿化案事件Controller
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@RestController
@RequestMapping("/shcy/afforestCase")
public class ShcyAfforestCaseController extends BaseController
{
    @Autowired
    private IShcyAfforestCaseService shcyAfforestCaseService;


    @Autowired
    private IShcyFileInfoService shcyFileInfoService;

    /**
     * 查询绿化案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforestCase:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyAfforestCase shcyAfforestCase)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();
        Long roleId = user.getRoleId();
        String username = user.getUserName();
        String roleName  =  user.getRoles().get(0).getRoleName();
        if(Objects.equals(roleName, "林长处置人")){
            if(Objects.equals(username, "管养单位")){
                shcyAfforestCase.setDealDept(username);
                startPage();
                List<ShcyAfforestCase> list = shcyAfforestCaseService.selectShcyAfforestCaseList(shcyAfforestCase);
                return getDataTable(list);
            }
            else if(Objects.equals(username, "物业公司")){
                shcyAfforestCase.setDealDept(username);
                startPage();
                List<ShcyAfforestCase> list = shcyAfforestCaseService.selectShcyAfforestCaseList(shcyAfforestCase);
                return getDataTable(list);
            }
        }
        if(Objects.equals(roleName, "超级管理员")){
            startPage();
            List<ShcyAfforestCase> list = shcyAfforestCaseService.selectShcyAfforestCaseList(shcyAfforestCase);
            return getDataTable(list);
        }
        if(Objects.equals(roleName,"建管委")){
            startPage();
            List<ShcyAfforestCase> list = shcyAfforestCaseService.selectShcyAfforestCaseList(shcyAfforestCase);
            return getDataTable(list);
        }
        startPage();
        List<ShcyCase> list = new ArrayList<>();
        return getDataTable(list);
    }

    /**
     * 导出绿化案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforestCase:export')")
    @Log(title = "绿化案事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyAfforestCase shcyAfforestCase)
    {
        List<ShcyAfforestCase> list = shcyAfforestCaseService.selectShcyAfforestCaseList(shcyAfforestCase);
        ExcelUtil<ShcyAfforestCase> util = new ExcelUtil<ShcyAfforestCase>(ShcyAfforestCase.class);
        util.exportExcel(response, list, "绿化案事件数据");
    }

    /**
     * 获取绿化案事件详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforestCase:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        ShcyAfforestCase shcyAfforestCase = shcyAfforestCaseService.selectShcyAfforestCaseById(id);

        if(shcyAfforestCase !=null){
            if(StringUtils.isNotEmpty(shcyAfforestCase.getAppendix())){
                String[] ids  = shcyAfforestCase.getAppendix().split(",");
                if(ids.length!=0){
                    List<String> imgurls = new ArrayList<>();
                    for (String imageid : ids) {
                        ShcyFileInfo shcyFileInfo = shcyFileInfoService.selectShcyFileInfoByFileId(Long.valueOf(imageid));
                        imgurls.add(shcyFileInfo.getFilePath());
                    }
                    shcyAfforestCase.setPhotoUrls(imgurls);

                }
            }

            if(StringUtils.isNotEmpty(shcyAfforestCase.getDealPhoto())){
                String[] ids  = shcyAfforestCase.getDealPhoto().split(",");
                if(ids.length!=0){
                    List<String> imgurls = new ArrayList<>();
                    for (String imageid : ids) {
                        ShcyFileInfo shcyFileInfo = shcyFileInfoService.selectShcyFileInfoByFileId(Long.valueOf(imageid));
                        imgurls.add(shcyFileInfo.getFilePath());
                    }
                    shcyAfforestCase.setDealPhotoUrls(imgurls);
                }
            }

        }
        return AjaxResult.success(shcyAfforestCase);
    }

    /**
     * 新增绿化案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforestCase:add')")
    @Log(title = "绿化案事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyAfforestCase shcyAfforestCase){

        int i = shcyAfforestCaseService.insertShcyAfforestCase(shcyAfforestCase);

        return AjaxResult.success("新增成功");
    }


    /**
     * 修改绿化案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforestCase:edit')")
    @Log(title = "绿化案事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyAfforestCase shcyAfforestCase)
    {
        Date finishDate = DateUtils.getNowDate();
        shcyAfforestCase.setDealFinishTime(finishDate);

        // int sign  = shcyAfforestCase.getDealTimeLimited().compareTo(finishDate);
        // if(sign>0){
        //     shcyAfforestCase.setDealInTimeState("0");
        // }else{
        //     shcyAfforestCase.setDealInTimeState("1");
        // }
        shcyAfforestCase.setDealStatus("0");
        int i = shcyAfforestCaseService.updateShcyAfforestCase(shcyAfforestCase);
        Long forestId =shcyAfforestCase.getForestId();
        Date time = DateUtils.getNowDate();

        return AjaxResult.success("新增成功！");
    }

    /**
     * 绿化案事件选择处理人
     * **/
    @Log(title = "选择处理人", businessType = BusinessType.UPDATE)
    @PostMapping(value="/chose")
    public AjaxResult editChoseDealPerson(@RequestBody ShcyAfforestCase shcyAfforestCase){
        shcyAfforestCase.setDealStatus("1");
        return toAjax(shcyAfforestCaseService.updateShcyAfforestCase(shcyAfforestCase));
    }

    /**
     * 删除绿化案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforestCase:remove')")
    @Log(title = "绿化案事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyAfforestCaseService.deleteShcyAfforestCaseByIds(ids));
    }

    /**
     * 林长处置人退单操作（林长管理员需要重新选择处置人）
     * **/
    @Log(title = "绿化案事件", businessType = BusinessType.UPDATE)
    @PostMapping(value="/return/{id}")
    public AjaxResult returnChosePerson(@PathVariable Long id)
    {
        return toAjax(shcyAfforestCaseService.returnShcyAffroestCaseById(id));
    }

}
