package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.NonPropertyContact;

/**
 * 非住宅物业联系表Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-31
 */
public interface NonPropertyContactMapper
{
    /**
     * 查询非住宅物业联系表
     *
     * @param id 非住宅物业联系表主键
     * @return 非住宅物业联系表
     */
    public NonPropertyContact selectNonPropertyContactById(Long id);

    /**
     * 查询非住宅物业联系表列表
     *
     * @param nonPropertyContact 非住宅物业联系表
     * @return 非住宅物业联系表集合
     */
    public List<NonPropertyContact> selectNonPropertyContactList(NonPropertyContact nonPropertyContact);

    /**
     * 新增非住宅物业联系表
     *
     * @param nonPropertyContact 非住宅物业联系表
     * @return 结果
     */
    public int insertNonPropertyContact(NonPropertyContact nonPropertyContact);

    /**
     * 修改非住宅物业联系表
     *
     * @param nonPropertyContact 非住宅物业联系表
     * @return 结果
     */
    public int updateNonPropertyContact(NonPropertyContact nonPropertyContact);

    /**
     * 删除非住宅物业联系表
     *
     * @param id 非住宅物业联系表主键
     * @return 结果
     */
    public int deleteNonPropertyContactById(Long id);

    /**
     * 批量删除非住宅物业联系表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNonPropertyContactByIds(Long[] ids);
}
