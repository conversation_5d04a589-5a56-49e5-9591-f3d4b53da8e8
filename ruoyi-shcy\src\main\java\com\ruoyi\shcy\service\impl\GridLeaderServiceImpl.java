package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.GridLeaderMapper;
import com.ruoyi.shcy.domain.GridLeader;
import com.ruoyi.shcy.service.IGridLeaderService;

/**
 * 网格联系领导Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class GridLeaderServiceImpl implements IGridLeaderService 
{
    @Autowired
    private GridLeaderMapper gridLeaderMapper;

    /**
     * 查询网格联系领导
     * 
     * @param id 网格联系领导主键
     * @return 网格联系领导
     */
    @Override
    public GridLeader selectGridLeaderById(Long id)
    {
        return gridLeaderMapper.selectGridLeaderById(id);
    }

    /**
     * 查询网格联系领导列表
     * 
     * @param gridLeader 网格联系领导
     * @return 网格联系领导
     */
    @Override
    public List<GridLeader> selectGridLeaderList(GridLeader gridLeader)
    {
        return gridLeaderMapper.selectGridLeaderList(gridLeader);
    }

    /**
     * 新增网格联系领导
     * 
     * @param gridLeader 网格联系领导
     * @return 结果
     */
    @Override
    public int insertGridLeader(GridLeader gridLeader)
    {
        return gridLeaderMapper.insertGridLeader(gridLeader);
    }

    /**
     * 修改网格联系领导
     * 
     * @param gridLeader 网格联系领导
     * @return 结果
     */
    @Override
    public int updateGridLeader(GridLeader gridLeader)
    {
        return gridLeaderMapper.updateGridLeader(gridLeader);
    }

    /**
     * 批量删除网格联系领导
     * 
     * @param ids 需要删除的网格联系领导主键
     * @return 结果
     */
    @Override
    public int deleteGridLeaderByIds(Long[] ids)
    {
        return gridLeaderMapper.deleteGridLeaderByIds(ids);
    }

    /**
     * 删除网格联系领导信息
     * 
     * @param id 网格联系领导主键
     * @return 结果
     */
    @Override
    public int deleteGridLeaderById(Long id)
    {
        return gridLeaderMapper.deleteGridLeaderById(id);
    }
}
