package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.IccAlarmRecord;
import com.ruoyi.shcy.dto.IccAlarmRecordHandleDTO;

import java.util.List;

/**
 * ICC报警记录Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface IIccAlarmRecordService 
{
    /**
     * 查询ICC报警记录
     * 
     * @param id ICC报警记录主键
     * @return ICC报警记录
     */
    public IccAlarmRecord selectIccAlarmRecordById(Long id);

    /**
     * 查询ICC报警记录列表
     * 
     * @param iccAlarmRecord ICC报警记录
     * @return ICC报警记录集合
     */
    public List<IccAlarmRecord> selectIccAlarmRecordList(IccAlarmRecord iccAlarmRecord);

    /**
     * 新增ICC报警记录
     * 
     * @param iccAlarmRecord ICC报警记录
     * @return 结果
     */
    public int insertIccAlarmRecord(IccAlarmRecord iccAlarmRecord);

    /**
     * 修改ICC报警记录
     * 
     * @param iccAlarmRecord ICC报警记录
     * @return 结果
     */
    public int updateIccAlarmRecord(IccAlarmRecord iccAlarmRecord);

    /**
     * 批量删除ICC报警记录
     * 
     * @param ids 需要删除的ICC报警记录主键集合
     * @return 结果
     */
    public int deleteIccAlarmRecordByIds(Long[] ids);

    /**
     * 删除ICC报警记录信息
     * 
     * @param id ICC报警记录主键
     * @return 结果
     */
    public int deleteIccAlarmRecordById(Long id);

    List<IccAlarmRecord> selectIccAlarmRecordUnprocessedList(IccAlarmRecord iccAlarmRecord);

    List<IccAlarmRecord> selectIccAlarmRecordProcessingList(IccAlarmRecord iccAlarmRecord);

    List<IccAlarmRecord> selectIccAlarmRecordProcessedList(IccAlarmRecord iccAlarmRecord);

    int hjzzHandle(IccAlarmRecordHandleDTO iccAlarmRecordHandleDTO);

    List<IccAlarmRecord> selectIccAlarmRecordUnprocessedAndProcessingList(IccAlarmRecord iccAlarmRecord);

    IccAlarmRecord selectIccAlarmRecordByCarRecordId(Long carRecordId);

    IccAlarmRecord selectIccAlarmRecordByAlarmRecordId(Long alarmRecordId);

    List<IccAlarmRecord> selectIccAlarmRecordByIds(Long[] alarmRecordIds);

}
