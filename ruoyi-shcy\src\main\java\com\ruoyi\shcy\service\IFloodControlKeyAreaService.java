package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.FloodControlKeyArea;

/**
 * 防汛防台重点区域Service接口
 * 
 * <AUTHOR>
 * @date 2023-09-22
 */
public interface IFloodControlKeyAreaService 
{
    /**
     * 查询防汛防台重点区域
     * 
     * @param id 防汛防台重点区域主键
     * @return 防汛防台重点区域
     */
    public FloodControlKeyArea selectFloodControlKeyAreaById(Long id);

    /**
     * 查询防汛防台重点区域列表
     * 
     * @param floodControlKeyArea 防汛防台重点区域
     * @return 防汛防台重点区域集合
     */
    public List<FloodControlKeyArea> selectFloodControlKeyAreaList(FloodControlKeyArea floodControlKeyArea);

    /**
     * 新增防汛防台重点区域
     * 
     * @param floodControlKeyArea 防汛防台重点区域
     * @return 结果
     */
    public int insertFloodControlKeyArea(FloodControlKeyArea floodControlKeyArea);

    /**
     * 修改防汛防台重点区域
     * 
     * @param floodControlKeyArea 防汛防台重点区域
     * @return 结果
     */
    public int updateFloodControlKeyArea(FloodControlKeyArea floodControlKeyArea);

    /**
     * 批量删除防汛防台重点区域
     * 
     * @param ids 需要删除的防汛防台重点区域主键集合
     * @return 结果
     */
    public int deleteFloodControlKeyAreaByIds(Long[] ids);

    /**
     * 删除防汛防台重点区域信息
     * 
     * @param id 防汛防台重点区域主键
     * @return 结果
     */
    public int deleteFloodControlKeyAreaById(Long id);
}
