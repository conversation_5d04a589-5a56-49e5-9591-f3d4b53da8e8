package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.NonPropertyContactMapper;
import com.ruoyi.shcy.domain.NonPropertyContact;
import com.ruoyi.shcy.service.INonPropertyContactService;

/**
 * 非住宅物业联系表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-01-31
 */
@Service
public class NonPropertyContactServiceImpl implements INonPropertyContactService 
{
    @Autowired
    private NonPropertyContactMapper nonPropertyContactMapper;

    /**
     * 查询非住宅物业联系表
     * 
     * @param id 非住宅物业联系表主键
     * @return 非住宅物业联系表
     */
    @Override
    public NonPropertyContact selectNonPropertyContactById(Long id)
    {
        return nonPropertyContactMapper.selectNonPropertyContactById(id);
    }

    /**
     * 查询非住宅物业联系表列表
     * 
     * @param nonPropertyContact 非住宅物业联系表
     * @return 非住宅物业联系表
     */
    @Override
    public List<NonPropertyContact> selectNonPropertyContactList(NonPropertyContact nonPropertyContact)
    {
        return nonPropertyContactMapper.selectNonPropertyContactList(nonPropertyContact);
    }

    /**
     * 新增非住宅物业联系表
     * 
     * @param nonPropertyContact 非住宅物业联系表
     * @return 结果
     */
    @Override
    public int insertNonPropertyContact(NonPropertyContact nonPropertyContact)
    {
        nonPropertyContact.setCreateTime(DateUtils.getNowDate());
        return nonPropertyContactMapper.insertNonPropertyContact(nonPropertyContact);
    }

    /**
     * 修改非住宅物业联系表
     * 
     * @param nonPropertyContact 非住宅物业联系表
     * @return 结果
     */
    @Override
    public int updateNonPropertyContact(NonPropertyContact nonPropertyContact)
    {
        nonPropertyContact.setUpdateTime(DateUtils.getNowDate());
        return nonPropertyContactMapper.updateNonPropertyContact(nonPropertyContact);
    }

    /**
     * 批量删除非住宅物业联系表
     * 
     * @param ids 需要删除的非住宅物业联系表主键
     * @return 结果
     */
    @Override
    public int deleteNonPropertyContactByIds(Long[] ids)
    {
        return nonPropertyContactMapper.deleteNonPropertyContactByIds(ids);
    }

    /**
     * 删除非住宅物业联系表信息
     * 
     * @param id 非住宅物业联系表主键
     * @return 结果
     */
    @Override
    public int deleteNonPropertyContactById(Long id)
    {
        return nonPropertyContactMapper.deleteNonPropertyContactById(id);
    }
}
