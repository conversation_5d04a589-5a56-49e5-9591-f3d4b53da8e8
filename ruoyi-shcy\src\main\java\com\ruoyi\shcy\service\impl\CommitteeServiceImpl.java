package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.CommitteeMapper;
import com.ruoyi.shcy.domain.Committee;
import com.ruoyi.shcy.service.ICommitteeService;

/**
 * 居委会信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class CommitteeServiceImpl implements ICommitteeService 
{
    @Autowired
    private CommitteeMapper committeeMapper;

    /**
     * 查询居委会信息
     * 
     * @param id 居委会信息主键
     * @return 居委会信息
     */
    @Override
    public Committee selectCommitteeById(Long id)
    {
        return committeeMapper.selectCommitteeById(id);
    }

    /**
     * 查询居委会信息列表
     * 
     * @param committee 居委会信息
     * @return 居委会信息
     */
    @Override
    public List<Committee> selectCommitteeList(Committee committee)
    {
        return committeeMapper.selectCommitteeList(committee);
    }

    /**
     * 新增居委会信息
     * 
     * @param committee 居委会信息
     * @return 结果
     */
    @Override
    public int insertCommittee(Committee committee)
    {
        committee.setCreateTime(DateUtils.getNowDate());
        return committeeMapper.insertCommittee(committee);
    }

    /**
     * 修改居委会信息
     * 
     * @param committee 居委会信息
     * @return 结果
     */
    @Override
    public int updateCommittee(Committee committee)
    {
        committee.setUpdateTime(DateUtils.getNowDate());
        return committeeMapper.updateCommittee(committee);
    }

    /**
     * 批量删除居委会信息
     * 
     * @param ids 需要删除的居委会信息主键
     * @return 结果
     */
    @Override
    public int deleteCommitteeByIds(Long[] ids)
    {
        return committeeMapper.deleteCommitteeByIds(ids);
    }

    /**
     * 删除居委会信息信息
     * 
     * @param id 居委会信息主键
     * @return 结果
     */
    @Override
    public int deleteCommitteeById(Long id)
    {
        return committeeMapper.deleteCommitteeById(id);
    }
}
