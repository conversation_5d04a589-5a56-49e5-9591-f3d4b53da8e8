package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.Vehicle;

import java.util.List;

/**
 * 过车记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-01
 */
public interface IVehicleService 
{
    /**
     * 查询过车记录
     * 
     * @param id 过车记录主键
     * @return 过车记录
     */
    public Vehicle selectVehicleById(Long id);

    /**
     * 查询过车记录列表
     * 
     * @param vehicle 过车记录
     * @return 过车记录集合
     */
    public List<Vehicle> selectVehicleList(Vehicle vehicle);

    /**
     * 新增过车记录
     * 
     * @param vehicle 过车记录
     * @return 结果
     */
    public int insertVehicle(Vehicle vehicle);

    /**
     * 修改过车记录
     * 
     * @param vehicle 过车记录
     * @return 结果
     */
    public int updateVehicle(Vehicle vehicle);

    /**
     * 批量删除过车记录
     * 
     * @param ids 需要删除的过车记录主键集合
     * @return 结果
     */
    public int deleteVehicleByIds(Long[] ids);

    /**
     * 删除过车记录信息
     * 
     * @param id 过车记录主键
     * @return 结果
     */
    public int deleteVehicleById(Long id);
}
