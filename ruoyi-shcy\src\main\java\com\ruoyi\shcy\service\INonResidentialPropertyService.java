package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.NonResidentialProperty;

/**
 * 物业管理处Service接口
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface INonResidentialPropertyService
{
    /**
     * 查询物业管理处
     *
     * @param id 物业管理处主键
     * @return 物业管理处
     */
    public NonResidentialProperty selectNonResidentialPropertyById(Long id);

    /**
     * 查询物业管理处列表
     *
     * @param nonResidentialProperty 物业管理处
     * @return 物业管理处集合
     */
    public List<NonResidentialProperty> selectNonResidentialPropertyList(NonResidentialProperty nonResidentialProperty);

    /**
     * 新增物业管理处
     *
     * @param nonResidentialProperty 物业管理处
     * @return 结果
     */
    public int insertNonResidentialProperty(NonResidentialProperty nonResidentialProperty);

    /**
     * 修改非住宅物业
     *
     * @param nonResidentialProperty 物业管理处
     * @return 结果
     */
    public int updateNonResidentialProperty(NonResidentialProperty nonResidentialProperty);

    /**
     * 批量删除非住宅物业
     *
     * @param ids 需要删除的物业管理处主键集合
     * @return 结果
     */
    public int deleteNonResidentialPropertyByIds(Long[] ids);

    /**
     * 删除物业管理处信息
     *
     * @param id 物业管理处主键
     * @return 结果
     */
    public int deleteNonResidentialPropertyById(Long id);
}
