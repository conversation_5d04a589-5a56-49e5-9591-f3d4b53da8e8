package com.ruoyi.icc.service;

import com.dahuatech.icc.exception.ClientException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.icc.vo.RecordVO;
import com.ruoyi.shcy.domain.IccAlarmRecord;

import java.util.List;

/**
 * icc服务
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
public interface IccService {

    /**
     * 获取访问令牌
     *
     * @return {@link String}
     * @throws ClientException 客户端异常
     */
    String getAccessToken() throws ClientException;

    /**
     * 报警记录分页查询
     *
     * @param pageNum              页码
     * @param pageSize             页面大小
     * @param alarmStartDateString 报警开始日期字符串
     * @param alarmEndDateString   报警结束日期字符串
     * @param alarmType            报警类型
     * @return {@link List}<{@link IccAlarmRecord}>
     * @throws ClientException 客户端异常
     */
    List<IccAlarmRecord> alarmRecordPage(int pageNum, int pageSize, String alarmStartDateString, String alarmEndDateString, int alarmType) throws ClientException;

    /**
     * 同步icc报警记录
     *
     * @param iccAlarmRecord icc报警记录
     */
    void syncIccAlarmRecord(IccAlarmRecord iccAlarmRecord);

    /**
     * 查询报警录像信息列表
     *
     * @param alarmCode 报警代码
     * @return {@link List}<{@link RecordVO}>
     */
    List<RecordVO> getAlarmRecords(String alarmCode) throws ClientException, JsonProcessingException;

    String replayByTime(String channelCode, String streamType, String recordSource, String recordType, String startTime, String endTime) throws ClientException;

    public String startVideo(String channelCode, String streamType) throws ClientException;
}
