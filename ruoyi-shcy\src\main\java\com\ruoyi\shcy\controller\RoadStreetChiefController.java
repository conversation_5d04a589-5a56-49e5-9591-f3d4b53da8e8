package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.RoadStreetChief;
import com.ruoyi.shcy.service.IRoadStreetChiefService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 街长路名Controller
 * 
 * <AUTHOR>
 * @date 2023-02-23
 */
@RestController
@RequestMapping("/shcy/chief")
public class RoadStreetChiefController extends BaseController
{
    @Autowired
    private IRoadStreetChiefService roadStreetChiefService;

    /**
     * 查询街长路名列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:chief:list')")
    @GetMapping("/list")
    public TableDataInfo list(RoadStreetChief roadStreetChief)
    {
        startPage();
        List<RoadStreetChief> list = roadStreetChiefService.selectRoadStreetChiefList(roadStreetChief);
        return getDataTable(list);
    }

    /**
     * 导出街长路名列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:chief:export')")
    @Log(title = "街长路名", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RoadStreetChief roadStreetChief)
    {
        List<RoadStreetChief> list = roadStreetChiefService.selectRoadStreetChiefList(roadStreetChief);
        ExcelUtil<RoadStreetChief> util = new ExcelUtil<RoadStreetChief>(RoadStreetChief.class);
        util.exportExcel(response, list, "街长路名数据");
    }

    /**
     * 获取街长路名详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:chief:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(roadStreetChiefService.selectRoadStreetChiefById(id));
    }

    /**
     * 新增街长路名
     */
    @PreAuthorize("@ss.hasPermi('shcy:chief:add')")
    @Log(title = "街长路名", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RoadStreetChief roadStreetChief)
    {
        return toAjax(roadStreetChiefService.insertRoadStreetChief(roadStreetChief));
    }

    /**
     * 修改街长路名
     */
    @PreAuthorize("@ss.hasPermi('shcy:chief:edit')")
    @Log(title = "街长路名", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RoadStreetChief roadStreetChief)
    {
        return toAjax(roadStreetChiefService.updateRoadStreetChief(roadStreetChief));
    }

    /**
     * 删除街长路名
     */
    @PreAuthorize("@ss.hasPermi('shcy:chief:remove')")
    @Log(title = "街长路名", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(roadStreetChiefService.deleteRoadStreetChiefByIds(ids));
    }
}
