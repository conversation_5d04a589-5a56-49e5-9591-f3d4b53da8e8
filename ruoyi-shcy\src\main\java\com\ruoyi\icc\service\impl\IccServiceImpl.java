package com.ruoyi.icc.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.dahuatech.hutool.http.Method;
import com.dahuatech.hutool.json.JSONUtil;
import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.http.DefaultClient;
import com.dahuatech.icc.oauth.http.IClient;
import com.dahuatech.icc.oauth.http.IccHttpHttpRequest;
import com.dahuatech.icc.oauth.http.IccTokenResponse;
import com.dahuatech.icc.oauth.model.v202010.GeneralRequest;
import com.dahuatech.icc.oauth.model.v202010.GeneralResponse;
import com.dahuatech.icc.oauth.profile.IccProfile;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.icc.vo.RecordVO;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import com.ruoyi.shcy.mapper.IccAlarmRecordMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * icc服务impl
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@Service
@AllArgsConstructor
@Slf4j
public class IccServiceImpl implements IccService {

    private final IccAlarmRecordMapper iccAlarmRecordMapper;

    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 获取访问令牌
     *
     * @return {@link String}
     * @throws ClientException 客户端异常
     */
    @Override
    public String getAccessToken() throws ClientException {
        IClient iClient = new DefaultClient();
        IccTokenResponse.IccToken token = iClient.getAccessToken();
        return token.getAccess_token();
    }

    /**
     * 报警记录分页查询
     *
     * @param pageNum              页码
     * @param pageSize             页面大小
     * @param alarmStartDateString 报警开始日期字符串
     * @param alarmEndDateString   报警结束日期字符串
     * @param alarmType            报警类型
     * @return {@link List}<{@link IccAlarmRecord}>
     * @throws ClientException 客户端异常
     */
    @Override
    public List<IccAlarmRecord> alarmRecordPage(int pageNum, int pageSize, String alarmStartDateString, String alarmEndDateString, int alarmType) throws ClientException {
        IClient iClient = new DefaultClient();
        GeneralRequest generalRequest =
                new GeneralRequest("/evo-apigw/evo-event/1.2.0/alarm-record/page", Method.POST);
        Map<String, Object> map = new HashMap<>();
        map.put("pageNum", pageNum);
        map.put("pageSize", pageSize);
        map.put("sort", "alarmDate");
        map.put("sortType", "DESC");
        // 不传时间参数，表示查询当天凌晨到当前时间的数据，格式：yyyy-MM-dd HH:mm:ss
        if (StrUtil.isNotEmpty(alarmStartDateString) && StrUtil.isNotEmpty(alarmEndDateString)) {
            map.put("alarmStartDateString", alarmStartDateString);
            map.put("alarmEndDateString", alarmEndDateString);
        }
        map.put("alarmType", alarmType);
        // 设置参数
        generalRequest.body(JSONUtil.toJsonStr(map));
        GeneralResponse response = iClient.doAction(generalRequest, generalRequest.getResponseClass());
        JSONObject obj = JSON.parseObject(response.getResult());
        JSONObject data = obj.getJSONObject("data");
        JSONArray pageDataList = data.getJSONArray("pageData");
        List<IccAlarmRecord> iccAlarmRecords = JSON.parseObject(pageDataList.toJSONString(), new TypeReference<List<IccAlarmRecord>>() {});
        // iccAlarmRecords 的alarmcode 去调用详情接口获取车牌 重新写入iccAlarmRecords
        for (IccAlarmRecord iccAlarmRecord : iccAlarmRecords) {
            String alarmDate = URLUtil.encode(iccAlarmRecord.getAlarmDate());
            String alarmCode = URLUtil.encode(iccAlarmRecord.getAlarmCode());
            String plate = alarmRecordQueryDetail(alarmCode, alarmDate);
            iccAlarmRecord.setLicensePlate(plate);
            if ("机动车违停".equals(iccAlarmRecord.getAlarmTypeName())) {
                iccAlarmRecord.setAlarmTypeName("偷倒垃圾");
            }
            // 判断iccAlarmRecord.getAlarmPosition() 是否包含环江路
            if (iccAlarmRecord.getAlarmPosition().contains("环江路")) {
                iccAlarmRecord.setAlarmPosition("环江路垃圾房");
            } else if (iccAlarmRecord.getAlarmPosition().contains("龙胜路")) {
                iccAlarmRecord.setAlarmPosition("龙胜路垃圾房");
            }
        }
        // 过滤掉车牌为空的数据
        List<IccAlarmRecord> filteredIccAlarmRecords = iccAlarmRecords.stream()
                .filter(record -> record.getLicensePlate() != null && !record.getLicensePlate().isEmpty())
                .collect(Collectors.toList());
        return filteredIccAlarmRecords;
    }

    public String alarmRecordQueryDetail(String alarmCode, String alarmDate) throws ClientException {
        // log.info("----开始执行----{}------", "报警记录详情查询");
        String plate = "";
        String Authorization="bearer "+getAccessToken();
        String ALARM_DETAIL_URL = IccProfile.URL_SCHEME + "/evo-apigw/evo-event/1.0.0/alarm-record/detail?alarmCode="+ alarmCode +"&alarmDate=" + alarmDate;
        IccHttpHttpRequest pr = new IccHttpHttpRequest(ALARM_DETAIL_URL, Method.GET);
        pr.getHttpRequest().header("Authorization",Authorization);
        String prBody = pr.execute();
        JSONObject obj = JSON.parseObject(prBody);
        JSONObject data = obj.getJSONObject("data");
        // 获取data 对象中的extend属性值
        String extend = data.get("extend").toString();
        // 解析 JSON 字符串
        JSONObject jsonObject = JSON.parseObject(extend);
        // 获取 params 对象
        JSONObject paramsObject = jsonObject.getJSONObject("params");
        // 获取 Objects 数组
        JSONArray objectsArray = paramsObject.getJSONArray("Objects");
        // 遍历 Objects 数组
        for (int i = 0; i < objectsArray.size(); i++) {
            JSONObject object = objectsArray.getJSONObject(i);
            // 获取 ObjectType 和 Text 值
            String objectType = object.getString("ObjectType");
            String text = object.getString("Text");
            // 判断 ObjectType 是否为 Plate
            if ("Plate".equals(objectType)) {
                // System.out.println("ObjectType为Plate的Text值为：" + text);
                // 如果只需要第一个匹配到的值，可以加上 break 语句
                plate = text;
                break;
            }
        }
        // log.info("----结束执行----{}------返回报文:{}", "报警记录详情查询", extend);
        return plate;

    }

    /**
     * 同步icc报警记录
     *
     * @param iccAlarmRecord icc报警记录
     */
    @Override
    public void syncIccAlarmRecord(IccAlarmRecord iccAlarmRecord) {
        // IccAlarmRecord alarmRecord = iccAlarmRecordMapper.selectIccAlarmRecordById(iccAlarmRecord.getId());
        IccAlarmRecord alarmRecord = iccAlarmRecordMapper.selectIccAlarmRecordByAlarmRecordId(iccAlarmRecord.getId());
        boolean exist = alarmRecord != null;
        if (exist) {
            // iccAlarmRecordMapper.updateIccAlarmRecord(iccAlarmRecord);
        } else {
            iccAlarmRecord.setAlarmRecordId(iccAlarmRecord.getId());
            iccAlarmRecordMapper.insertIccAlarmRecord(iccAlarmRecord);
        }
    }

    /**
     * 查询报警录像信息列表
     *
     * @param alarmCode 报警代码
     * @return {@link List}<{@link RecordVO}>
     * @throws ClientException         客户端异常
     * @throws JsonProcessingException json处理异常
     */
    @Override
    public List<RecordVO> getAlarmRecords(String alarmCode) throws ClientException, JsonProcessingException{
        IClient iClient = new DefaultClient();
        IccHttpHttpRequest queryRecordRequest = new IccHttpHttpRequest(IccProfile.URL_SCHEME + "/evo-apigw/admin/API/SS/Record/GetAlarmRecords", Method.POST);
        String queryRecordBody = "{ \"clientType\": \"WINPC\", \"clientMac\": \"30:9c:23:79:40:08\", \"clientPushId\": \"\", \"project\": \"\", \"method\": \"\", \"data\": { \"alarmCode\": \"%s\" } }";
        queryRecordBody = String.format(queryRecordBody, alarmCode);
        // log.info("请求参数：{}", queryRecordBody);
        queryRecordRequest.body(queryRecordBody);
        String queryRecordResponse = iClient.doAction(queryRecordRequest);
        JsonNode videoRecordList;
        try {
            JsonNode queryRecordResponseData = mapper.readValue(queryRecordResponse, JsonNode.class).get("data");
            videoRecordList = queryRecordResponseData.get("records");
            System.out.println("videoRecordList======> " + videoRecordList);
        } catch (JsonProcessingException e) {
            log.error("json format error", e);
            throw new RuntimeException("response format error");
        }
        List<RecordVO> recordVOList = new ArrayList<>();
        if (videoRecordList.isArray()) {
            for (JsonNode videoRecord : videoRecordList) {
                RecordVO recordVO = mapper.treeToValue(videoRecord, RecordVO.class);
                recordVOList.add(recordVO);
            }
        }
        System.out.println("recordVOList======> " + recordVOList);
        return recordVOList;
    }

    @Override
    public String replayByTime(String channelCode, String streamType,
                        String recordSource, String recordType,
                        String startTime, String endTime) throws ClientException {
        IClient iClient = new DefaultClient();
        IccHttpHttpRequest replayByTimeRequest = new IccHttpHttpRequest(IccProfile.URL_SCHEME + "/evo-apigw/admin/API/SS/Playback/StartPlaybackByTime", Method.POST);
        // 参数注释：
        //  channelId 视频通道编码
        //  streamType 码流类型：1=主码流，2=辅码流
        //  recordSource 录像来源：2=设备，3=中心
        //  recordType 录像类型：默认传1即可
        //  startTime 开始时间(时间戳：单位秒)
        //  endTime 结束时间(时间戳：单位秒)
        String replayByTimeBody = "{\"data\":{\"channelId\":\"%s\",\"streamType\":\"%s\",\"recordSource\":\"%s\",\"recordType\":\"%s\",\"startTime\":\"%s\",\"endTime\":\"%s\"}}";
        replayByTimeBody = String.format(replayByTimeBody, channelCode, streamType, recordSource, recordType, startTime, endTime);
        log.info("请求参数：{}", replayByTimeBody);
        replayByTimeRequest.body(replayByTimeBody);
        String replayByTimeResponse = iClient.doAction(replayByTimeRequest);
        String url;
        try {
            JsonNode replayByTimeResponseData = mapper.readValue(replayByTimeResponse, JsonNode.class).get("data");
            url = replayByTimeResponseData.get("url").asText() + "?token=" + replayByTimeResponseData.get("token").asText();
        } catch (JsonProcessingException e) {
            log.error("json format error", e);
            throw new RuntimeException("response format error");
        }
        System.out.println("url======> " + url);
        return url;
    }


    /**
     * 获取实时预览rtsp流地址
     *
     * @param channelCode   通道编码
     * @param streamType    码流类型：1=主码流，2=辅码流
     * @return  rtsp流地址
     * @throws ClientException  客户端异常
     */
    public String startVideo(String channelCode, String streamType) throws ClientException {
        IClient iClient = new DefaultClient();
        IccHttpHttpRequest startVideoRequest = new IccHttpHttpRequest(IccProfile.URL_SCHEME + "/evo-apigw/admin/API/MTS/Video/StartVideo", Method.POST);
        // 参数注释：
        //  channelId 视频通道编码
        //  dataType 视频类型：1=视频
        //  streamType 码流类型：1=主码流，2=辅码流
        String startVideoBody = "{\"data\":{\"channelId\":\"%s\",\"dataType\":\"1\",\"streamType\":\"%s\"}}";
        startVideoBody = String.format(startVideoBody, channelCode, streamType);
        log.info("请求参数：{}", startVideoBody);
        startVideoRequest.body(startVideoBody);
        String startVideoResponse = iClient.doAction(startVideoRequest);
        String rtspUrl;
        try {
            JsonNode data = mapper.readValue(startVideoResponse, JsonNode.class).get("data");
            rtspUrl = data.get("url").asText() + "?token=" + data.get("token").asText();
        } catch (JsonProcessingException e) {
            log.error("startVideoResponse[{}] format error", startVideoResponse, e);
            throw new RuntimeException("response format error");
        }
        return rtspUrl;
    }

}
