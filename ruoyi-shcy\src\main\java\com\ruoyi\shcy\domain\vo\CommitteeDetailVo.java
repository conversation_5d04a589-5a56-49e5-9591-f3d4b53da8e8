package com.ruoyi.shcy.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/11/8 15:15
 * @Version 1.0
 */

//居委巡查商铺详细信息
public class CommitteeDetailVo extends BaseEntity {

    //居委会
    @Excel(name="居委会")
    private String  committee;

    //居委管理商铺总数
    @Excel(name="居委管理商铺总数")
    private String committeeCheckShopTotal;

    //居委巡查商铺数量
    @Excel(name="居委巡查商铺数量")
    private String committeeCheckedShopNum;

    //居委会巡查商铺比例
    @Excel(name="居委会巡查商铺比例")
    private String committeeCheckedRatio;


    /**巡查日期*/
    @Excel(name = "巡查日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date curDate;

    private Long deptId;


    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getCommittee() {
        return committee;
    }

    public void setCommittee(String committee) {
        this.committee = committee;
    }

    public String getCommitteeCheckShopTotal() {
        return committeeCheckShopTotal;
    }

    public void setCommitteeCheckShopTotal(String committeeCheckShopTotal) {
        this.committeeCheckShopTotal = committeeCheckShopTotal;
    }

    public String getCommitteeCheckedShopNum() {
        return committeeCheckedShopNum;
    }

    public void setCommitteeCheckedShopNum(String committeeCheckedShopNum) {
        this.committeeCheckedShopNum = committeeCheckedShopNum;
    }

    public String getCommitteeCheckedRatio() {
        return committeeCheckedRatio;
    }

    public void setCommitteeCheckedRatio(String committeeCheckedRatio) {
        this.committeeCheckedRatio = committeeCheckedRatio;
    }

    public Date getCurDate() {
        return curDate;
    }

    public void setCurDate(Date curDate) {
        this.curDate = curDate;
    }
}
