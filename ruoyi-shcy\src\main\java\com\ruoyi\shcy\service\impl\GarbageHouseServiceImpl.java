package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.GarbageHouseMapper;
import com.ruoyi.shcy.domain.GarbageHouse;
import com.ruoyi.shcy.service.IGarbageHouseService;

/**
 * 小区垃圾房信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class GarbageHouseServiceImpl implements IGarbageHouseService 
{
    @Autowired
    private GarbageHouseMapper garbageHouseMapper;

    /**
     * 查询小区垃圾房信息
     * 
     * @param id 小区垃圾房信息主键
     * @return 小区垃圾房信息
     */
    @Override
    public GarbageHouse selectGarbageHouseById(Long id)
    {
        return garbageHouseMapper.selectGarbageHouseById(id);
    }

    /**
     * 查询小区垃圾房信息列表
     * 
     * @param garbageHouse 小区垃圾房信息
     * @return 小区垃圾房信息
     */
    @Override
    public List<GarbageHouse> selectGarbageHouseList(GarbageHouse garbageHouse)
    {
        return garbageHouseMapper.selectGarbageHouseList(garbageHouse);
    }

    /**
     * 新增小区垃圾房信息
     * 
     * @param garbageHouse 小区垃圾房信息
     * @return 结果
     */
    @Override
    public int insertGarbageHouse(GarbageHouse garbageHouse)
    {
        garbageHouse.setCreateTime(DateUtils.getNowDate());
        return garbageHouseMapper.insertGarbageHouse(garbageHouse);
    }

    /**
     * 修改小区垃圾房信息
     * 
     * @param garbageHouse 小区垃圾房信息
     * @return 结果
     */
    @Override
    public int updateGarbageHouse(GarbageHouse garbageHouse)
    {
        garbageHouse.setUpdateTime(DateUtils.getNowDate());
        return garbageHouseMapper.updateGarbageHouse(garbageHouse);
    }

    /**
     * 批量删除小区垃圾房信息
     * 
     * @param ids 需要删除的小区垃圾房信息主键
     * @return 结果
     */
    @Override
    public int deleteGarbageHouseByIds(Long[] ids)
    {
        return garbageHouseMapper.deleteGarbageHouseByIds(ids);
    }

    /**
     * 删除小区垃圾房信息信息
     * 
     * @param id 小区垃圾房信息主键
     * @return 结果
     */
    @Override
    public int deleteGarbageHouseById(Long id)
    {
        return garbageHouseMapper.deleteGarbageHouseById(id);
    }
}
