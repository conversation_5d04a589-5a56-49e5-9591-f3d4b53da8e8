package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.domain.vo.DbCenterRxJmqVO;
import com.ruoyi.shcy.domain.vo.DbCenterRxMyVO;
import com.ruoyi.shcy.domain.vo.RxReportCfgdVO;
import com.ruoyi.shcy.domain.vo.RxReportVO;

import java.text.ParseException;
import java.util.List;

/**
 * 网格化案件信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
public interface ITaskDbcenterService 
{
    /**
     * 查询网格化案件信息
     * 
     * @param id 网格化案件信息主键
     * @return 网格化案件信息
     */
    public TaskDbcenter selectTaskDbcenterById(Long id);

    /**
     * 查询网格化案件信息列表
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 网格化案件信息集合
     */
    public List<TaskDbcenter> selectTaskDbcenterList(TaskDbcenter taskDbcenter);

    /**
     * 新增网格化案件信息
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 结果
     */
    public int insertTaskDbcenter(TaskDbcenter taskDbcenter);

    /**
     * 修改网格化案件信息
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 结果
     */
    public int updateTaskDbcenter(TaskDbcenter taskDbcenter);

    /**
     * 批量删除网格化案件信息
     * 
     * @param ids 需要删除的网格化案件信息主键集合
     * @return 结果
     */
    public int deleteTaskDbcenterByIds(Long[] ids);

    /**
     * 删除网格化案件信息信息
     * 
     * @param id 网格化案件信息主键
     * @return 结果
     */
    public int deleteTaskDbcenterById(Long id);

    public List<Long> selectTaskDbcenterIdList();

    public List<Long> selectTaskDbcenterIdListBySynctime(String synctime);

    public TaskDbcenter selectTaskDbcenterByHotlinesn(String hotlinesn);

    public void handleRelatedTaskDbcenter(TaskDbcenter taskDbcenter);

    public List<TaskDbcenter> selectTaskDbcenterSyncList();

    public List<Long> selectTaskDbcenterSyncIdList();

    public List<String> selectTaskDbcenterSyncTaskidList();

    public int deleteTaskDbcenterNotShjd();

    public int syncDbcenterRxByTaskids(String[] taskids);

    public int syncDbcenter(String[] dataList);

    public List<TaskDbcenter> selectTaskDbcenterRxList(TaskDbcenter taskDbcenter);

    List<TaskDbcenter> selectTaskDbcenterListDesc(TaskDbcenter taskDbcenter);

    List<DbCenterRxJmqVO> selectTaskDbcenterListJmq(TaskDbcenter taskDbcenter);

    List<DbCenterRxJmqVO> selectTaskDbcenterListKs(TaskDbcenter taskDbcenter);

    DbCenterRxMyVO selectTaskDbcenterListMy(TaskDbcenter taskDbcenter);

    List<DbCenterRxMyVO> selectTaskDbcenterListMyQk(TaskDbcenter taskDbcenter);

    List<DbCenterRxMyVO> selectTaskDbcenterListMyd(TaskDbcenter taskDbcenter);

    List<DbCenterRxJmqVO> selectTaskDbcenterListJas(TaskDbcenter taskDbcenter);

    public int deleteTaskDbcenterReturnOther();

    public int syncData(String[] dataList);

    List<DbCenterRxMyVO> selectTaskDbcenterListMyQkJmq(TaskDbcenter taskDbcenter);

    List<DbCenterRxMyVO> selectTaskDbcenterListMydJmq(TaskDbcenter taskDbcenter);

    List<DbCenterRxMyVO> selectTaskDbcenterListMydKs(TaskDbcenter taskDbcenter);

    List<DbCenterRxMyVO> selectTaskDbcenterListMyQkKs(TaskDbcenter taskDbcenter);

    List<RxReportVO> selectListByParentappealclassification(TaskDbcenter taskDbcenter);

    List<RxReportVO> selectXlByParentappealclassification(TaskDbcenter taskDbcenter);

    List<RxReportVO> selectJmqByParentappealclassification(TaskDbcenter taskDbcenter);

    List<RxReportVO> selectTaskDbcenterListGroupByResidentialarea(TaskDbcenter taskDbcenter);

    List<DbCenterRxMyVO> selectTaskDbcenterListMyQs(TaskDbcenter taskDbcenter) throws ParseException;

    List<String> selectTaskDbcenterHotlinesnList();

    List<TaskDbcenter> selectSimpleTaskDbcenterList(TaskDbcenter taskDbcenter);
}
