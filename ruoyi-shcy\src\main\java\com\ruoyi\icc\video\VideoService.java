package com.ruoyi.icc.video;

import com.dahuatech.hutool.http.Method;
import com.dahuatech.icc.exception.ClientException;
import com.dahuatech.icc.oauth.http.DefaultClient;
import com.dahuatech.icc.oauth.http.IClient;
import com.dahuatech.icc.oauth.http.IccHttpHttpRequest;
import com.dahuatech.icc.oauth.profile.IccProfile;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 视频服务
 *
 * <AUTHOR>
 * @date 2023/10/13
 */
@Slf4j
public class VideoService {

    private final ObjectMapper mapper = new ObjectMapper();

    public VideoService() throws ClientException {
        iClient = new DefaultClient();
    }

    private final IClient iClient;

    /**
     * 获取实时预览rtsp流地址
     *
     * @param channelCode   通道编码
     * @param streamType    码流类型：1=主码流，2=辅码流
     * @return  rtsp流地址
     * @throws ClientException  客户端异常
     */
    public String startVideo(String channelCode, String streamType) throws ClientException {
        IccHttpHttpRequest startVideoRequest = new IccHttpHttpRequest(IccProfile.URL_SCHEME + "/evo-apigw/admin/API/MTS/Video/StartVideo", Method.POST);
        // 参数注释：
        //  channelId 视频通道编码
        //  dataType 视频类型：1=视频
        //  streamType 码流类型：1=主码流，2=辅码流
        String startVideoBody = "{\"data\":{\"channelId\":\"%s\",\"dataType\":\"1\",\"streamType\":\"%s\"}}";
        startVideoBody = String.format(startVideoBody, channelCode, streamType);
        log.info("请求参数：{}", startVideoBody);
        startVideoRequest.body(startVideoBody);
        String startVideoResponse = iClient.doAction(startVideoRequest);
        String rtspUrl;
        try {
            JsonNode data = mapper.readValue(startVideoResponse, JsonNode.class).get("data");
            rtspUrl = data.get("url").asText() + "?token=" + data.get("token").asText();
        } catch (JsonProcessingException e) {
            log.error("startVideoResponse[{}] format error", startVideoResponse, e);
            throw new RuntimeException("response format error");
        }
        return rtspUrl;
    }

    /**
     * 实时预览 “HLS、FLV、RTMP实时拉流”
     *
     * @param channelCode   通道编码
     * @param streamType    码流类型：1=主码流，2=辅码流
     * @param type  拉流方式：hls,flv,rtmp
     * @return  流地址
     * @throws ClientException  客户端异常
     */
    public String realtime(String channelCode, String streamType, String type) throws ClientException {
        IccHttpHttpRequest realtimeRequest = new IccHttpHttpRequest(IccProfile.URL_SCHEME + "/evo-apigw/admin/API/video/stream/realtime", Method.POST);
        // 参数注释：
        //  channelId 视频通道编码
        //  streamType 码流类型：1=主码流，2=辅码流
        //  type 拉流方式：hls,flv,rtmp
        String realtimeBody = "{\"data\":{\"channelId\":\"%s\",\"streamType\":\"%s\",\"type\":\"%s\"}}";
        realtimeBody = String.format(realtimeBody, channelCode, streamType, type);
        log.info("请求参数：{}", realtimeBody);
        realtimeRequest.body(realtimeBody);
        String realtimeResponse = iClient.doAction(realtimeRequest);
        String url;
        try {
            JsonNode data = mapper.readValue(realtimeResponse, JsonNode.class).get("data");
            url = data.get("url").asText();
        } catch (JsonProcessingException e) {
            log.error("realtimeResponse[{}] format error", realtimeResponse, e);
            throw new RuntimeException("response format error");
        }
        return url;
    }

    /**
     * 查询通道的录像列表
     *
     * @param channelCode   通道编码
     * @param streamType    码流类型：0=全部，1=主码流，2=辅码流
     * @param recordSource  录像来源：2=设备，3=中心
     * @param startTime     开始时间(时间戳：单位秒)
     * @param endTime       结束时间(时间戳：单位秒)
     * @return  录像列表
     * @throws ClientException  客户端异常
     */
    public JsonNode getRecords(String channelCode, String streamType, String recordSource, String startTime, String endTime) throws ClientException {
        IccHttpHttpRequest queryRecordRequest = new IccHttpHttpRequest(IccProfile.URL_SCHEME + "/evo-apigw/admin/API/SS/Record/QueryRecords", Method.POST);
        // 参数注释：
        //  channelId 视频通道编码
        //  streamType 码流类型：0=全部，1=主码流，2=辅码流
        //  recordSource 录像来源：2=设备，3=中心
        //  recordType 录像类型：0=全部，1=手动录像，2=报警录像，3=动态监测，4=视频丢失，5=视频遮挡，6=定时录像，7=全天候录像，8=文件录像转换（平台录像计划配的录像是定时录像）
        //  startTime 开始时间(时间戳：单位秒)
        //  endTime 结束时间(时间戳：单位秒)
        String queryRecordBody = "{\"data\":{\"channelId\":\"%s\",\"streamType\":\"%s\",\"recordSource\":\"%s\",\"recordType\":\"0\",\"startTime\":\"%s\",\"endTime\":\"%s\"}}";
        queryRecordBody = String.format(queryRecordBody, channelCode, streamType, recordSource, startTime, endTime);
        log.info("请求参数：{}", queryRecordBody);
        queryRecordRequest.body(queryRecordBody);
        String queryRecordResponse = iClient.doAction(queryRecordRequest);
        JsonNode videoRecordList;
        try {
            JsonNode queryRecordResponseData = mapper.readValue(queryRecordResponse, JsonNode.class).get("data");
            videoRecordList = queryRecordResponseData.get("records");
        } catch (JsonProcessingException e) {
            log.error("json format error", e);
            throw new RuntimeException("response format error");
        }
        return videoRecordList;
    }

    /**
     * 查询报警录像信息列表
     *
     * @param alarmCode 报警代码
     * @return 报警录像列表
     * @throws ClientException 客户端异常
     */
    public JsonNode getAlarmRecords(String alarmCode) throws ClientException {
        IccHttpHttpRequest queryRecordRequest = new IccHttpHttpRequest(IccProfile.URL_SCHEME + "/evo-apigw/admin/API/SS/Record/GetAlarmRecords", Method.POST);
        String queryRecordBody = "{ \"clientType\": \"WINPC\", \"clientMac\": \"30:9c:23:79:40:08\", \"clientPushId\": \"\", \"project\": \"\", \"method\": \"\", \"data\": { \"alarmCode\": \"%s\" } }";
        queryRecordBody = String.format(queryRecordBody, alarmCode);
        queryRecordRequest.body(queryRecordBody);
        String queryRecordResponse = iClient.doAction(queryRecordRequest);
        JsonNode videoRecordList;
        try {
            JsonNode queryRecordResponseData = mapper.readValue(queryRecordResponse, JsonNode.class).get("data");
            videoRecordList = queryRecordResponseData.get("records");
        } catch (JsonProcessingException e) {
            log.error("json format error", e);
            throw new RuntimeException("response format error");
        }
        return videoRecordList;
    }

    /**
     * 以HLS、RTMP方式进行录像回放
     *
     * @param channelCode   通道编码
     * @param streamType    码流类型：1=主码流，2=辅码流
     * @param type          拉流方式：支持hls/rtmp格式， flv录像类型不支持
     * @param recordSource  录像来源：2=设备录像，3=中心录像
     * @param recordType    录像类型：1 表示普通录像 默认1
     * @param startTime     开始时间,格式:"2020-11-12 11:10:11"
     * @param endTime       结束时间,格式:"2020-11-12 23:10:11"
     * @return  流地址
     * @throws ClientException  客户端异常
     */
    public String replay(String channelCode, String streamType, String type, String recordSource, String recordType, String startTime, String endTime) throws ClientException {
        IccHttpHttpRequest replayRequest = new IccHttpHttpRequest(IccProfile.URL_SCHEME + "/evo-apigw/admin/API/video/stream/record", Method.POST);
        // 参数注释：
        //  channelId 视频通道编码
        //  streamType 码流类型：1=主码流，2=辅码流
        //  type 拉流方式：支持hls/rtmp格式， flv录像类型不支持
        //  recordSource 录像来源：2=设备录像，3=中心录像
        //  recordType 录像类型：1 表示普通录像 默认1
        //  beginTime 开始时间,格式:"2020-11-12 11:10:11"
        //  endTime 结束时间,格式:"2020-11-12 23:10:11"
        String replayBody = "{\"data\":{\"channelId\":\"%s\",\"streamType\":\"%s\",\"type\":\"%s\",\"recordSource\":\"%s\",\"recordType\":\"%s\",\"beginTime\":\"%s\",\"endTime\":\"%s\"}}";
        replayBody = String.format(replayBody, channelCode, streamType, type, recordSource, recordType, startTime, endTime);
        log.info("请求参数：{}", replayBody);
        replayRequest.body(replayBody);
        String replayResponse = iClient.doAction(replayRequest);
        String url;
        try {
            JsonNode replayResponseData = mapper.readValue(replayResponse, JsonNode.class).get("data");
            url = replayResponseData.get("url").asText();
        } catch (JsonProcessingException e) {
            log.error("json format error", e);
            throw new RuntimeException("response format error");
        }
        return url;
    }

    /**
     * 获取报警录像回放地址，中心无法获取报警前录像，设备获取录像，HLS录像回放拼接
     *
     * @param alarmCode 报警代码
     * @return  报警录像回放地址
     * @throws ClientException  客户端异常
     */
    public String getAlarmVideoPlayback(String alarmCode)  throws ClientException {
        JsonNode records = getAlarmRecords(alarmCode);
        // 报警后录像
        JsonNode videoRecord = records.get(0);
        String channelId = videoRecord.get("channelId").asText();
        // String recordSource = videoRecord.get("recordSource").asText();
        // String recordType = videoRecord.get("videoRecordType").asText();
        String startTime = videoRecord.get("startTime").asText();
        String endTime = videoRecord.get("endTime").asText();
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formatStartTime = simpleDateFormat.format(new Date(Long.parseLong(startTime) * 1000));
        String formatEndTime = simpleDateFormat.format(new Date(Long.parseLong(endTime) * 1000));
        // 设备获取录像
        String originalUrl = replay(channelId, "1", "hls", "2", "1", formatStartTime, formatEndTime);
        // 报警前录像
        JsonNode beforeVideoRecord = records.get(1);
        String beforeStartTime = beforeVideoRecord.get("startTime").asText();
        // 构建正则表达式
        String patternString = "totallength/(\\d+)/begintime/(\\d+)/endtime/(\\d+)";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(originalUrl);
        // 转换为long类型进行计算
        long startTimestamp = Long.parseLong(beforeStartTime);
        long endTimestamp = Long.parseLong(endTime);
        long difference = endTimestamp - startTimestamp;
        // 将时间差转换为字符串
        String differenceAsString = Long.toString(difference);
        // 替换匹配的部分
        String modifiedUrl = matcher.replaceAll("totallength/" + differenceAsString + "/begintime/" + beforeStartTime + "/endtime/" + endTime);
        return modifiedUrl;
    }

}
