package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyFloodHazardCommitteeMapper;
import com.ruoyi.shcy.domain.ShcyFloodHazardCommittee;
import com.ruoyi.shcy.service.IShcyFloodHazardCommitteeService;

/**
 * 隐患排查居委会对应表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-26
 */
@Service
public class ShcyFloodHazardCommitteeServiceImpl implements IShcyFloodHazardCommitteeService 
{
    @Autowired
    private ShcyFloodHazardCommitteeMapper shcyFloodHazardCommitteeMapper;

    /**
     * 查询隐患排查居委会对应表
     * 
     * @param id 隐患排查居委会对应表主键
     * @return 隐患排查居委会对应表
     */
    @Override
    public ShcyFloodHazardCommittee selectShcyFloodHazardCommitteeById(Long id)
    {
        return shcyFloodHazardCommitteeMapper.selectShcyFloodHazardCommitteeById(id);
    }

    /**
     * 查询隐患排查居委会对应表列表
     * 
     * @param shcyFloodHazardCommittee 隐患排查居委会对应表
     * @return 隐患排查居委会对应表
     */
    @Override
    public List<ShcyFloodHazardCommittee> selectShcyFloodHazardCommitteeList(ShcyFloodHazardCommittee shcyFloodHazardCommittee)
    {
        return shcyFloodHazardCommitteeMapper.selectShcyFloodHazardCommitteeList(shcyFloodHazardCommittee);
    }

    /**
     * 新增隐患排查居委会对应表
     * 
     * @param shcyFloodHazardCommittee 隐患排查居委会对应表
     * @return 结果
     */
    @Override
    public int insertShcyFloodHazardCommittee(ShcyFloodHazardCommittee shcyFloodHazardCommittee)
    {
        shcyFloodHazardCommittee.setCreateTime(DateUtils.getNowDate());
        return shcyFloodHazardCommitteeMapper.insertShcyFloodHazardCommittee(shcyFloodHazardCommittee);
    }

    /**
     * 修改隐患排查居委会对应表
     * 
     * @param shcyFloodHazardCommittee 隐患排查居委会对应表
     * @return 结果
     */
    @Override
    public int updateShcyFloodHazardCommittee(ShcyFloodHazardCommittee shcyFloodHazardCommittee)
    {
        shcyFloodHazardCommittee.setUpdateTime(DateUtils.getNowDate());
        return shcyFloodHazardCommitteeMapper.updateShcyFloodHazardCommittee(shcyFloodHazardCommittee);
    }

    /**
     * 批量删除隐患排查居委会对应表
     * 
     * @param ids 需要删除的隐患排查居委会对应表主键
     * @return 结果
     */
    @Override
    public int deleteShcyFloodHazardCommitteeByIds(Long[] ids)
    {
        return shcyFloodHazardCommitteeMapper.deleteShcyFloodHazardCommitteeByIds(ids);
    }

    /**
     * 删除隐患排查居委会对应表信息
     * 
     * @param id 隐患排查居委会对应表主键
     * @return 结果
     */
    @Override
    public int deleteShcyFloodHazardCommitteeById(Long id)
    {
        return shcyFloodHazardCommitteeMapper.deleteShcyFloodHazardCommitteeById(id);
    }
}
