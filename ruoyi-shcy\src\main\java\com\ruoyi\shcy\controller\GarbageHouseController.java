package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.GarbageHouse;
import com.ruoyi.shcy.service.IGarbageHouseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区垃圾房信息Controller
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/garbageHouse")
public class GarbageHouseController extends BaseController
{
    @Autowired
    private IGarbageHouseService garbageHouseService;

    /**
     * 查询小区垃圾房信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:garbageHouse:list')")
    @GetMapping("/list")
    public TableDataInfo list(GarbageHouse garbageHouse)
    {
        startPage();
        List<GarbageHouse> list = garbageHouseService.selectGarbageHouseList(garbageHouse);
        return getDataTable(list);
    }

    /**
     * 导出小区垃圾房信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:garbageHouse:export')")
    @Log(title = "小区垃圾房信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GarbageHouse garbageHouse)
    {
        List<GarbageHouse> list = garbageHouseService.selectGarbageHouseList(garbageHouse);
        ExcelUtil<GarbageHouse> util = new ExcelUtil<GarbageHouse>(GarbageHouse.class);
        util.exportExcel(response, list, "小区垃圾房信息数据");
    }

    /**
     * 获取小区垃圾房信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:garbageHouse:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(garbageHouseService.selectGarbageHouseById(id));
    }

    /**
     * 新增小区垃圾房信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:garbageHouse:add')")
    @Log(title = "小区垃圾房信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GarbageHouse garbageHouse)
    {
        return toAjax(garbageHouseService.insertGarbageHouse(garbageHouse));
    }

    /**
     * 修改小区垃圾房信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:garbageHouse:edit')")
    @Log(title = "小区垃圾房信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GarbageHouse garbageHouse)
    {
        return toAjax(garbageHouseService.updateGarbageHouse(garbageHouse));
    }

    /**
     * 删除小区垃圾房信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:garbageHouse:remove')")
    @Log(title = "小区垃圾房信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(garbageHouseService.deleteGarbageHouseByIds(ids));
    }
}
