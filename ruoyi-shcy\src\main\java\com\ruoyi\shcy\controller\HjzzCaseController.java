package com.ruoyi.shcy.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.dto.HjzzCaseDTO;
import com.ruoyi.shcy.service.IHjzzCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 环境整治案事件Controller
 * 
 * <AUTHOR>
 * @date 2023-11-08
 */
@RestController
@RequestMapping("/shcy/hjzzCase")
public class HjzzCaseController extends BaseController
{
    @Autowired
    private IHjzzCaseService hjzzCaseService;

    @Autowired
    private ServerConfig serverConfig;

    /**
     * 查询环境整治案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCase:list')")
    @GetMapping("/list")
    public TableDataInfo list(HjzzCase hjzzCase)
    {
        hjzzCase.setCaseType("1");
        startPage();
        List<HjzzCase> list = hjzzCaseService.selectHjzzCaseList(hjzzCase);
        return getDataTable(list);
    }

    /**
     * 导出偷倒垃圾案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCase:export')")
    @Log(title = "偷倒垃圾案事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HjzzCase hjzzCase)
    {
        hjzzCase.setCaseType("1");
        List<HjzzCase> list = hjzzCaseService.selectHjzzCaseList(hjzzCase);
        List<HjzzCaseDTO> exportList = BeanUtil.copyToList(list, HjzzCaseDTO.class);
        ExcelUtil<HjzzCaseDTO> util = new ExcelUtil<HjzzCaseDTO>(HjzzCaseDTO.class);
        util.exportExcel(response, exportList, "偷倒垃圾案事件数据");
    }

    /**
     * 获取环境整治案事件详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCase:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hjzzCaseService.selectHjzzCaseById(id));
    }

    /**
     * 新增环境整治案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCase:add')")
    @Log(title = "环境整治案事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HjzzCase hjzzCase)
    {
        return toAjax(hjzzCaseService.insertHjzzCase(hjzzCase));
    }

    /**
     * 修改环境整治案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCase:edit')")
    @Log(title = "环境整治案事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HjzzCase hjzzCase)
    {
        return toAjax(hjzzCaseService.updateHjzzCase(hjzzCase));
    }

    /**
     * 删除环境整治案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCase:remove')")
    @Log(title = "环境整治案事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hjzzCaseService.deleteHjzzCaseByIds(ids));
    }

    /**
     * hjzz列表, 供大屏使用
     *
     * @param hjzzCase hjzz案例
     * @return {@link TableDataInfo}
     */
    @GetMapping("/hjzz/list")
    public TableDataInfo hjzzList(HjzzCase hjzzCase)
    {
        startPage();
        List<HjzzCase> list = hjzzCaseService.selectHjzzCaseList(hjzzCase);
        return getDataTable(list);
    }

    @GetMapping("/hjzz/getDumpingGarbageTypeCount")
    public AjaxResult getDumpingGarbageTypeCount() {
        return AjaxResult.success("操作成功", hjzzCaseService.getDumpingGarbageTypeCount());
    }

    @GetMapping("/hjzz/getIsFilingCount")
    public AjaxResult getIsFilingCount() {
        return AjaxResult.success("操作成功", hjzzCaseService.getIsFilingCount());
    }

    /**
     * 获得违规行为
     *
     * @return {@link AjaxResult}
     */
    @GetMapping("/hjzz/getViolations")
    public AjaxResult getViolations(HjzzCase hjzzCase) {
        return AjaxResult.success("操作成功", hjzzCaseService.getViolations(hjzzCase));
    }

    @GetMapping("/hjzz/history/list")
    public TableDataInfo historyList(HjzzCase hjzzCase, String keyword)
    {
        if (StrUtil.isNotEmpty(keyword)) {
            hjzzCase.getParams().put("keyword", keyword);
        }
        startPage();
        List<HjzzCase> list = hjzzCaseService.selectHistoryList(hjzzCase);
        return getDataTable(list);
    }

    @GetMapping("/hjzz/handle/list")
    public TableDataInfo handleList(HjzzCase hjzzCase, String keyword)
    {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            hjzzCase.setParams(params);
        }
        startPage();
        List<HjzzCase> list = hjzzCaseService.selectHandleList(hjzzCase);
        return getDataTable(list);
    }

    @PostMapping("/hjzz/handle")
    public AjaxResult hjzzHandle(@RequestBody HjzzCase hjzzCase)
    {
        return toAjax(hjzzCaseService.handleHjzzCase(hjzzCase));
    }

    @PostMapping("/hjzz/punish")
    public AjaxResult punishHjzzCase(@RequestBody HjzzCase hjzzCase)
    {
        return toAjax(hjzzCaseService.punishHjzzCase(hjzzCase));
    }

    @PostMapping("/uploadAttachment")
    public AjaxResult uploadAttachment(MultipartFile file) throws Exception
    {
        try
        {
            // 上传文件路径
            String filePath = RuoYiConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", fileName);
            ajax.put("newFileName", FileUtils.getName(fileName));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }
}
