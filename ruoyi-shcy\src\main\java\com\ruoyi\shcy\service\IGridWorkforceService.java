package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.GridWorkforce;

/**
 * 网格工作力量Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface IGridWorkforceService 
{
    /**
     * 查询网格工作力量
     * 
     * @param id 网格工作力量主键
     * @return 网格工作力量
     */
    public GridWorkforce selectGridWorkforceById(Long id);

    /**
     * 查询网格工作力量列表
     * 
     * @param gridWorkforce 网格工作力量
     * @return 网格工作力量集合
     */
    public List<GridWorkforce> selectGridWorkforceList(GridWorkforce gridWorkforce);

    /**
     * 新增网格工作力量
     * 
     * @param gridWorkforce 网格工作力量
     * @return 结果
     */
    public int insertGridWorkforce(GridWorkforce gridWorkforce);

    /**
     * 修改网格工作力量
     * 
     * @param gridWorkforce 网格工作力量
     * @return 结果
     */
    public int updateGridWorkforce(GridWorkforce gridWorkforce);

    /**
     * 批量删除网格工作力量
     * 
     * @param ids 需要删除的网格工作力量主键集合
     * @return 结果
     */
    public int deleteGridWorkforceByIds(Long[] ids);

    /**
     * 删除网格工作力量信息
     * 
     * @param id 网格工作力量主键
     * @return 结果
     */
    public int deleteGridWorkforceById(Long id);
}
