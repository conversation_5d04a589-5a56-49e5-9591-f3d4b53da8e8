package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小区垃圾房信息对象 shcy_garbage_house
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public class GarbageHouse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 街镇 */
    @Excel(name = "街镇")
    private String street;

    /** 居委会 */
    @Excel(name = "居委会")
    private String committee;

    /** 居委会id */
    @Excel(name = "居委会id")
    private Long committeeId;

    /** 居住小区 */
    @Excel(name = "居住小区")
    private String residential;

    /** 居住小区id */
    @Excel(name = "居住小区id")
    private Long residentialId;

    /** 点位地址 */
    @Excel(name = "点位地址")
    private String siteAddress;

    /** 村（委） */
    @Excel(name = "村", readConverterExp = "委=")
    private String village;

    /** 投放垃圾种类 */
    @Excel(name = "投放垃圾种类")
    private String garbageType;

    /** 投放时间 */
    @Excel(name = "投放时间")
    private String garbageOpenTime;

    /** 居民区卫生主任 */
    @Excel(name = "居民区卫生主任")
    private String residentialHealthDirector;

    /** 卫生主任电话 */
    @Excel(name = "卫生主任电话")
    private String healthDirectorPhone;

    /** 居民区书记 */
    @Excel(name = "居民区书记")
    private String residentialSecretary;

    /** 书记电话 */
    @Excel(name = "书记电话")
    private String secretaryPhone;

    /** 保洁员 */
    @Excel(name = "保洁员")
    private String residentialCleaning;

    /** 保洁电话 */
    @Excel(name = "保洁电话")
    private String cleaningPhone;

    /** 小区经理 */
    @Excel(name = "小区经理")
    private String residentialManage;

    /** 小区经理电话 */
    @Excel(name = "小区经理电话")
    private String managePhone;

    /** 经纬度 */
    @Excel(name = "经纬度")
    private String longitudeLatitude;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setStreet(String street)
    {
        this.street = street;
    }

    public String getStreet()
    {
        return street;
    }
    public void setCommittee(String committee)
    {
        this.committee = committee;
    }

    public String getCommittee()
    {
        return committee;
    }
    public void setCommitteeId(Long committeeId)
    {
        this.committeeId = committeeId;
    }

    public Long getCommitteeId()
    {
        return committeeId;
    }
    public void setResidential(String residential)
    {
        this.residential = residential;
    }

    public String getResidential()
    {
        return residential;
    }
    public void setResidentialId(Long residentialId)
    {
        this.residentialId = residentialId;
    }

    public Long getResidentialId()
    {
        return residentialId;
    }
    public void setSiteAddress(String siteAddress)
    {
        this.siteAddress = siteAddress;
    }

    public String getSiteAddress()
    {
        return siteAddress;
    }
    public void setVillage(String village)
    {
        this.village = village;
    }

    public String getVillage()
    {
        return village;
    }
    public void setGarbageType(String garbageType)
    {
        this.garbageType = garbageType;
    }

    public String getGarbageType()
    {
        return garbageType;
    }
    public void setGarbageOpenTime(String garbageOpenTime)
    {
        this.garbageOpenTime = garbageOpenTime;
    }

    public String getGarbageOpenTime()
    {
        return garbageOpenTime;
    }
    public void setResidentialHealthDirector(String residentialHealthDirector)
    {
        this.residentialHealthDirector = residentialHealthDirector;
    }

    public String getResidentialHealthDirector()
    {
        return residentialHealthDirector;
    }
    public void setHealthDirectorPhone(String healthDirectorPhone)
    {
        this.healthDirectorPhone = healthDirectorPhone;
    }

    public String getHealthDirectorPhone()
    {
        return healthDirectorPhone;
    }
    public void setResidentialSecretary(String residentialSecretary)
    {
        this.residentialSecretary = residentialSecretary;
    }

    public String getResidentialSecretary()
    {
        return residentialSecretary;
    }
    public void setSecretaryPhone(String secretaryPhone)
    {
        this.secretaryPhone = secretaryPhone;
    }

    public String getSecretaryPhone()
    {
        return secretaryPhone;
    }
    public void setResidentialCleaning(String residentialCleaning)
    {
        this.residentialCleaning = residentialCleaning;
    }

    public String getResidentialCleaning()
    {
        return residentialCleaning;
    }
    public void setCleaningPhone(String cleaningPhone)
    {
        this.cleaningPhone = cleaningPhone;
    }

    public String getCleaningPhone()
    {
        return cleaningPhone;
    }
    public void setResidentialManage(String residentialManage)
    {
        this.residentialManage = residentialManage;
    }

    public String getResidentialManage()
    {
        return residentialManage;
    }
    public void setManagePhone(String managePhone)
    {
        this.managePhone = managePhone;
    }

    public String getManagePhone()
    {
        return managePhone;
    }
    public void setLongitudeLatitude(String longitudeLatitude)
    {
        this.longitudeLatitude = longitudeLatitude;
    }

    public String getLongitudeLatitude()
    {
        return longitudeLatitude;
    }
    public void setLongitude(String longitude)
    {
        this.longitude = longitude;
    }

    public String getLongitude()
    {
        return longitude;
    }
    public void setLatitude(String latitude)
    {
        this.latitude = latitude;
    }

    public String getLatitude()
    {
        return latitude;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "GarbageHouse{" +
                "id=" + id +
                ", street='" + street + '\'' +
                ", committee='" + committee + '\'' +
                ", committeeId=" + committeeId +
                ", residential='" + residential + '\'' +
                ", residentialId=" + residentialId +
                ", siteAddress='" + siteAddress + '\'' +
                ", village='" + village + '\'' +
                ", garbageType='" + garbageType + '\'' +
                ", garbageOpenTime='" + garbageOpenTime + '\'' +
                ", residentialHealthDirector='" + residentialHealthDirector + '\'' +
                ", healthDirectorPhone='" + healthDirectorPhone + '\'' +
                ", residentialSecretary='" + residentialSecretary + '\'' +
                ", secretaryPhone='" + secretaryPhone + '\'' +
                ", residentialCleaning='" + residentialCleaning + '\'' +
                ", cleaningPhone='" + cleaningPhone + '\'' +
                ", residentialManage='" + residentialManage + '\'' +
                ", managePhone='" + managePhone + '\'' +
                ", longitudeLatitude='" + longitudeLatitude + '\'' +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
