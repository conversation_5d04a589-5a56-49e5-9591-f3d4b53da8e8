package com.ruoyi.shcy.service.impl;

import java.util.List;

import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyFloodHazardTypeMapper;
import com.ruoyi.shcy.domain.ShcyFloodHazardType;
import com.ruoyi.shcy.service.IShcyFloodHazardTypeService;

/**
 * 隐患排查类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
@Service
public class ShcyFloodHazardTypeServiceImpl implements IShcyFloodHazardTypeService
{
    @Autowired
    private ShcyFloodHazardTypeMapper shcyFloodHazardTypeMapper;

    /**
     * 查询隐患排查类型
     *
     * @param id 隐患排查类型主键
     * @return 隐患排查类型
     */
    @Override
    public ShcyFloodHazardType selectShcyFloodHazardTypeById(Long id)
    {
        return shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeById(id);
    }

    /**
     * 查询隐患排查类型列表
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 隐患排查类型
     */
    @Override
    public List<ShcyFloodHazardType> selectShcyFloodHazardTypeList(ShcyFloodHazardType shcyFloodHazardType)
    {
        return shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeList(shcyFloodHazardType);
    }

    /**
     * 新增隐患排查类型
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 结果
     */
    @Override
    public int insertShcyFloodHazardType(ShcyFloodHazardType shcyFloodHazardType)
    {
        ShcyFloodHazardType info = shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeById(shcyFloodHazardType.getParentId());

        shcyFloodHazardType.setAncestors(info.getAncestors() + "," + shcyFloodHazardType.getParentId());
        shcyFloodHazardType.setCreateTime(DateUtils.getNowDate());
        return shcyFloodHazardTypeMapper.insertShcyFloodHazardType(shcyFloodHazardType);
    }

    /**
     * 修改隐患排查类型
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 结果
     */
    @Override
    public int updateShcyFloodHazardType(ShcyFloodHazardType shcyFloodHazardType)
    {
        ShcyFloodHazardType newParentDept = shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeById(shcyFloodHazardType.getParentId());
        ShcyFloodHazardType oldDept = shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeById(shcyFloodHazardType.getId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept))
        {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getId();
            String oldAncestors = oldDept.getAncestors();
            shcyFloodHazardType.setAncestors(newAncestors);
            updateTypeChildren(shcyFloodHazardType.getId(), newAncestors, oldAncestors);
        }
        shcyFloodHazardType.setUpdateTime(DateUtils.getNowDate());
        int result = shcyFloodHazardTypeMapper.updateShcyFloodHazardType(shcyFloodHazardType);
        return result;
    }

    /**
     * 修改子元素关系
     *
     * @param id 被修改的ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateTypeChildren(Long id, String newAncestors, String oldAncestors)
    {
        List<ShcyFloodHazardType> children = shcyFloodHazardTypeMapper.selectChildrenTypeById(id);
        for (ShcyFloodHazardType child : children)
        {
            child.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
        }
        if (children.size() > 0)
        {
            shcyFloodHazardTypeMapper.updateTypeChildren(children);
        }
    }


    /**
     * 批量删除隐患排查类型
     *
     * @param ids 需要删除的隐患排查类型主键
     * @return 结果
     */
    @Override
    public int deleteShcyFloodHazardTypeByIds(Long[] ids)
    {
        return shcyFloodHazardTypeMapper.deleteShcyFloodHazardTypeByIds(ids);
    }

    /**
     * 删除隐患排查类型信息
     *
     * @param id 隐患排查类型主键
     * @return 结果
     */
    @Override
    public int deleteShcyFloodHazardTypeById(Long id)
    {
        return shcyFloodHazardTypeMapper.deleteShcyFloodHazardTypeById(id);
    }

    @Override
    public boolean hasChildById(Long id) {
        int result = shcyFloodHazardTypeMapper.hasChildByTypeId(id);
        return result > 0;
    }

}
