package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.domain.vo.ChartDataVO;
import com.ruoyi.shcy.domain.vo.HjzzCaseCountVO;
import com.ruoyi.shcy.domain.vo.ViolationDataVO;

import java.util.List;

/**
 * 环境整治案事件Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-08
 */
public interface IHjzzCaseService 
{
    /**
     * 查询环境整治案事件
     * 
     * @param id 环境整治案事件主键
     * @return 环境整治案事件
     */
    public HjzzCase selectHjzzCaseById(Long id);

    /**
     * 查询环境整治案事件列表
     * 
     * @param hjzzCase 环境整治案事件
     * @return 环境整治案事件集合
     */
    public List<HjzzCase> selectHjzzCaseList(HjzzCase hjzzCase);

    /**
     * 新增环境整治案事件
     * 
     * @param hjzzCase 环境整治案事件
     * @return 结果
     */
    public int insertHjzzCase(HjzzCase hjzzCase);

    /**
     * 修改环境整治案事件
     * 
     * @param hjzzCase 环境整治案事件
     * @return 结果
     */
    public int updateHjzzCase(HjzzCase hjzzCase);

    /**
     * 批量删除环境整治案事件
     * 
     * @param ids 需要删除的环境整治案事件主键集合
     * @return 结果
     */
    public int deleteHjzzCaseByIds(Long[] ids);

    /**
     * 删除环境整治案事件信息
     * 
     * @param id 环境整治案事件主键
     * @return 结果
     */
    public int deleteHjzzCaseById(Long id);

    public int handleHjzzCase(HjzzCase hjzzCase);

    public int punishHjzzCase(HjzzCase hjzzCase);

    public List<HjzzCase> selectHandleList(HjzzCase hjzzCase);

    public List<HjzzCase> selectHistoryList(HjzzCase hjzzCase);

    public long getPendingCaseCount();

    public long getProcessingCaseCount();

    public int invalidateCase(HjzzCase hjzzCase);

    public int regressionCase(HjzzCase hjzzCase);

    public long getCaseCount(HjzzCase hjzzCase);

    public List<ChartDataVO> getDumpingGarbageTypeCount();

    public List<ViolationDataVO> getViolations(HjzzCase hjzzCase);

    public HjzzCaseCountVO countHjzzCase(HjzzCase hjzzCase);

    public int convertCase(HjzzCase hjzzCase);

    List<HjzzCase> selectHjzzCaseWgList(HjzzCase hjzzCase);

    public HjzzCaseCountVO countHjzzCaseHistory(HjzzCase hjzzCase, String yearMonth);

    List<HjzzCase> selectHjzzCaseCount(HjzzCase hjzzCase);

    public List<ChartDataVO>  getIsFilingCount();
}
