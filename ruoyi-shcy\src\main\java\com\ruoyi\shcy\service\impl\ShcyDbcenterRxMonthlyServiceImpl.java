package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyDbcenterRxMonthlyMapper;
import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthly;
import com.ruoyi.shcy.service.IShcyDbcenterRxMonthlyService;

/**
 * 12345热线分析Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@Service
public class ShcyDbcenterRxMonthlyServiceImpl implements IShcyDbcenterRxMonthlyService 
{
    @Autowired
    private ShcyDbcenterRxMonthlyMapper shcyDbcenterRxMonthlyMapper;

    /**
     * 查询12345热线分析
     * 
     * @param id 12345热线分析主键
     * @return 12345热线分析
     */
    @Override
    public ShcyDbcenterRxMonthly selectShcyDbcenterRxMonthlyById(Long id)
    {
        return shcyDbcenterRxMonthlyMapper.selectShcyDbcenterRxMonthlyById(id);
    }

    /**
     * 查询12345热线分析列表
     * 
     * @param shcyDbcenterRxMonthly 12345热线分析
     * @return 12345热线分析
     */
    @Override
    public List<ShcyDbcenterRxMonthly> selectShcyDbcenterRxMonthlyList(ShcyDbcenterRxMonthly shcyDbcenterRxMonthly)
    {
        return shcyDbcenterRxMonthlyMapper.selectShcyDbcenterRxMonthlyList(shcyDbcenterRxMonthly);
    }

    /**
     * 新增12345热线分析
     * 
     * @param shcyDbcenterRxMonthly 12345热线分析
     * @return 结果
     */
    @Override
    public int insertShcyDbcenterRxMonthly(ShcyDbcenterRxMonthly shcyDbcenterRxMonthly)
    {
        shcyDbcenterRxMonthly.setCreateTime(DateUtils.getNowDate());
        return shcyDbcenterRxMonthlyMapper.insertShcyDbcenterRxMonthly(shcyDbcenterRxMonthly);
    }

    /**
     * 修改12345热线分析
     * 
     * @param shcyDbcenterRxMonthly 12345热线分析
     * @return 结果
     */
    @Override
    public int updateShcyDbcenterRxMonthly(ShcyDbcenterRxMonthly shcyDbcenterRxMonthly)
    {
        shcyDbcenterRxMonthly.setUpdateTime(DateUtils.getNowDate());
        return shcyDbcenterRxMonthlyMapper.updateShcyDbcenterRxMonthly(shcyDbcenterRxMonthly);
    }

    /**
     * 批量删除12345热线分析
     * 
     * @param ids 需要删除的12345热线分析主键
     * @return 结果
     */
    @Override
    public int deleteShcyDbcenterRxMonthlyByIds(Long[] ids)
    {
        return shcyDbcenterRxMonthlyMapper.deleteShcyDbcenterRxMonthlyByIds(ids);
    }

    /**
     * 删除12345热线分析信息
     * 
     * @param id 12345热线分析主键
     * @return 结果
     */
    @Override
    public int deleteShcyDbcenterRxMonthlyById(Long id)
    {
        return shcyDbcenterRxMonthlyMapper.deleteShcyDbcenterRxMonthlyById(id);
    }
}
