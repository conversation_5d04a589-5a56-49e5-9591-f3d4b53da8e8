package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ParcelInformation;

/**
 * 宗地信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-31
 */
public interface ParcelInformationMapper
{
    /**
     * 查询宗地信息
     *
     * @param id 宗地信息主键
     * @return 宗地信息
     */
    public ParcelInformation selectParcelInformationById(Long id);

    /**
     * 查询宗地信息列表
     *
     * @param parcelInformation 宗地信息
     * @return 宗地信息集合
     */
    public List<ParcelInformation> selectParcelInformationList(ParcelInformation parcelInformation);

    /**
     * 新增宗地信息
     *
     * @param parcelInformation 宗地信息
     * @return 结果
     */
    public int insertParcelInformation(ParcelInformation parcelInformation);

    /**
     * 修改宗地信息
     *
     * @param parcelInformation 宗地信息
     * @return 结果
     */
    public int updateParcelInformation(ParcelInformation parcelInformation);

    /**
     * 删除宗地信息
     *
     * @param id 宗地信息主键
     * @return 结果
     */
    public int deleteParcelInformationById(Long id);

    /**
     * 批量删除宗地信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteParcelInformationByIds(Long[] ids);
}
