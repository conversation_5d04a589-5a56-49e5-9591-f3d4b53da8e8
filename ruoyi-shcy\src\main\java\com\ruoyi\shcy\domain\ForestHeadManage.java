package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 林长管理对象 shcy_forest_head_manage
 * 
 * <AUTHOR>
 * @date 2022-08-05
 */
public class ForestHeadManage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 林长ID */
    private Long id;

    /** 林长 */
    @Excel(name = "林长")
    private String forestHead;

    /** 副林长 */
    @Excel(name = "副林长")
    private String deputyForestHead;

    /** 包干区域 */
    @Excel(name = "包干区域")
    private String packageArea;

    /** 资源类型 */
    @Excel(name = "资源类型")
    private String resourceType;

    /** 名称 */
    @Excel(name = "名称")
    private String name;

    /** 面积 */
    @Excel(name = "面积")
    private String area;

    /** 管理单位 */
    @Excel(name = "管理单位")
    private String managementUnit;

    /** 管理单位责任人 */
    @Excel(name = "管理单位责任人")
    private String managementUnitPerson;

    /** 管理单位电话 */
    @Excel(name = "管理单位电话")
    private String managementUnitTelphone;

    /** 养护单位 */
    @Excel(name = "养护单位")
    private String conservationUnit;

    /** 养护单位责任人 */
    @Excel(name = "养护单位责任人")
    private String conservationUnitPerson;

    /** 养护单位电话 */
    @Excel(name = "养护单位电话")
    private String conservationUnitTelphone;

    /** 管理方式 */
    @Excel(name = "管理方式")
    private String managementStyle;

    /** 工作职责 */
    @Excel(name = "工作职责")
    private String jobDuty;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setForestHead(String forestHead) 
    {
        this.forestHead = forestHead;
    }

    public String getForestHead() 
    {
        return forestHead;
    }
    public void setDeputyForestHead(String deputyForestHead) 
    {
        this.deputyForestHead = deputyForestHead;
    }

    public String getDeputyForestHead() 
    {
        return deputyForestHead;
    }
    public void setPackageArea(String packageArea) 
    {
        this.packageArea = packageArea;
    }

    public String getPackageArea() 
    {
        return packageArea;
    }
    public void setResourceType(String resourceType) 
    {
        this.resourceType = resourceType;
    }

    public String getResourceType() 
    {
        return resourceType;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setArea(String area) 
    {
        this.area = area;
    }

    public String getArea() 
    {
        return area;
    }
    public void setManagementUnit(String managementUnit) 
    {
        this.managementUnit = managementUnit;
    }

    public String getManagementUnit() 
    {
        return managementUnit;
    }
    public void setManagementUnitPerson(String managementUnitPerson) 
    {
        this.managementUnitPerson = managementUnitPerson;
    }

    public String getManagementUnitPerson() 
    {
        return managementUnitPerson;
    }
    public void setManagementUnitTelphone(String managementUnitTelphone) 
    {
        this.managementUnitTelphone = managementUnitTelphone;
    }

    public String getManagementUnitTelphone() 
    {
        return managementUnitTelphone;
    }
    public void setConservationUnit(String conservationUnit) 
    {
        this.conservationUnit = conservationUnit;
    }

    public String getConservationUnit() 
    {
        return conservationUnit;
    }
    public void setConservationUnitPerson(String conservationUnitPerson) 
    {
        this.conservationUnitPerson = conservationUnitPerson;
    }

    public String getConservationUnitPerson() 
    {
        return conservationUnitPerson;
    }
    public void setConservationUnitTelphone(String conservationUnitTelphone) 
    {
        this.conservationUnitTelphone = conservationUnitTelphone;
    }

    public String getConservationUnitTelphone() 
    {
        return conservationUnitTelphone;
    }
    public void setManagementStyle(String managementStyle) 
    {
        this.managementStyle = managementStyle;
    }

    public String getManagementStyle() 
    {
        return managementStyle;
    }
    public void setJobDuty(String jobDuty) 
    {
        this.jobDuty = jobDuty;
    }

    public String getJobDuty() 
    {
        return jobDuty;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("forestHead", getForestHead())
            .append("deputyForestHead", getDeputyForestHead())
            .append("packageArea", getPackageArea())
            .append("resourceType", getResourceType())
            .append("name", getName())
            .append("area", getArea())
            .append("managementUnit", getManagementUnit())
            .append("managementUnitPerson", getManagementUnitPerson())
            .append("managementUnitTelphone", getManagementUnitTelphone())
            .append("conservationUnit", getConservationUnit())
            .append("conservationUnitPerson", getConservationUnitPerson())
            .append("conservationUnitTelphone", getConservationUnitTelphone())
            .append("managementStyle", getManagementStyle())
            .append("jobDuty", getJobDuty())
            .toString();
    }
}
