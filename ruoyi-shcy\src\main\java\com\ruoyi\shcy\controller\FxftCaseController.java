package com.ruoyi.shcy.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.FxftCase;
import com.ruoyi.shcy.service.IFxftCaseService;
import com.ruoyi.shcy.service.IShcyFileInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 防汛防台案事件Controller
 * 
 * <AUTHOR>
 * @date 2023-10-31
 */
@RestController
@RequestMapping("/shcy/fxftCase")
public class FxftCaseController extends BaseController
{
    @Autowired
    private IFxftCaseService fxftCaseService;

    @Autowired
    private IShcyFileInfoService shcyFileInfoService;

    /**
     * 查询防汛防台案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:fxftCase:list')")
    @GetMapping("/list")
    public TableDataInfo list(FxftCase fxftCase)
    {
        startPage();
        List<FxftCase> list = fxftCaseService.selectFxftCaseList(fxftCase);
        return getDataTable(list);
    }

    /**
     * 导出防汛防台案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:fxftCase:export')")
    @Log(title = "防汛防台案事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FxftCase fxftCase)
    {
        List<FxftCase> list = fxftCaseService.selectFxftCaseList(fxftCase);
        ExcelUtil<FxftCase> util = new ExcelUtil<FxftCase>(FxftCase.class);
        util.exportExcel(response, list, "防汛防台案事件数据");
    }

    /**
     * 获取防汛防台案事件详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:fxftCase:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(fxftCaseService.selectFxftCaseById(id));
    }

    /**
     * 新增防汛防台案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:fxftCase:add')")
    @Log(title = "防汛防台案事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FxftCase fxftCase)
    {
        return toAjax(fxftCaseService.insertFxftCase(fxftCase));
    }

    /**
     * 修改防汛防台案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:fxftCase:edit')")
    @Log(title = "防汛防台案事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FxftCase fxftCase)
    {
        return toAjax(fxftCaseService.updateFxftCase(fxftCase));
    }

    /**
     * 删除防汛防台案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:fxftCase:remove')")
    @Log(title = "防汛防台案事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(fxftCaseService.deleteFxftCaseByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('shcy:fxftCase:list')")
    @GetMapping("/fxft/list")
    public TableDataInfo fxftList(FxftCase fxftCase, String keyword,String pipelineType,String deptName)
    {
        if (StrUtil.isNotEmpty(pipelineType)) {
            if(StrUtil.isNotEmpty(deptName) && ("建管中心".equals(deptName)|| "防汛办".equals(deptName)) )
            {
                Map<String, Object> params = new HashMap<String, Object>() {
                    {
                        put("pipelineType", pipelineType);
                    }
                };
                fxftCase.setParams(params);
            }
           else {
                return getDataTable(new ArrayList<FxftCase>());
            }
        }
        else if(StrUtil.isNotEmpty(deptName) && "锦石市政".equals(deptName) )
        {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("pipelineType", "市政管网");
                }
            };
            fxftCase.setParams(params);
        }
        else if(StrUtil.isNotEmpty(deptName) && "建管中心".equals(deptName))
        {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("pipelineType", "小区三级管网");
                }
            };
            fxftCase.setParams(params);
        }else if(StrUtil.isNotEmpty(deptName) && "防汛办".equals(deptName)){
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("pipelineType", null);
                }
            };
            fxftCase.setParams(params);
        }
        else {
            return getDataTable(new ArrayList<FxftCase>());
        }
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            fxftCase.setParams(params);
        }

        startPage();
        // fxftCase.setCaseDealBy(getUsername());
        List<FxftCase> list = fxftCaseService.selectFxftCaseListByCaseNumber(fxftCase);
        return getDataTable(list);
    }

    @PostMapping("/fxft/handle")
    public AjaxResult fxftHandle(@RequestBody FxftCase fxftCase)
    {
        return toAjax(fxftCaseService.handleFxftCase(fxftCase));
    }

    @GetMapping(value = "/fxft/info/{alarmRecordId}")
    public AjaxResult getFxftInfo(@PathVariable("alarmRecordId") Long alarmRecordId)
    {
        return AjaxResult.success(fxftCaseService.selectFxftCaseByAlarmRecordId(alarmRecordId));
    }

    @PreAuthorize("@ss.hasPermi('shcy:fxftCase:list')")
    @GetMapping("/getCaseCount")
    public AjaxResult getCaseCount(FxftCase fxftCase, String keyword,String pipelineType,String deptName) {
        if (StrUtil.isNotEmpty(pipelineType)) {
            if(StrUtil.isNotEmpty(deptName) && ("建管中心".equals(deptName)|| "防汛办".equals(deptName)) )
            {
                Map<String, Object> params = new HashMap<String, Object>() {
                    {
                        put("pipelineType", pipelineType);
                    }
                };
                fxftCase.setParams(params);

            }
            else {
                return AjaxResult.success(0);
            }

        }
        else if(StrUtil.isNotEmpty(deptName) && "锦石市政".equals(deptName) )
        {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("pipelineType", "市政管网");
                }
            };
            fxftCase.setParams(params);
        }
        else if(StrUtil.isNotEmpty(deptName) && "建管中心".equals(deptName))
        {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("pipelineType", "小区三级管网");
                }
            };
            fxftCase.setParams(params);
        }else if(StrUtil.isNotEmpty(deptName) && "防汛办".equals(deptName)){
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("pipelineType", null);
                }
            };
            fxftCase.setParams(params);
        }
        else {
            return AjaxResult.success(0);
        }
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            fxftCase.setParams(params);
        }
        return AjaxResult.success("操作成功", fxftCaseService.getCaseCount(fxftCase));
    }

}
