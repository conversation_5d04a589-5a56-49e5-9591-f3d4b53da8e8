package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.Cover;
import com.ruoyi.shcy.service.ICoverService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 井盖信息Controller
 * 
 * <AUTHOR>
 * @date 2022-12-21
 */
@RestController
@RequestMapping("/shcy/cover")
public class CoverController extends BaseController
{
    @Autowired
    private ICoverService coverService;

    /**
     * 查询井盖信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:cover:list')")
    @GetMapping("/list")
    public TableDataInfo list(Cover cover)
    {
        startPage();
        List<Cover> list = coverService.selectCoverList(cover);
        return getDataTable(list);
    }

    /**
     * 导出井盖信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:cover:export')")
    @Log(title = "井盖信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Cover cover)
    {
        List<Cover> list = coverService.selectCoverList(cover);
        ExcelUtil<Cover> util = new ExcelUtil<Cover>(Cover.class);
        util.exportExcel(response, list, "井盖信息数据");
    }

    /**
     * 获取井盖信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:cover:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(coverService.selectCoverById(id));
    }

    /**
     * 新增井盖信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:cover:add')")
    @Log(title = "井盖信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Cover cover)
    {
        return toAjax(coverService.insertCover(cover));
    }

    /**
     * 修改井盖信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:cover:edit')")
    @Log(title = "井盖信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Cover cover)
    {
        return toAjax(coverService.updateCover(cover));
    }

    /**
     * 删除井盖信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:cover:remove')")
    @Log(title = "井盖信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(coverService.deleteCoverByIds(ids));
    }
}
