package com.ruoyi.shcy.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.dahuatech.icc.exception.ClientException;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FfmpegUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.icc.video.VideoService;
import com.ruoyi.shcy.constant.HjzzConstants;
import com.ruoyi.shcy.constant.SmsConstants;
import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import com.ruoyi.shcy.dto.IccAlarmRecordHandleDTO;
import com.ruoyi.shcy.mapper.HjzzCaseMapper;
import com.ruoyi.shcy.mapper.IccAlarmRecordMapper;
import com.ruoyi.shcy.service.IIccAlarmRecordService;
import com.ruoyi.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * ICC报警记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Service
@Slf4j
public class IccAlarmRecordServiceImpl implements IIccAlarmRecordService
{

    @Autowired
    private IccAlarmRecordMapper iccAlarmRecordMapper;

    @Autowired
    private HjzzCaseMapper hjzzCaseMapper;

    @Autowired
    private ISysConfigService configService;

    private VideoService videoService;

    @Autowired
    private IccService iccService;

    private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    {
        try {
            videoService = new VideoService();
        } catch (ClientException e) {
            log.error("初始化客户端失败", e);
            videoService = null;
        }
    }

    /**
     * 查询ICC报警记录
     *
     * @param id ICC报警记录主键
     * @return ICC报警记录
     */
    @Override
    public IccAlarmRecord selectIccAlarmRecordById(Long id)
    {
        return iccAlarmRecordMapper.selectIccAlarmRecordById(id);
    }

    /**
     * 查询ICC报警记录列表
     *
     * @param iccAlarmRecord ICC报警记录
     * @return ICC报警记录
     */
    @Override
    public List<IccAlarmRecord> selectIccAlarmRecordList(IccAlarmRecord iccAlarmRecord)
    {
        return iccAlarmRecordMapper.selectIccAlarmRecordList(iccAlarmRecord);
    }

    /**
     * 新增ICC报警记录
     *
     * @param iccAlarmRecord ICC报警记录
     * @return 结果
     */
    @Override
    public int insertIccAlarmRecord(IccAlarmRecord iccAlarmRecord)
    {
        return iccAlarmRecordMapper.insertIccAlarmRecord(iccAlarmRecord);
    }

    /**
     * 修改ICC报警记录
     *
     * @param iccAlarmRecord ICC报警记录
     * @return 结果
     */
    @Override
    public int updateIccAlarmRecord(IccAlarmRecord iccAlarmRecord)
    {
        return iccAlarmRecordMapper.updateIccAlarmRecord(iccAlarmRecord);
    }

    /**
     * 批量删除ICC报警记录
     *
     * @param ids 需要删除的ICC报警记录主键
     * @return 结果
     */
    @Override
    public int deleteIccAlarmRecordByIds(Long[] ids)
    {
        return iccAlarmRecordMapper.deleteIccAlarmRecordByIds(ids);
    }

    /**
     * 删除ICC报警记录信息
     *
     * @param id ICC报警记录主键
     * @return 结果
     */
    @Override
    public int deleteIccAlarmRecordById(Long id)
    {
        return iccAlarmRecordMapper.deleteIccAlarmRecordById(id);
    }

    @Override
    public List<IccAlarmRecord> selectIccAlarmRecordUnprocessedList(IccAlarmRecord iccAlarmRecord) {
        return iccAlarmRecordMapper.selectIccAlarmRecordUnprocessedList(iccAlarmRecord);
    }

    @Override
    public List<IccAlarmRecord> selectIccAlarmRecordProcessingList(IccAlarmRecord iccAlarmRecord) {
        return iccAlarmRecordMapper.selectIccAlarmRecordProcessingList(iccAlarmRecord);
    }

    @Override
    public List<IccAlarmRecord> selectIccAlarmRecordProcessedList(IccAlarmRecord iccAlarmRecord) {
        return iccAlarmRecordMapper.selectIccAlarmRecordProcessedList(iccAlarmRecord);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int hjzzHandle(IccAlarmRecordHandleDTO iccAlarmRecordHandleDTO) {

        // 1.误报处理
        if (HjzzConstants.FALSE_ALARM.equals(iccAlarmRecordHandleDTO.getType())) {
            for (Long id : iccAlarmRecordHandleDTO.getIds()) {
                IccAlarmRecord iccAlarmRecord = new IccAlarmRecord();
                iccAlarmRecord.setId(id);
                iccAlarmRecord.setStatus(HjzzConstants.FALSE_ALARM);
                iccAlarmRecordMapper.updateIccAlarmRecordStatus(iccAlarmRecord);
            }
        }
        // 2.非误报处理
        else {
            for (Long id : iccAlarmRecordHandleDTO.getIds()) {
                IccAlarmRecord iccAlarmRecord = new IccAlarmRecord();
                iccAlarmRecord.setId(id);
                iccAlarmRecord.setStatus(HjzzConstants.UNPROCESSED);
                iccAlarmRecordMapper.updateIccAlarmRecordStatus(iccAlarmRecord);
                // 2.1.获取报警记录
                IccAlarmRecord alarmRecord = iccAlarmRecordMapper.selectIccAlarmRecordById(id);

                // 2.2.新建案件
                HjzzCase hjzzCase = new HjzzCase();
                hjzzCase.setAlarmDate(alarmRecord.getAlarmDate());
                hjzzCase.setCaseName(alarmRecord.getAlarmTypeName());
                hjzzCase.setCaseType(HjzzConstants.CASE_TYPE_TDLJ);
                hjzzCase.setCaseDescription("通过布控摄像头抓拍偷倒垃圾行为");
                hjzzCase.setCirculationState(HjzzConstants.UNPROCESSED);
                hjzzCase.setDealInTimeState("0");
                hjzzCase.setAddress(alarmRecord.getAlarmPosition());
                hjzzCase.setAlarmRecordId(id);
                // 截至日期 =》 当前时间 + 1小时
                String timeLimit = configService.selectConfigByKey("shcy.hjzz.timeLimit");
                hjzzCase.setCaseEndTime(DateUtils.addHours(new Date(), Integer.parseInt(timeLimit)));
                String investigationTImeLimit = configService.selectConfigByKey("shcy.hjzz.investigationTImeLimit");
                hjzzCase.setInvestigationDeadline(DateUtils.addHours(new Date(), Integer.parseInt(investigationTImeLimit)));
                // 事件编号 =》 日期 + 10位随机数
                hjzzCase.setCaseNumber(DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(10));
                hjzzCase.setLicensePlate(alarmRecord.getLicensePlate());
                hjzzCase.setVehicleType(alarmRecord.getVehicleType());
                hjzzCase.setCreateBy(SecurityUtils.getLoginUser().getUsername());
                hjzzCase.setCreateTime(new Date());
                hjzzCaseMapper.insertHjzzCase(hjzzCase);

                // ffmpeg视频转换
                String m3u8Url = getM3u8Url(alarmRecord.getAlarmCode());
                String mp4Path = RuoYiConfig.getProfile() + "/hjzz/" + hjzzCase.getCaseNumber() + ".mp4";
                StringBuilder command = new StringBuilder();
                command.append("ffmpeg -i ")
                        .append(m3u8Url)
                        .append(" -vcodec copy -acodec copy -absf aac_adtstoasc ")
                        .append(mp4Path)
                        .append(" -y");
                // try {
                //     Runtime.getRuntime().exec(command.toString());
                //     log.info("执行命令：" + command.toString());
                // } catch (IOException e) {
                //     throw new RuntimeException(e);
                // }
                String[] cmdLine = {"cmd", "/c", command.toString()};
                // 异步执行

                // FfmpegUtils.useCmd(cmdLine);
                CompletableFuture.runAsync(() -> {
                    FfmpegUtils.useCmd(cmdLine);
                });

                // 是否发送短信
                String sendSms = configService.selectConfigByKey("shcy.hjzz.sendSms");
                boolean isSendSms = Convert.toBool(sendSms);
                // 发送短信
                if (isSendSms) {
                    // 参数
                    LinkedHashMap<String, String> params = new LinkedHashMap<>();
                    params.put("road", alarmRecord.getAlarmPosition());
                    params.put("date", alarmRecord.getAlarmDate());
                    params.put("car_num", alarmRecord.getLicensePlate());
                    String phones = configService.selectConfigByKey("shcy.hjzz.phone");
                    if (StrUtil.isNotEmpty(phones)) {
                        String[] split = phones.split(",");
                        // split转list
                        List<String> phoneList = Arrays.asList(split);
                        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
                        phoneList.forEach(phone1 -> {
                            smsBlend.sendMessageAsync(phone1, SmsConstants.TEMPLATE_CODE_TDLJ, params);
                        });
                    }
                }
            }

        }
        return 1;
    }

    @Override
    public List<IccAlarmRecord> selectIccAlarmRecordUnprocessedAndProcessingList(IccAlarmRecord iccAlarmRecord) {
        return iccAlarmRecordMapper.selectIccAlarmRecordUnprocessedAndProcessingList(iccAlarmRecord);
    }

    @Override
    public IccAlarmRecord selectIccAlarmRecordByCarRecordId(Long carRecordId) {
        return iccAlarmRecordMapper.selectIccAlarmRecordByCarRecordId(carRecordId);
    };

    @Override
    public IccAlarmRecord selectIccAlarmRecordByAlarmRecordId(Long alarmRecordId) {
        return iccAlarmRecordMapper.selectIccAlarmRecordByAlarmRecordId(alarmRecordId);
    };

    private String getM3u8Url(String alarmCode) {
        String m3u8Url = "";
        try {
            // JsonNode records = videoService.getAlarmRecords(alarmCode);
            // JsonNode videoRecord = records.get(0);
            // String channelId = videoRecord.get("channelId").asText();
            // String recordSource = videoRecord.get("recordSource").asText();
            // String startTime = videoRecord.get("startTime").asText();
            // String endTime = videoRecord.get("endTime").asText();
            // startTime = simpleDateFormat.format(new Date(Long.parseLong(startTime) * 1000));
            // endTime = simpleDateFormat.format(new Date(Long.parseLong(endTime) * 1000));
            // String url = videoService.replay(channelId, "1", "hls", recordSource, "2", startTime, endTime);
            String url = videoService.getAlarmVideoPlayback(alarmCode);
            String accessToken = iccService.getAccessToken();
            m3u8Url = url + "?token=" + accessToken;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return m3u8Url;
    }

    @Override
    public List<IccAlarmRecord> selectIccAlarmRecordByIds(Long[] alarmRecordIds) {
        return iccAlarmRecordMapper.selectIccAlarmRecordByIds(alarmRecordIds);
    }

}
