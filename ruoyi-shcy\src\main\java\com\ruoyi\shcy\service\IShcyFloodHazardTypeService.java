package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyFloodHazardType;

/**
 * 隐患排查类型Service接口
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
public interface IShcyFloodHazardTypeService
{
    /**
     * 查询隐患排查类型
     *
     * @param id 隐患排查类型主键
     * @return 隐患排查类型
     */
    public ShcyFloodHazardType selectShcyFloodHazardTypeById(Long id);

    /**
     * 查询隐患排查类型列表
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 隐患排查类型集合
     */
    public List<ShcyFloodHazardType> selectShcyFloodHazardTypeList(ShcyFloodHazardType shcyFloodHazardType);

    /**
     * 新增隐患排查类型
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 结果
     */
    public int insertShcyFloodHazardType(ShcyFloodHazardType shcyFloodHazardType);

    /**
     * 修改隐患排查类型
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 结果
     */
    public int updateShcyFloodHazardType(ShcyFloodHazardType shcyFloodHazardType);

    /**
     * 批量删除隐患排查类型
     *
     * @param ids 需要删除的隐患排查类型主键集合
     * @return 结果
     */
    public int deleteShcyFloodHazardTypeByIds(Long[] ids);

    /**
     * 删除隐患排查类型信息
     *
     * @param id 隐患排查类型主键
     * @return 结果
     */
    public int deleteShcyFloodHazardTypeById(Long id);

    boolean hasChildById(Long id);
}
