package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ForestHeadManage;
import com.ruoyi.shcy.service.IForestHeadManageService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 林长管理Controller
 * 
 * <AUTHOR>
 * @date 2022-08-05
 */
@RestController
@RequestMapping("/shcy/forest")
public class ForestHeadManageController extends BaseController
{
    @Autowired
    private IForestHeadManageService forestHeadManageService;

    /**
     * 查询林长管理列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:forest:list')")
    @GetMapping("/list")
    public TableDataInfo list(ForestHeadManage forestHeadManage)
    {
        startPage();
        List<ForestHeadManage> list = forestHeadManageService.selectForestHeadManageList(forestHeadManage);
        return getDataTable(list);
    }

    /**
     * 导出林长管理列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:forest:export')")
    @Log(title = "林长管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ForestHeadManage forestHeadManage)
    {
        List<ForestHeadManage> list = forestHeadManageService.selectForestHeadManageList(forestHeadManage);
        ExcelUtil<ForestHeadManage> util = new ExcelUtil<ForestHeadManage>(ForestHeadManage.class);
        util.exportExcel(response, list, "林长管理数据");
    }

    /**
     * 获取林长管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:forest:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(forestHeadManageService.selectForestHeadManageById(id));
    }

    /**
     * 新增林长管理
     */
    @PreAuthorize("@ss.hasPermi('shcy:forest:add')")
    @Log(title = "林长管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ForestHeadManage forestHeadManage)
    {
        return toAjax(forestHeadManageService.insertForestHeadManage(forestHeadManage));
    }

    /**
     * 修改林长管理
     */
    @PreAuthorize("@ss.hasPermi('shcy:forest:edit')")
    @Log(title = "林长管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ForestHeadManage forestHeadManage)
    {
        return toAjax(forestHeadManageService.updateForestHeadManage(forestHeadManage));
    }

    /**
     * 删除林长管理
     */
    @PreAuthorize("@ss.hasPermi('shcy:forest:remove')")
    @Log(title = "林长管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(forestHeadManageService.deleteForestHeadManageByIds(ids));
    }
}
