package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.GreenGridMapper;
import com.ruoyi.shcy.domain.GreenGrid;
import com.ruoyi.shcy.service.IGreenGridService;

/**
 * 绿化网格Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-02-09
 */
@Service
public class GreenGridServiceImpl implements IGreenGridService 
{
    @Autowired
    private GreenGridMapper greenGridMapper;

    /**
     * 查询绿化网格
     * 
     * @param id 绿化网格主键
     * @return 绿化网格
     */
    @Override
    public GreenGrid selectGreenGridById(Long id)
    {
        return greenGridMapper.selectGreenGridById(id);
    }

    /**
     * 查询绿化网格列表
     * 
     * @param greenGrid 绿化网格
     * @return 绿化网格
     */
    @Override
    public List<GreenGrid> selectGreenGridList(GreenGrid greenGrid)
    {
        return greenGridMapper.selectGreenGridList(greenGrid);
    }

    /**
     * 新增绿化网格
     * 
     * @param greenGrid 绿化网格
     * @return 结果
     */
    @Override
    public int insertGreenGrid(GreenGrid greenGrid)
    {
        greenGrid.setCreateTime(DateUtils.getNowDate());
        return greenGridMapper.insertGreenGrid(greenGrid);
    }

    /**
     * 修改绿化网格
     * 
     * @param greenGrid 绿化网格
     * @return 结果
     */
    @Override
    public int updateGreenGrid(GreenGrid greenGrid)
    {
        greenGrid.setUpdateTime(DateUtils.getNowDate());
        return greenGridMapper.updateGreenGrid(greenGrid);
    }

    /**
     * 批量删除绿化网格
     * 
     * @param ids 需要删除的绿化网格主键
     * @return 结果
     */
    @Override
    public int deleteGreenGridByIds(Long[] ids)
    {
        return greenGridMapper.deleteGreenGridByIds(ids);
    }

    /**
     * 删除绿化网格信息
     * 
     * @param id 绿化网格主键
     * @return 结果
     */
    @Override
    public int deleteGreenGridById(Long id)
    {
        return greenGridMapper.deleteGreenGridById(id);
    }
}
