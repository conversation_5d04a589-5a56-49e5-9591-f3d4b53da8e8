package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 井盖信息对象 shcy_cover
 *
 * <AUTHOR>
 * @date 2022-12-21
 */
public class Cover extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 外业点号 */
    @Excel(name = "外业点号")
    private String fieldStationNo;

    /** 管线性质 */
    @Excel(name = "管线性质")
    private String pipelineProperty;

    /** 埋深（米） */
    @Excel(name = "埋深", readConverterExp = "米=")
    private String burialDepth;

    /** 管线材质 */
    @Excel(name = "管线材质")
    private String pipelineMaterial;

    /** 所在道路 */
    @Excel(name = "所在道路")
    private String road;

    /** 井深（米） */
    @Excel(name = "井深", readConverterExp = "米=")
    private String wellDepth;

    /** 井盖材质 */
    @Excel(name = "井盖材质")
    private String coverMaterial;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;


    /** 吴淞高程 */
    @Excel(name ="吴淞高程")
    private String wsgc;

    public String getWsgc() {
        return wsgc;
    }

    public void setWsgc(String wsgc) {
        this.wsgc = wsgc;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setFieldStationNo(String fieldStationNo)
    {
        this.fieldStationNo = fieldStationNo;
    }

    public String getFieldStationNo()
    {
        return fieldStationNo;
    }
    public void setPipelineProperty(String pipelineProperty)
    {
        this.pipelineProperty = pipelineProperty;
    }

    public String getPipelineProperty()
    {
        return pipelineProperty;
    }
    public void setBurialDepth(String burialDepth)
    {
        this.burialDepth = burialDepth;
    }

    public String getBurialDepth()
    {
        return burialDepth;
    }
    public void setPipelineMaterial(String pipelineMaterial)
    {
        this.pipelineMaterial = pipelineMaterial;
    }

    public String getPipelineMaterial()
    {
        return pipelineMaterial;
    }
    public void setRoad(String road)
    {
        this.road = road;
    }

    public String getRoad()
    {
        return road;
    }
    public void setWellDepth(String wellDepth)
    {
        this.wellDepth = wellDepth;
    }

    public String getWellDepth()
    {
        return wellDepth;
    }
    public void setCoverMaterial(String coverMaterial)
    {
        this.coverMaterial = coverMaterial;
    }

    public String getCoverMaterial()
    {
        return coverMaterial;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setCoordinate(String coordinate)
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate()
    {
        return coordinate;
    }

    @Override
    public String toString() {
        return "Cover{" +
                "id=" + id +
                ", fieldStationNo='" + fieldStationNo + '\'' +
                ", pipelineProperty='" + pipelineProperty + '\'' +
                ", burialDepth='" + burialDepth + '\'' +
                ", pipelineMaterial='" + pipelineMaterial + '\'' +
                ", road='" + road + '\'' +
                ", wellDepth='" + wellDepth + '\'' +
                ", coverMaterial='" + coverMaterial + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", wsgc='" + wsgc + '\'' +
                '}';
    }
}
