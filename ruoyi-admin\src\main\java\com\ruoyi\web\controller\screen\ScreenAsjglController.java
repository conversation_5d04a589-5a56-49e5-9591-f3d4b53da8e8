package com.ruoyi.web.controller.screen;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.screen.vo.DlCountVO;
import com.ruoyi.screen.vo.SqVO;
import com.ruoyi.screen.vo.SqxlVo;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.dto.TaskDbcenterRxDTO;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 大屏城运业务 Controller
 */
@RestController
@RequestMapping("/screen/asjgl")
public class ScreenAsjglController extends BaseController {

    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    /**
     * 当前年月热线工单数量统计
     */
    @GetMapping("/yearMonthHotlineCaseNumber")
    public AjaxResult yearMonthHotlineCaseNumber() {
        Map<String, String> timeRange = getTimeRange();
        String beginTime = timeRange.get("beginTime");
        String endTime = timeRange.get("endTime");

        List<TaskDbcenter> list = getTaskDbcenterList(beginTime, endTime);

        // 当前年度的数据
        Map<String, Long> map = list.stream().collect(Collectors.groupingBy(task -> DateUtil.format(task.getDiscovertime(), "yyyy-MM"), Collectors.counting()));

        // 上个年度的数据
        Map<String, String> previousYearRange = getPreviousYearRange(beginTime, endTime);
        String previousBeginTime = previousYearRange.get("beginTime");
        String previousEndTime = previousYearRange.get("endTime");

        List<TaskDbcenter> previousYearList = getTaskDbcenterList(previousBeginTime, previousEndTime);
        Map<String, Long> previousYearMap = previousYearList.stream().collect(Collectors.groupingBy(task -> DateUtil.format(task.getDiscovertime(), "yyyy-MM"), Collectors.counting()));

        // 根据beginTime和endTime列出所有的年月，yyyy-MM格式返回字符串数组
        List<String > monthList = DateUtils.listMonths(beginTime, endTime);

        // 根据previousBeginTime和previousEndTime列出所有的年月，yyyy-MM格式返回字符串数组
        List<String > previousMonthList = DateUtils.listMonths(previousBeginTime, previousEndTime);

        HashMap<String, Object> result = new HashMap<String, Object>() {{
            put("monthList", monthList);
            put("yearMonthCaseNumberList", map);
            put("previousMonthList", previousMonthList);
            put("previousYearCaseNumberList", previousYearMap);
        }};
        return AjaxResult.success(result);
    }

    /**
     * 根据三级主责部门subexecutedeptnameMh统计案件数量
     */
    @GetMapping("/caseNumberBySubexecutedeptnameMh")
    public AjaxResult caseNumberBySubexecutedeptnameMh() {
        Map<String, String> timeRange = getTimeRange();
        String beginTime = timeRange.get("beginTime");
        String endTime = timeRange.get("endTime");

        List<TaskDbcenter> list = getTaskDbcenterList(beginTime, endTime);

        // 使用stream从list集合中过滤掉subexecutedeptnameMh为空的数据，然后根据subexecutedeptnameMh字段进行分组，统计每组的数量
        Map<String, Long> map = list.stream()
            .filter(task -> task.getSubexecutedeptnameMh() != null && !task.getSubexecutedeptnameMh().isEmpty())
            .collect(Collectors.groupingBy(TaskDbcenter::getSubexecutedeptnameMh, Collectors.counting()));  

        HashMap<String, Object> result = new HashMap<String, Object>() {{
            put("subexecutedeptnameMhList", getSubexecutedeptnameMhList());
            put("residentialAreaList", getResidentialAreaList());
            put("caseNumberBySubexecutedeptnameMhList", map);
        }};
        return AjaxResult.success(result);
            
    }

    /**
     * 根据案件类型诉求大类parentappealclassification、诉求小类appealclassification统计案件数量
     */ 
    @GetMapping("/caseNumberByDemandType")
    public AjaxResult caseNumberByDemandType(String type, String beginTime, String endTime) {
        if (StrUtil.isEmpty(beginTime) || StrUtil.isEmpty(endTime)) {
            Map<String, String> timeRange = getTimeRange();
            beginTime = timeRange.get("beginTime");
            endTime = timeRange.get("endTime");
        }

        List<TaskDbcenter> list = getTaskDbcenterList(beginTime, endTime);
        list = filterCasesByType(list, type);

        // 1.先统计诉求大类parentappealclassification的数量，在统计诉求小类appealclassification的数量 最终定义一个List<SqVO>，包含sqdl、sqdlCount、sqxl、sqxlCount
        Map<String, Long> sqdlMap = list.stream()
            .filter(task -> task.getParentappealclassification() != null && !task.getParentappealclassification().isEmpty())
            .collect(Collectors.groupingBy(TaskDbcenter::getParentappealclassification, Collectors.counting()));

        List<SqVO> sqVOList = new ArrayList<>();

        // 遍历诉求大类，统计每个大类下面的诉求小类appealclassification的数量
        for (Map.Entry<String, Long> entry : sqdlMap.entrySet()) {
            String sqdl = entry.getKey();
            long sqdlCount = entry.getValue();
            // 统计诉求小类appealclassification的数量
            Map<String, Long> sqxlMap = list.stream()
                .filter(task -> task.getAppealclassification() != null && !task.getAppealclassification().isEmpty() && task.getParentappealclassification().equals(sqdl))
                .collect(Collectors.groupingBy(TaskDbcenter::getAppealclassification, Collectors.counting()));
            List<SqxlVo> sqxlList = new ArrayList<>();
            for (Map.Entry<String, Long> sqxlEntry : sqxlMap.entrySet()) {
                String sqxl = sqxlEntry.getKey();
                long sqxlCount = sqxlEntry.getValue();
                SqxlVo sqxlVo = new SqxlVo();
                sqxlVo.setSqxl(sqxl);
                sqxlVo.setSqxlCount(sqxlCount);
                sqxlList.add(sqxlVo);
            }
            SqVO sqVO = new SqVO();
            sqVO.setSqdl(sqdl);
            sqVO.setSqdlCount(sqdlCount);
            sqVO.setSqxlList(sqxlList);
            sqVOList.add(sqVO);
        }

        // 对sqVOList按照sqdlCount降序排序
        sqVOList.sort(Comparator.comparing(SqVO::getSqdlCount).reversed());
        return AjaxResult.success(sqVOList);
    }

    /**
     * 根据开始时间、结束时间获取案事件列表
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @param type 类型（1、案件总数 2、重复案件 3、重点案件 4、紧急案件）
     * @param residentialarea 居民区
     */
    @GetMapping("/caseList")
    public AjaxResult caseList(String beginTime, String endTime, String type, String residentialarea) {
        List<TaskDbcenter> list = getTaskDbcenterList(beginTime, endTime);
        list = filterCasesByType(list, type);
        if (StrUtil.isNotEmpty(residentialarea)) {
            list = list.stream().filter(task -> residentialarea.equals(task.getResidentialarea())).collect(Collectors.toList());
        }
        List<TaskDbcenterRxDTO> resultList = BeanUtil.copyToList(list, TaskDbcenterRxDTO.class);
        return AjaxResult.success(resultList);
    }

    /**
     * 根据类型获取TOP1居民区名称
     * @param type 类型（1、案件总数 2、重复案件 3、重点案件 4、紧急案件）
     */
    @GetMapping("/getMaxCaseNumberResidentialareaByType")
    public AjaxResult getMaxCaseNumberResidentialareaByType(String type) {
        Map<String, String> timeRange = getTimeRange();
        String beginTime = timeRange.get("beginTime");
        String endTime = timeRange.get("endTime");
        List<TaskDbcenter> list = getTaskDbcenterList(beginTime, endTime);
        list = filterCasesByType(list, type);
        // 使用stream从list集合中过滤掉residentialarea为空的数据，然后根据residentialarea字段进行分组，统计每组的数量
        Map<String, Long> map = list.stream()
            .filter(task -> task.getResidentialarea() != null && !task.getResidentialarea().isEmpty())
            .collect(Collectors.groupingBy(TaskDbcenter::getResidentialarea, Collectors.counting()));
        // 根据案件数量降序排序
        String maxResidentialarea = map.entrySet().stream().max(Comparator.comparing(Map.Entry::getValue)).get().getKey();
        return AjaxResult.success((Object) maxResidentialarea);
    }

    /**
     * 根据type和residentialarea获取案件总数最多的前三个诉求大类
     */
    @GetMapping("/getCountByTypeAndResidentialarea")
    public AjaxResult getCountByTypeAndResidentialarea(String type, String residentialarea) {
        Map<String, String> timeRange = getTimeRange();
        String beginTime = timeRange.get("beginTime");
        String endTime = timeRange.get("endTime");
        List<TaskDbcenter> list = getTaskDbcenterList(beginTime, endTime);
        list = filterCasesByType(list, type);
        // 判断是否需要过滤居民区
        if (StrUtil.isNotEmpty(residentialarea)) {
            list = list.stream().filter(task -> residentialarea.equals(task.getResidentialarea())).collect(Collectors.toList());
        }
        // 使用stream从list集合中过滤掉parentappealclassification为空的数据，然后根据parentappealclassification字段进行分组，统计每组的数量
        Map<String, Long> map = list.stream()
            .filter(task -> task.getParentappealclassification() != null && !task.getParentappealclassification().isEmpty())
            .collect(Collectors.groupingBy(TaskDbcenter::getParentappealclassification, Collectors.counting()));
        // 根据案件数量降序排序
        List<Map.Entry<String, Long>> sortedList = map.entrySet().stream().sorted((o1, o2) -> o2.getValue().compareTo(o1.getValue())).collect(Collectors.toList());
        List<String> resultList = new ArrayList<>();
        for (int i = 0; i < 3 && i < sortedList.size(); i++) {
            resultList.add(sortedList.get(i).getKey());
        }

        List<DlCountVO> dlCountList = new ArrayList<>();
        // 遍历resultList，获取每个诉求大类对应的案件list，再根据Discovertime分组，统计每组的数量 返回Map<String,Long>
        for (String parentappealclassification : resultList) {
            List<TaskDbcenter> caseList = list.stream().filter(task -> parentappealclassification.equals(task.getParentappealclassification())).collect(Collectors.toList());
            Map<String, Long> caseMap = caseList.stream().collect(Collectors.groupingBy(task -> DateUtil.format(task.getDiscovertime(), "yyyy-MM"), Collectors.counting()));
            DlCountVO dlCountVO = new DlCountVO();
            dlCountVO.setDl(parentappealclassification);
            dlCountVO.setDlCount(caseMap);
            dlCountList.add(dlCountVO);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("monthList", DateUtils.listMonths(beginTime, endTime));
        result.put("dlCountList", dlCountList);

        return AjaxResult.success(result);
    }


    /**
     * 根据所属居委会residentialarea统计案件数量
     */
    @GetMapping("/caseNumberByResidentialarea")
    public AjaxResult caseNumberByResidentialarea(String type) {
        Map<String, String> timeRange = getTimeRange();
        String beginTime = timeRange.get("beginTime");
        String endTime = timeRange.get("endTime");

        List<TaskDbcenter> list = getTaskDbcenterList(beginTime, endTime);
        list = filterCasesByType(list, type);

        // 使用stream从list集合中过滤掉residentialarea为空的数据，然后根据residentialarea字段进行分组，统计每组的数量
        Map<String, Long> map = list.stream()
            .filter(task -> task.getResidentialarea() != null && !task.getResidentialarea().isEmpty())
            .collect(Collectors.groupingBy(TaskDbcenter::getResidentialarea, Collectors.counting()));

        return AjaxResult.success(map);
    }

    /**
     * 获取热线紧急类工单
     */
    @GetMapping("/getUrgentCaseList")
    public AjaxResult getUrgentCaseList() {
        Map<String, String> timeRange = getTimeRange();
        String beginTime = timeRange.get("beginTime");
        String endTime = timeRange.get("endTime");
        List<TaskDbcenter> list = getTaskDbcenterList(beginTime, endTime);
        // 4: 紧急案件
        list = filterCasesByType(list, "4");
        return AjaxResult.success(list);
    }

    private Map<String, String> getTimeRange() {
        LocalDate currentDate = LocalDate.now();
        // 临时将当前日期设置为2024-10-31,后期再改回来
        // currentDate = LocalDate.of(2024, 10, 31);
        int currentYear = currentDate.getYear();
        Month currentMonth = currentDate.getMonth();
        String beginTime;
        String endTime;

        if (currentMonth == Month.NOVEMBER || currentMonth == Month.DECEMBER) {
            int nextYear = currentYear + 1;
            LocalDate startDate = LocalDate.of(nextYear - 1, Month.NOVEMBER, 1);
            LocalDate endDate = LocalDate.of(nextYear, Month.OCTOBER, 31);
            beginTime = startDate.toString();
            endTime = endDate.toString();
        } else {
            LocalDate startDate = LocalDate.of(currentYear - 1, Month.NOVEMBER, 1);
            LocalDate endDate = LocalDate.of(currentYear, Month.OCTOBER, 31);
            beginTime = startDate.toString();
            endTime = endDate.toString();
        }

        Map<String, String> timeRange = new HashMap<>();
        timeRange.put("beginTime", beginTime);
        timeRange.put("endTime", endTime);
        return timeRange;
    }

    public static Map<String, String> getPreviousYearRange(String startDateStr, String endDateStr) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 解析开始日期和结束日期
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        // 计算上个年度的开始日期和结束日期
        LocalDate previousStartDate = startDate.minusYears(1);
        LocalDate previousEndDate = endDate.minusYears(1);

        // 格式化回字符串
        Map<String, String> previousYearRange = new HashMap<>();
        previousYearRange.put("beginTime", previousStartDate.format(formatter));
        previousYearRange.put("endTime", previousEndDate.format(formatter));

        return previousYearRange;
    }

    private List<TaskDbcenter> getTaskDbcenterList(String beginTime, String endTime) {
        TaskDbcenter taskDbcenter = new TaskDbcenter();
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("beginTime", beginTime);
        taskDbcenter.getParams().put("endTime", endTime);
        return taskDbcenterService.selectSimpleTaskDbcenterList(taskDbcenter);
    }

    /**
     * 获取三级主责部门列表
     */
    private List<String> getSubexecutedeptnameMhList() {
        return Arrays.asList(
            "石化街道党政办公室",
            "石化街道社区党群办公室",
            "石化街道社区管理办",
            "石化街道服务办",
            "石化街道社区平安办公室",
            "石化街道社会工作办公室",
            "石化街道干部人事办公室",
            "石化街道营商环境办公室",
            "石化街道社区事务受理中心",
            "石化街道建管中心",
            "石化街道综合行政执法队",
            "石化街道城运中心（网格化）",
            "石化街道城运中心（应急）",
            "石化街道管违办",
            "昭缘经济发展有限公司",
            "石化街道社区平安办隔离点",
            "石化街道社区党群服务中心"
        );
    }

    /**
     * 获取居民区列表
     */
    private List<String> getResidentialAreaList() {
        return Arrays.asList(
                "三村居民区",
                "东礁一居民区",
                "山龙居民区",
                "东村居民区",
                "辰凯居民区",
                "梅州居民区",
                "紫卫居民区",
                "东礁二居民区",
                "十二村居民区",
                "卫清居民区",
                "海棠居民区",
                "九村居民区",
                "十三村居民区",
                "滨二居民区",
                "滨一居民区",
                "桥园居民区",
                "合生居民区",
                "东泉居民区",
                "合浦居民区",
                "临三居民区",
                "十村居民区",
                "七村居民区",
                "柳城居民区",
                "临蒙居民区",
                "山鑫居民区",
                "四村居民区"
        );
    }

    /**
     * 根据类型过滤案件列表
     * @param list 原始案件列表
     * @param type 类型（1、案件总数 2、重复案件 3、重点案件 4、紧急案件）
     * @return 过滤后的案件列表
     */
    private List<TaskDbcenter> filterCasesByType(List<TaskDbcenter> list, String type) {
        switch (type) {
            case "2":
                // 重复案件 isduplicate = 是
                return list.stream().filter(task -> "是".equals(task.getIsduplicate())).collect(Collectors.toList());
            case "3":
                // 重点案件 (诉求大类parentappealclassification包含以下类型)
                return list.stream().filter(task -> {
                    String appealType = task.getParentappealclassification();
                    return appealType != null && (
                        appealType.equals("违法建筑") ||
                        appealType.equals("大气污染") ||
                        appealType.equals("求助类") ||
                        appealType.equals("讨薪") ||
                        appealType.equals("防汛") ||
                        appealType.equals("违规生产") ||
                        appealType.equals("消防安全")
                    );
                }).collect(Collectors.toList());
            case "4":
                // 紧急案件 紧急程度urgentdegree=1或者2
                return list.stream().filter(task -> task.getUrgentdegree() == 1 || task.getUrgentdegree() == 2).collect(Collectors.toList());
            default:
                // 案件总数，不需要过滤
                return list;
        }
    }

}
