package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.HjzzCaseMonthly;

import java.util.List;

/**
 * 每月案件情况分析Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-22
 */
public interface HjzzCaseMonthlyMapper 
{
    /**
     * 查询每月案件情况分析
     * 
     * @param id 每月案件情况分析主键
     * @return 每月案件情况分析
     */
    public HjzzCaseMonthly selectHjzzCaseMonthlyById(Long id);

    /**
     * 查询每月案件情况分析列表
     * 
     * @param hjzzCaseMonthly 每月案件情况分析
     * @return 每月案件情况分析集合
     */
    public List<HjzzCaseMonthly> selectHjzzCaseMonthlyList(HjzzCaseMonthly hjzzCaseMonthly);

    /**
     * 新增每月案件情况分析
     * 
     * @param hjzzCaseMonthly 每月案件情况分析
     * @return 结果
     */
    public int insertHjzzCaseMonthly(HjzzCaseMonthly hjzzCaseMonthly);

    /**
     * 修改每月案件情况分析
     * 
     * @param hjzzCaseMonthly 每月案件情况分析
     * @return 结果
     */
    public int updateHjzzCaseMonthly(HjzzCaseMonthly hjzzCaseMonthly);

    /**
     * 删除每月案件情况分析
     * 
     * @param id 每月案件情况分析主键
     * @return 结果
     */
    public int deleteHjzzCaseMonthlyById(Long id);

    /**
     * 批量删除每月案件情况分析
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHjzzCaseMonthlyByIds(Long[] ids);

    HjzzCaseMonthly selectHjzzCaseMonthlyByYearAndMonth(HjzzCaseMonthly hjzzCaseMonthly);
}
