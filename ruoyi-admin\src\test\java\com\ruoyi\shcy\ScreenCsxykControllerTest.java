package com.ruoyi.shcy;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDate;
import java.time.Month;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest
public class ScreenCsxykControllerTest {

    @Autowired
    private ITaskDbcenterService taskDbcenterService;


    @Test
    public void contextLoads() {
        // 这里没有任何代码，只是确保应用程序上下文能够成功加载
    }


    @Test
    public void testHotlineCaseNumber() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        Month currentMonth = currentDate.getMonth();
        String beginTime;
        String endTime;
        // 判断当前月份
        if (currentMonth == Month.NOVEMBER || currentMonth == Month.DECEMBER) {
            // 当前月份是11或12月，取下一个周期（今年11月到明年10月）
            int nextYear = currentYear + 1;
            LocalDate startDate = LocalDate.of(nextYear - 1, Month.NOVEMBER, 1);
            LocalDate endDate = LocalDate.of(nextYear, Month.OCTOBER, 31);
            beginTime = startDate.toString();
            endTime = endDate.toString();
        } else {
            // 当前月份不是11或12月，取上一个周期（上年11月到今年10月）
            LocalDate startDate = LocalDate.of(currentYear - 1, Month.NOVEMBER, 1);
            LocalDate endDate = LocalDate.of(currentYear, Month.OCTOBER, 31);
            beginTime = startDate.toString();
            endTime = endDate.toString();
        }


        TaskDbcenter taskDbcenter = new TaskDbcenter();
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("beginTime", beginTime);
        taskDbcenter.getParams().put("endTime", endTime);
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterList(taskDbcenter);

        // 根据list集合中的discovertime字段进行年月分组beginTime到endTime的所有年月，然后统计每组的数量
        // 使用stream从list集合中过滤出discovertime在beginTime和endTime之间的案件，然后根据discovertime字段进行年月分组，统计每组的数量,
        Map<String, Long> map = list.stream().filter(task -> DateUtil.isIn(task.getDiscovertime(), DateUtil.parse(beginTime), DateUtil.parse(endTime))).collect(Collectors.groupingBy(task -> DateUtil.format(task.getDiscovertime(), "yyyy-MM"), Collectors.counting()));
        Map<String, Long> repeatMap = list.stream().filter(task -> DateUtil.isIn(task.getDiscovertime(), DateUtil.parse(beginTime), DateUtil.parse(endTime)) && task.getRelatedhotlinesn() != null).collect(Collectors.groupingBy(task -> DateUtil.format(task.getDiscovertime(), "yyyy-MM"), Collectors.counting()));

        List<String> monthList = DateUtils.listMonths(beginTime, endTime);

        HashMap<String, Object> result = new HashMap<String, Object>() {{
            put("monthList", monthList);
            put("allData", map);
            put("repeatData", repeatMap);
        }};

        System.out.println(result);
    }
}
