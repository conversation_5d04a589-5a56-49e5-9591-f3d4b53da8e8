package com.ruoyi.web.controller.screen;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.bigdata.service.JsqcyService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.FxftCase;
import com.ruoyi.shcy.domain.SensorDevice;
import com.ruoyi.shcy.domain.WaterLevelRealtime;
import com.ruoyi.shcy.service.IFxftCaseService;
import com.ruoyi.shcy.service.IJsDbcenterService;
import com.ruoyi.shcy.service.ISensorDeviceService;
import com.ruoyi.shcy.service.IWaterLevelRealtimeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 大屏防汛防台 Controller
 *
 * @date 2024-04-08
 */
@Api("大屏防汛防台接口")
@RestController
@RequestMapping("/screen/fxft")
public class ScreenFxftController {

    @Autowired
    private IFxftCaseService fxftCaseService;

    @Autowired
    private JsqcyService jsqcyService;

    @Autowired
    private ISensorDeviceService sensorDeviceService;

    @Autowired
    private IWaterLevelRealtimeService waterLevelRealtimeService;

    /**
     * 物联感知事件统计
     */
    @ApiOperation("物联感知事件统计")
    @GetMapping("/iotEvent")
    public AjaxResult iotEvent() {

        // LocalDate currentDate = LocalDate.now(); // 获取当前日期
        // LocalDate startDate = currentDate.minusMonths(11); // 计算开始日期，当前日期的前11个月

        int year = LocalDate.now().getYear();
        // 创建当年5月1日的日期
        LocalDate startDate = LocalDate.of(year, 5, 1);
        // 计算结束日期，即开始日期的后11个月
        LocalDate endDate = startDate.plusMonths(11);

        List<String> monthList = new ArrayList<>();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusMonths(1)) {
            String month = date.format(formatter);
            monthList.add(month);
        }

        FxftCase fxftCase = new FxftCase();
        LocalDate beginTime = LocalDate.of(startDate.getYear(), startDate.getMonth(), 1);
        LocalDate endTime = LocalDate.of(endDate.getYear(), endDate.getMonth(), 1).plusMonths(1).minusDays(1);
        fxftCase.getParams().put("beginTime", beginTime.toString());
        fxftCase.getParams().put("endTime", endTime.toString());
        List<FxftCase> list = fxftCaseService.selectFxftCaseList(fxftCase);
        // 使用stream从list集合中过滤出createTime在beginTime和endTime之间的案件，然后根据createTime字段进行年月分组，统计每组的数量
        Map<String, Long> map = list.stream().collect(Collectors.groupingBy(item -> DateUtil.format(item.getCreateTime(), "yyyy-MM"), Collectors.counting()));
        // 使用stream从list集合中过滤出circulationState为0的案件，然后根据createTime字段进行年月分组，统计每组的数量
        Map<String, Long> processedMap = list.stream().filter(item -> FxftConstants.PROCESSED.equals(item.getCirculationState())).collect(Collectors.groupingBy(item -> DateUtil.format(item.getCreateTime(), "yyyy-MM"), Collectors.counting()));

        Map<String, Object> result = new HashMap<String, Object>() {{
            put("monthList", monthList);
            put("allList", map);
            put("processedList", processedMap);
        }};
        return AjaxResult.success(result);
    }

    /**
     * 获取物联感知事件统计数据（年度、本月报警统计数据）
     *
     * @return AjaxResult
     */
    @GetMapping("/getWlgzsjData")
    public AjaxResult getWlgzsjData() {

        // 获取当前月份，如果小于5月，startDate取去年的5月1日，否则取今年的5月1日
        int month = LocalDate.now().getMonthValue();
        int year = LocalDate.now().getYear();
        int lastYear = year - 1;
        LocalDate startDate = month < 5 ? LocalDate.of(lastYear, 5, 1) : LocalDate.of(year, 5, 1);
        LocalDate endDate = startDate.plusMonths(11);

        FxftCase fxftCase = new FxftCase();
        fxftCase.getParams().put("beginTime", startDate.toString());
        fxftCase.getParams().put("endTime", endDate.toString());
        List<FxftCase> list = fxftCaseService.selectFxftCaseList(fxftCase);

        // 定义一个Map，存放年度报警数、年度已处理数、年度处理比例、本月报警数、本年度已处理报警数、本月处理比例
        Map<String, Object> map = new HashMap<>();
        map.put("yearTotal", list.size());
        long yearProcessed = list.stream().filter(item -> FxftConstants.PROCESSED.equals(item.getCirculationState())).count();
        map.put("yearProcessed", yearProcessed);
        map.put("yearProcessedRate", list.size() > 0 ? (double)yearProcessed / list.size() : 0);
        long monthTotal = list.stream().filter(item -> item.getCreateTime().getMonth() == month).count();
        long monthProcessed = list.stream().filter(item -> item.getCreateTime().getMonth() == month && FxftConstants.PROCESSED.equals(item.getCirculationState())).count();
        map.put("monthTotal", monthTotal);
        map.put("monthProcessed", monthProcessed);
        map.put("monthProcessedRate", monthTotal > 0 ? (double)monthProcessed / monthTotal : 0);
        return AjaxResult.success(map);
    }

    @ApiOperation("获取防汛防台(综合汛情积水)数据")
    @GetMapping("/getFxftzhxqjsData")
    public AjaxResult getFxftzhxqjsData() {
        // 金山铁路支蒙山路下立交(隆平路、金卫支线、临桂路)
        String mslWaterValue = "0";
        // 金山铁路支卫零路线下立交(隆平路、金卫支线、临桂路)
        String wllWaterValue = "0";
        // String fxftzhxqjsData = jsqcyService.getFxftzhxqjsData();
        // if (StringUtils.isNotEmpty(fxftzhxqjsData)) {
        //     // 对json数据进行解析，获取data对象中的data数组
        //     JSONObject jsonObject = JSON.parseObject(fxftzhxqjsData);
        //     JSONObject dataObject = jsonObject.getJSONObject("data");
        //     // 判断dataObject是否为空
        //     if (dataObject != null) {
        //         JSONArray dataArray = dataObject.getJSONArray("data");
        //         // 判断dataArray是否为null
        //         if (dataArray != null) {
        //             for (int i = 0; i < dataArray.size(); i++) {
        //                 JSONObject item = dataArray.getJSONObject(i);
        //                 if ("PCLJ8859".equals(item.getString("id"))) {
        //                     mslWaterValue = item.getString("waterValue");
        //                 }
        //                 if ("PCLJ8972".equals(item.getString("id"))) {
        //                     wllWaterValue = item.getString("waterValue");
        //                 }
        //             }
        //         }
        //     }
        // }

        // 12-蒙山路 13-卫零路
        WaterLevelRealtime mslWaterLevelRealtime = waterLevelRealtimeService.selectWaterLevelRealtimeByWaterLevelId(12);
        WaterLevelRealtime wllWaterLevelRealtime = waterLevelRealtimeService.selectWaterLevelRealtimeByWaterLevelId(13);
        if (mslWaterLevelRealtime != null) {
            mslWaterValue = mslWaterLevelRealtime.getWaterLevel() != null ? mslWaterLevelRealtime.getWaterLevel().toString() : "0";
        }
        if (wllWaterLevelRealtime != null) {
            wllWaterValue = wllWaterLevelRealtime.getWaterLevel() != null ? wllWaterLevelRealtime.getWaterLevel().toString() : "0";
        }
        Map<String, String> map = new HashMap<>();
        map.put("mslWaterValue", mslWaterValue);
        map.put("wllWaterValue", wllWaterValue);
        return AjaxResult.success(map);
    }

    @ApiOperation("获取防汛防台(综合汛情水位)数据")
    @GetMapping("/getFxftzhxqswData")
    public AjaxResult getFxftzhxqswData() {
        String outWater = "0";
        String fxftzhxqswData = jsqcyService.getFxftzhxqswData();
        if (StringUtils.isNotEmpty(fxftzhxqswData)) {
            JSONObject jsonObject = JSON.parseObject(fxftzhxqswData);
            JSONObject dataObject = jsonObject.getJSONObject("data");
            if (dataObject != null) {
                JSONArray dataArray = dataObject.getJSONArray("data");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);
                    if ("SW63405900".equals(item.getString("id"))) {
                        outWater = item.getString("outWater");
                        break;
                    }
                }
            }
        }
        Map<String, String> map = new HashMap<>();
        map.put("outWater", outWater);
        return AjaxResult.success(map);
    }

    @ApiOperation("获取防汛防台(综合汛情雨量)数据")
    @GetMapping("/getFxftzhxqylData")
    public AjaxResult getFxftzhxqylData() {
        String[] hoursArr = {"1", "3", "12", "24", "48"};
        Map<String, String> rainValues = new HashMap<>();
        String fxftzhxqylData = jsqcyService.getFxftzhxqylData();
        if (StringUtils.isNotEmpty(fxftzhxqylData)) {
            JSONObject jsonObject = JSON.parseObject(fxftzhxqylData);
            JSONObject dataObject = jsonObject.getJSONObject("data");
            if (dataObject != null) {
                JSONArray dataArray = dataObject.getJSONArray("data");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);
                    String hours = item.getString("hours");
                    for (String hoursStr : hoursArr) {
                        if ("QX1160101".equals(item.getString("id")) && hoursStr.equals(hours)) {
                            rainValues.put(hoursStr, item.getString("rainValue"));
                        }
                    }
                }
                // 遍历hoursArr，如果rainValues中不存在hoursStr，则将rainValues中hoursStr的值设置为0
                for (String hoursStr : hoursArr) {
                    if (!rainValues.containsKey(hoursStr)) {
                        rainValues.put(hoursStr, "0");
                    }
                }
            }
        } else {
            for (String hoursStr : hoursArr) {
                rainValues.put(hoursStr, "0");
                // 随机1-5的数字
                // rainValues.put(hoursStr, String.valueOf((int)(Math.random() * 5 + 1)));
            }
        }
        // 将rainValues中的value按key顺序排序转成数组类型为double
        double[] rainValuesArr = new double[hoursArr.length];
        for (int i = 0; i < hoursArr.length; i++) {
            rainValuesArr[i] = Double.parseDouble(rainValues.get(hoursArr[i]));
        }
        return AjaxResult.success(rainValuesArr);
    }

    @ApiOperation("获取防汛防台(综合汛情风速风向)数据")
    @GetMapping("/getFxftzhxqfsfxData")
    public AjaxResult getFxftzhxqfsfxData() {
        Map<String, Object> map = new HashMap<>();
        String fxftzhxqfsfxData = jsqcyService.getFxftzhxqfsfxData();
        if (StringUtils.isNotEmpty(fxftzhxqfsfxData)) {
            JSONObject jsonObject = JSON.parseObject(fxftzhxqfsfxData);
            JSONObject dataObject = jsonObject.getJSONObject("data");
            if (dataObject != null) {
                JSONArray dataArray = dataObject.getJSONArray("data");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);
                    // 金山嘴
                    if ("SW63405900".equals(item.getString("id"))) {
                        String windirection = item.getString("windirection");
                        String windway = item.getString("windway");
                        String windspeed = item.getString("windspeed");
                        String fengli = item.getString("fengli");
                        Map<String, Object> jsz = new HashMap<>();
                        jsz.put("winddirection", windirection);
                        jsz.put("windway", windway);
                        jsz.put("windspeed", windspeed);
                        jsz.put("fengli", fengli);
                        map.put("jsz", jsz);
                    }
                    // 上海化工
                    if ("SW63406000".equals(item.getString("id"))) {
                        String windirection = item.getString("windirection");
                        String windway = item.getString("windway");
                        String windspeed = item.getString("windspeed");
                        String fengli = item.getString("fengli");
                        Map<String, Object> shhg = new HashMap<>();
                        shhg.put("winddirection", windirection);
                        shhg.put("windway", windway);
                        shhg.put("windspeed", windspeed);
                        shhg.put("fengli", fengli);
                        map.put("shhg", shhg);
                    }
                    // 国家气象站
                    if ("QX058460".equals(item.getString("id"))) {
                        String windirection = item.getString("windirection");
                        String windway = item.getString("windway");
                        String windspeed = item.getString("windspeed");
                        String fengli = item.getString("fengli");
                        Map<String, Object> gjqxz = new HashMap<>();
                        gjqxz.put("winddirection", windirection);
                        gjqxz.put("windway", windway);
                        gjqxz.put("windspeed", windspeed);
                        gjqxz.put("fengli", fengli);
                        map.put("gjqxz", gjqxz);
                    }
                }
            }
        } else {
            Map<String, Object> jsz = new HashMap<>();
            jsz.put("winddirection", "0");
            jsz.put("windway", "0");
            jsz.put("windspeed", "0");
            jsz.put("fengli", "0");
            map.put("jsz", jsz);

            Map<String, Object> shhg = new HashMap<>();
            shhg.put("winddirection", "0");
            shhg.put("windway", "0");
            shhg.put("windspeed", "0");
            shhg.put("fengli", "0");
            map.put("shhg", shhg);

            Map<String, Object> gjqxz = new HashMap<>();
            gjqxz.put("winddirection", "0");
            gjqxz.put("windway", "0");
            gjqxz.put("windspeed", "0");
            gjqxz.put("fengli", "0");
            map.put("gjqxz", gjqxz);
        }
        return AjaxResult.success(map);
    }

    @ApiOperation("获取天气预报(当天)数据")
    @GetMapping("/getTqybData")
    public AjaxResult getTqybData() {
        Map<String, String> map = new HashMap<>();
        String tqybData = jsqcyService.getTqybData();
        if (StringUtils.isNotEmpty(tqybData)) {
            JSONObject jsonObject = JSON.parseObject(tqybData);
            JSONObject dataObject = jsonObject.getJSONObject("data");
            if (dataObject != null) {
                JSONArray dataArray = dataObject.getJSONArray("data");
                JSONObject item = dataArray.getJSONObject(0);
            }
        }
        return AjaxResult.success(map);
    }

    @ApiOperation("获取防汛防台(综合汛情泵闸)数据")
    @GetMapping("/getFxftzhxqbzData")
    public AjaxResult getFxftzhxqbzData() {
        Map<String, Object> map = new HashMap<>();
        String fxftzhxqbzData = jsqcyService.getFxftzhxqbzData();
        if (StringUtils.isNotEmpty(fxftzhxqbzData)) {
            JSONObject jsonObject = JSON.parseObject(fxftzhxqbzData);
            JSONObject dataObject = jsonObject.getJSONObject("data");
            if (dataObject != null) {
                JSONArray dataArray = dataObject.getJSONArray("data");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject item = dataArray.getJSONObject(i);
                    // 龙泉港水闸
                    if ("DF000052".equals(item.getString("id"))) {
                        String inWater = item.getString("inWater");
                        String outWater = item.getString("outWater");
                        String dateTime = item.getString("dateTime");
                        Map<String, String> map1 = new HashMap<>();
                        map1.put("inWater", inWater);
                        map1.put("outWater", outWater);
                        map1.put("dateTime", dateTime);
                        map.put("lqgsz", map1);
                    }
                    // 张泾河枢纽
                    if ("SL000913".equals(item.getString("id"))) {
                        String inWater = item.getString("inWater");
                        String outWater = item.getString("outWater");
                        String dateTime = item.getString("dateTime");
                        Map<String, String> map2 = new HashMap<>();
                        map2.put("inWater", inWater);
                        map2.put("outWater", outWater);
                        map2.put("dateTime", dateTime);
                        map.put("zjhsz", map2);
                    }
                }
            }
        }else {
            Map<String, String> map1 = new HashMap<>();
            map1.put("inWater", "0");
            map1.put("outWater", "0");
            map1.put("dateTime", "");
            map.put("lqgsz", map1);

            Map<String, String> map2 = new HashMap<>();
            map2.put("inWater", "0");
            map2.put("outWater", "0");
            map2.put("dateTime", "");
            map.put("zjhsz", map2);
        }
        return AjaxResult.success(map);
    }

    @ApiOperation("获取气象数据")
    @GetMapping("/getFxftxbData")
    public AjaxResult getFxftxbData() {
        Map<String, Object> map = new HashMap<>();
        List<SensorDevice> sensorDeviceList = sensorDeviceService.selectSensorDeviceList(null);
        if (sensorDeviceList != null) {
            for (SensorDevice item : sensorDeviceList) {
                if ("温湿度".equals(item.getSensorimeiTypeName())) {
                    if (!map.containsKey("wsd")) {
                        map.put("wsd", item.getValue() != null ? item.getValue() : "");
                    }
                } else if ("雨量".equals(item.getSensorimeiTypeName())) {
                    if (!map.containsKey("yl")) {
                        map.put("yl", item.getValue() != null ? item.getValue() : "");
                    }
                }
            }
        }
        return AjaxResult.success(map);
    }
}
