package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ConstructionWasteSiteMapper;
import com.ruoyi.shcy.domain.ConstructionWasteSite;
import com.ruoyi.shcy.service.IConstructionWasteSiteService;

/**
 * 小区建筑垃圾堆放点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class ConstructionWasteSiteServiceImpl implements IConstructionWasteSiteService 
{
    @Autowired
    private ConstructionWasteSiteMapper constructionWasteSiteMapper;

    /**
     * 查询小区建筑垃圾堆放点
     * 
     * @param id 小区建筑垃圾堆放点主键
     * @return 小区建筑垃圾堆放点
     */
    @Override
    public ConstructionWasteSite selectConstructionWasteSiteById(Long id)
    {
        return constructionWasteSiteMapper.selectConstructionWasteSiteById(id);
    }

    /**
     * 查询小区建筑垃圾堆放点列表
     * 
     * @param constructionWasteSite 小区建筑垃圾堆放点
     * @return 小区建筑垃圾堆放点
     */
    @Override
    public List<ConstructionWasteSite> selectConstructionWasteSiteList(ConstructionWasteSite constructionWasteSite)
    {
        return constructionWasteSiteMapper.selectConstructionWasteSiteList(constructionWasteSite);
    }

    /**
     * 新增小区建筑垃圾堆放点
     * 
     * @param constructionWasteSite 小区建筑垃圾堆放点
     * @return 结果
     */
    @Override
    public int insertConstructionWasteSite(ConstructionWasteSite constructionWasteSite)
    {
        constructionWasteSite.setCreateTime(DateUtils.getNowDate());
        return constructionWasteSiteMapper.insertConstructionWasteSite(constructionWasteSite);
    }

    /**
     * 修改小区建筑垃圾堆放点
     * 
     * @param constructionWasteSite 小区建筑垃圾堆放点
     * @return 结果
     */
    @Override
    public int updateConstructionWasteSite(ConstructionWasteSite constructionWasteSite)
    {
        constructionWasteSite.setUpdateTime(DateUtils.getNowDate());
        return constructionWasteSiteMapper.updateConstructionWasteSite(constructionWasteSite);
    }

    /**
     * 批量删除小区建筑垃圾堆放点
     * 
     * @param ids 需要删除的小区建筑垃圾堆放点主键
     * @return 结果
     */
    @Override
    public int deleteConstructionWasteSiteByIds(Long[] ids)
    {
        return constructionWasteSiteMapper.deleteConstructionWasteSiteByIds(ids);
    }

    /**
     * 删除小区建筑垃圾堆放点信息
     * 
     * @param id 小区建筑垃圾堆放点主键
     * @return 结果
     */
    @Override
    public int deleteConstructionWasteSiteById(Long id)
    {
        return constructionWasteSiteMapper.deleteConstructionWasteSiteById(id);
    }
}
