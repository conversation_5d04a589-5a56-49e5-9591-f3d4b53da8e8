package com.ruoyi.artemis;

import com.ruoyi.common.utils.CoordinateTransform;
import com.ruoyi.common.utils.GisUtils;
import org.junit.jupiter.api.Test;

public class CoordinateTransformTest {


    @Test
    public void test() {
        //百度地图 BD09 坐标 转 WGS84
        // double[] lngLat_wgs84 = CoordinateTransform.transformBD09ToWGS84(120.644049, 31.285887);
        // System.out.println("lng :" + lngLat_wgs84[0] + ",lat :" + lngLat_wgs84[1]);


        // 121.337655
        // 30.715366

        // 121.332289,30.749183
        //WGS84  坐标 转 百度地图 BD09
        double[] lngLat_bd09 = CoordinateTransform.transformWGS84ToBD09(121.332289, 30.749183);
        System.out.println("lng :" + lngLat_bd09[0] + ",lat :" + lngLat_bd09[1]);

        String convertcc = GisUtils.convertcc(lngLat_bd09[0]+"", lngLat_bd09[1]+"");
        System.out.println(convertcc);
    }
}
