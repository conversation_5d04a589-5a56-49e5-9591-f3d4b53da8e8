package com.ruoyi.shcy;

import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.service.IApartmentRentalService;
import com.ruoyi.shcy.service.IShcyFileInfoService;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2022/11/29 9:04
 * @Version 1.0
 */


@Component
public class DemoTest {


    @Autowired
    private IApartmentRentalService apartmentRentalService;

    @Autowired
    private IShcyFileInfoService shcyFileInfoService;
    /**
     * 计算两个经纬度之间的距离
     * @param gpsFrom 第一个经纬度
     * @param gpsTo 第二个经纬度
     * @param ellipsoid 计算方式
     * @return 返回的距离，单位m
     */
    public static double getDistanceMeter(GlobalCoordinates gpsFrom, GlobalCoordinates gpsTo, Ellipsoid ellipsoid)
    {
        //创建GeodeticCalculator，调用计算方法，传入坐标系、经纬度用于计算距离
        GeodeticCurve geoCurve = new GeodeticCalculator().calculateGeodeticCurve(ellipsoid, gpsFrom, gpsTo);

        return geoCurve.getEllipsoidalDistance();
    }

    public static void main(String[] args) {
        double lon1 = 121.342084;
        double lat1 = 30.714098;
        double lon2 = 121.341960;
        double lat2 = 30.714166;

        GlobalCoordinates source = new GlobalCoordinates(lon1, lat1);
        GlobalCoordinates target = new GlobalCoordinates(lon2, lat2);

        double meter1 = getDistanceMeter(source, target, Ellipsoid.Sphere);
        double meter2 = getDistanceMeter(source, target, Ellipsoid.WGS84);

        System.out.println("Sphere坐标系计算结果："+meter1 + "米");

        System.out.println("WGS84坐标系计算结果："+meter2 + "米");
    }


    @Test
    public void getAparmentRental(){
        ShcyFileInfo shcyFileInfo = new ShcyFileInfo();
        shcyFileInfo.setFileName("测试文件1");
        shcyFileInfo.setFilePath("/ruoyi/config/2023/05/22/测试文件2.jpg");
        shcyFileInfoService.insertShcyFileInfo(shcyFileInfo);
        Long  autoId = shcyFileInfo.getFileId();
        System.out.println(autoId);

    }

}