package com.ruoyi.shcy.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.service.IShopService;
import com.ruoyi.system.service.ISysDeptService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.Employees;
import com.ruoyi.shcy.service.IEmployeesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 从业人员基本信息Controller
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@RestController
@RequestMapping("/shcy/employees")
public class EmployeesController extends BaseController
{
    @Autowired
    private IEmployeesService employeesService;

    @Autowired
    private IShopService shopService;

    @Autowired
    private ISysDeptService iSysDeptService;

    /**
     * 查询从业人员基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:employees:list')")
    @GetMapping("/list")
    public TableDataInfo list(Employees employees)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();
        if(Objects.equals(user.getUserName(), "admin")){
            // 获取当前的用户名称
            startPage();
            List<Employees> list = employeesService.selectEmployeesList(employees);
            return getDataTable(list);
        }

        if(deptId!=null && !Objects.equals(user.getUserName(), "admin")){
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if(StringUtils.isNotEmpty(sysDept.getIsCommittee())&&sysDept.getIsCommittee().equals("1")) {     //获取居委会对应查看的店铺列表
                startPage();
                employees.setDeptId(sysDept.getDeptId());
                List<Employees> list = employeesService.selectEmployeesList(employees);
                return getDataTable(list);
            }else if(StringUtils.isNotEmpty(sysDept.getIsCommittee())&&sysDept.getIsCommittee().equals("2")){ //获取网格长查看多个居委会数据

                TableDataInfo tableDataInfo = selectWanggezhangShopEmployeeList(user, employees, employeesService);
                return tableDataInfo;
            }
        }
        // 获取当前的用户名称
        startPage();
//        List<Employees> list = employeesService.selectEmployeesList(employees);
        List<Employees> list = new ArrayList<>();
        return getDataTable(list);



    }

    public static TableDataInfo  selectWanggezhangShopEmployeeList(SysUser user,Employees employees,IEmployeesService employeesService){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();


        List<Employees> shopEmployeeList = employeesService.selectEmployeesList(employees).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getDeptId())).collect(Collectors.toList());

        int num = shopEmployeeList.size();
        shopEmployeeList = shopEmployeeList.stream()
                .skip((pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(shopEmployeeList);
        rspData.setTotal(num);
        return rspData;
    }

    /**
     * 导出从业人员基本信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:employees:export')")
    @Log(title = "从业人员基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Employees employees)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();

        if(user.getUserName().equals("admin") ){
            // 获取当前的用户名称
            List<Employees> list = employeesService.selectEmployeesList(employees);
            ExcelUtil<Employees> util = new ExcelUtil<Employees>(Employees.class);
            util.exportExcel(response, list, "从业人员基本信息数据");
        }

        if(deptId!=null && !Objects.equals(user.getUserName(), "admin")){
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if(StringUtils.isNotEmpty(sysDept.getIsCommittee())&&sysDept.getIsCommittee().equals("1")) {     //获取居委会对应查看的店铺列表
                employees.setDeptId(deptId);
                List<Employees> list = employeesService.selectEmployeesList(employees);
                ExcelUtil<Employees> util = new ExcelUtil<Employees>(Employees.class);
                util.exportExcel(response, list, "从业人员基本信息数据");

            }else if(StringUtils.isNotEmpty(sysDept.getIsCommittee()) &&sysDept.getIsCommittee().equals("2")){ //获取网格长查看多个居委会数据
                List<Employees> employeesList = employeesService.selectEmployeesList(employees).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getDeptId())).collect(Collectors.toList());
                ExcelUtil<Employees> util = new ExcelUtil<Employees>(Employees.class);
                util.exportExcel(response, employeesList, "从业人员基本信息数据");
            }
        }

    }

    /**
     * 获取从业人员基本信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:employees:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(employeesService.selectEmployeesById(id));
    }

    /**
     * 新增从业人员基本信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:employees:add')")
    @Log(title = "从业人员基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Employees employees)
    {
        return toAjax(employeesService.insertEmployees(employees));
    }

    /**
     * 修改从业人员基本信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:employees:edit')")
    @Log(title = "从业人员基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Employees employees)
    {
        return toAjax(employeesService.updateEmployees(employees));
    }

    /**
     * 删除从业人员基本信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:employees:remove')")
    @Log(title = "从业人员基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(employeesService.deleteEmployeesByIds(ids));
    }
}
