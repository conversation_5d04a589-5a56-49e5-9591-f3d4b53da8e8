package com.ruoyi.shcy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.domain.SpecialTask;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.mapper.SpecialTaskMapper;
import com.ruoyi.shcy.service.ISpecialTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 专项任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
@Service
public class SpecialTaskServiceImpl implements ISpecialTaskService 
{
    @Autowired
    private SpecialTaskMapper specialTaskMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    /**
     * 查询专项任务
     * 
     * @param id 专项任务主键
     * @return 专项任务
     */
    @Override
    public SpecialTask selectSpecialTaskById(Long id)
    {
        return handlePhoto(specialTaskMapper.selectSpecialTaskById(id));
    }

    private SpecialTask handlePhoto(SpecialTask specialTask) {
        // 收集所有的文件ID
        List<Long> allFileIds = new ArrayList<>();
        if (StrUtil.isNotEmpty(specialTask.getScenePhoto())) {
            allFileIds.addAll(StrUtil.split(specialTask.getScenePhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        if (StrUtil.isNotEmpty(specialTask.getDisposalPhoto())) {
            allFileIds.addAll(StrUtil.split(specialTask.getDisposalPhoto(), ',').stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList()));
        }
        
        // 如果有文件ID,则一次性查询所有文件信息
        if (!allFileIds.isEmpty()) {
            // 假设 shcyFileInfoMapper 中新增了批量查询方法
            List<ShcyFileInfo> allFileInfos = shcyFileInfoMapper.selectShcyFileInfoByFileIds(allFileIds);
            // 转换为Map方便查找
            Map<Long, String> filePathMap = allFileInfos.stream()
                    .collect(Collectors.toMap(ShcyFileInfo::getFileId, ShcyFileInfo::getFilePath));

            // 设置各字段的图片URL
            if (StrUtil.isNotEmpty(specialTask.getScenePhoto())) {
                specialTask.setScenePhotoUrls(
                        StrUtil.split(specialTask.getScenePhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
            if (StrUtil.isNotEmpty(specialTask.getDisposalPhoto())) {
                specialTask.setDisposalPhotoUrls(
                        StrUtil.split(specialTask.getDisposalPhoto(), ',').stream()
                                .map(fileId -> filePathMap.get(Long.valueOf(fileId)))
                                .collect(Collectors.toList())
                );
            }
        }
        return specialTask;
    }

    /**
     * 查询专项任务列表
     * 
     * @param specialTask 专项任务
     * @return 专项任务
     */
    @Override
    public List<SpecialTask> selectSpecialTaskList(SpecialTask specialTask)
    {
        return specialTaskMapper.selectSpecialTaskList(specialTask);
    }

    /**
     * 新增专项任务
     * 
     * @param specialTask 专项任务
     * @return 结果
     */
    @Override
    public int insertSpecialTask(SpecialTask specialTask)
    {
        specialTask.setCreateTime(DateUtils.getNowDate());
        return specialTaskMapper.insertSpecialTask(specialTask);
    }

    /**
     * 修改专项任务
     * 
     * @param specialTask 专项任务
     * @return 结果
     */
    @Override
    public int updateSpecialTask(SpecialTask specialTask)
    {
        specialTask.setUpdateTime(DateUtils.getNowDate());
        return specialTaskMapper.updateSpecialTask(specialTask);
    }

    /**
     * 批量删除专项任务
     * 
     * @param ids 需要删除的专项任务主键
     * @return 结果
     */
    @Override
    public int deleteSpecialTaskByIds(Long[] ids)
    {
        return specialTaskMapper.deleteSpecialTaskByIds(ids);
    }

    /**
     * 删除专项任务信息
     * 
     * @param id 专项任务主键
     * @return 结果
     */
    @Override
    public int deleteSpecialTaskById(Long id)
    {
        return specialTaskMapper.deleteSpecialTaskById(id);
    }

    @Override
    public long getCaseCount(SpecialTask specialTask) {
        return specialTaskMapper.getCaseCount(specialTask);
    }
}
