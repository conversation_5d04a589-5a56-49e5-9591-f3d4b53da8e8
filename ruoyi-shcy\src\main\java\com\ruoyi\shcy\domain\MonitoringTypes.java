package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 监控类型对象 shcy_monitoring_types
 * 
 * <AUTHOR>
 * @date 2023-10-11
 */
public class MonitoringTypes extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 监控类型ID */
    private Long monitoringTypeId;

    /** 监控类型编码 */
    @Excel(name = "监控类型编码")
    private String monitoringTypeCode;

    /** 监控类型名称 */
    @Excel(name = "监控类型名称")
    private String monitoringTypeName;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    private Integer monitoringTypeSort;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setMonitoringTypeId(Long monitoringTypeId) 
    {
        this.monitoringTypeId = monitoringTypeId;
    }

    public Long getMonitoringTypeId() 
    {
        return monitoringTypeId;
    }
    public void setMonitoringTypeCode(String monitoringTypeCode) 
    {
        this.monitoringTypeCode = monitoringTypeCode;
    }

    public String getMonitoringTypeCode() 
    {
        return monitoringTypeCode;
    }
    public void setMonitoringTypeName(String monitoringTypeName) 
    {
        this.monitoringTypeName = monitoringTypeName;
    }

    public String getMonitoringTypeName() 
    {
        return monitoringTypeName;
    }
    public void setMonitoringTypeSort(Integer monitoringTypeSort) 
    {
        this.monitoringTypeSort = monitoringTypeSort;
    }

    public Integer getMonitoringTypeSort() 
    {
        return monitoringTypeSort;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("monitoringTypeId", getMonitoringTypeId())
            .append("monitoringTypeCode", getMonitoringTypeCode())
            .append("monitoringTypeName", getMonitoringTypeName())
            .append("monitoringTypeSort", getMonitoringTypeSort())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
