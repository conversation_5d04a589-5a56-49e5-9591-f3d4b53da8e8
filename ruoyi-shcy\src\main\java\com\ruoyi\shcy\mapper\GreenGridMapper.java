package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.GreenGrid;

/**
 * 绿化网格Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-09
 */
public interface GreenGridMapper 
{
    /**
     * 查询绿化网格
     * 
     * @param id 绿化网格主键
     * @return 绿化网格
     */
    public GreenGrid selectGreenGridById(Long id);

    /**
     * 查询绿化网格列表
     * 
     * @param greenGrid 绿化网格
     * @return 绿化网格集合
     */
    public List<GreenGrid> selectGreenGridList(GreenGrid greenGrid);

    /**
     * 新增绿化网格
     * 
     * @param greenGrid 绿化网格
     * @return 结果
     */
    public int insertGreenGrid(GreenGrid greenGrid);

    /**
     * 修改绿化网格
     * 
     * @param greenGrid 绿化网格
     * @return 结果
     */
    public int updateGreenGrid(GreenGrid greenGrid);

    /**
     * 删除绿化网格
     * 
     * @param id 绿化网格主键
     * @return 结果
     */
    public int deleteGreenGridById(Long id);

    /**
     * 批量删除绿化网格
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGreenGridByIds(Long[] ids);
}
