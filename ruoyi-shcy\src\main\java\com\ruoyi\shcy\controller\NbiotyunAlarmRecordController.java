package com.ruoyi.shcy.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.LiquidLevelDevice;
import com.ruoyi.shcy.domain.NbiotyunAlarmRecord;
import com.ruoyi.shcy.dto.NbiotyunAlarmRecordDTO;
import com.ruoyi.shcy.dto.NbiotyunAlarmRecordHandleDTO;
import com.ruoyi.shcy.service.ILiquidLevelDeviceService;
import com.ruoyi.shcy.service.INbiotyunAlarmRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 报警记录Controller
 * 
 * <AUTHOR>
 * @date 2023-09-04
 */
@RestController
@RequestMapping("/shcy/alarmRecord")
public class NbiotyunAlarmRecordController extends BaseController
{
    @Autowired
    private INbiotyunAlarmRecordService nbiotyunAlarmRecordService;

    @Autowired
    private ILiquidLevelDeviceService liquidLevelDeviceService;

    /**
     * 查询报警记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:alarmRecord:list')")
    @GetMapping("/list")
    public TableDataInfo list(NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        startPage();
        // List<NbiotyunAlarmRecord> list = nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordList(nbiotyunAlarmRecord);
        List<NbiotyunAlarmRecord> list = nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordGroupByList(nbiotyunAlarmRecord);
        return getDataTable(list);
    }

    /**
     * 导出报警记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:alarmRecord:export')")
    @Log(title = "报警记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        List<NbiotyunAlarmRecord> list = nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordList(nbiotyunAlarmRecord);
        ExcelUtil<NbiotyunAlarmRecord> util = new ExcelUtil<NbiotyunAlarmRecord>(NbiotyunAlarmRecord.class);
        util.exportExcel(response, list, "报警记录数据");
    }

    /**
     * 获取报警记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:alarmRecord:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordById(id));
    }

    /**
     * 新增报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:alarmRecord:add')")
    @Log(title = "报警记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        return toAjax(nbiotyunAlarmRecordService.insertNbiotyunAlarmRecord(nbiotyunAlarmRecord));
    }

    /**
     * 修改报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:alarmRecord:edit')")
    @Log(title = "报警记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        return toAjax(nbiotyunAlarmRecordService.updateNbiotyunAlarmRecord(nbiotyunAlarmRecord));
    }

    /**
     * 删除报警记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:alarmRecord:remove')")
    @Log(title = "报警记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(nbiotyunAlarmRecordService.deleteNbiotyunAlarmRecordByIds(ids));
    }

    @GetMapping("/fxft/list")
    public TableDataInfo fxftList(NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        nbiotyunAlarmRecord.getParams().put("beginTime", DateUtil.today() + " 00:00:00");
        nbiotyunAlarmRecord.getParams().put("endTime", DateUtil.today() + " 23:59:59");

        startPage();
        List<NbiotyunAlarmRecord> list = new ArrayList<>();
        if (StrUtil.isEmpty(nbiotyunAlarmRecord.getStatus())) {
           list = nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordList(nbiotyunAlarmRecord);
        } else {
            switch (nbiotyunAlarmRecord.getStatus()) {
                case FxftConstants.ALARM_STATE_UNPROCESSED:
                    list = nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordUnprocessedList(nbiotyunAlarmRecord);
                    break;
                case FxftConstants.ALARM_STATE_PROCESSING:
                    list = nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordProcessingList(nbiotyunAlarmRecord);
                    break;
                case FxftConstants.ALARM_STATE_PROCESSED:
                    list = nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordProcessedList(nbiotyunAlarmRecord);
                    break;
            }
        }
        List<NbiotyunAlarmRecordDTO> result = BeanUtil.copyToList(list, NbiotyunAlarmRecordDTO.class);

        // 判断result不为空
        if (CollUtil.isNotEmpty(result)) {
            List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceService.selectLiquidLevelDeviceList(null);
            // 将集合liquidLevelDevices转换为Map，key为deviceImei，value为monitoredWaterBody
            result.forEach(item -> {
                liquidLevelDevices.forEach(liquidLevelDevice -> {
                    if (StrUtil.equals(item.getDeviceImei(), liquidLevelDevice.getDeviceImei())) {
                        item.setMonitoredWaterBody(liquidLevelDevice.getMonitoredWaterBody());
                    }
                });
            });
        }

        return getDataTable(result);
    }

    @PostMapping("/fxft/handle")
    public AjaxResult fxftHandle(@RequestBody NbiotyunAlarmRecordHandleDTO nbiotyunAlarmRecordHandleDTO)
    {
        return toAjax(nbiotyunAlarmRecordService.fxftHandle(nbiotyunAlarmRecordHandleDTO));
    }
}
