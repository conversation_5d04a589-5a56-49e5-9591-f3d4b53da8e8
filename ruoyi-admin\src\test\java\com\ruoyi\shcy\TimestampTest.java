package com.ruoyi.shcy;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;

public class TimestampTest {

    @Test
    public void testTimestamp() {

        // 将当前时间转化为时间戳
        // 获取当前日期
        // LocalDate currentDate = LocalDate.now();

        LocalDate currentDate = LocalDate.of(2024, 1, 29);

        // 获取当天的开始时间，即凌晨 00:00:00
        LocalDateTime startOfDay = currentDate.atStartOfDay();

        // 获取当天的结束时间，即晚上 23:59:59
        LocalDateTime endOfDay = LocalDateTime.of(currentDate, LocalTime.MAX);

        // 转化为时间戳字符串（单位：秒）
        String startTimestamp = String.valueOf(startOfDay.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());
        String endTimestamp = String.valueOf(endOfDay.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond());

        System.out.println(startTimestamp);
        System.out.println(endTimestamp);
    }
}
