package com.ruoyi.shcy.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.FxftCase;
import com.ruoyi.shcy.domain.NbiotyunAlarmRecord;
import com.ruoyi.shcy.dto.NbiotyunAlarmRecordDTO;
import com.ruoyi.shcy.dto.NbiotyunAlarmRecordHandleDTO;
import com.ruoyi.shcy.mapper.FxftCaseMapper;
import com.ruoyi.shcy.mapper.NbiotyunAlarmRecordMapper;
import com.ruoyi.shcy.service.INbiotyunAlarmRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 报警记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-04
 */
@Service
public class NbiotyunAlarmRecordServiceImpl implements INbiotyunAlarmRecordService 
{
    @Autowired
    private NbiotyunAlarmRecordMapper nbiotyunAlarmRecordMapper;

    @Autowired
    private FxftCaseMapper fxfxCaseMapper;

    /**
     * 查询报警记录
     * 
     * @param id 报警记录主键
     * @return 报警记录
     */
    @Override
    public NbiotyunAlarmRecord selectNbiotyunAlarmRecordById(Long id)
    {
        return nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordById(id);
    }

    /**
     * 查询报警记录列表
     * 
     * @param nbiotyunAlarmRecord 报警记录
     * @return 报警记录
     */
    @Override
    public List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordList(NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        return nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordList(nbiotyunAlarmRecord);
    }

    /**
     * 新增报警记录
     * 
     * @param nbiotyunAlarmRecord 报警记录
     * @return 结果
     */
    @Override
    public int insertNbiotyunAlarmRecord(NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        nbiotyunAlarmRecord.setCreateTime(DateUtils.getNowDate());
        return nbiotyunAlarmRecordMapper.insertNbiotyunAlarmRecord(nbiotyunAlarmRecord);
    }

    /**
     * 修改报警记录
     * 
     * @param nbiotyunAlarmRecord 报警记录
     * @return 结果
     */
    @Override
    public int updateNbiotyunAlarmRecord(NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        nbiotyunAlarmRecord.setUpdateTime(DateUtils.getNowDate());
        return nbiotyunAlarmRecordMapper.updateNbiotyunAlarmRecord(nbiotyunAlarmRecord);
    }

    /**
     * 批量删除报警记录
     * 
     * @param ids 需要删除的报警记录主键
     * @return 结果
     */
    @Override
    public int deleteNbiotyunAlarmRecordByIds(Long[] ids)
    {
        return nbiotyunAlarmRecordMapper.deleteNbiotyunAlarmRecordByIds(ids);
    }

    /**
     * 删除报警记录信息
     * 
     * @param id 报警记录主键
     * @return 结果
     */
    @Override
    public int deleteNbiotyunAlarmRecordById(Long id)
    {
        return nbiotyunAlarmRecordMapper.deleteNbiotyunAlarmRecordById(id);
    }

    /**
     * 按列表选择nbiotyun报警记录组
     *
     * @param nbiotyunAlarmRecord nbiotyun报警记录
     * @return {@link List}<{@link NbiotyunAlarmRecord}>
     */
    @Override
    public List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordGroupByList(NbiotyunAlarmRecord nbiotyunAlarmRecord) {
        return nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordGroupByList(nbiotyunAlarmRecord);
    }

    /**
     * 通过imei获取报警记录
     *
     * @param deviceImei 设备imei
     * @return {@link NbiotyunAlarmRecord}
     */
    @Override
    public NbiotyunAlarmRecord getAlarmRecordByImei(String deviceImei) {
        return nbiotyunAlarmRecordMapper.getAlarmRecordByImei(deviceImei);
    }

    /**
     * fithande
     *
     * @param nbiotyunAlarmRecordHandleDTO nbiotyun报警记录句柄dto
     * @return int
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int fxftHandle(NbiotyunAlarmRecordHandleDTO nbiotyunAlarmRecordHandleDTO) {
        // 1.误报处理
        if (FxftConstants.FALSE_ALARM.equals(nbiotyunAlarmRecordHandleDTO.getType())) {
            for (Long id : nbiotyunAlarmRecordHandleDTO.getIds()) {
                NbiotyunAlarmRecord nbiotyunAlarmRecord = new NbiotyunAlarmRecord();
                nbiotyunAlarmRecord.setId(id);
                nbiotyunAlarmRecord.setStatus(FxftConstants.FALSE_ALARM);
                nbiotyunAlarmRecordMapper.updateNbiotyunAlarmRecordStatus(nbiotyunAlarmRecord);
            }
        }
        // 2.非误报处理
        else {
            for (Long id : nbiotyunAlarmRecordHandleDTO.getIds()) {
                NbiotyunAlarmRecord nbiotyunAlarmRecord = new NbiotyunAlarmRecord();
                nbiotyunAlarmRecord.setId(id);
                nbiotyunAlarmRecord.setStatus(FxftConstants.UNPROCESSED);
                nbiotyunAlarmRecordMapper.updateNbiotyunAlarmRecordStatus(nbiotyunAlarmRecord);
                // 2.1.获取报警记录
                NbiotyunAlarmRecord alarmRecord = nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordById(id);
                // 2.2.新建案件
                FxftCase fxftCase = new FxftCase();
                fxftCase.setCaseName("液位超限报警");
                fxftCase.setCaseType("设备报警");
                // 事件描述
                NbiotyunAlarmRecordDTO nbiotyunAlarmRecordDTO = new NbiotyunAlarmRecordDTO();
                nbiotyunAlarmRecordDTO.setDeviceAttrValue(alarmRecord.getDeviceAttrValue());
                fxftCase.setCaseDescription(nbiotyunAlarmRecordDTO.getAlarmReason());
                fxftCase.setCaseDealBy(alarmRecord.getResponsiblePerson());
                fxftCase.setCirculationState(FxftConstants.UNPROCESSED);
                fxftCase.setDealInTimeState("0");
                // 报警设备安装地址
                fxftCase.setAddress(alarmRecord.getDetailedLocation());
                fxftCase.setAlarmRecordId(id);
                // 截至日期 =》 当前时间 + 24小时
                fxftCase.setCaseEndTime(DateUtils.addHours(new Date(), 24));
                fxftCase.setDeviceImei(alarmRecord.getDeviceImei());
                // 事件编号 =》 日期 + 10位随机数
                fxftCase.setCaseNumber(DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(10));
                fxftCase.setCreateBy(SecurityUtils.getLoginUser().getUsername());
                fxftCase.setCreateTime(new Date());
                fxfxCaseMapper.insertFxftCase(fxftCase);
            }
        }

        return 1;
    }

    @Override
    public List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordUnprocessedList(NbiotyunAlarmRecord nbiotyunAlarmRecord) {
        return nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordUnprocessedList(nbiotyunAlarmRecord);
    }

    @Override
    public List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordProcessingList(NbiotyunAlarmRecord nbiotyunAlarmRecord) {
        return nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordProcessingList(nbiotyunAlarmRecord);
    }

    @Override
    public List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordProcessedList(NbiotyunAlarmRecord nbiotyunAlarmRecord) {
        return nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordProcessedList(nbiotyunAlarmRecord);
    }
}
