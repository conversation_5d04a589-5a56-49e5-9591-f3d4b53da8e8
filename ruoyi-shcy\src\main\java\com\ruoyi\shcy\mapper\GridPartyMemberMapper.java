package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.GridPartyMember;

/**
 * 网格党支部成员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface GridPartyMemberMapper 
{
    /**
     * 查询网格党支部成员
     * 
     * @param id 网格党支部成员主键
     * @return 网格党支部成员
     */
    public GridPartyMember selectGridPartyMemberById(Long id);

    /**
     * 查询网格党支部成员列表
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 网格党支部成员集合
     */
    public List<GridPartyMember> selectGridPartyMemberList(GridPartyMember gridPartyMember);

    /**
     * 新增网格党支部成员
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 结果
     */
    public int insertGridPartyMember(GridPartyMember gridPartyMember);

    /**
     * 修改网格党支部成员
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 结果
     */
    public int updateGridPartyMember(GridPartyMember gridPartyMember);

    /**
     * 删除网格党支部成员
     * 
     * @param id 网格党支部成员主键
     * @return 结果
     */
    public int deleteGridPartyMemberById(Long id);

    /**
     * 批量删除网格党支部成员
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGridPartyMemberByIds(Long[] ids);
}
