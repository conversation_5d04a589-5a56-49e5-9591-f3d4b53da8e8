package com.ruoyi.shcy.domain;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 12345热线分析对象 shcy_dbcenter_rx_monthly
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
public class ShcyDbcenterRxMonthly extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 年份 */
    @Excel(name = "年份")
    private Long year;

    /** 月份 */
    @Excel(name = "月份")
    private Long month;

    /** 统计年份 */
    @Excel(name = "统计年份")
    private Long yeartj;

    /** 统计月份 */
    @Excel(name = "统计月份")
    private Long monthtj;

    /** 期数 */
    @Excel(name = "期数")
    private Long qs;

    /** 超期案件数 */
    @Excel(name = "超期案件数")
    private Long cqajs;

    /** 事部件开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "事部件开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sbjStartTime;

    /** 事部件结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "事部件结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sbjEndTime;

    private String status;


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<ShcyDbcenterRxMonthlyExtend> dbcenterRxMonthlyExtendList;

    public List<ShcyDbcenterRxMonthlyExtend> getDbcenterRxMonthlyExtendList() {
        return dbcenterRxMonthlyExtendList;
    }

    public void setDbcenterRxMonthlyExtendList(List<ShcyDbcenterRxMonthlyExtend> dbcenterRxMonthlyExtendList) {
        this.dbcenterRxMonthlyExtendList = dbcenterRxMonthlyExtendList;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setYear(Long year) 
    {
        this.year = year;
    }

    public Long getYear() 
    {
        return year;
    }
    public void setMonth(Long month) 
    {
        this.month = month;
    }

    public Long getMonth() 
    {
        return month;
    }
    public void setYeartj(Long yeartj) 
    {
        this.yeartj = yeartj;
    }

    public Long getYeartj() 
    {
        return yeartj;
    }
    public void setMonthtj(Long monthtj) 
    {
        this.monthtj = monthtj;
    }

    public Long getMonthtj() 
    {
        return monthtj;
    }
    public void setQs(Long qs) 
    {
        this.qs = qs;
    }

    public Long getQs() 
    {
        return qs;
    }
    public void setCqajs(Long cqajs) 
    {
        this.cqajs = cqajs;
    }

    public Long getCqajs() 
    {
        return cqajs;
    }
    public void setSbjStartTime(Date sbjStartTime) 
    {
        this.sbjStartTime = sbjStartTime;
    }

    public Date getSbjStartTime() 
    {
        return sbjStartTime;
    }
    public void setSbjEndTime(Date sbjEndTime) 
    {
        this.sbjEndTime = sbjEndTime;
    }

    public Date getSbjEndTime() 
    {
        return sbjEndTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("year", getYear())
            .append("month", getMonth())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("yeartj", getYeartj())
            .append("monthtj", getMonthtj())
            .append("qs", getQs())
            .append("cqajs", getCqajs())
            .append("sbjStartTime", getSbjStartTime())
            .append("sbjEndTime", getSbjEndTime())
            .append("status",getStatus())
            .toString();
    }
}
