package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 路段名对象 shcy_road
 * 
 * <AUTHOR>
 * @date 2023-02-09
 */
public class ShcyRoad extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 路段 */
    @Excel(name = "路段")
    private String road;

    /** 路段起 */
    @Excel(name = "路段起")
    private String roadUp;

    /** 路段止 */
    @Excel(name = "路段止")
    private String roadDown;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRoad(String road) 
    {
        this.road = road;
    }

    public String getRoad() 
    {
        return road;
    }
    public void setRoadUp(String roadUp) 
    {
        this.roadUp = roadUp;
    }

    public String getRoadUp() 
    {
        return roadUp;
    }
    public void setRoadDown(String roadDown) 
    {
        this.roadDown = roadDown;
    }

    public String getRoadDown() 
    {
        return roadDown;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("road", getRoad())
            .append("roadUp", getRoadUp())
            .append("roadDown", getRoadDown())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
