package com.ruoyi.shcy.service.impl;

import java.util.List;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyAfforestCaseMapper;
import com.ruoyi.shcy.domain.ShcyAfforestCase;
import com.ruoyi.shcy.service.IShcyAfforestCaseService;

/**
 * 绿化案事件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@Service
public class ShcyAfforestCaseServiceImpl implements IShcyAfforestCaseService
{
    @Autowired
    private ShcyAfforestCaseMapper shcyAfforestCaseMapper;

    /**
     * 查询绿化案事件
     *
     * @param id 绿化案事件主键
     * @return 绿化案事件
     */
    @Override
    public ShcyAfforestCase selectShcyAfforestCaseById(Long id)
    {
        return shcyAfforestCaseMapper.selectShcyAfforestCaseById(id);
    }

    /**
     * 查询绿化案事件列表
     *
     * @param shcyAfforestCase 绿化案事件
     * @return 绿化案事件
     */
    @Override
    public List<ShcyAfforestCase> selectShcyAfforestCaseList(ShcyAfforestCase shcyAfforestCase)
    {
        return shcyAfforestCaseMapper.selectShcyAfforestCaseList(shcyAfforestCase);
    }

    /**
     * 新增绿化案事件
     *
     * @param shcyAfforestCase 绿化案事件
     * @return 结果
     */
    @Override
    public int insertShcyAfforestCase(ShcyAfforestCase shcyAfforestCase)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        shcyAfforestCase.setCreateTime(DateUtils.getNowDate());
        shcyAfforestCase.setCreateBy(user.getUserName());
        shcyAfforestCase.setFillDate(DateUtils.getNowDate());
        if("7天".equals(shcyAfforestCase.getDealDay())){
           shcyAfforestCase.setDealTimeLimited(DateUtil.offset(DateUtils.getNowDate(), DateField.DAY_OF_MONTH,7));
        }else if("3天".equals(shcyAfforestCase.getDealDay())){
            shcyAfforestCase.setDealTimeLimited(DateUtil.offset(DateUtils.getNowDate(), DateField.DAY_OF_MONTH,3));
        } else if("5天".equals(shcyAfforestCase.getDealDay())){
            shcyAfforestCase.setDealTimeLimited(DateUtil.offset(DateUtils.getNowDate(), DateField.DAY_OF_MONTH,5));
        }
        return shcyAfforestCaseMapper.insertShcyAfforestCase(shcyAfforestCase);
    }

    /**
     * 修改绿化案事件
     *
     * @param shcyAfforestCase 绿化案事件
     * @return 结果
     */
    @Override
    public int updateShcyAfforestCase(ShcyAfforestCase shcyAfforestCase)
    {
        shcyAfforestCase.setUpdateTime(DateUtils.getNowDate());
        shcyAfforestCase.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        return shcyAfforestCaseMapper.updateShcyAfforestCase(shcyAfforestCase);
    }

    /**
     * 批量删除绿化案事件
     *
     * @param ids 需要删除的绿化案事件主键
     * @return 结果
     */
    @Override
    public int deleteShcyAfforestCaseByIds(Long[] ids)
    {
        return shcyAfforestCaseMapper.deleteShcyAfforestCaseByIds(ids);
    }

    /**
     * 删除绿化案事件信息
     *
     * @param id 绿化案事件主键
     * @return 结果
     */
    @Override
    public int deleteShcyAfforestCaseById(Long id)
    {
        return shcyAfforestCaseMapper.deleteShcyAfforestCaseById(id);
    }

    /**
     * 绿化id查询出当前的案事件
     *
     * @param forestId
     */
    @Override
    public ShcyAfforestCase selectShcyAfforestCaseByForestId(Long forestId) {
        return  shcyAfforestCaseMapper.selectShcyAfforestCaseByForestId(forestId);
    }


    /**
     * 退单指定时间（重新选择操作人）
     *
     * @param id
     **/
    @Override
    public int returnShcyAffroestCaseById(Long id) {
        return shcyAfforestCaseMapper.returnShcyAffroestCaseById(id);
    }
}
