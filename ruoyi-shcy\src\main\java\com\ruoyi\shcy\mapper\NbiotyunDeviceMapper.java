package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.NbiotyunDevice;

import java.util.List;

/**
 * 设备管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-01
 */
public interface NbiotyunDeviceMapper 
{
    /**
     * 查询设备管理
     * 
     * @param deviceId 设备管理主键
     * @return 设备管理
     */
    public NbiotyunDevice selectNbiotyunDeviceByDeviceId(Long deviceId);

    /**
     * 查询设备管理列表
     * 
     * @param nbiotyunDevice 设备管理
     * @return 设备管理集合
     */
    public List<NbiotyunDevice> selectNbiotyunDeviceList(NbiotyunDevice nbiotyunDevice);

    /**
     * 新增设备管理
     * 
     * @param nbiotyunDevice 设备管理
     * @return 结果
     */
    public int insertNbiotyunDevice(NbiotyunDevice nbiotyunDevice);

    /**
     * 修改设备管理
     * 
     * @param nbiotyunDevice 设备管理
     * @return 结果
     */
    public int updateNbiotyunDevice(NbiotyunDevice nbiotyunDevice);

    /**
     * 删除设备管理
     * 
     * @param deviceId 设备管理主键
     * @return 结果
     */
    public int deleteNbiotyunDeviceByDeviceId(Long deviceId);

    /**
     * 批量删除设备管理
     * 
     * @param deviceIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNbiotyunDeviceByDeviceIds(Long[] deviceIds);

    String selectResponsiblePerson(String deviceImei);
}
