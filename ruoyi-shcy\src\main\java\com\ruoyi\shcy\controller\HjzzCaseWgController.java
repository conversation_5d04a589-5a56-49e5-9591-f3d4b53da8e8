package com.ruoyi.shcy.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import com.ruoyi.shcy.dto.HjzzCaseWgDTO;
import com.ruoyi.shcy.dto.HjzzCaseWgImageDTO;
import com.ruoyi.shcy.service.IHjzzCaseService;
import com.ruoyi.shcy.service.IIccAlarmRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 违规生产入侵案件Controller
 * 
 * <AUTHOR>
 * @date 2023-11-08
 */
@RestController
@RequestMapping("/shcy/hjzzCaseWg")
public class HjzzCaseWgController extends BaseController
{
    @Autowired
    private IHjzzCaseService hjzzCaseService;

    @Autowired
    private IIccAlarmRecordService iccAlarmRecordService;

    /**
     * 查询违规生产入侵案件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseWg:list')")
    @GetMapping("/list")
    public TableDataInfo list(HjzzCase hjzzCase)
    {
        hjzzCase.setCaseType("2");
        startPage();
        List<HjzzCase> list = hjzzCaseService.selectHjzzCaseWgList(hjzzCase);
        return getDataTable(list);
    }

    /**
     * 导出违规生产入侵案件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseWg:export')")
    @Log(title = "违规生产入侵案件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HjzzCase hjzzCase)
    {
        hjzzCase.setCaseType("2");
        List<HjzzCase> list = hjzzCaseService.selectHjzzCaseWgList(hjzzCase);
        List<HjzzCaseWgDTO> exportList = BeanUtil.copyToList(list, HjzzCaseWgDTO.class);
        ExcelUtil<HjzzCaseWgDTO> util = new ExcelUtil<HjzzCaseWgDTO>(HjzzCaseWgDTO.class);
        util.exportExcel(response, exportList, "违规生产入侵案件数据");
    }

    /**
     * 导出违规生产入侵案件列表（图片）
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseWg:export')")
    @Log(title = "违规生产入侵案件（图片）", businessType = BusinessType.EXPORT)
    @PostMapping("/exportImage")
    public void exportImage(HttpServletResponse response, HjzzCase hjzzCase)
    {
        hjzzCase.setCaseType("2");
        List<HjzzCase> list = hjzzCaseService.selectHjzzCaseWgList(hjzzCase);
        List<HjzzCaseWgImageDTO> exportList = BeanUtil.copyToList(list, HjzzCaseWgImageDTO.class);

        // 获取exportList中所有的alarmRecordId，转为Long类型数组
        Long[] alarmRecordIds = exportList.stream().map(HjzzCaseWgImageDTO::getAlarmRecordId).toArray(Long[]::new);
        List<IccAlarmRecord> alarmRecordList = iccAlarmRecordService.selectIccAlarmRecordByIds(alarmRecordIds);
        // 将alarmRecordList转化为Map，key为alarmRecordId，value为alarmPicture
        Map<Long, String> alarmRecordMap = alarmRecordList.stream().collect(Collectors.toMap(IccAlarmRecord::getId, IccAlarmRecord::getAlarmPicture));
        // 将alarmPicture赋值给exportList中的image，'https://172.16.33.100/evo-apigw/evo-oss/' + alarmPicture
        exportList.forEach(item -> item.setImage("https://172.16.33.100/evo-apigw/evo-oss/" + alarmRecordMap.get(item.getAlarmRecordId())));
        ExcelUtil<HjzzCaseWgImageDTO> util = new ExcelUtil<HjzzCaseWgImageDTO>(HjzzCaseWgImageDTO.class);
        util.exportExcel(response, exportList, "违规生产入侵案件数据");
    }

    /**
     * 获取违规生产入侵案件详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseWg:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hjzzCaseService.selectHjzzCaseById(id));
    }

    /**
     * 新增违规生产入侵案件
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseWg:add')")
    @Log(title = "违规生产入侵案件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HjzzCase hjzzCase)
    {
        return toAjax(hjzzCaseService.insertHjzzCase(hjzzCase));
    }

    /**
     * 修改违规生产入侵案件
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseWg:edit')")
    @Log(title = "违规生产入侵案件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HjzzCase hjzzCase)
    {
        return toAjax(hjzzCaseService.updateHjzzCase(hjzzCase));
    }

    /**
     * 删除违规生产入侵案件
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCaseWg:remove')")
    @Log(title = "违规生产入侵案件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hjzzCaseService.deleteHjzzCaseByIds(ids));
    }

    @GetMapping("/getHjzzMonitorWgEventList")
    public TableDataInfo getHjzzMonitorWgEventList(HjzzCase hjzzCase)
    {
        Map<String, Object> params = hjzzCase.getParams();
        params.put("beginTime", DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN));
        params.put("endTime", DateUtil.format(DateUtil.date(), DatePattern.NORM_DATE_PATTERN));
        hjzzCase.setParams(params);
        hjzzCase.setCaseType("2");
        startPage();
        List<HjzzCase> list = hjzzCaseService.selectHjzzCaseWgList(hjzzCase);
        return getDataTable(list);
    }
}
