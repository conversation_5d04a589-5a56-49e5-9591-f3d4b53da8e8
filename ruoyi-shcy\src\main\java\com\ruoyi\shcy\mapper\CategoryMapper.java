package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.Category;

/**
 * 行业大类Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-11-08
 */
public interface CategoryMapper 
{
    /**
     * 查询行业大类
     * 
     * @param id 行业大类主键
     * @return 行业大类
     */
    public Category selectCategoryById(Long id);

    /**
     * 查询行业大类列表
     * 
     * @param category 行业大类
     * @return 行业大类集合
     */
    public List<Category> selectCategoryList(Category category);

    /**
     * 新增行业大类
     * 
     * @param category 行业大类
     * @return 结果
     */
    public int insertCategory(Category category);

    /**
     * 修改行业大类
     * 
     * @param category 行业大类
     * @return 结果
     */
    public int updateCategory(Category category);

    /**
     * 删除行业大类
     * 
     * @param id 行业大类主键
     * @return 结果
     */
    public int deleteCategoryById(Long id);

    /**
     * 批量删除行业大类
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCategoryByIds(Long[] ids);
}
