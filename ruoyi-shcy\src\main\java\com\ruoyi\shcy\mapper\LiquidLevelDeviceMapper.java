package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.LiquidLevelDevice;

import java.util.List;

/**
 * 液位超限感知设备Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-28
 */
public interface LiquidLevelDeviceMapper 
{
    /**
     * 查询液位超限感知设备
     * 
     * @param id 液位超限感知设备主键
     * @return 液位超限感知设备
     */
    public LiquidLevelDevice selectLiquidLevelDeviceById(Long id);

    /**
     * 查询液位超限感知设备列表
     * 
     * @param liquidLevelDevice 液位超限感知设备
     * @return 液位超限感知设备集合
     */
    public List<LiquidLevelDevice> selectLiquidLevelDeviceList(LiquidLevelDevice liquidLevelDevice);

    /**
     * 新增液位超限感知设备
     * 
     * @param liquidLevelDevice 液位超限感知设备
     * @return 结果
     */
    public int insertLiquidLevelDevice(LiquidLevelDevice liquidLevelDevice);

    /**
     * 修改液位超限感知设备
     * 
     * @param liquidLevelDevice 液位超限感知设备
     * @return 结果
     */
    public int updateLiquidLevelDevice(LiquidLevelDevice liquidLevelDevice);

    /**
     * 删除液位超限感知设备
     * 
     * @param id 液位超限感知设备主键
     * @return 结果
     */
    public int deleteLiquidLevelDeviceById(Long id);

    /**
     * 批量删除液位超限感知设备
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLiquidLevelDeviceByIds(Long[] ids);

    List<String> fxftAddress();

    List<String> selectImeiList();

    LiquidLevelDevice selectLiquidLevelDeviceByDeviceImei(String deviceImei);

    public int batchUpdateLiquidLevelDevice(List<LiquidLevelDevice> liquidLevelDevices);

    public int batchUpdateLiquidLevelDeviceState(List<LiquidLevelDevice> liquidLevelDevices);
}
