package com.ruoyi.web.controller.sthj;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.shcy.domain.SpecialTask;
import com.ruoyi.shcy.service.ISpecialTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 专项任务Controller
 * <AUTHOR>
 *
 */
@RestController
@RequestMapping("/sthj/zxrw")
public class ZxrwController extends BaseController {

    @Autowired
    private ISpecialTaskService specialTaskService;

    /**
     * 上报
     */
    @PostMapping("/sb")
    public AjaxResult sb(@RequestBody SpecialTask specialTask) {
        // inspectionResult 为 现场无问题或者即知即改时，状态改为已处理, 处置中为待处理
        if ("现场无问题".equals(specialTask.getInspectionResult()) || "即知即改".equals(specialTask.getInspectionResult())) {
            specialTask.setCirculationState("0");
        } else {
            specialTask.setCirculationState("1");
        }
        specialTask.setCreateBy(getUsername());
        return toAjax(specialTaskService.insertSpecialTask(specialTask));
    }

    /**
     * 查询列表
     */
    @GetMapping("/list")
    public TableDataInfo list(SpecialTask specialTask, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            specialTask.setParams(params);
        }
        startPage();
        List<SpecialTask> list = specialTaskService.selectSpecialTaskList(specialTask);
        return getDataTable(list);
    }

    /**
     * 获取案件数量
     */
    @GetMapping("/getCaseCount")
    public AjaxResult getCaseCount(SpecialTask specialTask, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            specialTask.setParams(params);
        }
        return AjaxResult.success(specialTaskService.getCaseCount(specialTask));
    }

    /**
     * 获取案件详情
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getCase(@PathVariable("id") Long id) {
        return AjaxResult.success(specialTaskService.selectSpecialTaskById(id));
    }

    /**
     * 处理
     */
    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody SpecialTask specialTask) {
        specialTask.setCirculationState("0");
        specialTask.setUpdateBy(getUsername());
        return toAjax(specialTaskService.updateSpecialTask(specialTask));
    }

}
