package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.FloodControlKeyArea;
import com.ruoyi.shcy.service.IFloodControlKeyAreaService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 防汛防台重点区域Controller
 * 
 * <AUTHOR>
 * @date 2023-09-22
 */
@RestController
@RequestMapping("/shcy/floodControlKeyArea")
public class FloodControlKeyAreaController extends BaseController
{
    @Autowired
    private IFloodControlKeyAreaService floodControlKeyAreaService;

    /**
     * 查询防汛防台重点区域列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodControlKeyArea:list')")
    @GetMapping("/list")
    public TableDataInfo list(FloodControlKeyArea floodControlKeyArea)
    {
        startPage();
        List<FloodControlKeyArea> list = floodControlKeyAreaService.selectFloodControlKeyAreaList(floodControlKeyArea);
        return getDataTable(list);
    }

    /**
     * 导出防汛防台重点区域列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodControlKeyArea:export')")
    @Log(title = "防汛防台重点区域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FloodControlKeyArea floodControlKeyArea)
    {
        List<FloodControlKeyArea> list = floodControlKeyAreaService.selectFloodControlKeyAreaList(floodControlKeyArea);
        ExcelUtil<FloodControlKeyArea> util = new ExcelUtil<FloodControlKeyArea>(FloodControlKeyArea.class);
        util.exportExcel(response, list, "防汛防台重点区域数据");
    }

    /**
     * 获取防汛防台重点区域详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodControlKeyArea:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(floodControlKeyAreaService.selectFloodControlKeyAreaById(id));
    }

    /**
     * 新增防汛防台重点区域
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodControlKeyArea:add')")
    @Log(title = "防汛防台重点区域", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FloodControlKeyArea floodControlKeyArea)
    {
        return toAjax(floodControlKeyAreaService.insertFloodControlKeyArea(floodControlKeyArea));
    }

    /**
     * 修改防汛防台重点区域
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodControlKeyArea:edit')")
    @Log(title = "防汛防台重点区域", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FloodControlKeyArea floodControlKeyArea)
    {
        return toAjax(floodControlKeyAreaService.updateFloodControlKeyArea(floodControlKeyArea));
    }

    /**
     * 删除防汛防台重点区域
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodControlKeyArea:remove')")
    @Log(title = "防汛防台重点区域", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(floodControlKeyAreaService.deleteFloodControlKeyAreaByIds(ids));
    }
}
