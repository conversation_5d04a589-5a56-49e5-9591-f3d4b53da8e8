package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.SensorDevice;

import java.util.List;

/**
 * 传感器设备Service接口
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
public interface ISensorDeviceService 
{
    /**
     * 查询传感器设备
     * 
     * @param id 传感器设备主键
     * @return 传感器设备
     */
    public SensorDevice selectSensorDeviceById(Long id);

    /**
     * 查询传感器设备列表
     * 
     * @param sensorDevice 传感器设备
     * @return 传感器设备集合
     */
    public List<SensorDevice> selectSensorDeviceList(SensorDevice sensorDevice);

    /**
     * 新增传感器设备
     * 
     * @param sensorDevice 传感器设备
     * @return 结果
     */
    public int insertSensorDevice(SensorDevice sensorDevice);

    /**
     * 修改传感器设备
     * 
     * @param sensorDevice 传感器设备
     * @return 结果
     */
    public int updateSensorDevice(SensorDevice sensorDevice);

    /**
     * 批量删除传感器设备
     * 
     * @param ids 需要删除的传感器设备主键集合
     * @return 结果
     */
    public int deleteSensorDeviceByIds(Long[] ids);

    /**
     * 删除传感器设备信息
     * 
     * @param id 传感器设备主键
     * @return 结果
     */
    public int deleteSensorDeviceById(Long id);

    public SensorDevice selectSensorDeviceByImei(String imei);
}
