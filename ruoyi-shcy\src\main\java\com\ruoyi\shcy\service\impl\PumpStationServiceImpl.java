package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.PumpStation;
import com.ruoyi.shcy.mapper.PumpStationMapper;
import com.ruoyi.shcy.service.IPumpStationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 泵站Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-28
 */
@Service
public class PumpStationServiceImpl implements IPumpStationService 
{
    @Autowired
    private PumpStationMapper pumpStationMapper;

    /**
     * 查询泵站
     * 
     * @param id 泵站主键
     * @return 泵站
     */
    @Override
    public PumpStation selectPumpStationById(Long id)
    {
        return pumpStationMapper.selectPumpStationById(id);
    }

    /**
     * 查询泵站列表
     * 
     * @param pumpStation 泵站
     * @return 泵站
     */
    @Override
    public List<PumpStation> selectPumpStationList(PumpStation pumpStation)
    {
        return pumpStationMapper.selectPumpStationList(pumpStation);
    }

    /**
     * 新增泵站
     * 
     * @param pumpStation 泵站
     * @return 结果
     */
    @Override
    public int insertPumpStation(PumpStation pumpStation)
    {
        return pumpStationMapper.insertPumpStation(pumpStation);
    }

    /**
     * 修改泵站
     * 
     * @param pumpStation 泵站
     * @return 结果
     */
    @Override
    public int updatePumpStation(PumpStation pumpStation)
    {
        return pumpStationMapper.updatePumpStation(pumpStation);
    }

    /**
     * 批量删除泵站
     * 
     * @param ids 需要删除的泵站主键
     * @return 结果
     */
    @Override
    public int deletePumpStationByIds(Long[] ids)
    {
        return pumpStationMapper.deletePumpStationByIds(ids);
    }

    /**
     * 删除泵站信息
     * 
     * @param id 泵站主键
     * @return 结果
     */
    @Override
    public int deletePumpStationById(Long id)
    {
        return pumpStationMapper.deletePumpStationById(id);
    }

    @Override
    public List<PumpStation> selectPumpStationListByIds(Long[] ids) {
        return pumpStationMapper.selectPumpStationListByIds(ids);
    }
}
