package com.ruoyi.shcy.service;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.shcy.domain.CheckRecord;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.ShopCheckLog;
import com.ruoyi.shcy.domain.vo.CategoryDetailVo;
import com.ruoyi.shcy.domain.vo.CommitteeDetailVo;
import com.ruoyi.shcy.domain.vo.ShopDetailVo;

import java.util.List;

/**
 * 店铺信息Service接口
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
public interface IShopService
{
    /**
     * 查询店铺信息
     *
     * @param id 店铺信息主键
     * @return 店铺信息
     */
    public Shop selectShopById(Long id);

    /**
     * 查询店铺信息列表
     *
     * @param shop 店铺信息
     * @return 店铺信息集合
     */
    public List<Shop> selectShopList(Shop shop);

    /**
     * 大屏接口统计店铺数量
     * */
    public int selectShopCount();

    /**
     * 根据统一信用社会信用代码查询
     * @param creditCode
     * @return 店铺信息
     * */
    public Shop  selectShopByCreditCode(String creditCode);

    /**
     * 根据商铺的统一信用代码修改店铺的监管状态
     * **/
    public int updateShopSupervisiorStatus(String shopCreditCode);


    /**
     * 查询店铺信息列表（正常开业的）
     * @param shop 店铺信息
     * @return 店铺信息集合
     * */
    public List<Shop> selectShopListWithNoram(Shop shop);

    /**
     * 城管巡检 已检查列表（是否存在跨门经营，门责制是否落实）
     *
     * **/
    public List<Shop> selectCheckedShopListWithNormalCg(ShopCheckLog shopCheckLog);



    /**
     * 导入用户数据
     *
     * @param shopList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importShop(List<Shop> shopList, Boolean isUpdateSupport, String operName);


    /**
     * 新增店铺信息
     *
     * @param shop 店铺信息
     * @return 结果
     */
    public int insertShop(Shop shop);

    /**
     * 修改店铺信息
     *
     * @param shop 店铺信息
     * @return 结果
     */
    public int updateShop(Shop shop);

    /**
     * 每个月修改一次店铺检查状态
     *
     * **/
    public int updateCheckStatus(Long id);


    /**
     * 批量删除店铺信息
     *
     * @param ids 需要删除的店铺信息主键集合
     * @return 结果
     */
    public int deleteShopByIds(Long[] ids);

    /**
     * 删除店铺信息信息
     *
     * @param id 店铺信息主键
     * @return 结果
     */
    public int deleteShopById(Long id);

    /**
     * 查询出来所有的商铺
     * */
    List<Shop> selectAllShops(Shop shop);


    /**
     * 每天夜里12点修改店铺的巡查状态
     * **/
    public int updateShopCheckStatus(Long shopDeparmentId);

    /**
     * 查询 居委会下店铺的数量
     *
     * */
    public List<Shop>  selectCommitteeShopNum(Shop shop);

    /**
     * 查询对应大类下管理的商铺信息
     * */
    public List<CategoryDetailVo> selectCategoryShopNum(String shopCategory);



    /**
     * 各个大类下巡查店铺的数量
     * */
//    public List<CategoryDetailVo> selectCategoryCheckedShopNum(@Param("category") String category,@Param("checkDate") Date checkDate);
    public List<CategoryDetailVo> selectCategoryCheckedShopNum(Shop shop);






    /**
     * 查询店铺总数
     * */
    public List<ShopDetailVo> selectShopTotal(Shop shop);

    /**
     * 查询 今日巡查店铺总数
     *
     * */

    public List<ShopDetailVo> selectTodayCheckShopTotal(ShopCheckLog shopCheckLog);

    /**
     * 查询今日巡查店铺正常数量
     *
     * */
    public List<ShopDetailVo> selectTodayCheckShopNomalNum(ShopCheckLog shopCheckLog);

    /**
     * 查询今日巡查店铺学业数量
     *
     * */
    public List<ShopDetailVo> selectTodayCheckShopRestNum(ShopCheckLog shopCheckLog);

    /**
     * 查询今日店铺关停数量
     *
     * */
    public List<ShopDetailVo> selectTodayCheckShopCloseNum(Shop shop);


    /**
     * 查询今日店铺重点监管数量
     * **/
    public List<ShopDetailVo> selectSupervisorShop(Shop shop);

    /**
     * 查询通告类型为 谈话的店铺
     *
     * **/
    public  List<ShopDetailVo> selectTalkShop(Shop shop);

    /**
     * 查询通告类型为 督办的店铺
     *
     * **/
    public  List<ShopDetailVo> selectHandleShop(Shop shop);

    /**
     * 查询通告类型为 黄牌的店铺
     *
     * **/
    public  List<ShopDetailVo> selectYellowCardShop(Shop shop);



    /**
     * 各个居委会管理的店铺数量
     * */
    public List<CommitteeDetailVo>  selectCommitteeCheckShopTotal(SysDept sysDept);


    /**
     *
     * 居委会巡查商铺数量
     *
     * */
//    public List<CommitteeDetailVo> selectCommitteeCheckedShopNum(@Param("deptId") Long deptId, @Param("checkDate") Date checkDate);
    public List<CommitteeDetailVo> selectCommitteeCheckedShopNum(Shop shop);


    //*****************************************************************根据店铺开业状态去查询********************************************

    /**
     * 查询开业正常商铺数量
     *
     **/
    public List<Shop>  selectOpenNormalShopNum(Shop shop);


    /**
     * 大屏查询开业正常商铺数量
     * */
    public int selectOpenNomalShopCount();

    /**
     * 查询歇业商铺数量
     *
     **/
    public List<Shop>  selectOpenRestShopNum(Shop shop);

    /**
     * 大屏查询歇业商铺数量
     * */
    public int   selectOpenRestShopCount();

    /**
     * 查询关停商铺数量
     *
     **/
    public List<Shop>  selectOpenStopShopNum(Shop shop);

    /**
     * 大屏查询关停商铺数量
     * */
    public int selectOpenStopShopCount();



    /**
     * 查询未巡查商铺信息
     * **/
    public List<Shop> selectNoCheckedShopList(CheckRecord checkRecord);

    List<Shop> selectCheckShopList();


    /**
     * 查询对应街长路段名称的商铺信息
     * **/

    List<Shop> selectShopByChiefRoad(String road);

    List<Shop> selectAllYyShops();

    List<Shop> selectAllShutdownShops();
}
