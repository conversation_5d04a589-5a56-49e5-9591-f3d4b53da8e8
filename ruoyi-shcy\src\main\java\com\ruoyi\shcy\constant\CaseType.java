package com.ruoyi.shcy.constant;

/**
 * 案件类型
 */
public enum CaseType {
    TRI_COMBINATION_PHENOMENON("1", "是否存在“三合一”住人现象"),
    ADVERTISEMENT_HAZARD("2", "广告牌存在安全隐患"),
    NO_LICENSE_DISPLAYED("3", "证照公示"),
    STREET_SHOP_CONSTRUCTION_SAFETY("4", "沿街商铺施工安全"),
    STREET_SHOP_CIVILIZED_CONSTRUCTION("5", "沿街商铺文明施工");

    private final String value;
    private final String name;

    // 构造函数
    CaseType(String value, String name) {
        this.value = value;
        this.name = name;
    }

    // 获取案件类型的值
    public String getValue() {
        return value;
    }

    // 获取案件类型的名称
    public String getName() {
        return name;
    }

}

