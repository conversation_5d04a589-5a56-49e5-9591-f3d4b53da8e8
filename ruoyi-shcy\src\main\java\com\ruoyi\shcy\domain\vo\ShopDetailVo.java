package com.ruoyi.shcy.domain.vo;

/**
 * <AUTHOR>
 * @Date 2022/11/8 10:22
 * @Version 1.0
 */

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/** 每日巡查店铺详情信息*/
public class ShopDetailVo {



    // ********************************************店铺本身实际数据**********************************************
    // ********************************************店铺本身实际数据**********************************************
    //店铺数据
    /** 店铺总数*/
    @Excel(name="店铺总数量")
    private String ShopTotal;

    /** 今日巡查店铺数量 */
    @Excel(name="今日巡查店铺数量")
    private String checkShopTotal;

    /** 今日正常店铺数量 */
    @Excel(name="今日正常店铺数量")
    private String shopNormalNum;

    /** 今日歇业店铺数量 */
    @Excel(name="今日歇业店铺数量")
    private String shopRestNum;

    /** 今日关停店铺数量 */
    @Excel(name="今日关停店铺数量")
    private String shopCloseNum;

    /**重点监管店铺数量 */
    @Excel(name="重点监管商铺数量")
    private String shopSupervisorNum;

    /** 谈话商铺数量 */
    @Excel(name="谈话商铺数量")
    private String shopTalkNum;

    /** 督办商铺数量 */
    @Excel(name="督办商铺数量")
    private String shopHandleNum;

    /** 黄牌商铺数量*/
    @Excel(name="黄牌商铺数量")
    private String shopYellowCardNum;






    // ********************************************从业人员数据**********************************************
    // ********************************************从业人员数据**********************************************


    /** 从业人员总人数*/
    @Excel(name="从业人员总人数")
    private String EmployeesTotal;

    /** 每日巡查从业人员数*/
    @Excel(name="今日巡查从业人员数")
    private String checkEmployeesTotal;

    /** 每日核酸正常人员数*/
    @Excel(name="今日核酸正常人员数")
    private String checkNoramEmployeesNum;

    /** 每日核酸异常人员数*/
    @Excel(name="今日核酸异常人员数")
    private String checkAbnormalEmployeesNum;

    /** 每日离岗人员数*/
    @Excel(name="今日离岗人数")
    private String  checkRetiredEmployeesNum;



    /**巡查日期*/
    @Excel(name = "巡查日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date  curDate;


    /** 商铺的员工数量 */
    private String  employeeNum;

    /** 商铺名称数量 */
    private String shopName;

    public String getShopSupervisorNum() {
        return shopSupervisorNum;
    }

    public void setShopSupervisorNum(String shopSupervisorNum) {
        this.shopSupervisorNum = shopSupervisorNum;
    }

    public String getShopTalkNum() {
        return shopTalkNum;
    }

    public void setShopTalkNum(String shopTalkNum) {
        this.shopTalkNum = shopTalkNum;
    }

    public String getShopHandleNum() {
        return shopHandleNum;
    }

    public void setShopHandleNum(String shopHandleNum) {
        this.shopHandleNum = shopHandleNum;
    }

    public String getShopYellowCardNum() {
        return shopYellowCardNum;
    }

    public void setShopYellowCardNum(String shopYellowCardNum) {
        this.shopYellowCardNum = shopYellowCardNum;
    }

    public String getEmployeeNum() {
        return employeeNum;
    }

    public void setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopTotal() {
        return ShopTotal;
    }

    public void setShopTotal(String shopTotal) {
        ShopTotal = shopTotal;
    }

    public String getCheckShopTotal() {
        return checkShopTotal;
    }

    public void setCheckShopTotal(String checkShopTotal) {
        this.checkShopTotal = checkShopTotal;
    }

    public String getShopNormalNum() {
        return shopNormalNum;
    }

    public void setShopNormalNum(String shopNormalNum) {
        this.shopNormalNum = shopNormalNum;
    }

    public String getShopRestNum() {
        return shopRestNum;
    }

    public void setShopRestNum(String shopRestNum) {
        this.shopRestNum = shopRestNum;
    }

    public String getShopCloseNum() {
        return shopCloseNum;
    }

    public void setShopCloseNum(String shopCloseNum) {
        this.shopCloseNum = shopCloseNum;
    }

    public Date getCurDate() {
        return curDate;
    }

    public void setCurDate(Date curDate) {
        this.curDate = curDate;
    }


    public String getEmployeesTotal() {
        return EmployeesTotal;
    }

    public void setEmployeesTotal(String employeesTotal) {
        EmployeesTotal = employeesTotal;
    }

    public String getCheckEmployeesTotal() {
        return checkEmployeesTotal;
    }

    public void setCheckEmployeesTotal(String checkEmployeesTotal) {
        this.checkEmployeesTotal = checkEmployeesTotal;
    }

    public String getCheckNoramEmployeesNum() {
        return checkNoramEmployeesNum;
    }

    public void setCheckNoramEmployeesNum(String checkNoramEmployeesNum) {
        this.checkNoramEmployeesNum = checkNoramEmployeesNum;
    }

    public String getCheckAbnormalEmployeesNum() {
        return checkAbnormalEmployeesNum;
    }

    public void setCheckAbnormalEmployeesNum(String checkAbnormalEmployeesNum) {
        this.checkAbnormalEmployeesNum = checkAbnormalEmployeesNum;
    }

    public String getCheckRetiredEmployeesNum() {
        return checkRetiredEmployeesNum;
    }

    public void setCheckRetiredEmployeesNum(String checkRetiredEmployeesNum) {
        this.checkRetiredEmployeesNum = checkRetiredEmployeesNum;
    }



    @Override
    public String toString() {
        return "ShopDetailVo{" +
                "checkShopTotal='" + checkShopTotal + '\'' +
                ", shopNormalNum='" + shopNormalNum + '\'' +
                ", shopRestNum='" + shopRestNum + '\'' +
                ", shopCloseNum='" + shopCloseNum + '\'' +
                ", curDate=" + curDate +
                '}';
    }
}
