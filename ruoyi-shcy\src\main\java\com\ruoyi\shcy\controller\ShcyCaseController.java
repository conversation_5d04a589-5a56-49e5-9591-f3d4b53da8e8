package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.ShcyCase;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.service.IShcyCaseService;
import com.ruoyi.shcy.service.IShcyFileInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 案事件Controller
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@RestController
@RequestMapping("/shcy/case")
public class ShcyCaseController extends BaseController
{
    @Autowired
    private IShcyCaseService shcyCaseService;


    @Autowired
    private IShcyFileInfoService shcyFileInfoService;

    /**
     * 查询案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:case:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyCase shcyCase)
    {
        // 案事件权限 admin 和 营商办 查看所有案事件
        String username = getUsername();
        SysUser user = getLoginUser().getUser();
        String deptName = user.getDept().getDeptName();
        if("admin".equals(username) || "营商办".equals(username)  || "城运中心".equals(deptName)) {
            // all
            shcyCase.getParams().put("permission", 1);
        } else if ("应急中心".equals(username)) {
            // CaseType 对应 1、4
            shcyCase.getParams().put("permission", 2);
        } else if ("综合执法队".equals(username)) {
            // CaseType 对应 2、5
            shcyCase.getParams().put("permission", 3);
        } else if ("市场监管所".equals(username)) {
            // CaseType 对应 3
            shcyCase.getParams().put("permission", 4);
        } else {
            // CaseType 对应 9 代表空数据
            shcyCase.getParams().put("permission", 5);
        }
        startPage();
        List<ShcyCase> list = shcyCaseService.selectShcyCaseList(shcyCase);
        return getDataTable(list);
    }

    /**
     * 导出案事件列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:case:export')")
    @Log(title = "案事件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyCase shcyCase)
    {
        List<ShcyCase> list = shcyCaseService.selectShcyCaseList(shcyCase);
        ExcelUtil<ShcyCase> util = new ExcelUtil<ShcyCase>(ShcyCase.class);
        util.exportExcel(response, list, "案事件数据");
    }

    /**
     * 获取案事件详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:case:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        ShcyCase shcyCase = shcyCaseService.selectShcyCaseById(id);
        if(shcyCase !=null){
            if(StringUtils.isNotEmpty(shcyCase.getCaseDealPhoto())){
                String[] ids  = shcyCase.getCaseDealPhoto().split(",");
                if(ids.length!=0){
                    List<String> imgurls = new ArrayList<>();
                    for (String imageid : ids) {
                        ShcyFileInfo shcyFileInfo = shcyFileInfoService.selectShcyFileInfoByFileId(Long.valueOf(imageid));
                        imgurls.add(shcyFileInfo.getFilePath());
                    }
                    shcyCase.setPhotoUrls(imgurls);
                }
            }
        }
        return AjaxResult.success(shcyCase);
    }

    /**
     * 新增案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:case:add')")
    @Log(title = "案事件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyCase shcyCase)
    {
        return toAjax(shcyCaseService.insertShcyCase(shcyCase));
    }

    /**
     * 修改案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:case:edit')")
    @Log(title = "案事件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyCase shcyCase)
    {
        Date finishDate = DateUtils.getNowDate();
        shcyCase.setCaseFinishTime(finishDate);
        shcyCase.setCirculationState("0");
        return toAjax(shcyCaseService.updateShcyCase(shcyCase));
    }

    @Log(title = "选择处理人", businessType = BusinessType.UPDATE)
    @PostMapping(value="/chose")
    public AjaxResult editChoseDealPerson(@RequestBody ShcyCase shcyCase){
        shcyCase.setCirculationState("1");
        return toAjax(shcyCaseService.updateShcyCase(shcyCase));
    }

    /**
     * 删除案事件
     */
    @PreAuthorize("@ss.hasPermi('shcy:case:remove')")
    @Log(title = "案事件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyCaseService.deleteShcyCaseByIds(ids));
    }

    /**
     * 街长处置人退单操作（街长管理员需要重新选择处置人）
     * */
    @Log(title = "案事件", businessType = BusinessType.UPDATE)
    @PostMapping(value="/return/{id}")
    public AjaxResult returnShcyCase(@PathVariable Long id)
    {
        return toAjax(shcyCaseService.returnShcyCaseById(id));
    }
}
