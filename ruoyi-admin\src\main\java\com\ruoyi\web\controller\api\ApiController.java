package com.ruoyi.web.controller.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.shcy.domain.Control;
import com.ruoyi.shcy.domain.ForestHeadManage;
import com.ruoyi.shcy.domain.MapArea;
import com.ruoyi.shcy.dto.ControlDto;
import com.ruoyi.shcy.dto.CoordinateDto;
import com.ruoyi.shcy.dto.ForestHeadManageDto;
import com.ruoyi.shcy.dto.MapAreaDto;
import com.ruoyi.shcy.service.IControlService;
import com.ruoyi.shcy.service.IForestHeadManageService;
import com.ruoyi.shcy.service.IMapAreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Anonymous
@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
public class ApiController {

    private final IMapAreaService mapAreaService;
    private final IControlService controlService;
    private final IForestHeadManageService forestHeadManageService;

    /**
     * 得到地图区域列表
     *
     * @param mapArea 地图区域
     * @return {@link AjaxResult}
     */
    @GetMapping("/getMapAreaList")
    public AjaxResult getMapAreaList(MapArea mapArea)
    {
        List<MapArea> mapAreas = mapAreaService.selectMapAreaList(mapArea);
        ArrayList<MapAreaDto> mapAreaDtos = new ArrayList<>();
        mapAreas.forEach(m-> {
            MapAreaDto mapAreaDto = new MapAreaDto();
            mapAreaDto.setAreaId(m.getAreaId());
            mapAreaDto.setAreaName(m.getAreaName());
            if (StrUtil.isNotEmpty(m.getAreaRange())) {
                ArrayList<CoordinateDto> list = CollUtil.newArrayList();
                String[] arr = m.getAreaRange().split(";");
                for (String s : arr) {
                    String[] split = s.split(",");
                    CoordinateDto coordinateDto = new CoordinateDto();
                    coordinateDto.setLongitude(split[0]);
                    coordinateDto.setLatitude(split[1]);
                    list.add(coordinateDto);
                }
                mapAreaDto.setAreaRange(list);
            }
            mapAreaDtos.add(mapAreaDto);
        });
        return AjaxResult.success(mapAreaDtos);
    }

    /**
     * 获取监控点列表
     *
     * @param control 监控点
     * @return {@link AjaxResult}
     */
    @GetMapping("/getControlList")
    public AjaxResult getControlList(Control control) {
        List<Control> controls = controlService.selectControlList(control);
        List<ControlDto> controlDtos = BeanUtil.copyToList(controls, ControlDto.class);
        return AjaxResult.success(controlDtos);
    }

    /**
     * 获取监控点坐标数组
     *
     * @param control 监控点
     * @return {@link AjaxResult}
     */
    @GetMapping("/getControlArray")
    public AjaxResult getControlArray(Control control) {
        List<Control> controls = controlService.selectControlList(control);
        ArrayList<String[]> arr = new ArrayList<String[]>();
        controls.forEach(c->{
            String[] arr1 = {c.getControlLon(), c.getControlLat()};
            arr.add(arr1);
        });
        return AjaxResult.success(arr);
    }

    /**
     * 获取林长管理列表
     *
     * @param forestHeadManage 林长管理
     * @return {@link AjaxResult}
     */
    @GetMapping("/getForestHeadManageList")
    public AjaxResult getForestHeadManageList(ForestHeadManage forestHeadManage) {
        List<ForestHeadManage> forestHeadManages = forestHeadManageService.selectForestHeadManageList(forestHeadManage);
        List<ForestHeadManageDto> forestHeadManageDtos = BeanUtil.copyToList(forestHeadManages, ForestHeadManageDto.class);
        return AjaxResult.success(forestHeadManageDtos);
    }



}
