package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.MonitoringTypes;

import java.util.List;

/**
 * 监控类型Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-11
 */
public interface IMonitoringTypesService 
{
    /**
     * 查询监控类型
     * 
     * @param monitoringTypeId 监控类型主键
     * @return 监控类型
     */
    public MonitoringTypes selectMonitoringTypesByMonitoringTypeId(Long monitoringTypeId);

    /**
     * 查询监控类型列表
     * 
     * @param monitoringTypes 监控类型
     * @return 监控类型集合
     */
    public List<MonitoringTypes> selectMonitoringTypesList(MonitoringTypes monitoringTypes);

    /**
     * 新增监控类型
     * 
     * @param monitoringTypes 监控类型
     * @return 结果
     */
    public int insertMonitoringTypes(MonitoringTypes monitoringTypes);

    /**
     * 修改监控类型
     * 
     * @param monitoringTypes 监控类型
     * @return 结果
     */
    public int updateMonitoringTypes(MonitoringTypes monitoringTypes);

    /**
     * 批量删除监控类型
     * 
     * @param monitoringTypeIds 需要删除的监控类型主键集合
     * @return 结果
     */
    public int deleteMonitoringTypesByMonitoringTypeIds(Long[] monitoringTypeIds);

    /**
     * 删除监控类型信息
     * 
     * @param monitoringTypeId 监控类型主键
     * @return 结果
     */
    public int deleteMonitoringTypesByMonitoringTypeId(Long monitoringTypeId);

    /**
     * 查询所有监视类型
     *
     * @return {@link List}<{@link MonitoringTypes}>
     */
    List<MonitoringTypes> selectAllMonitoringTypes();

    /**
     * 获取所有监控类型名称
     *
     * @return {@link List}<{@link String}>
     */
    List<String> getAllMonitoringTypeName();
}
