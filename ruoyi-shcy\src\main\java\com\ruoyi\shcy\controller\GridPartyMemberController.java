package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.GridPartyMember;
import com.ruoyi.shcy.service.IGridPartyMemberService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网格党支部成员Controller
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@RestController
@RequestMapping("/shcy/gridPartyMember")
public class GridPartyMemberController extends BaseController
{
    @Autowired
    private IGridPartyMemberService gridPartyMemberService;

    /**
     * 查询网格党支部成员列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridPartyMember:list')")
    @GetMapping("/list")
    public TableDataInfo list(GridPartyMember gridPartyMember)
    {
        startPage();
        List<GridPartyMember> list = gridPartyMemberService.selectGridPartyMemberList(gridPartyMember);
        return getDataTable(list);
    }

    /**
     * 导出网格党支部成员列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridPartyMember:export')")
    @Log(title = "网格党支部成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GridPartyMember gridPartyMember)
    {
        List<GridPartyMember> list = gridPartyMemberService.selectGridPartyMemberList(gridPartyMember);
        ExcelUtil<GridPartyMember> util = new ExcelUtil<GridPartyMember>(GridPartyMember.class);
        util.exportExcel(response, list, "网格党支部成员数据");
    }

    /**
     * 获取网格党支部成员详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridPartyMember:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(gridPartyMemberService.selectGridPartyMemberById(id));
    }

    /**
     * 新增网格党支部成员
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridPartyMember:add')")
    @Log(title = "网格党支部成员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GridPartyMember gridPartyMember)
    {
        return toAjax(gridPartyMemberService.insertGridPartyMember(gridPartyMember));
    }

    /**
     * 修改网格党支部成员
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridPartyMember:edit')")
    @Log(title = "网格党支部成员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GridPartyMember gridPartyMember)
    {
        return toAjax(gridPartyMemberService.updateGridPartyMember(gridPartyMember));
    }

    /**
     * 删除网格党支部成员
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridPartyMember:remove')")
    @Log(title = "网格党支部成员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(gridPartyMemberService.deleteGridPartyMemberByIds(ids));
    }
}
