package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.PersonnelGrid;

/**
 * 两类人员网格化Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-18
 */
public interface PersonnelGridMapper 
{
    /**
     * 查询两类人员网格化
     * 
     * @param id 两类人员网格化主键
     * @return 两类人员网格化
     */
    public PersonnelGrid selectPersonnelGridById(Long id);

    /**
     * 查询两类人员网格化列表
     * 
     * @param personnelGrid 两类人员网格化
     * @return 两类人员网格化集合
     */
    public List<PersonnelGrid> selectPersonnelGridList(PersonnelGrid personnelGrid);

    /**
     * 新增两类人员网格化
     * 
     * @param personnelGrid 两类人员网格化
     * @return 结果
     */
    public int insertPersonnelGrid(PersonnelGrid personnelGrid);

    /**
     * 修改两类人员网格化
     * 
     * @param personnelGrid 两类人员网格化
     * @return 结果
     */
    public int updatePersonnelGrid(PersonnelGrid personnelGrid);

    /**
     * 删除两类人员网格化
     * 
     * @param id 两类人员网格化主键
     * @return 结果
     */
    public int deletePersonnelGridById(Long id);

    /**
     * 批量删除两类人员网格化
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePersonnelGridByIds(Long[] ids);
}
