package com.ruoyi.shcy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.domain.JsDbcenter;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.domain.ShcyWgh;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.mapper.ShcyWghMapper;
import com.ruoyi.shcy.mapper.TaskDbcenterMapper;
import com.ruoyi.shcy.service.IJsDbcenterService;
import com.ruoyi.shcy.service.IShcyWghService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网格化案件信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-10
 */
@Service
public class ShcyWghServiceImpl implements IShcyWghService 
{
    @Autowired
    private ShcyWghMapper shcyWghMapper;

    @Autowired
    private TaskDbcenterMapper taskDbcenterMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    @Autowired
    private IJsDbcenterService jsDbcenterService;

    /**
     * 查询网格化案件信息
     * 
     * @param id 网格化案件信息主键
     * @return 网格化案件信息
     */
    @Override
    public ShcyWgh selectShcyWghById(Long id)
    {
        return handlePhoto(shcyWghMapper.selectShcyWghById(id));
    }

    private ShcyWgh handlePhoto(ShcyWgh shcyWgh) {
        if (StrUtil.isNotEmpty(shcyWgh.getDealPhoto())) {
            List<String> photoUrls = new ArrayList<>();
            String[] fileIds = shcyWgh.getDealPhoto().split(",");
            for (String fileId : fileIds) {
                ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
                photoUrls.add(shcyFileInfo.getFilePath());
            }
            shcyWgh.setDealPhotos(photoUrls);
        }

        return shcyWgh;
    }

    private List<ShcyWgh> handlePhotos(List<ShcyWgh> shcyWgh) {
        for(ShcyWgh theShcyWgh:shcyWgh)
        {
            if (StrUtil.isNotEmpty(theShcyWgh.getDealPhoto()) ) {
                List<String> photoUrls = new ArrayList<>();
                String[] fileIds = theShcyWgh.getDealPhoto().split(",");
                for (String fileId : fileIds) {
                    if(StringUtils.isNotEmpty(fileId))
                    {
                        ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
                        if(shcyFileInfo != null)
                        {
                            photoUrls.add(shcyFileInfo.getFilePath());
                        }
                    }

                }
                theShcyWgh.setDealPhotos(photoUrls);
            }
        }

        return shcyWgh;
    }


    /**
     * 查询网格化案件信息列表
     * 
     * @param shcyWgh 网格化案件信息
     * @return 网格化案件信息
     */
    @Override
    public List<ShcyWgh> selectShcyWghList(ShcyWgh shcyWgh)
    {
        return handlePhotos(shcyWghMapper.selectShcyWghList(shcyWgh));
    }

    /**
     * 新增网格化案件信息
     * 
     * @param shcyWgh 网格化案件信息
     * @return 结果
     */
    @Override
    public int insertShcyWgh(ShcyWgh shcyWgh)
    {
        shcyWgh.setCreateTime(DateUtils.getNowDate());
        return shcyWghMapper.insertShcyWgh(shcyWgh);
    }

    /**
     * 修改网格化案件信息
     * 
     * @param shcyWgh 网格化案件信息
     * @return 结果
     */
    @Override
    public int updateShcyWgh(ShcyWgh shcyWgh)
    {
        shcyWgh.setUpdateTime(DateUtils.getNowDate());
        return shcyWghMapper.updateShcyWgh(shcyWgh);
    }

    /**
     * 批量删除网格化案件信息
     * 
     * @param ids 需要删除的网格化案件信息主键
     * @return 结果
     */
    @Override
    public int deleteShcyWghByIds(Long[] ids)
    {
        return shcyWghMapper.deleteShcyWghByIds(ids);
    }

    /**
     * 删除网格化案件信息信息
     * 
     * @param id 网格化案件信息主键
     * @return 结果
     */
    @Override
    public int deleteShcyWghById(Long id)
    {
        return shcyWghMapper.deleteShcyWghById(id);
    }

    @Override
    public long getCaseCount(ShcyWgh shcyWgh) {
        return shcyWghMapper.getCaseCount(shcyWgh);
    }

    @Override
    public List<ShcyWgh> selectHandleList(ShcyWgh shcyWgh) {
        return shcyWghMapper.selectHandleList(shcyWgh);
    }

    @Override
    public List<ShcyWgh> selectHistoryList(ShcyWgh shcyWgh) {
        return shcyWghMapper.selectHistoryList(shcyWgh);
    }

    @Override
    public int handleShcyWgh(ShcyWgh shcyWgh) {
//        TaskDbcenter taskDbcenter = taskDbcenterMapper.selectTaskDbcenterById(shcyWgh.getWghId());
//        iccAlarmRecord.setStatus(HjzzConstants.PROCESSED);
//        iccAlarmRecordMapper.updateIccAlarmRecord(iccAlarmRecord);


        // 现场处置人
        shcyWgh.setDealBy(SecurityUtils.getLoginUser().getUsername());
        // 现场处置时间
        shcyWgh.setCaseFinishTime(DateUtils.getNowDate());

        return shcyWghMapper.updateShcyWgh(shcyWgh);
    }

    @Override
    public List<ShcyWgh> selectShcyWghListByTaskDbcenterIds(List<Long> taskDbcenterIds) {
        return shcyWghMapper.selectShcyWghListByTaskDbcenterIds(taskDbcenterIds);
    }

    @Override
    public ShcyWgh selectShcyWghByTaskDbcenterId(String taskId) {
        return shcyWghMapper.selectShcyWghByTaskDbcenterId(taskId);
    }

    @Override
    public List<ShcyWgh> selectShcyWghListForReport(ShcyWgh shcyWgh) {
        return shcyWghMapper.selectShcyWghList(shcyWgh);
    }

    @Override
    public int syncYsbData() {
        ShcyWgh shcyWgh = new ShcyWgh();
        shcyWgh.setCirculationState("4");
        List<ShcyWgh> shcyWghs = shcyWghMapper.selectShcyWghList(shcyWgh);
        // 判断shcyWghs是否为空
        if (CollUtil.isEmpty(shcyWghs)) {
            return 1;
        }
        // 将shcyWghs中的taskid转为数组
        String[] taskids = shcyWghs.stream().map(ShcyWgh::getTaskId).toArray(String[]::new);
        List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenterSyncListByTaskid(taskids);
        // 取出jsDbcenters中statusname为"已结案"的taskid
        List<String> ids = jsDbcenters.stream().filter(jsDbcenter -> "已结案".equals(jsDbcenter.getStatusname())).map(JsDbcenter::getTaskid).collect(Collectors.toList());
        // 判断ids是否为空
        if (CollUtil.isEmpty(ids)) {
            return 1;
        }
        return shcyWghMapper.updateShcyWghByTaskIds(ids);
    }
}
