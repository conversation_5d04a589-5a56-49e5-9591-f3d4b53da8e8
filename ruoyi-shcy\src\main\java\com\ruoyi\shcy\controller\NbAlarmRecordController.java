package com.ruoyi.shcy.controller;


import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.nbiotyun.domain.AlarmRecordDTO;
import com.ruoyi.nbiotyun.domain.NbAlarmRecordVO;
import com.ruoyi.nbiotyun.service.NbiotyunService;
import com.ruoyi.shcy.domain.NbiotyunAlarmRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/shcy/nbAlarmRecord")
public class NbAlarmRecordController extends BaseController {

    @Autowired
    private NbiotyunService nbiotyunService;

    /**
     * 查询报警记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:nbAlarmRecord:list')")
    @GetMapping("/list")
    public AjaxResult list(NbiotyunAlarmRecord nbiotyunAlarmRecord)
    {
        String startTime = nbiotyunAlarmRecord.getParams().get("beginTime").toString();
        String endTime = nbiotyunAlarmRecord.getParams().get("endTime").toString();
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        List<AlarmRecordDTO> filteredResult = result.stream()
                .filter(alarmRecordDTO -> alarmRecordDTO.getDeviceAttrValue().contains("液位超限"))
                .collect(Collectors.toList());

        // 按deviceImei分组，转换为NbAlarmRecordVO对象
        List<NbAlarmRecordVO> voList = filteredResult.stream()
                .collect(Collectors.groupingBy(AlarmRecordDTO::getDeviceImei))
                .entrySet().stream()
                .map(entry -> {
                    String deviceImei = entry.getKey();
                    List<AlarmRecordDTO> groupedList = entry.getValue();
                    
                    NbAlarmRecordVO vo = new NbAlarmRecordVO();
                    vo.setDeviceImei(deviceImei);
                    vo.setTodayAlarmNum(groupedList.size());
                    
                    // 获取最新的报警时间
                    String latestAlarmTime = groupedList.stream()
                            .max(Comparator.comparing(AlarmRecordDTO::getAlarmTime))
                            .map(AlarmRecordDTO::getAlarmTime)
                            .orElse("");
                    vo.setAlarmTime(latestAlarmTime);
                    
                    // 获取安装点位和报警原因（取第一个记录的值）
                    if (!groupedList.isEmpty()) {
                        AlarmRecordDTO firstRecord = groupedList.get(0);
                        vo.setDetailedLocation(firstRecord.getDetailedLocation());
                        vo.setDeviceEventName(firstRecord.getDeviceEventName());
                    }
                    
                    return vo;
                })
                .collect(Collectors.toList());
        
        return AjaxResult.success(voList);
    }
}
