package com.ruoyi.nbiotyun;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.nbiotyun.domain.AlarmRecordDTO;
import com.ruoyi.nbiotyun.domain.DeviceDTO;
import com.ruoyi.nbiotyun.service.NbiotyunService;
import com.ruoyi.shcy.mapper.NbiotyunDeviceMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * nbiotyun服务测试
 *
 * <AUTHOR>
 * @date 2023/08/31
 */
@SpringBootTest
public class NbiotyunServiceTest {

    @Autowired
    private NbiotyunService nbiotyunService;

    @Autowired
    private NbiotyunDeviceMapper nbiotyunDeviceMapper;

    @Test
    public void testSelectResponsiblePerson(){
        System.out.println(nbiotyunDeviceMapper.selectResponsiblePerson("863882046591274"));
    }

    @Test
    public void test(){
        List<DeviceDTO> deviceDTOList = nbiotyunService.getList(1, 100, 1);
        // for(DeviceDTO deviceDTO: deviceDTOList){
        //     nbiotyunService.syncNbiotyunDevice(deviceDTO);
        // }

        System.out.println(deviceDTOList);
    }

    @Test
    public void testAlarmRecord(){
        // List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(1, "2024-06-26 00:00:00", "2024-06-26 23:59:59");
        // System.out.println(result);
        // // nbiotyunService.syncNbiotyunAlarmRecord(result);


        String startTime = "2025-06-26" + " 15:00:00";
        String endTime = "2025-06-26" + " 18:00:00";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        String date = "2025-06-26";
        String date1 = "15:00-18:00";
        nbiotyunService.syncData(result, date, date1);
    }

}
