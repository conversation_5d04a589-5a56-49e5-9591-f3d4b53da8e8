package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ApartmentRental;
import com.ruoyi.shcy.service.IApartmentRentalService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 公寓租赁房Controller
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/apartmentRental")
public class ApartmentRentalController extends BaseController
{
    @Autowired
    private IApartmentRentalService apartmentRentalService;

    /**
     * 查询公寓租赁房列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:apartmentRental:list')")
    @GetMapping("/list")
    public TableDataInfo list(ApartmentRental apartmentRental)
    {
        startPage();
        List<ApartmentRental> list = apartmentRentalService.selectApartmentRentalList(apartmentRental);
        return getDataTable(list);
    }

    /**
     * 导出公寓租赁房列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:apartmentRental:export')")
    @Log(title = "公寓租赁房", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ApartmentRental apartmentRental)
    {
        List<ApartmentRental> list = apartmentRentalService.selectApartmentRentalList(apartmentRental);
        ExcelUtil<ApartmentRental> util = new ExcelUtil<ApartmentRental>(ApartmentRental.class);
        util.exportExcel(response, list, "公寓租赁房数据");
    }

    /**
     * 获取公寓租赁房详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:apartmentRental:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(apartmentRentalService.selectApartmentRentalById(id));
    }

    /**
     * 新增公寓租赁房
     */
    @PreAuthorize("@ss.hasPermi('shcy:apartmentRental:add')")
    @Log(title = "公寓租赁房", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ApartmentRental apartmentRental)
    {
        return toAjax(apartmentRentalService.insertApartmentRental(apartmentRental));
    }

    /**
     * 修改公寓租赁房
     */
    @PreAuthorize("@ss.hasPermi('shcy:apartmentRental:edit')")
    @Log(title = "公寓租赁房", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ApartmentRental apartmentRental)
    {
        return toAjax(apartmentRentalService.updateApartmentRental(apartmentRental));
    }

    /**
     * 删除公寓租赁房
     */
    @PreAuthorize("@ss.hasPermi('shcy:apartmentRental:remove')")
    @Log(title = "公寓租赁房", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(apartmentRentalService.deleteApartmentRentalByIds(ids));
    }
}
