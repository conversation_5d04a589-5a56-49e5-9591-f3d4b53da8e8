package com.ruoyi.bigdata;

import com.ruoyi.shcy.domain.JsDbcenter;
import com.ruoyi.shcy.service.IJsDbcenterService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class JsDbcenterTest {

    @Autowired
    private IJsDbcenterService jsDbcenterService;

    @Test
    public void contextLoads() {
        // 这里没有任何代码，只是确保应用程序上下文能够成功加载
    }

    @Test
    public void testSelectJsDbcenterList() {
        // JsDbcenter jsDbcenter = new JsDbcenter();
        // jsDbcenter.setTaskid("2404G1037573");
        // List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenterList(jsDbcenter);
        // for (JsDbcenter jsDbcenter1 : jsDbcenters) {
        //     System.out.println(jsDbcenter1);
        // }

        // String startTime = DateUtil.today() + " 00:00:00";
        // String endTime = DateUtil.tomorrow() + " 00:00:00";

        // String startTime = "2023-11-01 00:00:00";
        // String endTime = "2024-04-04 00:00:00";
        // List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenter12345List(startTime, endTime);
        // System.out.println(jsDbcenters.size());

        String startTime1 = "2023-10-01 00:00:00";
        String endTime1 = "2024-04-04 00:00:00";
        List<JsDbcenter> jsDbcenters1 = jsDbcenterService.selectJsDbcenterCaseList(startTime1, endTime1);
        System.out.println(jsDbcenters1.size());


    }
}
