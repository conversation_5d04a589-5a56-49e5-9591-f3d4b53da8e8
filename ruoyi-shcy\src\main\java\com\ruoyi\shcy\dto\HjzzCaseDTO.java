package com.ruoyi.shcy.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class HjzzCaseDTO {

    @Excel(name = "事件编号")
    private String caseNumber;

    @Excel(name = "车牌信息")
    private String licensePlate;

    @Excel(name = "事件类型", readConverterExp = "1=偷倒垃圾,2=违规生产入侵")
    private String caseType;

    @Excel(name = "事件地点")
    private String address;

    @Excel(name = "现场处置人")
    private String caseDealBy;

    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm", type = Excel.Type.EXPORT)
    private Date createTime;

    @Excel(name = "现场处置截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm", type = Excel.Type.EXPORT)
    private Date caseEndTime;

    @Excel(name = "现场处置时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm", type = Excel.Type.EXPORT)
    private Date caseFinishTime;

    @Excel(name = "查处截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm", type = Excel.Type.EXPORT)
    private Date investigationDeadline;

    @Excel(name = "查处完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm", type = Excel.Type.EXPORT)
    private Date investigationCompleteTime;

    @Excel(name = "状态", readConverterExp = "0=已完成,1=待处理,2=处理中,3=作废,4=退回")
    private String circulationState;

}
