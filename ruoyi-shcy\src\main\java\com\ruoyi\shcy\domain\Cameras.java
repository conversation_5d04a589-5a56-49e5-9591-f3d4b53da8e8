package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 监控资源对象 shcy_cameras
 *
 * <AUTHOR>
 * @date 2023-03-28
 */
public class Cameras extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 监控点编号 */
    @Excel(name = "监控点编号")
    private String cameraIndexCode;

    /** 监控点国标编号 */
    @Excel(name = "监控点国标编号")
    private String gbIndexCode;

    /** 监控点名称 */
    @Excel(name = "监控点名称")
    private String name;

    /** 所属设备编号 */
    private String deviceIndexCode;

    /** 经度 */
    private String longitude;

    /** 纬度 */
    private String latitude;

    /** 海拔高度 */
    private String altitude;

    /** 摄像机像素 */
    private Integer pixel;

    /** 监控点类型 */
    private Integer cameraType;

    /** 监控类型 */
    private String cameraTypeName;

    /** 安装位置 */
    private String installPlace;

    /** 矩阵编号 */
    private String matrixCode;

    /** 通道号 */
    private Integer chanNum;

    /** 可视域相关 */
    private String viewshed;

    /** 能力集 */
    private String capabilitySet;

    /** 能力集说明 */
    private String capabilitySetName;

    /** 智能分析能力集 */
    private String intelligentSet;

    /** 智能分析能力集说明 */
    private String intelligentSetName;

    /** 录像存储位置 */
    private String recordLocation;

    /** 录像存储位置说明 */
    private String recordLocationName;

    /** 所属设备类型 */
    private String deviceResourceType;

    /** 所属设备类型说明 */
    private String deviceResourceTypeName;

    /** 通道子类型 */
    private String channelType;

    /** 通道子类型说明
 */
    private String channelTypeName;

    /** 传输协议 */
    private Integer transType;

    /** 传输协议类型说明 */
    private String transTypeName;

    /** 所属区域编号 */
    private String unitIndexCode;

    /** 接入协议 */
    private String treatyType;

    /** 接入协议类型说明 */
    private String treatyTypeName;

    /** 在线状态 */
    private Integer status;

    /** 状态说明 */
    private String statusName;

    /** 类型 */
    private String type;

    /** 坐标 */
    private String coordinate;

    /** 设备编码 */
    @Excel(name = "设备编码")
    private String equipmentCode;

    /** 设备名称1 */
    @Excel(name = "设备名称1")
    private String cameraName1;

    /** 设备名称2 */
    @Excel(name = "设备名称2")
    private String cameraName2;

    /** 设备名称3 */
    @Excel(name = "设备名称3")
    private String cameraName3;

    /** 设备厂商 */
    @Excel(name = "设备厂商")
    private String cameraManufacturer;

    /** 设备型号 */
    @Excel(name = "设备型号")
    private String cameraModel;

    /** 简要位置信息 */
    @Excel(name = "简要位置信息")
    private String positionName;

    /** IPV4地址 */
    @Excel(name = "IPV4地址")
    private String ipAddress;

    /** MAC地址 */
    @Excel(name = "MAC地址")
    private String macAddress;

    /** 摄像机功能类型 */
    @Excel(name = "摄像机功能类型")
    private String cameraUseType;

    /** 补光类型 */
    @Excel(name = "补光类型")
    private String fillLightAttribute;

    /** 摄像机编码格式 */
    @Excel(name = "摄像机编码格式")
    private String cameraEncodingFormat;

    /** 对应存储设备IP */
    @Excel(name = "对应存储设备IP")
    private String storageEquipmentIp;

    /** 对应存储设备通道 */
    @Excel(name = "对应存储设备通道")
    private String storageEquipmentChannel;

    /** 摄像机位置类型 */
    @Excel(name = "摄像机位置类型")
    private String cameraPositionType;

    /** 监控方向 */
    @Excel(name = "监控方向")
    private String monitorDirection;

    /** 联网属性 */
    @Excel(name = "联网属性")
    private String networkProperty;

    /** 安装时间 */
    @Excel(name = "安装时间")
    private String fixTime;

    /** 管理单位 */
    @Excel(name = "管理单位")
    private String managementUnit;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String managementPhone;

    /** 设备状态 */
    @Excel(name = "设备状态")
    private String equipmentState;

    /** 录像/图片保存天数 */
    @Excel(name = "录像/图片保存天数")
    private String vedeoStorageDays;

    /** 视频/图片分辨率 */
    @Excel(name = "视频/图片分辨率")
    private String vedeoResolution;

    /** 视频信号类型 */
    @Excel(name = "视频信号类型")
    private String vedeoSignalType;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCameraIndexCode(String cameraIndexCode)
    {
        this.cameraIndexCode = cameraIndexCode;
    }

    public String getCameraIndexCode()
    {
        return cameraIndexCode;
    }
    public void setGbIndexCode(String gbIndexCode)
    {
        this.gbIndexCode = gbIndexCode;
    }

    public String getGbIndexCode()
    {
        return gbIndexCode;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setDeviceIndexCode(String deviceIndexCode)
    {
        this.deviceIndexCode = deviceIndexCode;
    }

    public String getDeviceIndexCode()
    {
        return deviceIndexCode;
    }
    public void setLongitude(String longitude)
    {
        this.longitude = longitude;
    }

    public String getLongitude()
    {
        return longitude;
    }
    public void setLatitude(String latitude)
    {
        this.latitude = latitude;
    }

    public String getLatitude()
    {
        return latitude;
    }
    public void setAltitude(String altitude)
    {
        this.altitude = altitude;
    }

    public String getAltitude()
    {
        return altitude;
    }
    public void setPixel(Integer pixel)
    {
        this.pixel = pixel;
    }

    public Integer getPixel()
    {
        return pixel;
    }
    public void setCameraType(Integer cameraType)
    {
        this.cameraType = cameraType;
    }

    public Integer getCameraType()
    {
        return cameraType;
    }
    public void setCameraTypeName(String cameraTypeName)
    {
        this.cameraTypeName = cameraTypeName;
    }

    public String getCameraTypeName()
    {
        return cameraTypeName;
    }
    public void setInstallPlace(String installPlace)
    {
        this.installPlace = installPlace;
    }

    public String getInstallPlace()
    {
        return installPlace;
    }
    public void setMatrixCode(String matrixCode)
    {
        this.matrixCode = matrixCode;
    }

    public String getMatrixCode()
    {
        return matrixCode;
    }
    public void setChanNum(Integer chanNum)
    {
        this.chanNum = chanNum;
    }

    public Integer getChanNum()
    {
        return chanNum;
    }
    public void setViewshed(String viewshed)
    {
        this.viewshed = viewshed;
    }

    public String getViewshed()
    {
        return viewshed;
    }
    public void setCapabilitySet(String capabilitySet)
    {
        this.capabilitySet = capabilitySet;
    }

    public String getCapabilitySet()
    {
        return capabilitySet;
    }
    public void setCapabilitySetName(String capabilitySetName)
    {
        this.capabilitySetName = capabilitySetName;
    }

    public String getCapabilitySetName()
    {
        return capabilitySetName;
    }
    public void setIntelligentSet(String intelligentSet)
    {
        this.intelligentSet = intelligentSet;
    }

    public String getIntelligentSet()
    {
        return intelligentSet;
    }
    public void setIntelligentSetName(String intelligentSetName)
    {
        this.intelligentSetName = intelligentSetName;
    }

    public String getIntelligentSetName()
    {
        return intelligentSetName;
    }
    public void setRecordLocation(String recordLocation)
    {
        this.recordLocation = recordLocation;
    }

    public String getRecordLocation()
    {
        return recordLocation;
    }
    public void setRecordLocationName(String recordLocationName)
    {
        this.recordLocationName = recordLocationName;
    }

    public String getRecordLocationName()
    {
        return recordLocationName;
    }
    public void setDeviceResourceType(String deviceResourceType)
    {
        this.deviceResourceType = deviceResourceType;
    }

    public String getDeviceResourceType()
    {
        return deviceResourceType;
    }
    public void setDeviceResourceTypeName(String deviceResourceTypeName)
    {
        this.deviceResourceTypeName = deviceResourceTypeName;
    }

    public String getDeviceResourceTypeName()
    {
        return deviceResourceTypeName;
    }
    public void setChannelType(String channelType)
    {
        this.channelType = channelType;
    }

    public String getChannelType()
    {
        return channelType;
    }
    public void setChannelTypeName(String channelTypeName)
    {
        this.channelTypeName = channelTypeName;
    }

    public String getChannelTypeName()
    {
        return channelTypeName;
    }
    public void setTransType(Integer transType)
    {
        this.transType = transType;
    }

    public Integer getTransType()
    {
        return transType;
    }
    public void setTransTypeName(String transTypeName)
    {
        this.transTypeName = transTypeName;
    }

    public String getTransTypeName()
    {
        return transTypeName;
    }
    public void setUnitIndexCode(String unitIndexCode)
    {
        this.unitIndexCode = unitIndexCode;
    }

    public String getUnitIndexCode()
    {
        return unitIndexCode;
    }
    public void setTreatyType(String treatyType)
    {
        this.treatyType = treatyType;
    }

    public String getTreatyType()
    {
        return treatyType;
    }
    public void setTreatyTypeName(String treatyTypeName)
    {
        this.treatyTypeName = treatyTypeName;
    }

    public String getTreatyTypeName()
    {
        return treatyTypeName;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setStatusName(String statusName)
    {
        this.statusName = statusName;
    }

    public String getStatusName()
    {
        return statusName;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setCoordinate(String coordinate)
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate()
    {
        return coordinate;
    }


    public String getEquipmentCode() {
        return equipmentCode;
    }

    public void setEquipmentCode(String equipmentCode) {
        this.equipmentCode = equipmentCode;
    }

    public String getCameraName1() {
        return cameraName1;
    }

    public void setCameraName1(String cameraName1) {
        this.cameraName1 = cameraName1;
    }

    public String getCameraName2() {
        return cameraName2;
    }

    public void setCameraName2(String cameraName2) {
        this.cameraName2 = cameraName2;
    }

    public String getCameraName3() {
        return cameraName3;
    }

    public void setCameraName3(String cameraName3) {
        this.cameraName3 = cameraName3;
    }

    public String getCameraManufacturer() {
        return cameraManufacturer;
    }

    public void setCameraManufacturer(String cameraManufacturer) {
        this.cameraManufacturer = cameraManufacturer;
    }

    public String getCameraModel() {
        return cameraModel;
    }

    public void setCameraModel(String cameraModel) {
        this.cameraModel = cameraModel;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getCameraUseType() {
        return cameraUseType;
    }

    public void setCameraUseType(String cameraUseType) {
        this.cameraUseType = cameraUseType;
    }

    public String getFillLightAttribute() {
        return fillLightAttribute;
    }

    public void setFillLightAttribute(String fillLightAttribute) {
        this.fillLightAttribute = fillLightAttribute;
    }

    public String getCameraEncodingFormat() {
        return cameraEncodingFormat;
    }

    public void setCameraEncodingFormat(String cameraEncodingFormat) {
        this.cameraEncodingFormat = cameraEncodingFormat;
    }

    public String getStorageEquipmentIp() {
        return storageEquipmentIp;
    }

    public void setStorageEquipmentIp(String storageEquipmentIp) {
        this.storageEquipmentIp = storageEquipmentIp;
    }

    public String getStorageEquipmentChannel() {
        return storageEquipmentChannel;
    }

    public void setStorageEquipmentChannel(String storageEquipmentChannel) {
        this.storageEquipmentChannel = storageEquipmentChannel;
    }

    public String getCameraPositionType() {
        return cameraPositionType;
    }

    public void setCameraPositionType(String cameraPositionType) {
        this.cameraPositionType = cameraPositionType;
    }

    public String getMonitorDirection() {
        return monitorDirection;
    }

    public void setMonitorDirection(String monitorDirection) {
        this.monitorDirection = monitorDirection;
    }

    public String getNetworkProperty() {
        return networkProperty;
    }

    public void setNetworkProperty(String networkProperty) {
        this.networkProperty = networkProperty;
    }

    public String getFixTime() {
        return fixTime;
    }

    public void setFixTime(String fixTime) {
        this.fixTime = fixTime;
    }

    public String getManagementUnit() {
        return managementUnit;
    }

    public void setManagementUnit(String managementUnit) {
        this.managementUnit = managementUnit;
    }

    public String getManagementPhone() {
        return managementPhone;
    }

    public void setManagementPhone(String managementPhone) {
        this.managementPhone = managementPhone;
    }

    public String getEquipmentState() {
        return equipmentState;
    }

    public void setEquipmentState(String equipmentState) {
        this.equipmentState = equipmentState;
    }

    public String getVedeoStorageDays() {
        return vedeoStorageDays;
    }

    public void setVedeoStorageDays(String vedeoStorageDays) {
        this.vedeoStorageDays = vedeoStorageDays;
    }

    public String getVedeoResolution() {
        return vedeoResolution;
    }

    public void setVedeoResolution(String vedeoResolution) {
        this.vedeoResolution = vedeoResolution;
    }

    public String getVedeoSignalType() {
        return vedeoSignalType;
    }

    public void setVedeoSignalType(String vedeoSignalType) {
        this.vedeoSignalType = vedeoSignalType;
    }

    @Override
    public String toString() {
        return "Cameras{" +
                "id=" + id +
                ", cameraIndexCode='" + cameraIndexCode + '\'' +
                ", gbIndexCode='" + gbIndexCode + '\'' +
                ", name='" + name + '\'' +
                ", deviceIndexCode='" + deviceIndexCode + '\'' +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", altitude='" + altitude + '\'' +
                ", pixel=" + pixel +
                ", cameraType=" + cameraType +
                ", cameraTypeName='" + cameraTypeName + '\'' +
                ", installPlace='" + installPlace + '\'' +
                ", matrixCode='" + matrixCode + '\'' +
                ", chanNum=" + chanNum +
                ", viewshed='" + viewshed + '\'' +
                ", capabilitySet='" + capabilitySet + '\'' +
                ", capabilitySetName='" + capabilitySetName + '\'' +
                ", intelligentSet='" + intelligentSet + '\'' +
                ", intelligentSetName='" + intelligentSetName + '\'' +
                ", recordLocation='" + recordLocation + '\'' +
                ", recordLocationName='" + recordLocationName + '\'' +
                ", deviceResourceType='" + deviceResourceType + '\'' +
                ", deviceResourceTypeName='" + deviceResourceTypeName + '\'' +
                ", channelType='" + channelType + '\'' +
                ", channelTypeName='" + channelTypeName + '\'' +
                ", transType=" + transType +
                ", transTypeName='" + transTypeName + '\'' +
                ", unitIndexCode='" + unitIndexCode + '\'' +
                ", treatyType='" + treatyType + '\'' +
                ", treatyTypeName='" + treatyTypeName + '\'' +
                ", status=" + status +
                ", statusName='" + statusName + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", equipmentCode='" + equipmentCode + '\'' +
                ", cameraName1='" + cameraName1 + '\'' +
                ", cameraName2='" + cameraName2 + '\'' +
                ", cameraName3='" + cameraName3 + '\'' +
                ", cameraManufacturer='" + cameraManufacturer + '\'' +
                ", cameraModel='" + cameraModel + '\'' +
                ", positionName='" + positionName + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", macAddress='" + macAddress + '\'' +
                ", cameraUseType='" + cameraUseType + '\'' +
                ", fillLightAttribute='" + fillLightAttribute + '\'' +
                ", cameraEncodingFormat='" + cameraEncodingFormat + '\'' +
                ", storageEquipmentIp='" + storageEquipmentIp + '\'' +
                ", storageEquipmentChannel='" + storageEquipmentChannel + '\'' +
                ", cameraPositionType='" + cameraPositionType + '\'' +
                ", monitorDirection='" + monitorDirection + '\'' +
                ", networkProperty='" + networkProperty + '\'' +
                ", fixTime='" + fixTime + '\'' +
                ", managementUnit='" + managementUnit + '\'' +
                ", managementPhone='" + managementPhone + '\'' +
                ", equipmentState='" + equipmentState + '\'' +
                ", vedeoStorageDays='" + vedeoStorageDays + '\'' +
                ", vedeoResolution='" + vedeoResolution + '\'' +
                ", vedeoSignalType='" + vedeoSignalType + '\'' +
                '}';
    }
}
