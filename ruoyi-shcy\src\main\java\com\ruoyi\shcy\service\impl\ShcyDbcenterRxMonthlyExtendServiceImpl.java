package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyDbcenterRxMonthlyExtendMapper;
import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthlyExtend;
import com.ruoyi.shcy.service.IShcyDbcenterRxMonthlyExtendService;

/**
 * 12345热线分析拓展Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@Service
public class ShcyDbcenterRxMonthlyExtendServiceImpl implements IShcyDbcenterRxMonthlyExtendService 
{
    @Autowired
    private ShcyDbcenterRxMonthlyExtendMapper shcyDbcenterRxMonthlyExtendMapper;

    /**
     * 查询12345热线分析拓展
     * 
     * @param id 12345热线分析拓展主键
     * @return 12345热线分析拓展
     */
    @Override
    public ShcyDbcenterRxMonthlyExtend selectShcyDbcenterRxMonthlyExtendById(Long id)
    {
        return shcyDbcenterRxMonthlyExtendMapper.selectShcyDbcenterRxMonthlyExtendById(id);
    }

    /**
     * 查询12345热线分析拓展列表
     * 
     * @param shcyDbcenterRxMonthlyExtend 12345热线分析拓展
     * @return 12345热线分析拓展
     */
    @Override
    public List<ShcyDbcenterRxMonthlyExtend> selectShcyDbcenterRxMonthlyExtendList(ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend)
    {
        return shcyDbcenterRxMonthlyExtendMapper.selectShcyDbcenterRxMonthlyExtendList(shcyDbcenterRxMonthlyExtend);
    }

    /**
     * 新增12345热线分析拓展
     * 
     * @param shcyDbcenterRxMonthlyExtend 12345热线分析拓展
     * @return 结果
     */
    @Override
    public int insertShcyDbcenterRxMonthlyExtend(ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend)
    {
        shcyDbcenterRxMonthlyExtend.setCreateTime(DateUtils.getNowDate());
        return shcyDbcenterRxMonthlyExtendMapper.insertShcyDbcenterRxMonthlyExtend(shcyDbcenterRxMonthlyExtend);
    }

    /**
     * 修改12345热线分析拓展
     * 
     * @param shcyDbcenterRxMonthlyExtend 12345热线分析拓展
     * @return 结果
     */
    @Override
    public int updateShcyDbcenterRxMonthlyExtend(ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend)
    {
        shcyDbcenterRxMonthlyExtend.setUpdateTime(DateUtils.getNowDate());
        return shcyDbcenterRxMonthlyExtendMapper.updateShcyDbcenterRxMonthlyExtend(shcyDbcenterRxMonthlyExtend);
    }

    /**
     * 批量删除12345热线分析拓展
     * 
     * @param ids 需要删除的12345热线分析拓展主键
     * @return 结果
     */
    @Override
    public int deleteShcyDbcenterRxMonthlyExtendByIds(Long[] ids)
    {
        return shcyDbcenterRxMonthlyExtendMapper.deleteShcyDbcenterRxMonthlyExtendByIds(ids);
    }

    /**
     * 删除12345热线分析拓展信息
     * 
     * @param id 12345热线分析拓展主键
     * @return 结果
     */
    @Override
    public int deleteShcyDbcenterRxMonthlyExtendById(Long id)
    {
        return shcyDbcenterRxMonthlyExtendMapper.deleteShcyDbcenterRxMonthlyExtendById(id);
    }
}
