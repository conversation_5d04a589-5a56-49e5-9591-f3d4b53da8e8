package com.ruoyi.shcy.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.ibatis.type.Alias;

/**
 * 街长路名对象 shcy_road_street_chief
 *
 * <AUTHOR>
 * @date 2023-02-23
 */
@Alias("RoadStreetChief")
public class RoadStreetChief extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 街长 */
    @Excel(name = "街长")
    private String streetChief;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String chiefPhone;

    /** 路名 */
    @Excel(name = "路名")
    private String roadName;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 第二街长 */
    @Excel(name = "第二街长")
    private String streetSubChief;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String subChiefPhone;

    /** 类型2 **/
    @Excel(name="类型2")
    private String type2;

    /** 坐标2*/
    @Excel(name = "坐标2")
    private String coordinate2;

    /** 路段编号*/
    @Excel(name="路段编号")
    private String  roadId;

    /** 是否重点路段*/
    @Excel(name="是否重点路段")
    private String isKeynoteRoad;

    /** 第三街长 */
    @Excel(name = "第三街长")
    private String streetThirdChief;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String thirdChiefPhone;


    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setStreetChief(String streetChief)
    {
        this.streetChief = streetChief;
    }

    public String getStreetChief()
    {
        return streetChief;
    }
    public void setChiefPhone(String chiefPhone)
    {
        this.chiefPhone = chiefPhone;
    }

    public String getChiefPhone()
    {
        return chiefPhone;
    }
    public void setRoadName(String roadName)
    {
        this.roadName = roadName;
    }

    public String getRoadName()
    {
        return roadName;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setCoordinate(String coordinate)
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate()
    {
        return coordinate;
    }

    public String getStreetSubChief() {
        return streetSubChief;
    }

    public void setStreetSubChief(String streetSubChief) {
        this.streetSubChief = streetSubChief;
    }

    public String getSubChiefPhone() {
        return subChiefPhone;
    }

    public void setSubChiefPhone(String subChiefPhone) {
        this.subChiefPhone = subChiefPhone;
    }

    public String getType2() {
        return type2;
    }

    public void setType2(String type2) {
        this.type2 = type2;
    }

    public String getCoordinate2() {
        return coordinate2;
    }

    public void setCoordinate2(String coordinate2) {
        this.coordinate2 = coordinate2;
    }


    public String getRoadId() {
        return roadId;
    }

    public void setRoadId(String roadId) {
        this.roadId = roadId;
    }

    public String getIsKeynoteRoad() {
        return isKeynoteRoad;
    }

    public void setIsKeynoteRoad(String isKeynoteRoad) {
        this.isKeynoteRoad = isKeynoteRoad;
    }

    public String getStreetThirdChief() {
        return streetThirdChief;
    }

    public void setStreetThirdChief(String streetThirdChief) {
        this.streetThirdChief = streetThirdChief;
    }

    public String getThirdChiefPhone() {
        return thirdChiefPhone;
    }

    public void setThirdChiefPhone(String thirdChiefPhone) {
        this.thirdChiefPhone = thirdChiefPhone;
    }

    @Override
    public String toString() {
        return "RoadStreetChief{" +
                "id=" + id +
                ", streetChief='" + streetChief + '\'' +
                ", chiefPhone='" + chiefPhone + '\'' +
                ", roadName='" + roadName + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", streetSubChief='" + streetSubChief + '\'' +
                ", subChiefPhone='" + subChiefPhone + '\'' +
                ", streetThirdChief='" + streetThirdChief + '\'' +
                ", thirdChiefPhone='" + thirdChiefPhone + '\'' +
                ", type2='" + type2 + '\'' +
                ", coordinate2='" + coordinate2 + '\'' +
                ", roadId='" + roadId + '\'' +
                ", isKeynoteRoad='" + isKeynoteRoad + '\'' +
                '}';
    }
}
