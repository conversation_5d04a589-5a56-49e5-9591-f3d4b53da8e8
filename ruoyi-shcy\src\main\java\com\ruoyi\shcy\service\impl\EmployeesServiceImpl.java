package com.ruoyi.shcy.service.impl;

import java.util.List;

import cn.hutool.core.date.DateTime;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.SupervisorLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.EmployeesMapper;
import com.ruoyi.shcy.domain.Employees;
import com.ruoyi.shcy.service.IEmployeesService;

/**
 * 从业人员基本信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@Service
public class EmployeesServiceImpl implements IEmployeesService
{
    @Autowired
    private EmployeesMapper employeesMapper;

    /**
     * 查询从业人员基本信息
     *
     * @param id 从业人员基本信息主键
     * @return 从业人员基本信息
     */
    @Override
    public Employees selectEmployeesById(Long id)
    {
        return employeesMapper.selectEmployeesById(id);
    }

    /**
     * 查询从业人员基本信息列表
     *
     * @param employees 从业人员基本信息
     * @return 从业人员基本信息
     */
    @Override
    public List<Employees> selectEmployeesList(Employees employees)
    {
        return employeesMapper.selectEmployeesList(employees);
    }


//    /**
//     * 导入用户数据
//     *
//     * @param employeeList    用户数据列表
//     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
//     * @param operName        操作用户
//     * @return 结果
//     */
//    @Override
//    public String importEmployee(List<Employees> employeeList, Boolean isUpdateSupport, String operName)
//    {
//        if (StringUtils.isNull(employeeList) || employeeList.size() == 0)
//        {
//            throw new ServiceException("导入商铺数据不能为空！");
//        }
//        int successNum = 0;
//        int failureNum = 0;
//        StringBuilder successMsg = new StringBuilder();
//        StringBuilder failureMsg = new StringBuilder();
//        for (Employees employee : employeeList)
//        {
//            try
//            {
//                // 验证是否存在这个员工
//                Shop u = employeesMapper.selectShopByCreditCode(shop.getShopCreditCode());
//                if (StringUtils.isNull(u)){
//                    this.insertShop(shop);
//                    successNum++;
//                    successMsg.append("<br/>" + successNum + "、账号 " + shop.getShopName() + " 导入成功");
//                }
//                else if (isUpdateSupport)
//                {
//                    Shop checkShop =  shopMapper.selectShopByCreditCode(shop.getShopCreditCode());
//                    if(StringUtils.isNotEmpty(checkShop.getShopCreditCode())){
//                        this.updateShopSuervisiorStatus(checkShop.getShopCreditCode());   //更新店铺的监管状态--->重点监管
//
//                        //向重点监管日志表中插入数据
//                        SupervisorLog supervisorLog = new SupervisorLog();
//                        supervisorLog.setShopId(checkShop.getId());
//                        supervisorLog.setSupervisorStatus("重点监管");
//                        supervisorLog.setShopName(checkShop.getShopName());
//                        supervisorLog.setCreateTime(new DateTime());
//                        supervisorLog.setCreateDate(DateUtils.getNowDate());
//                        supervisorLogMapper.insertSupervisorLog(supervisorLog);
//                    }
//
//
//
//
//                    successNum++;
//                    successMsg.append("<br/>" + successNum + "、账号 " + shop.getShopName() + " 更新成功");
//                }
//                else
//                {
//                    failureNum++;
//                    failureMsg.append("<br/>" + failureNum + "、账号 " + shop.getShopName() + " 已存在");
//                }
//            }
//            catch (Exception e)
//            {
//                failureNum++;
//                String msg = "<br/>" + failureNum + "、账号 " + shop.getShopName() + " 导入失败：";
//                failureMsg.append(msg + e.getMessage());
//                log.error(msg, e);
//            }
//        }
//        if (failureNum > 0)
//        {
//            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
//            throw new ServiceException(failureMsg.toString());
//        }
//        else
//        {
//            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
//        }
//        return successMsg.toString();
//    }

    /**
     * 新增从业人员基本信息
     *
     * @param employees 从业人员基本信息
     * @return 结果
     */
    @Override
    public int insertEmployees(Employees employees)
    {
        employees.setCreateTime(DateUtils.getNowDate());
        return employeesMapper.insertEmployees(employees);
    }

    /**
     * 修改从业人员基本信息
     *
     * @param employees 从业人员基本信息
     * @return 结果
     */
    @Override
    public int updateEmployees(Employees employees)
    {
        employees.setUpdateTime(DateUtils.getNowDate());
        return employeesMapper.updateEmployees(employees);
    }

    /**
     * 批量删除从业人员基本信息
     *
     * @param ids 需要删除的从业人员基本信息主键
     * @return 结果
     */
    @Override
    public int deleteEmployeesByIds(Long[] ids)
    {
        return employeesMapper.deleteEmployeesByIds(ids);
    }

    /**
     * 删除从业人员基本信息信息
     *
     * @param id 从业人员基本信息主键
     * @return 结果
     */
    @Override
    public int deleteEmployeesById(Long id)
    {
        return employeesMapper.deleteEmployeesById(id);
    }
}
