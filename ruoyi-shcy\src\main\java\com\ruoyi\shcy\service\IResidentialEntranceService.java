package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.ResidentialEntrance;

/**
 * 小区出入门口Service接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface IResidentialEntranceService 
{
    /**
     * 查询小区出入门口
     * 
     * @param id 小区出入门口主键
     * @return 小区出入门口
     */
    public ResidentialEntrance selectResidentialEntranceById(Long id);

    /**
     * 查询小区出入门口列表
     * 
     * @param residentialEntrance 小区出入门口
     * @return 小区出入门口集合
     */
    public List<ResidentialEntrance> selectResidentialEntranceList(ResidentialEntrance residentialEntrance);

    /**
     * 新增小区出入门口
     * 
     * @param residentialEntrance 小区出入门口
     * @return 结果
     */
    public int insertResidentialEntrance(ResidentialEntrance residentialEntrance);

    /**
     * 修改小区出入门口
     * 
     * @param residentialEntrance 小区出入门口
     * @return 结果
     */
    public int updateResidentialEntrance(ResidentialEntrance residentialEntrance);

    /**
     * 批量删除小区出入门口
     * 
     * @param ids 需要删除的小区出入门口主键集合
     * @return 结果
     */
    public int deleteResidentialEntranceByIds(Long[] ids);

    /**
     * 删除小区出入门口信息
     * 
     * @param id 小区出入门口主键
     * @return 结果
     */
    public int deleteResidentialEntranceById(Long id);
}
