package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 区域信息对象 shcy_regions
 * 
 * <AUTHOR>
 * @date 2023-03-24
 */
public class Regions extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 区域编号 */
    @Excel(name = "区域编号")
    private String indexCode;

    /** 区域名称 */
    @Excel(name = "区域名称")
    private String name;

    /** 父区域编号 */
    @Excel(name = "父区域编号")
    private String parentIndexCode;

    /** 树编码 */
    @Excel(name = "树编码")
    private String treeCode;

    /** 国标编码 */
    @Excel(name = "国标编码")
    private String externalIndexCode;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setIndexCode(String indexCode) 
    {
        this.indexCode = indexCode;
    }

    public String getIndexCode() 
    {
        return indexCode;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setParentIndexCode(String parentIndexCode) 
    {
        this.parentIndexCode = parentIndexCode;
    }

    public String getParentIndexCode() 
    {
        return parentIndexCode;
    }
    public void setTreeCode(String treeCode) 
    {
        this.treeCode = treeCode;
    }

    public String getTreeCode() 
    {
        return treeCode;
    }
    public void setExternalIndexCode(String externalIndexCode) 
    {
        this.externalIndexCode = externalIndexCode;
    }

    public String getExternalIndexCode() 
    {
        return externalIndexCode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("indexCode", getIndexCode())
            .append("name", getName())
            .append("parentIndexCode", getParentIndexCode())
            .append("treeCode", getTreeCode())
            .append("externalIndexCode", getExternalIndexCode())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
