package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.GridLeader;
import com.ruoyi.shcy.service.IGridLeaderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网格联系领导Controller
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@RestController
@RequestMapping("/shcy/gridLeader")
public class GridLeaderController extends BaseController
{
    @Autowired
    private IGridLeaderService gridLeaderService;

    /**
     * 查询网格联系领导列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridLeader:list')")
    @GetMapping("/list")
    public TableDataInfo list(GridLeader gridLeader)
    {
        startPage();
        List<GridLeader> list = gridLeaderService.selectGridLeaderList(gridLeader);
        return getDataTable(list);
    }

    /**
     * 导出网格联系领导列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridLeader:export')")
    @Log(title = "网格联系领导", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GridLeader gridLeader)
    {
        List<GridLeader> list = gridLeaderService.selectGridLeaderList(gridLeader);
        ExcelUtil<GridLeader> util = new ExcelUtil<GridLeader>(GridLeader.class);
        util.exportExcel(response, list, "网格联系领导数据");
    }

    /**
     * 获取网格联系领导详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridLeader:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(gridLeaderService.selectGridLeaderById(id));
    }

    /**
     * 新增网格联系领导
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridLeader:add')")
    @Log(title = "网格联系领导", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GridLeader gridLeader)
    {
        return toAjax(gridLeaderService.insertGridLeader(gridLeader));
    }

    /**
     * 修改网格联系领导
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridLeader:edit')")
    @Log(title = "网格联系领导", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GridLeader gridLeader)
    {
        return toAjax(gridLeaderService.updateGridLeader(gridLeader));
    }

    /**
     * 删除网格联系领导
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridLeader:remove')")
    @Log(title = "网格联系领导", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(gridLeaderService.deleteGridLeaderByIds(ids));
    }
}
