package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 过车记录对象 shcy_vehicle
 * 
 * <AUTHOR>
 * @date 2024-08-01
 */
public class Vehicle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 通道 */
    private String channel;

    /** 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date passTime;

    /** 车牌 */
    @Excel(name = "车牌")
    private String licensePlate;

    /** 车辆类型 */
    @Excel(name = "车辆类型")
    private String vehicleType;

    /** 车牌特写 */
    private String licensePlateCloseup;

    /** 车牌颜色 */
    private String licensePlateColor;

    /** 车身抠图 */
    private String vehicleCutout;

    /** 车身颜色 */
    private String vehicleColor;

    /** 车标 */
    private String vehicleBrand;

    /** 安全带 */
    private String seatbelt;

    /** 打电话 */
    private String phoneUsage;

    /** 车内饰品 */
    private String interiorItems;

    /** 全景图 */
    private String panorama;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setChannel(String channel) 
    {
        this.channel = channel;
    }

    public String getChannel() 
    {
        return channel;
    }
    public void setPassTime(Date passTime) 
    {
        this.passTime = passTime;
    }

    public Date getPassTime() 
    {
        return passTime;
    }
    public void setLicensePlate(String licensePlate) 
    {
        this.licensePlate = licensePlate;
    }

    public String getLicensePlate() 
    {
        return licensePlate;
    }
    public void setLicensePlateCloseup(String licensePlateCloseup) 
    {
        this.licensePlateCloseup = licensePlateCloseup;
    }

    public String getLicensePlateCloseup() 
    {
        return licensePlateCloseup;
    }
    public void setLicensePlateColor(String licensePlateColor) 
    {
        this.licensePlateColor = licensePlateColor;
    }

    public String getLicensePlateColor() 
    {
        return licensePlateColor;
    }
    public void setVehicleCutout(String vehicleCutout) 
    {
        this.vehicleCutout = vehicleCutout;
    }

    public String getVehicleCutout() 
    {
        return vehicleCutout;
    }
    public void setVehicleType(String vehicleType) 
    {
        this.vehicleType = vehicleType;
    }

    public String getVehicleType() 
    {
        return vehicleType;
    }
    public void setVehicleColor(String vehicleColor) 
    {
        this.vehicleColor = vehicleColor;
    }

    public String getVehicleColor() 
    {
        return vehicleColor;
    }
    public void setVehicleBrand(String vehicleBrand) 
    {
        this.vehicleBrand = vehicleBrand;
    }

    public String getVehicleBrand() 
    {
        return vehicleBrand;
    }
    public void setSeatbelt(String seatbelt) 
    {
        this.seatbelt = seatbelt;
    }

    public String getSeatbelt() 
    {
        return seatbelt;
    }
    public void setPhoneUsage(String phoneUsage) 
    {
        this.phoneUsage = phoneUsage;
    }

    public String getPhoneUsage() 
    {
        return phoneUsage;
    }
    public void setInteriorItems(String interiorItems) 
    {
        this.interiorItems = interiorItems;
    }

    public String getInteriorItems() 
    {
        return interiorItems;
    }
    public void setPanorama(String panorama) 
    {
        this.panorama = panorama;
    }

    public String getPanorama() 
    {
        return panorama;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("channel", getChannel())
            .append("passTime", getPassTime())
            .append("licensePlate", getLicensePlate())
            .append("licensePlateCloseup", getLicensePlateCloseup())
            .append("licensePlateColor", getLicensePlateColor())
            .append("vehicleCutout", getVehicleCutout())
            .append("vehicleType", getVehicleType())
            .append("vehicleColor", getVehicleColor())
            .append("vehicleBrand", getVehicleBrand())
            .append("seatbelt", getSeatbelt())
            .append("phoneUsage", getPhoneUsage())
            .append("interiorItems", getInteriorItems())
            .append("panorama", getPanorama())
            .toString();
    }
}
