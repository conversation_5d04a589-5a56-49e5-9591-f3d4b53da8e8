package com.ruoyi.web.controller.screen;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.screen.vo.CheckRecordShopNumberVO;
import com.ruoyi.screen.vo.CheckRecordShopVO;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.*;
import com.ruoyi.shcy.domain.vo.CamerasVO;
import com.ruoyi.shcy.domain.vo.CommitteeListInfoVo;
import com.ruoyi.shcy.domain.vo.ShopCommitteeCheckStatusVo;
import com.ruoyi.shcy.service.*;
import com.ruoyi.system.service.ISysDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 大屏通用Controller
 *
 * <AUTHOR>
 * @Date 2022/12/21 15:20
 * @Version 1.0
 */
@Api("大屏接口")
@RestController
@RequestMapping("/screen/api")
public class ScreenController extends BaseController {

    @Autowired
    private IApartmentRentalService apartmentRentalService;

    @Autowired
    private ICommitteeService committeeService;

    @Autowired
    private IConstructionWasteSiteService constructionWasteSiteService;

    @Autowired
    private ICoverService coverService;

    @Autowired
    private IGarbageHouseService garbageHouseService;

    @Autowired
    private IHotelService hotelService;

    @Autowired
    private IInstitutionsService institutionsService;

    @Autowired
    private ILargeGarbageSiteService largeGarbageSiteService;

    @Autowired
    private INonResidentialPropertyService nonResidentialPropertyService;

    @Autowired
    private IRainwaterSewagePipeService rainwaterSewagePipeService;

    @Autowired
    private IResidentialEntranceService residentialEntranceService;

    @Autowired
    private IResidentialsService residentialsService;

    @Autowired
    private IShopService shopService;

    @Autowired
    private IEntertainmentPlacesService entertainmentPlacesService;

    @Autowired
    private INonPropertyContactService nonPropertyContactService;

    @Autowired
    private IParcelInformationService parcelInformationService;

    @Autowired
    private IRiverLakeService riverLakeService;

    @Autowired
    private IGreenGridService greenGridService;

    @Autowired
    private ICheckRecordService checkRecordService;

    @Autowired
    private IShopCheckLogService shopCheckLogService;

    @Autowired
    private ISysDeptService iSysDeptService;

    @Autowired
    private IRoadStreetChiefService roadStreetChiefService;

    @Autowired
    private IPumpStationService pumpStationService;

    @Autowired
    private ICamerasService camerasService;

    @Autowired
    private IPersonnelGridService personnelGridService;

    @Autowired
    private ILiquidLevelDeviceService liquidLevelDeviceService;

    @Autowired
    private INbiotyunAlarmRecordService nbiotyunAlarmRecordService;

    @Autowired
    private IIccDeviceService iccDeviceService;

    @Autowired
    private IFloodControlKeyAreaService floodControlKeyAreaService;

    @Autowired
    private IFloodReserveInfoService floodReserveInfoService;

    @Autowired
    private IFloodShelterInfoService floodShelterInfoService;

    @Autowired
    private ISensorDeviceService sensorDeviceService;

    @Autowired
    private IFactoryWarehouseService factoryWarehouseService;

    @Autowired
    private IComprehensiveGridService comprehensiveGridService;

    @Autowired
    private IGridWorkforceService gridWorkforceService;

    /**
     * 查询商铺信息列表
     */
    @GetMapping("/shopList")
    public AjaxResult shop(Shop shop) {
        List<Shop> list = shopService.selectShopList(shop);
        return AjaxResult.success(list);
    }

    /**
     * 查询公寓租赁房列表
     */
    @GetMapping("/apartmentRentalList")
    public AjaxResult apartmentRental(ApartmentRental apartmentRental) {
        List<ApartmentRental> list = apartmentRentalService.selectApartmentRentalList(apartmentRental);
        return AjaxResult.success(list);
    }

    /**
     * 查询居委会信息列表
     */
    @GetMapping("/committeeList")
    public AjaxResult committee(Committee committee) {
        List<Committee> list = committeeService.selectCommitteeList(committee);
        return AjaxResult.success(list);
    }

    /**
     * 查询小区建筑垃圾堆放点列表
     */
    @GetMapping("/constructionWasteSiteList")
    public AjaxResult constructionWasteSite(ConstructionWasteSite constructionWasteSite) {

        List<ConstructionWasteSite> list = constructionWasteSiteService.selectConstructionWasteSiteList(constructionWasteSite);
        return AjaxResult.success(list);
    }

    /**
     * 查询井盖信息列表
     */
    @GetMapping("/coverList")
    public AjaxResult cover(Cover cover) {
        List<Cover> list = coverService.selectCoverList(cover);
        return AjaxResult.success(list);
    }

    /**
     * 查询小区垃圾房信息列表
     */
    @GetMapping("/garbageHouseList")
    public AjaxResult garbageHouse(GarbageHouse garbageHouse) {
        List<GarbageHouse> list = garbageHouseService.selectGarbageHouseList(garbageHouse);
        return AjaxResult.success(list);
    }

    /**
     * 查询宾旅馆信息列表
     */
    @GetMapping("/hotelList")
    public AjaxResult hotel(Hotel hotel) {
        List<Hotel> list = hotelService.selectHotelList(hotel);
        return AjaxResult.success(list);
    }

    /**
     * 查询企事业单位列表
     */
    @GetMapping("/institutionsList")
    public AjaxResult institutions(Institutions institutions) {

        List<Institutions> list = institutionsService.selectInstitutionsList(institutions);
        return AjaxResult.success(list);
    }

    /**
     * 查询小区大件垃圾堆放点列表
     */
    @GetMapping("/largeGarbageSiteList")
    public AjaxResult largeGarbageSite(LargeGarbageSite largeGarbageSite) {

        List<LargeGarbageSite> list = largeGarbageSiteService.selectLargeGarbageSiteList(largeGarbageSite);
        return AjaxResult.success(list);
    }

    /**
     * 查询物业管理处列表
     */
    @GetMapping("/nonResidentialPropertyList")
    public AjaxResult nonResidentialProperty(NonResidentialProperty nonResidentialProperty) {

        List<NonResidentialProperty> list = nonResidentialPropertyService.selectNonResidentialPropertyList(nonResidentialProperty);
        return AjaxResult.success(list);
    }

    /**
     * 查询雨污管道信息列表
     */
    @GetMapping("/rainwaterSewagePipeList")
    public AjaxResult rainwaterSewagePipe(RainwaterSewagePipe rainwaterSewagePipe) {

        List<RainwaterSewagePipe> list = rainwaterSewagePipeService.selectRainwaterSewagePipeList(rainwaterSewagePipe);
        return AjaxResult.success(list);
    }

    /**
     * 查询小区出入门口列表
     */
    @GetMapping("/residentialEntranceList")
    public AjaxResult residentialEntrance(ResidentialEntrance residentialEntrance) {

        List<ResidentialEntrance> list = residentialEntranceService.selectResidentialEntranceList(residentialEntrance);
        return AjaxResult.success(list);
    }

    /**
     * 查询小区信息列表
     */
    @GetMapping("/residentialsList")
    public AjaxResult residentials(Residentials residentials) {

        List<Residentials> list = residentialsService.selectResidentialsList(residentials);
        return AjaxResult.success(list);
    }

    /**
     * 查询文化娱乐场所列表
     */
    @GetMapping("/entainmentList")
    public AjaxResult entainment(EntertainmentPlaces entertainmentPlaces) {
        List<EntertainmentPlaces> list = entertainmentPlacesService.selectEntertainmentPlacesList(entertainmentPlaces);

        return AjaxResult.success(list);
    }

    /**
     * 查询非住宅物业联系列表
     */
    @GetMapping("/propertyContractList")
    public AjaxResult propertyContract(NonPropertyContact nonPropertyContact) {
        List<NonPropertyContact> list = nonPropertyContactService.selectNonPropertyContactList(nonPropertyContact);
        return AjaxResult.success(list);
    }

    /**
     * 查询宗地信息列表
     **/
    @GetMapping("/parcelList")
    public AjaxResult parcelInformation(ParcelInformation parcelInformation) {
        List<ParcelInformation> list = parcelInformationService.selectParcelInformationList(parcelInformation);

        return AjaxResult.success(list);
    }

    /**
     * 查询厂房仓库信息列表
     **/
    @GetMapping("/factoryWarehouseList")
    public AjaxResult factoryWarehouse(FactoryWarehouse factoryWarehouse) {
        List<FactoryWarehouse> list = factoryWarehouseService.selectFactoryWarehouseList(factoryWarehouse);
        return AjaxResult.success(list);
    }

    /**
     * 河湖水体信息列表
     **/
    @GetMapping("/riverLakeList")
    public AjaxResult riverlake(RiverLake riverLake) {
        List<RiverLake> list = riverLakeService.selectRiverLakeList(riverLake);
        return AjaxResult.success(list);
    }

    /**
     * 绿化网格信息
     **/
    @GetMapping("/greenGridList")
    public AjaxResult greengrid(GreenGrid greenGrid) {
        List<GreenGrid> list = greenGridService.selectGreenGridList(greenGrid);
        return AjaxResult.success(list);
    }


    /**
     * 大屏商铺巡查情况
     **/
    @GetMapping(value = "/shopCheckRecord")
    public AjaxResult shopCheckRecord(CheckRecord checkRecord) {
        System.out.println(System.currentTimeMillis() / 1000);

        Map<String, Object> checkShopList = new HashMap<>();

        //*****************************************商铺开业信息(总数、正常数、歇业数、关停数)***********************************************
        //查询商铺总数
        checkShopList.put("ShopTotal", shopService.selectShopCount());

        //查询正常商铺总数
        checkShopList.put("ShopNormalNum", shopService.selectOpenNomalShopCount());

        //查询歇业商铺总数
        checkShopList.put("ShopRestNum", shopService.selectOpenRestShopCount());

        //查询关停商铺总数
        checkShopList.put("ShopCloseNum", shopService.selectOpenStopShopCount());


        //****************************************每日巡查数据**************************************************************************
//            今日已巡查商铺数
        String today = DateUtil.today();
        Map<String, Object> params = new HashMap<>();
        params.put("beginTime", today);
        params.put("endTime", today);
        checkRecord.setParams(params);

        List<CheckRecord> checkedShopNumDay = checkRecordService.selectCheckedShopNum(checkRecord);
        checkShopList.put("checkedShopNumDay", checkedShopNumDay);


        //今日巡查商铺通过
        List<CheckRecord> checkedAndPassShopNumDay = checkRecordService.selectCheckAndPassShop(checkRecord);
        checkShopList.put("checkedAndPassShopNumDay", checkedAndPassShopNumDay);


        //今日巡查商铺未通过数
        List<CheckRecord> checkedAndNoPassShopNumDay = checkRecordService.selectCheckAndNoPassShop(checkRecord);
        checkShopList.put("checkedAndNoPassShopNumDay", checkedAndNoPassShopNumDay);

        //今日未巡查商铺
        List<Shop> noCheckedshopDay = shopService.selectNoCheckedShopList(checkRecord);
        checkShopList.put("noCheckedShopDay", noCheckedshopDay);

        System.out.println(System.currentTimeMillis() / 1000);
        //*****************************************每月巡查数据***********************************************
        boolean dayOrMonth = true;
        if (dayOrMonth) {
            CheckRecord checkRecordMonth = new CheckRecord();
            Map<String, Object> params2 = new HashMap<>();
            params2.put("flag", "1");
            params2.put("beginTime", "");
            params.put("endTime", "");
            checkRecordMonth.setParams(params2);

            List<CheckRecord> checkedShopNumMonth = checkRecordService.selectCheckedShopNum(checkRecordMonth);
            checkShopList.put("checkedShopNumMonth", checkedShopNumMonth);

            //本月巡查商铺通过
            List<CheckRecord> checkedAndPassShopNumMonth = checkRecordService.selectCheckAndPassShop(checkRecordMonth);
            checkShopList.put("checkedAndPassShopNumMonth", checkedAndPassShopNumMonth);


            //本月巡查商铺未通过数
            List<CheckRecord> checkedAndNoPassShopNumMonth = checkRecordService.selectCheckAndNoPassShop(checkRecordMonth);
            checkShopList.put("checkedAndNoPassShopNumMonth", checkedAndNoPassShopNumMonth);


            //本月未巡查商铺
            List<Shop> noCheckedshopMonth = shopService.selectNoCheckedShopList(checkRecordMonth);
            checkShopList.put("noCheckedShopMonth", noCheckedshopMonth);

        }
        System.out.println(System.currentTimeMillis() / 1000);
        return AjaxResult.success(checkShopList);
    }


    /**
     * 大屏居委信息表
     */
    @GetMapping(value = "/juweihui")
    public AjaxResult shopJuweihui(SysDept sysDept) {
        CommitteeListInfoVo committeeListInfoVo = new CommitteeListInfoVo();
        List<SysDept> depts = iSysDeptService.selectAllCommittee(sysDept);
        committeeListInfoVo.setDepts(depts);
        Long deptId = shopCheckLogService.getDeptIdOfNewthCheckRecord();
        if (deptId != null) {
            committeeListInfoVo.setDeptId(deptId);
            List<ShopCommitteeCheckStatusVo> list = shopCheckLogService.selectShopCheckStatusByDeptId(deptId);
            committeeListInfoVo.setCheckRecords(list);
        }
        return AjaxResult.success(committeeListInfoVo);
    }

    /**
     * 根据居委会id查询对应巡查商铺信息
     */
    @GetMapping(value = "/juweihui/{deptId}")
    public AjaxResult shopCheckStatusWithDeptId(@PathVariable("deptId") Long deptId) {
        List<ShopCommitteeCheckStatusVo> list = shopCheckLogService.selectShopCheckStatusByDeptId(deptId);
        return AjaxResult.success(list);
    }

    /**
     * 路段名及街长信息数据
     **/
    @GetMapping(value = "/roadStreet")
    public AjaxResult roadStreenChief(RoadStreetChief roadStreetChief) {
        List<RoadStreetChief> roadStreetChiefs = roadStreetChiefService.selectRoadStreetChiefList(roadStreetChief);
        return AjaxResult.success(roadStreetChiefs);
    }

    /**
     * 获取巡查商铺数量
     **/
    @ApiOperation("获取巡查商铺数量")
    @GetMapping(value = "/checkRecordShopNumber")
    public AjaxResult checkRecordShopNumber() {
        List<Shop> shopList = shopService.selectCheckShopList();
        Integer shopTotal = shopList.size();
        Long shopNormalNum = shopList.stream().filter(x-> "营业".equals(x.getIsOpen())).count();
        Long shopRestNum = shopList.stream().filter(x-> "歇业".equals(x.getIsOpen())).count();
        Long shopCloseNum = shopList.stream().filter(x-> "关停".equals(x.getIsOpen())).count();
        List<CheckRecord> checkRecordList = checkRecordService.selectCheckRecordShopList();
        String todayStr = DateUtil.today();
        String monthStr = DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_PATTERN);
        Long checkedShopNumDay = checkRecordList.stream().filter(x -> todayStr.equals(DateUtil.formatDate(x.getCheckDate()))).filter(x -> "检查通过".equals(x.getCheckStatus()) || "检查未通过".equals(x.getCheckStatus())).count();
        Long checkedAndPassShopNumDay = checkRecordList.stream().filter(x -> todayStr.equals(DateUtil.formatDate(x.getCheckDate()))).filter(x -> "检查通过".equals(x.getCheckStatus())).count();
        Long checkedAndNoPassShopNumDay = checkRecordList.stream().filter(x -> todayStr.equals(DateUtil.formatDate(x.getCheckDate()))).filter(x -> "检查未通过".equals(x.getCheckStatus())).count();
        CheckRecord checkRecordDay = new CheckRecord();
        checkRecordDay.setParams(new HashMap<String, Object>() {
            {
                put("beginTime", todayStr);
                put("endTime", todayStr);
            }
        });
        Integer noCheckedShopDay = shopService.selectNoCheckedShopList(checkRecordDay).size();
        Long checkedShopNumMonth = checkRecordList.stream().filter(x -> monthStr.equals(DateUtil.format(x.getCheckDate(), DatePattern.SIMPLE_MONTH_PATTERN))).filter(x -> "检查通过".equals(x.getCheckStatus()) || "检查未通过".equals(x.getCheckStatus())).count();
        Long checkedAndPassShopNumMonth = checkRecordList.stream().filter(x -> monthStr.equals(DateUtil.format(x.getCheckDate(), DatePattern.SIMPLE_MONTH_PATTERN))).filter(x -> "检查通过".equals(x.getCheckStatus())).count();
        Long checkedAndNoPassShopNumMonth = checkRecordList.stream().filter(x -> monthStr.equals(DateUtil.format(x.getCheckDate(), DatePattern.SIMPLE_MONTH_PATTERN))).filter(x -> "检查未通过".equals(x.getCheckStatus())).count();
        CheckRecord checkRecordMonth = new CheckRecord();
        checkRecordMonth.setParams(new HashMap<String, Object>() {
            {
                put("flag", "1");
            }
        });
        Integer noCheckedShopMonth = shopService.selectNoCheckedShopList(checkRecordMonth).size();
        return AjaxResult.success(CheckRecordShopNumberVO.builder().
                shopTotal(shopTotal).
                shopNormalNum(shopNormalNum.intValue()).
                shopRestNum(shopRestNum.intValue()).
                shopCloseNum(shopCloseNum.intValue()).
                checkedShopNumDay(checkedShopNumDay.intValue()).
                checkedAndPassShopNumDay(checkedAndPassShopNumDay.intValue()).
                checkedAndNoPassShopNumDay(checkedAndNoPassShopNumDay.intValue()).
                noCheckedShopDay(noCheckedShopDay).
                checkedShopNumMonth(checkedShopNumMonth.intValue()).
                checkedAndPassShopNumMonth(checkedAndPassShopNumMonth.intValue()).
                checkedAndNoPassShopNumMonth(checkedAndNoPassShopNumMonth.intValue()).
                noCheckedShopMonth(noCheckedShopMonth).
                build());
    }

    /**
     * 获取巡查商铺
     **/
    @ApiOperation("获取巡查商铺")
    @GetMapping(value = "/checkRecordShop")
    public AjaxResult checkRecordShop() {
        List<CheckRecord> checkRecordList = checkRecordService.selectCheckRecordShopList();
        String todayStr = DateUtil.today();
        String monthStr = DateUtil.format(new Date(), DatePattern.SIMPLE_MONTH_PATTERN);
        List<CheckRecord> todayPassShop = checkRecordList.stream().filter(x -> todayStr.equals(DateUtil.formatDate(x.getCheckDate()))).filter(x -> "检查通过".equals(x.getCheckStatus())).collect(Collectors.toList());
        List<CheckRecord> todayNotPassShop = checkRecordList.stream().filter(x -> todayStr.equals(DateUtil.formatDate(x.getCheckDate()))).filter(x -> "检查未通过".equals(x.getCheckStatus())).collect(Collectors.toList());
        List<CheckRecord> monthPassShop = checkRecordList.stream().filter(x -> monthStr.equals(DateUtil.format(x.getCheckDate(), DatePattern.SIMPLE_MONTH_PATTERN))).filter(x -> "检查通过".equals(x.getCheckStatus())).collect(Collectors.toList());
        List<CheckRecord> monthNotPassShop = checkRecordList.stream().filter(x -> monthStr.equals(DateUtil.format(x.getCheckDate(), DatePattern.SIMPLE_MONTH_PATTERN))).filter(x -> "检查未通过".equals(x.getCheckStatus())).collect(Collectors.toList());
        CheckRecord checkRecordMonth = new CheckRecord();
        checkRecordMonth.setParams(new HashMap<String, Object>() {
            {
                put("flag", "1");
            }
        });
        List<Shop> monthNotCheckShop = shopService.selectNoCheckedShopList(checkRecordMonth);
        return AjaxResult.success(CheckRecordShopVO.builder().
                todayPassShop(todayPassShop).
                todayNotPassShop(todayNotPassShop).
                monthPassShop(monthPassShop).
                monthNotPassShop(monthNotPassShop).
                monthNotCheckShop(monthNotCheckShop).
                build());
    }

    /**
     * 获取检查日志详细信息(最近三天)
     */
    @GetMapping(value = "/three/{id}")
    public AjaxResult getInfoThreeDays(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shopCheckLogService.selectShopCheckLogByIdThree(id));
    }

    /**
     * 泵站列表
     *
     * @param pumpStation 泵站
     * @return {@link AjaxResult}
     */
    @ApiOperation("获取泵站列表")
    @GetMapping("/pumpStationList")
    public AjaxResult pumpStationList(PumpStation pumpStation) {
        List<PumpStation> pumpStations = pumpStationService.selectPumpStationList(pumpStation);
        return AjaxResult.success(pumpStations);
    }

    /**
     * 泵站区域列表
     */
    @ApiOperation("获取泵站列表")
    @GetMapping("/pumpStationAreaList/{ids}")
    public AjaxResult pumpStationAreaList(@PathVariable Long[] ids) {
        List<PumpStation> pumpStations = pumpStationService.selectPumpStationListByIds(ids);
        return AjaxResult.success(pumpStations);
    }

    /**
     * 获取监控资源列表
     *
     * @param cameras 监控资源
     * @return {@link AjaxResult}
     */
    @ApiOperation("获取监控资源列表")
    @GetMapping("/camerasList")
    public AjaxResult camerasList(Cameras cameras) {
        List<Cameras> list = camerasService.selectCamerasList(cameras);
        List<CamerasVO> camerasList = BeanUtil.copyToList(list.stream()
                .filter(obj -> obj.getCoordinate() != null)
                .collect(Collectors.toList()), CamerasVO.class);
        return AjaxResult.success(camerasList);
    }

    /**
     * 获取两类人员网格化数据
     *
     * @param personnelGrid 两类人员网格化
     * @return {@link AjaxResult}
     */
    @ApiOperation("获取两类人员网格化数据")
    @GetMapping("/personnelGridList")
    public AjaxResult personnelGridList(PersonnelGrid personnelGrid) {
        List<PersonnelGrid> personnelGridList = personnelGridService.selectPersonnelGridList(personnelGrid);
        return AjaxResult.success(personnelGridList);
    }

    /**
     * 液位装置清单
     *
     * @param liquidLevelDevice 液位装置
     * @return {@link AjaxResult}
     */
    @ApiOperation("液位装置清单")
    @GetMapping("/liquidLevelDeviceList")
    public AjaxResult liquidLevelDeviceList(LiquidLevelDevice liquidLevelDevice) {
        List<LiquidLevelDevice> liquidLevelDeviceList = getLiquidLevelDeviceList(liquidLevelDevice);

        // List<String> imeiList = getImeiList();
        // List<LiquidLevelDevice> filteredLiquidLevelDeviceList = liquidLevelDeviceList.stream()
        //         .filter(device -> !imeiList.contains(device.getDeviceImei()))
        //         .collect(Collectors.toList());

        // 从liquidLevelDeviceList中筛选出alarmStatus为'0'的数据
        List<LiquidLevelDevice> filteredLiquidLevelDeviceList = liquidLevelDeviceList.stream()
                .filter(device -> "0".equals(device.getAlarmStatus()))
                .collect(Collectors.toList());
        return AjaxResult.success(filteredLiquidLevelDeviceList);
    }

    /**
     * 液位装置报警清单
     *
     * @param liquidLevelDevice 液位装置
     * @return {@link AjaxResult}
     */
    @ApiOperation("液位装置报警清单")
    @GetMapping("/liquidLevelDeviceAlarmList")
    public AjaxResult liquidLevelDeviceAlarmList(LiquidLevelDevice liquidLevelDevice) {
        List<LiquidLevelDevice> liquidLevelDeviceList = getLiquidLevelDeviceList(liquidLevelDevice);

        // List<String> imeiList = getImeiList();
        // List<LiquidLevelDevice> filteredLiquidLevelDeviceList = liquidLevelDeviceList.stream()
        //         .filter(device -> imeiList.contains(device.getDeviceImei()))
        //         .collect(Collectors.toList());

        // 从liquidLevelDeviceList中筛选出alarmStatus为'1'的数据
        List<LiquidLevelDevice> filteredLiquidLevelDeviceList = liquidLevelDeviceList.stream()
                .filter(device -> "1".equals(device.getAlarmStatus()))
                .collect(Collectors.toList());
        return AjaxResult.success(filteredLiquidLevelDeviceList);
    }

    /**
     * 液位装置区域列表
     *
     * @param floodControlKeyArea 防洪重点区
     * @return {@link AjaxResult}
     */
    @ApiOperation("液位装置区域列表")
    @GetMapping("/liquidLevelDeviceAreaList")
    public AjaxResult liquidLevelDeviceAreaList(FloodControlKeyArea floodControlKeyArea) {
        List<FloodControlKeyArea> floodControlKeyAreas = floodControlKeyAreaService.selectFloodControlKeyAreaList(floodControlKeyArea);
        return AjaxResult.success(floodControlKeyAreas);
    }

    /**
     * 根据设备imei获取设备报警记录
     *
     * @param deviceImei 设备imei
     * @return {@link AjaxResult}
     */
    @ApiOperation("根据设备imei获取设备报警记录")
    @GetMapping("/getAlarmRecordByImei")
    public AjaxResult getAlarmRecordByImei(String deviceImei) {
        NbiotyunAlarmRecord alarmRecord = nbiotyunAlarmRecordService.getAlarmRecordByImei(deviceImei);
        return AjaxResult.success(alarmRecord);
    }

    /**
     * icc监控设备列表
     *
     * @param iccDevice icc设备
     * @return {@link AjaxResult}
     */
    @ApiOperation("icc监控设备列表")
    @GetMapping("/iccDeviceList")
    public AjaxResult iccDeviceList(IccDevice iccDevice) {
        List<IccDevice> iccDevices = iccDeviceService.selectIccDeviceList(iccDevice);
        return AjaxResult.success(iccDevices);
    }

    /**
     * 获取液位设备列表
     *
     * @param liquidLevelDevice 液位装置
     * @return {@link List}<{@link LiquidLevelDevice}>
     */
    private List<LiquidLevelDevice> getLiquidLevelDeviceList(LiquidLevelDevice liquidLevelDevice) {
        liquidLevelDevice.setType("point");
        return liquidLevelDeviceService.selectLiquidLevelDeviceList(liquidLevelDevice);
    }

    /**
     * 获取imei列表
     *
     * @return {@link List}<{@link String}>
     */
    private List<String> getImeiList() {
        List<NbiotyunAlarmRecord> nbiotyunAlarmRecords = nbiotyunAlarmRecordService.selectNbiotyunAlarmRecordGroupByList(new NbiotyunAlarmRecord());
        return nbiotyunAlarmRecords.stream().filter(item -> FxftConstants.UNPROCESSED.equals(item.getStatus())).map(NbiotyunAlarmRecord::getDeviceImei).collect(Collectors.toList());
    }

    @ApiOperation("防汛物资储备信息列表")
    @GetMapping("/floodReserveInfoList")
    public AjaxResult floodReserveInfoList(FloodReserveInfo floodReserveInfo) {
        List<FloodReserveInfo> list = floodReserveInfoService.selectFloodReserveInfoList(floodReserveInfo);
        return AjaxResult.success(list);
    }

    @ApiOperation("防汛安置点信息列表")
    @GetMapping("/floodShelterInfoList")
    public AjaxResult floodShelterInfoList(FloodShelterInfo floodShelterInfo) {
        List<FloodShelterInfo> list = floodShelterInfoService.selectFloodShelterInfoList(floodShelterInfo);
        return AjaxResult.success(list);
    }

    @ApiOperation("获取设备在线数量")
    @GetMapping("/getDeviceStatusNum")
    public AjaxResult getDeviceStatusNum() {
        List<LiquidLevelDevice> liquidLevelDevices = liquidLevelDeviceService.selectLiquidLevelDeviceList(new LiquidLevelDevice());
        // 计算出liquidLevelDevices中deviceStatus为在线的数量
        long fxftOnlineNum = liquidLevelDevices.stream().filter(item -> "在线".equals(item.getDeviceState())).count();
        // 计算出liquidLevelDevices的总数
        long fxftTotalNum = liquidLevelDevices.size();
        List<IccDevice> iccDevices = iccDeviceService.selectIccDeviceList(new IccDevice());
        // 计算出iccDevices中deviceStatus为在线的数量
        long hjzzOnlineNum = iccDevices.stream().filter(item -> "在线".equals(item.getDeviceStatus())).count();
        // 计算出iccDevices的总数
        long hjzzTotalNum = iccDevices.size();
        Map<String, Object> map = new HashMap<>();
        map.put("fxftOnlineNum", fxftOnlineNum);
        map.put("fxftTotalNum", fxftTotalNum);
        map.put("hjzzOnlineNum", hjzzOnlineNum);
        map.put("hjzzTotalNum", hjzzTotalNum);
        return AjaxResult.success(map);
    }

    /**
     * 航天水位传感器列表
     */
    @ApiOperation("航天水位传感器列表")
    @GetMapping("/sensorDeviceList")
    public AjaxResult sensorDeviceList(SensorDevice sensorDevice) {
        sensorDevice.setSensorimeiTypeName("水位");
        List<SensorDevice> sensorDevices = sensorDeviceService.selectSensorDeviceList(sensorDevice);
        return AjaxResult.success(sensorDevices);
    }

    /**
     * 获取综合网格列表
     */
    @ApiOperation("获取综合网格列表")
    @GetMapping("/comprehensiveGridList")
    public AjaxResult comprehensiveGridList(ComprehensiveGrid comprehensiveGrid) {
        List<ComprehensiveGrid> comprehensiveGrids = comprehensiveGridService.selectComprehensiveGridList(comprehensiveGrid);
        return AjaxResult.success(comprehensiveGrids);
    }

    /**
     * 根据id获取综合网格
     */
    @ApiOperation("根据id获取综合网格") 
    @GetMapping("/getComprehensiveGridById/{id}")
    public AjaxResult getComprehensiveGridById(@PathVariable("id") Long id) {
        ComprehensiveGrid comprehensiveGrid = comprehensiveGridService.selectComprehensiveGridById(id);
        return AjaxResult.success(comprehensiveGrid);
    }

    /**
     * 获取网格工作力量列表
     */ 
    @ApiOperation("获取网格工作力量列表")
    @GetMapping("/getGridWorkforceList")
    public AjaxResult getGridWorkforceList(GridWorkforce gridWorkforce) {
        List<GridWorkforce> gridWorkforces = gridWorkforceService.selectGridWorkforceList(gridWorkforce);
        return AjaxResult.success(gridWorkforces);
    }
    
}
