package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 传感器设备对象 shcy_sensor_device
 *
 * <AUTHOR>
 * @date 2024-08-28
 */
public class SensorDevice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String deviceName;

    /**
     * 传感器类型
     */
    @Excel(name = "传感器类型")
    private String sensorimeiTypeName;

    /**
     * 状态
     */
    @Excel(name = "状态")
    private String status;

    /**
     * imei
     */
    @Excel(name = "imei")
    private String imei;

    /**
     * 值
     */
    private String value;

    /**
     * 单位
     */
    private String unit;

    /**
     * 水位类型
     */
    private String waterLevelType;

    private String type;

    private String coordinate;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setSensorimeiTypeName(String sensorimeiTypeName) {
        this.sensorimeiTypeName = sensorimeiTypeName;
    }

    public String getSensorimeiTypeName() {
        return sensorimeiTypeName;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public String getImei() {
        return imei;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnit() {
        return unit;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public String getWaterLevelType() {
        return waterLevelType;
    }

    public void setWaterLevelType(String waterLevelType) {
        this.waterLevelType = waterLevelType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deviceName", getDeviceName())
                .append("sensorimeiTypeName", getSensorimeiTypeName())
                .append("status", getStatus())
                .append("imei", getImei())
                .append("value", getValue())
                .append("unit", getUnit())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
