package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.HotelMapper;
import com.ruoyi.shcy.domain.Hotel;
import com.ruoyi.shcy.service.IHotelService;

/**
 * 宾旅馆信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class HotelServiceImpl implements IHotelService 
{
    @Autowired
    private HotelMapper hotelMapper;

    /**
     * 查询宾旅馆信息
     * 
     * @param id 宾旅馆信息主键
     * @return 宾旅馆信息
     */
    @Override
    public Hotel selectHotelById(Long id)
    {
        return hotelMapper.selectHotelById(id);
    }

    /**
     * 查询宾旅馆信息列表
     * 
     * @param hotel 宾旅馆信息
     * @return 宾旅馆信息
     */
    @Override
    public List<Hotel> selectHotelList(Hotel hotel)
    {
        return hotelMapper.selectHotelList(hotel);
    }

    /**
     * 新增宾旅馆信息
     * 
     * @param hotel 宾旅馆信息
     * @return 结果
     */
    @Override
    public int insertHotel(Hotel hotel)
    {
        hotel.setCreateTime(DateUtils.getNowDate());
        return hotelMapper.insertHotel(hotel);
    }

    /**
     * 修改宾旅馆信息
     * 
     * @param hotel 宾旅馆信息
     * @return 结果
     */
    @Override
    public int updateHotel(Hotel hotel)
    {
        hotel.setUpdateTime(DateUtils.getNowDate());
        return hotelMapper.updateHotel(hotel);
    }

    /**
     * 批量删除宾旅馆信息
     * 
     * @param ids 需要删除的宾旅馆信息主键
     * @return 结果
     */
    @Override
    public int deleteHotelByIds(Long[] ids)
    {
        return hotelMapper.deleteHotelByIds(ids);
    }

    /**
     * 删除宾旅馆信息信息
     * 
     * @param id 宾旅馆信息主键
     * @return 结果
     */
    @Override
    public int deleteHotelById(Long id)
    {
        return hotelMapper.deleteHotelById(id);
    }
}
