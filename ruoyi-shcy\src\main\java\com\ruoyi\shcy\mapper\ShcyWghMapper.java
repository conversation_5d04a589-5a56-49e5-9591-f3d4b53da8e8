package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.ShcyWgh;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网格化案件信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-10
 */
public interface ShcyWghMapper 
{
    /**
     * 查询网格化案件信息
     * 
     * @param id 网格化案件信息主键
     * @return 网格化案件信息
     */
    public ShcyWgh selectShcyWghById(Long id);

    /**
     * 查询网格化案件信息列表
     * 
     * @param shcyWgh 网格化案件信息
     * @return 网格化案件信息集合
     */
    public List<ShcyWgh> selectShcyWghList(ShcyWgh shcyWgh);

    /**
     * 新增网格化案件信息
     * 
     * @param shcyWgh 网格化案件信息
     * @return 结果
     */
    public int insertShcyWgh(ShcyWgh shcyWgh);

    /**
     * 修改网格化案件信息
     * 
     * @param shcyWgh 网格化案件信息
     * @return 结果
     */
    public int updateShcyWgh(ShcyWgh shcyWgh);

    /**
     * 删除网格化案件信息
     * 
     * @param id 网格化案件信息主键
     * @return 结果
     */
    public int deleteShcyWghById(Long id);

    /**
     * 批量删除网格化案件信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyWghByIds(Long[] ids);

    public long getCaseCount(ShcyWgh shcyWgh);

    public List<ShcyWgh> selectHandleList(ShcyWgh shcyWgh);

    public List<ShcyWgh> selectHistoryList(ShcyWgh shcyWgh);

    public List<ShcyWgh> selectShcyWghListByTaskDbcenterIds(@Param("taskDbcenterIds") List<Long> taskDbcenterIds);

    public ShcyWgh selectShcyWghByTaskDbcenterId(String taskId);

    public int updateShcyWghByTaskIds(@Param("taskIds") List<String> taskIds);
}
