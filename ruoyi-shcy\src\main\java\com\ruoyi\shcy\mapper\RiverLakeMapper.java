package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.RiverLake;

/**
 * 河湖水体Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-03
 */
public interface RiverLakeMapper 
{
    /**
     * 查询河湖水体
     * 
     * @param id 河湖水体主键
     * @return 河湖水体
     */
    public RiverLake selectRiverLakeById(Long id);

    /**
     * 查询河湖水体列表
     * 
     * @param riverLake 河湖水体
     * @return 河湖水体集合
     */
    public List<RiverLake> selectRiverLakeList(RiverLake riverLake);

    /**
     * 新增河湖水体
     * 
     * @param riverLake 河湖水体
     * @return 结果
     */
    public int insertRiverLake(RiverLake riverLake);

    /**
     * 修改河湖水体
     * 
     * @param riverLake 河湖水体
     * @return 结果
     */
    public int updateRiverLake(RiverLake riverLake);

    /**
     * 删除河湖水体
     * 
     * @param id 河湖水体主键
     * @return 结果
     */
    public int deleteRiverLakeById(Long id);

    /**
     * 批量删除河湖水体
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRiverLakeByIds(Long[] ids);
}
