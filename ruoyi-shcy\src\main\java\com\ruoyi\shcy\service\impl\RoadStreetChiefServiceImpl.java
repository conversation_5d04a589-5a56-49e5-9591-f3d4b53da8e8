package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.RoadStreetChiefMapper;
import com.ruoyi.shcy.domain.RoadStreetChief;
import com.ruoyi.shcy.service.IRoadStreetChiefService;

/**
 * 街长路名Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-02-23
 */
@Service
public class RoadStreetChiefServiceImpl implements IRoadStreetChiefService 
{
    @Autowired
    private RoadStreetChiefMapper roadStreetChiefMapper;

    /**
     * 查询街长路名
     * 
     * @param id 街长路名主键
     * @return 街长路名
     */
    @Override
    public RoadStreetChief selectRoadStreetChiefById(Long id)
    {
        return roadStreetChiefMapper.selectRoadStreetChiefById(id);
    }

    /**
     * 查询街长路名列表
     * 
     * @param roadStreetChief 街长路名
     * @return 街长路名
     */
    @Override
    public List<RoadStreetChief> selectRoadStreetChiefList(RoadStreetChief roadStreetChief)
    {
        return roadStreetChiefMapper.selectRoadStreetChiefList(roadStreetChief);
    }

    /**
     * 新增街长路名
     * 
     * @param roadStreetChief 街长路名
     * @return 结果
     */
    @Override
    public int insertRoadStreetChief(RoadStreetChief roadStreetChief)
    {
        roadStreetChief.setCreateTime(DateUtils.getNowDate());
        return roadStreetChiefMapper.insertRoadStreetChief(roadStreetChief);
    }

    /**
     * 修改街长路名
     * 
     * @param roadStreetChief 街长路名
     * @return 结果
     */
    @Override
    public int updateRoadStreetChief(RoadStreetChief roadStreetChief)
    {
        roadStreetChief.setUpdateTime(DateUtils.getNowDate());
        return roadStreetChiefMapper.updateRoadStreetChief(roadStreetChief);
    }

    /**
     * 批量删除街长路名
     * 
     * @param ids 需要删除的街长路名主键
     * @return 结果
     */
    @Override
    public int deleteRoadStreetChiefByIds(Long[] ids)
    {
        return roadStreetChiefMapper.deleteRoadStreetChiefByIds(ids);
    }

    /**
     * 删除街长路名信息
     * 
     * @param id 街长路名主键
     * @return 结果
     */
    @Override
    public int deleteRoadStreetChiefById(Long id)
    {
        return roadStreetChiefMapper.deleteRoadStreetChiefById(id);
    }
}
