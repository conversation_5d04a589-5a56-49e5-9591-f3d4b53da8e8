package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.RegionsMapper;
import com.ruoyi.shcy.domain.Regions;
import com.ruoyi.shcy.service.IRegionsService;

/**
 * 区域信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-24
 */
@Service
public class RegionsServiceImpl implements IRegionsService 
{
    @Autowired
    private RegionsMapper regionsMapper;

    /**
     * 查询区域信息
     * 
     * @param id 区域信息主键
     * @return 区域信息
     */
    @Override
    public Regions selectRegionsById(Long id)
    {
        return regionsMapper.selectRegionsById(id);
    }

    /**
     * 查询区域信息列表
     * 
     * @param regions 区域信息
     * @return 区域信息
     */
    @Override
    public List<Regions> selectRegionsList(Regions regions)
    {
        return regionsMapper.selectRegionsList(regions);
    }

    /**
     * 新增区域信息
     * 
     * @param regions 区域信息
     * @return 结果
     */
    @Override
    public int insertRegions(Regions regions)
    {
        regions.setCreateTime(DateUtils.getNowDate());
        return regionsMapper.insertRegions(regions);
    }

    /**
     * 修改区域信息
     * 
     * @param regions 区域信息
     * @return 结果
     */
    @Override
    public int updateRegions(Regions regions)
    {
        regions.setUpdateTime(DateUtils.getNowDate());
        return regionsMapper.updateRegions(regions);
    }

    /**
     * 批量删除区域信息
     * 
     * @param ids 需要删除的区域信息主键
     * @return 结果
     */
    @Override
    public int deleteRegionsByIds(Long[] ids)
    {
        return regionsMapper.deleteRegionsByIds(ids);
    }

    /**
     * 删除区域信息信息
     * 
     * @param id 区域信息主键
     * @return 结果
     */
    @Override
    public int deleteRegionsById(Long id)
    {
        return regionsMapper.deleteRegionsById(id);
    }
}
