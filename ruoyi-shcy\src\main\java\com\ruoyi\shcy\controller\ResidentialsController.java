package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.shcy.domain.Committee;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.Residentials;
import com.ruoyi.shcy.service.IResidentialsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 小区信息Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/residentials")
public class ResidentialsController extends BaseController
{
    @Autowired
    private IResidentialsService residentialsService;

    /**
     * 查询小区信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentials:list')")
    @GetMapping("/list")
    public TableDataInfo list(Residentials residentials)
    {
        startPage();
        List<Residentials> list = residentialsService.selectResidentialsList(residentials);
        return getDataTable(list);
    }

    /**
     * 查询小区信息列表（不分页）
     * **/
    @GetMapping("/residentialslist")
    public AjaxResult residentialsList(Residentials residentials)
    {
        List<Residentials> list = residentialsService.selectResidentialsList(residentials);
        return AjaxResult.success(list);
    }

    /**
     * 导出小区信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentials:export')")
    @Log(title = "小区信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Residentials residentials)
    {
        List<Residentials> list = residentialsService.selectResidentialsList(residentials);
        ExcelUtil<Residentials> util = new ExcelUtil<Residentials>(Residentials.class);
        util.exportExcel(response, list, "小区信息数据");
    }

    /**
     * 获取小区信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentials:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(residentialsService.selectResidentialsById(id));
    }

    /**
     * 新增小区信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentials:add')")
    @Log(title = "小区信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Residentials residentials)
    {
        return toAjax(residentialsService.insertResidentials(residentials));
    }

    /**
     * 修改小区信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentials:edit')")
    @Log(title = "小区信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Residentials residentials)
    {
        return toAjax(residentialsService.updateResidentials(residentials));
    }

    /**
     * 删除小区信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:residentials:remove')")
    @Log(title = "小区信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(residentialsService.deleteResidentialsByIds(ids));
    }
}
