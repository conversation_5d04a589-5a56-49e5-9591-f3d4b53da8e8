package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.NbiotyunDevice;
import com.ruoyi.shcy.service.INbiotyunDeviceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 设备管理Controller
 * 
 * <AUTHOR>
 * @date 2023-09-01
 */
@RestController
@RequestMapping("/shcy/nbiotyunDevice")
public class NbiotyunDeviceController extends BaseController
{
    @Autowired
    private INbiotyunDeviceService nbiotyunDeviceService;

    /**
     * 查询设备管理列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:nbiotyunDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(NbiotyunDevice nbiotyunDevice)
    {
        startPage();
        List<NbiotyunDevice> list = nbiotyunDeviceService.selectNbiotyunDeviceList(nbiotyunDevice);
        return getDataTable(list);
    }

    /**
     * 导出设备管理列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:nbiotyunDevice:export')")
    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NbiotyunDevice nbiotyunDevice)
    {
        List<NbiotyunDevice> list = nbiotyunDeviceService.selectNbiotyunDeviceList(nbiotyunDevice);
        ExcelUtil<NbiotyunDevice> util = new ExcelUtil<NbiotyunDevice>(NbiotyunDevice.class);
        util.exportExcel(response, list, "设备管理数据");
    }

    /**
     * 获取设备管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:nbiotyunDevice:query')")
    @GetMapping(value = "/{deviceId}")
    public AjaxResult getInfo(@PathVariable("deviceId") Long deviceId)
    {
        return AjaxResult.success(nbiotyunDeviceService.selectNbiotyunDeviceByDeviceId(deviceId));
    }

    /**
     * 新增设备管理
     */
    @PreAuthorize("@ss.hasPermi('shcy:nbiotyunDevice:add')")
    @Log(title = "设备管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NbiotyunDevice nbiotyunDevice)
    {
        return toAjax(nbiotyunDeviceService.insertNbiotyunDevice(nbiotyunDevice));
    }

    /**
     * 修改设备管理
     */
    @PreAuthorize("@ss.hasPermi('shcy:nbiotyunDevice:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NbiotyunDevice nbiotyunDevice)
    {
        return toAjax(nbiotyunDeviceService.updateNbiotyunDevice(nbiotyunDevice));
    }

    /**
     * 删除设备管理
     */
    @PreAuthorize("@ss.hasPermi('shcy:nbiotyunDevice:remove')")
    @Log(title = "设备管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{deviceIds}")
    public AjaxResult remove(@PathVariable Long[] deviceIds)
    {
        return toAjax(nbiotyunDeviceService.deleteNbiotyunDeviceByDeviceIds(deviceIds));
    }
}
