package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.IccDevice;

import java.util.List;

/**
 * icc监控设备Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
public interface IccDeviceMapper 
{
    /**
     * 查询icc监控设备
     * 
     * @param id icc监控设备主键
     * @return icc监控设备
     */
    public IccDevice selectIccDeviceById(Long id);

    /**
     * 查询icc监控设备列表
     * 
     * @param iccDevice icc监控设备
     * @return icc监控设备集合
     */
    public List<IccDevice> selectIccDeviceList(IccDevice iccDevice);

    /**
     * 新增icc监控设备
     * 
     * @param iccDevice icc监控设备
     * @return 结果
     */
    public int insertIccDevice(IccDevice iccDevice);

    /**
     * 修改icc监控设备
     * 
     * @param iccDevice icc监控设备
     * @return 结果
     */
    public int updateIccDevice(IccDevice iccDevice);

    /**
     * 删除icc监控设备
     * 
     * @param id icc监控设备主键
     * @return 结果
     */
    public int deleteIccDeviceById(Long id);

    /**
     * 批量删除icc监控设备
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIccDeviceByIds(Long[] ids);

    List<String> selectHjzzAddressList();
}
