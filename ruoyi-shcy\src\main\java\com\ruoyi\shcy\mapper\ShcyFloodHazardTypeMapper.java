package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyFloodHazardType;

/**
 * 隐患排查类型Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
public interface ShcyFloodHazardTypeMapper
{
    /**
     * 查询隐患排查类型
     *
     * @param id 隐患排查类型主键
     * @return 隐患排查类型
     */
    public ShcyFloodHazardType selectShcyFloodHazardTypeById(Long id);

    /**
     * 查询隐患排查类型列表
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 隐患排查类型集合
     */
    public List<ShcyFloodHazardType> selectShcyFloodHazardTypeList(ShcyFloodHazardType shcyFloodHazardType);

    /**
     * 新增隐患排查类型
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 结果
     */
    public int insertShcyFloodHazardType(ShcyFloodHazardType shcyFloodHazardType);

    /**
     * 修改隐患排查类型
     *
     * @param shcyFloodHazardType 隐患排查类型
     * @return 结果
     */
    public int updateShcyFloodHazardType(ShcyFloodHazardType shcyFloodHazardType);

    /**
     * 删除隐患排查类型
     *
     * @param id 隐患排查类型主键
     * @return 结果
     */
    public int deleteShcyFloodHazardTypeById(Long id);

    /**
     * 批量删除隐患排查类型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyFloodHazardTypeByIds(Long[] ids);

    List<ShcyFloodHazardType> selectChildrenTypeById(Long id);

    void updateTypeChildren(List<ShcyFloodHazardType> children);

    int hasChildByTypeId(Long id);

    List<ShcyFloodHazardType> selectShcyFloodHazardTypeByParentId(long parseLong);
}
