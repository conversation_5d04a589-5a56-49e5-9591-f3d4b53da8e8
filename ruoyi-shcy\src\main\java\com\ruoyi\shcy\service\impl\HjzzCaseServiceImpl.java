package com.ruoyi.shcy.service.impl;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.dahuatech.icc.exception.ClientException;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FfmpegUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.icc.video.VideoService;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.constant.HjzzConstants;
import com.ruoyi.shcy.domain.*;
import com.ruoyi.shcy.domain.vo.ChartDataVO;
import com.ruoyi.shcy.domain.vo.HjzzCaseCountVO;
import com.ruoyi.shcy.domain.vo.ViolationDataVO;
import com.ruoyi.shcy.mapper.HjzzCaseMapper;
import com.ruoyi.shcy.mapper.IccAlarmRecordMapper;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.service.IHjzzCaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 环境整治案事件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-08
 */
@Service
@Slf4j
public class HjzzCaseServiceImpl implements IHjzzCaseService
{
    @Autowired
    private HjzzCaseMapper hjzzCaseMapper;

    @Autowired
    private IccAlarmRecordMapper iccAlarmRecordMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    private VideoService videoService;

    @Autowired
    private IccService iccService;

    private final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    {
        try {
            videoService = new VideoService();
        } catch (ClientException e) {
            log.error("初始化客户端失败", e);
            videoService = null;
        }
    }

    /**
     * 查询环境整治案事件
     *
     * @param id 环境整治案事件主键
     * @return 环境整治案事件
     */
    @Override
    public HjzzCase selectHjzzCaseById(Long id)
    {
        return handlePhoto(hjzzCaseMapper.selectHjzzCaseById(id));
    }

    /**
     * 查询环境整治案事件列表
     *
     * @param hjzzCase 环境整治案事件
     * @return 环境整治案事件
     */
    @Override
    public List<HjzzCase> selectHjzzCaseList(HjzzCase hjzzCase)
    {
        List<HjzzCase> hjzzCaseList = hjzzCaseMapper.selectHjzzCaseList(hjzzCase);
        for(HjzzCase oldHjzzCase : hjzzCaseList){
            handlePhoto(oldHjzzCase);
        }
        return hjzzCaseList;
    }

    /**
     * 新增环境整治案事件
     *
     * @param hjzzCase 环境整治案事件
     * @return 结果
     */
    @Override
    public int insertHjzzCase(HjzzCase hjzzCase)
    {
        hjzzCase.setCreateTime(DateUtils.getNowDate());
        return hjzzCaseMapper.insertHjzzCase(hjzzCase);
    }

    /**
     * 修改环境整治案事件
     *
     * @param hjzzCase 环境整治案事件
     * @return 结果
     */
    @Override
    public int updateHjzzCase(HjzzCase hjzzCase)
    {
        hjzzCase.setUpdateTime(DateUtils.getNowDate());
        return hjzzCaseMapper.updateHjzzCase(hjzzCase);
    }

    /**
     * 批量删除环境整治案事件
     *
     * @param ids 需要删除的环境整治案事件主键
     * @return 结果
     */
    @Override
    public int deleteHjzzCaseByIds(Long[] ids)
    {
        return hjzzCaseMapper.deleteHjzzCaseByIds(ids);
    }

    /**
     * 删除环境整治案事件信息
     *
     * @param id 环境整治案事件主键
     * @return 结果
     */
    @Override
    public int deleteHjzzCaseById(Long id)
    {
        return hjzzCaseMapper.deleteHjzzCaseById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int handleHjzzCase(HjzzCase hjzzCase) {
        IccAlarmRecord iccAlarmRecord = iccAlarmRecordMapper.selectIccAlarmRecordById(hjzzCase.getAlarmRecordId());
        iccAlarmRecord.setStatus(HjzzConstants.PROCESSED);
        iccAlarmRecordMapper.updateIccAlarmRecord(iccAlarmRecord);

        String alarmTypeName = iccAlarmRecord.getAlarmTypeName();

        // 更新案件状态 - 0:已完成 1:待处理 2:处理中 3:作废 4:退回

        if (Objects.equals(alarmTypeName, HjzzConstants.ALARM_TYPE_NAME_TDLJ)) {
            hjzzCase.setCirculationState(HjzzConstants.CIRCULATION_STATE_PROCESSING);
        } else if (Objects.equals(alarmTypeName, HjzzConstants.ALARM_TYPE_NAME_WGSC)) {
            hjzzCase.setCirculationState(HjzzConstants.CIRCULATION_STATE_FINISHED);
        }
        // 现场处置人
        hjzzCase.setCaseDealBy(SecurityUtils.getLoginUser().getUsername());
        // 现场处置时间
        hjzzCase.setCaseFinishTime(DateUtils.getNowDate());
        // 现场处置按时完成状态
        if (DateUtils.getNowDate().compareTo(hjzzCase.getCaseEndTime()) < 0) {
            hjzzCase.setDealInTimeState(HjzzConstants.OVERTIME_STATE_NORMAL);
        } else {
            hjzzCase.setDealInTimeState(HjzzConstants.OVERTIME_STATE_OVERTIME);
        }
        return hjzzCaseMapper.updateHjzzCase(hjzzCase);
    }

    @Override
    public int punishHjzzCase(HjzzCase hjzzCase) {
        hjzzCase.setCirculationState("0");
        // 查处完成时间
        hjzzCase.setInvestigationCompleteTime(DateUtils.getNowDate());
        // 查处完成状态
        HjzzCase hc = hjzzCaseMapper.selectHjzzCaseById(hjzzCase.getId());
        if (hc.getInvestigationDeadline() == null) {
            throw new RuntimeException("案件未设置查处截止时间");
        }
        if (DateUtils.getNowDate().compareTo(hc.getInvestigationDeadline()) < 0) {
            hjzzCase.setInvestigationCompleteState(HjzzConstants.OVERTIME_STATE_NORMAL);
        } else {
            hjzzCase.setInvestigationCompleteState(HjzzConstants.OVERTIME_STATE_OVERTIME);
        }
        // 查处人
        hjzzCase.setCheckUpdateBy(SecurityUtils.getLoginUser().getUsername());
        hjzzCase.setCheckUpdateTime(DateUtils.getNowDate());
        return hjzzCaseMapper.updateHjzzCase(hjzzCase);
    }

    @Override
    public List<HjzzCase> selectHandleList(HjzzCase hjzzCase) {
        return hjzzCaseMapper.selectHandleList(hjzzCase);
    }

    @Override
    public List<HjzzCase> selectHistoryList(HjzzCase hjzzCase) {
        return hjzzCaseMapper.selectHistoryList(hjzzCase);
    }

    @Override
    public long getPendingCaseCount() {
        return hjzzCaseMapper.getPendingCaseCount();
    }

    @Override
    public long getProcessingCaseCount() {
        return hjzzCaseMapper.getProcessingCaseCount();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int invalidateCase(HjzzCase hjzzCase) {
        IccAlarmRecord iccAlarmRecord = iccAlarmRecordMapper.selectIccAlarmRecordById(hjzzCase.getAlarmRecordId());
        iccAlarmRecord.setStatus(HjzzConstants.FALSE_ALARM);
        iccAlarmRecordMapper.updateIccAlarmRecord(iccAlarmRecord);
        hjzzCase.setCirculationState("3");
        return hjzzCaseMapper.updateHjzzCase(hjzzCase);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int regressionCase(HjzzCase hjzzCase) {
        IccAlarmRecord iccAlarmRecord = iccAlarmRecordMapper.selectIccAlarmRecordById(hjzzCase.getAlarmRecordId());
        iccAlarmRecord.setStatus("");
        iccAlarmRecordMapper.updateIccAlarmRecord(iccAlarmRecord);
        hjzzCase.setCirculationState("4");
        return hjzzCaseMapper.updateHjzzCase(hjzzCase);
    }

    @Override
    public long getCaseCount(HjzzCase hjzzCase) {
        return hjzzCaseMapper.getCaseCount(hjzzCase);
    }

    @Override
    public List<ChartDataVO> getDumpingGarbageTypeCount() {
        List<HjzzCase> hjzzCases = hjzzCaseMapper.selectCompletedList(new HjzzCase());
        List<ChartDataVO> chartDataVOS = new ArrayList<>();
        List<String> dumpingGarbageTypes = new ArrayList<>();
        dumpingGarbageTypes.add("生活垃圾");
        dumpingGarbageTypes.add("建筑垃圾、渣土");
        for (String dumpingGarbageType : dumpingGarbageTypes) {
            ChartDataVO chartDataVO = new ChartDataVO();
            chartDataVO.setName(dumpingGarbageType);
            chartDataVO.setValue(hjzzCases.stream().filter(hjzzCase -> dumpingGarbageType.equals(hjzzCase.getDumpingGarbageType())).count());
            chartDataVOS.add(chartDataVO);
        }
        return chartDataVOS;
    }

    @Override
    public List<ViolationDataVO> getViolations(HjzzCase hjzzCase) {
        return hjzzCaseMapper.getViolations(hjzzCase);
    }

    @Override
    public HjzzCaseCountVO countHjzzCase(HjzzCase hjzzCase) {
        // 查询时间段内的处置情况
        List<HjzzCase> hjzzCases = hjzzCaseMapper.selectHjzzCaseCount(hjzzCase);
        // 有效总数: 除了退回的案件
        int currentTotal = (int) hjzzCases.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).count();
        // 作废数量
        int currentDiscarded = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_INVALID.equals(hjzzCase1.getCirculationState())).count();
        // 使用stream流过滤出circulationState为1和2的案件数量
        int currentInProcess = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_UNPROCESSED.equals(hjzzCase1.getCirculationState()) || HjzzConstants.CIRCULATION_STATE_PROCESSING.equals(hjzzCase1.getCirculationState())).count();
        // 使用stream流过滤出circulationState为0的案件数量
        int currentCompleted = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState())).count();
        // 口头教育数量
        int currentKtjy = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && "否".equals(hjzzCase1.getIsFiling())).count();
        // 立案查处数量
        int currentLacc = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && !"否".equals(hjzzCase1.getIsFiling())).count();
        // 结案率 = 已结案数量 / (总数量 - 作废数量)
        double currentClosureRate = calculateClosureRate(currentTotal, currentDiscarded, currentCompleted);
        // 本月处置情况
        Map<String, Object> monthlyParams = new HashMap<String, Object>() {
            {
                put("beginTime", DateUtil.format(DateUtil.beginOfMonth(new Date()), DatePattern.NORM_DATE_PATTERN));
                put("endTime", DateUtil.format(DateUtil.endOfMonth(new Date()), DatePattern.NORM_DATE_PATTERN));
            }
        };
        HjzzCase monthlyHjzzCase = new HjzzCase();
        monthlyHjzzCase.setCaseType("1");
        monthlyHjzzCase.setParams(monthlyParams);
        List<HjzzCase> monthlyHjzzCases = hjzzCaseMapper.selectHjzzCaseCount(monthlyHjzzCase);
        int monthlyTotal = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).count();
        // 作废数量
        int monthlyDiscarded = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_INVALID.equals(hjzzCase1.getCirculationState())).count();
        int monthlyInProcess = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_UNPROCESSED.equals(hjzzCase1.getCirculationState()) || HjzzConstants.CIRCULATION_STATE_PROCESSING.equals(hjzzCase1.getCirculationState())).count();
        int monthlyCompleted = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState())).count();
        // 口头教育数量
        int monthlyKtjy = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && "否".equals(hjzzCase1.getIsFiling())).count();
        // 立案查处数量
        int monthlyLacc = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && !"否".equals(hjzzCase1.getIsFiling())).count();
        double monthlyClosureRate = calculateClosureRate(monthlyTotal, monthlyDiscarded, monthlyCompleted);
        // 上月处置情况
        Map<String, Object> lastMonthParams = new HashMap<String, Object>() {
            {
                put("beginTime", DateUtil.format(DateUtil.beginOfMonth(DateUtil.lastMonth()), DatePattern.NORM_DATE_PATTERN));
                put("endTime", DateUtil.format(DateUtil.endOfMonth(DateUtil.lastMonth()), DatePattern.NORM_DATE_PATTERN));
            }
        };
        HjzzCase lastMonthHjzzCase = new HjzzCase();
        lastMonthHjzzCase.setCaseType("1");
        lastMonthHjzzCase.setParams(lastMonthParams);
        List<HjzzCase> lastMonthHjzzCases = hjzzCaseMapper.selectHjzzCaseCount(lastMonthHjzzCase);
        int lastMonthTotal = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).count();
        // 作废数量
        int lastMonthDiscarded = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_INVALID.equals(hjzzCase1.getCirculationState())).count();
        int lastMonthInProcess = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_UNPROCESSED.equals(hjzzCase1.getCirculationState()) || HjzzConstants.CIRCULATION_STATE_PROCESSING.equals(hjzzCase1.getCirculationState())).count();
        int lastMonthCompleted = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState())).count();
        // 口头教育数量
        int lastMonthKtjy = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && "否".equals(hjzzCase1.getIsFiling())).count();
        // 立案查处数量
        int lastMonthLacc = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && !"否".equals(hjzzCase1.getIsFiling())).count();
        double lastMonthClosureRate = calculateClosureRate(lastMonthTotal, lastMonthDiscarded, lastMonthCompleted);
        HjzzCaseCountVO hjzzCaseCountVO = new HjzzCaseCountVO();
        hjzzCaseCountVO.setCurrentTotal(currentTotal);
        hjzzCaseCountVO.setCurrentDiscarded(currentDiscarded);
        hjzzCaseCountVO.setCurrentInProcess(currentInProcess);
        hjzzCaseCountVO.setCurrentCompleted(currentCompleted);
        hjzzCaseCountVO.setCurrentKtjy(currentKtjy);
        hjzzCaseCountVO.setCurrentLacc(currentLacc);
        hjzzCaseCountVO.setCurrentClosureRate(currentClosureRate);
        hjzzCaseCountVO.setMonthlyTotal(monthlyTotal);
        hjzzCaseCountVO.setMonthlyDiscarded(monthlyDiscarded);
        hjzzCaseCountVO.setMonthlyInProcess(monthlyInProcess);
        hjzzCaseCountVO.setMonthlyCompleted(monthlyCompleted);
        hjzzCaseCountVO.setMonthlyKtjy(monthlyKtjy);
        hjzzCaseCountVO.setMonthlyLacc(monthlyLacc);
        hjzzCaseCountVO.setMonthlyClosureRate(monthlyClosureRate);
        hjzzCaseCountVO.setLastMonthTotal(lastMonthTotal);
        hjzzCaseCountVO.setLastMonthDiscarded(lastMonthDiscarded);
        hjzzCaseCountVO.setLastMonthInProcess(lastMonthInProcess);
        hjzzCaseCountVO.setLastMonthCompleted(lastMonthCompleted);
        hjzzCaseCountVO.setLastMonthKtjy(lastMonthKtjy);
        hjzzCaseCountVO.setLastMonthLacc(lastMonthLacc);
        hjzzCaseCountVO.setLastMonthClosureRate(lastMonthClosureRate);
        // 将hjzzCases使用stream流过滤掉circulationState为4的数据，生成新的List
        List<HjzzCase> newList = hjzzCases.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).collect(java.util.stream.Collectors.toList());
        // hjzzCases根据circulationState重新排序，按照已完成—处理中—待处理—作废—退回(0、2、1、3、4)排序
        hjzzCaseCountVO.setHjzzCaseList(sortList(newList));
        return hjzzCaseCountVO;
    }

    @Override
    public int convertCase(HjzzCase hjzzCase) {
        Long alarmRecordId = hjzzCase.getAlarmRecordId();
        IccAlarmRecord alarmRecord = iccAlarmRecordMapper.selectIccAlarmRecordById(alarmRecordId);
        // ffmpeg视频转换
        String m3u8Url = getM3u8Url(alarmRecord.getAlarmCode());
        String mp4Path = RuoYiConfig.getProfile() + "/hjzz/" + hjzzCase.getCaseNumber() + ".mp4";
        StringBuilder command = new StringBuilder();
        command.append("ffmpeg -i ")
                .append(m3u8Url)
                .append(" -vcodec copy -acodec copy -absf aac_adtstoasc ")
                .append(mp4Path)
                .append(" -y");
        System.out.println(command.toString());
        // try {
        //     Runtime.getRuntime().exec(command.toString());
        // } catch (IOException e) {
        //     throw new RuntimeException(e);
        // }

        String[] cmdLine = {"cmd", "/c", command.toString()};
        // FfmpegUtils.useCmd(cmdLine);
        CompletableFuture.runAsync(() -> {
            FfmpegUtils.useCmd(cmdLine);
        });
        return 1;
    }

    @Override
    public List<HjzzCase> selectHjzzCaseWgList(HjzzCase hjzzCase) {
        return hjzzCaseMapper.selectHjzzCaseWgList(hjzzCase);
    }

    @Override
    public HjzzCaseCountVO countHjzzCaseHistory(HjzzCase hjzzCase, String yearMonth) {
        // 将yearMonth转换为Date
        Date date = DateUtil.parse(yearMonth, "yyyy-MM");
        // 将yearMonth转换为上个月的Date
        Date lastMonthDate = DateUtil.offsetMonth(date, -1);
        // 查询时间段内的处置情况
        List<HjzzCase> hjzzCases = hjzzCaseMapper.selectHjzzCaseCount(hjzzCase);
        // 有效总数: 除了退回的案件
        int currentTotal = (int) hjzzCases.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).count();
        // 作废数量
        int currentDiscarded = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_INVALID.equals(hjzzCase1.getCirculationState())).count();
        // 使用stream流过滤出circulationState为1和2的案件数量
        int currentInProcess = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_UNPROCESSED.equals(hjzzCase1.getCirculationState()) || HjzzConstants.CIRCULATION_STATE_PROCESSING.equals(hjzzCase1.getCirculationState())).count();
        // 使用stream流过滤出circulationState为0的案件数量
        int currentCompleted = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState())).count();
        // 口头教育数量
        int currentKtjy = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && "否".equals(hjzzCase1.getIsFiling())).count();
        // 立案查处数量
        int currentLacc = (int) hjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && !"否".equals(hjzzCase1.getIsFiling())).count();
        // 结案率 = 已结案数量 / (总数量 - 作废数量)
        double currentClosureRate = calculateClosureRate(currentTotal, currentDiscarded, currentCompleted);
        // 本月处置情况
        Map<String, Object> monthlyParams = new HashMap<String, Object>() {
            {
                put("beginTime", DateUtil.format(DateUtil.beginOfMonth(date), DatePattern.NORM_DATE_PATTERN));
                put("endTime", DateUtil.format(DateUtil.endOfMonth(date), DatePattern.NORM_DATE_PATTERN));
            }
        };
        HjzzCase monthlyHjzzCase = new HjzzCase();
        monthlyHjzzCase.setCaseType("1");
        monthlyHjzzCase.setParams(monthlyParams);
        List<HjzzCase> monthlyHjzzCases = hjzzCaseMapper.selectHjzzCaseCount(monthlyHjzzCase);
        int monthlyTotal = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).count();
        // 作废数量
        int monthlyDiscarded = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_INVALID.equals(hjzzCase1.getCirculationState())).count();
        int monthlyInProcess = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_UNPROCESSED.equals(hjzzCase1.getCirculationState()) || HjzzConstants.CIRCULATION_STATE_PROCESSING.equals(hjzzCase1.getCirculationState())).count();
        int monthlyCompleted = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState())).count();
        // 口头教育数量
        int monthlyKtjy = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && "否".equals(hjzzCase1.getIsFiling())).count();
        // 立案查处数量
        int monthlyLacc = (int) monthlyHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && !"否".equals(hjzzCase1.getIsFiling())).count();
        double monthlyClosureRate = calculateClosureRate(monthlyTotal, monthlyDiscarded, monthlyCompleted);
        // 上月处置情况
        Map<String, Object> lastMonthParams = new HashMap<String, Object>() {
            {
                put("beginTime", DateUtil.format(DateUtil.beginOfMonth(lastMonthDate), DatePattern.NORM_DATE_PATTERN));
                put("endTime", DateUtil.format(DateUtil.endOfMonth(lastMonthDate), DatePattern.NORM_DATE_PATTERN));
            }
        };
        HjzzCase lastMonthHjzzCase = new HjzzCase();
        lastMonthHjzzCase.setCaseType("1");
        lastMonthHjzzCase.setParams(lastMonthParams);
        List<HjzzCase> lastMonthHjzzCases = hjzzCaseMapper.selectHjzzCaseCount(lastMonthHjzzCase);
        int lastMonthTotal = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).count();
        // 作废数量
        int lastMonthDiscarded = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_INVALID.equals(hjzzCase1.getCirculationState())).count();
        int lastMonthInProcess = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_UNPROCESSED.equals(hjzzCase1.getCirculationState()) || HjzzConstants.CIRCULATION_STATE_PROCESSING.equals(hjzzCase1.getCirculationState())).count();
        int lastMonthCompleted = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState())).count();
        // 口头教育数量
        int lastMonthKtjy = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && "否".equals(hjzzCase1.getIsFiling())).count();
        // 立案查处数量
        int lastMonthLacc = (int) lastMonthHjzzCases.stream().filter(hjzzCase1 -> HjzzConstants.CIRCULATION_STATE_FINISHED.equals(hjzzCase1.getCirculationState()) && !"否".equals(hjzzCase1.getIsFiling())).count();
        double lastMonthClosureRate = calculateClosureRate(lastMonthTotal, lastMonthDiscarded, lastMonthCompleted);
        HjzzCaseCountVO hjzzCaseCountVO = new HjzzCaseCountVO();
        hjzzCaseCountVO.setCurrentTotal(currentTotal);
        hjzzCaseCountVO.setCurrentDiscarded(currentDiscarded);
        hjzzCaseCountVO.setCurrentInProcess(currentInProcess);
        hjzzCaseCountVO.setCurrentCompleted(currentCompleted);
        hjzzCaseCountVO.setCurrentKtjy(currentKtjy);
        hjzzCaseCountVO.setCurrentLacc(currentLacc);
        hjzzCaseCountVO.setCurrentClosureRate(currentClosureRate);
        hjzzCaseCountVO.setMonthlyTotal(monthlyTotal);
        hjzzCaseCountVO.setMonthlyDiscarded(monthlyDiscarded);
        hjzzCaseCountVO.setMonthlyInProcess(monthlyInProcess);
        hjzzCaseCountVO.setMonthlyCompleted(monthlyCompleted);
        hjzzCaseCountVO.setMonthlyKtjy(monthlyKtjy);
        hjzzCaseCountVO.setMonthlyLacc(monthlyLacc);
        hjzzCaseCountVO.setMonthlyClosureRate(monthlyClosureRate);
        hjzzCaseCountVO.setLastMonthTotal(lastMonthTotal);
        hjzzCaseCountVO.setLastMonthDiscarded(lastMonthDiscarded);
        hjzzCaseCountVO.setLastMonthInProcess(lastMonthInProcess);
        hjzzCaseCountVO.setLastMonthCompleted(lastMonthCompleted);
        hjzzCaseCountVO.setLastMonthKtjy(lastMonthKtjy);
        hjzzCaseCountVO.setLastMonthLacc(lastMonthLacc);
        hjzzCaseCountVO.setLastMonthClosureRate(lastMonthClosureRate);
        // 将hjzzCases使用stream流过滤掉circulationState为4的数据，生成新的List
        List<HjzzCase> newList = hjzzCases.stream().filter(hjzzCase1 -> !HjzzConstants.CIRCULATION_STATE_RETURN.equals(hjzzCase1.getCirculationState())).collect(java.util.stream.Collectors.toList());
        // hjzzCases根据circulationState重新排序，按照已完成—处理中—待处理—作废—退回(0、2、1、3、4)排序
        hjzzCaseCountVO.setHjzzCaseList(sortList(newList));
        return hjzzCaseCountVO;
    }

    private List<HjzzCase> sortList(List<HjzzCase> hjzzCases) {
        // 使用Collections.sort方法并提供自定义Comparator
        Collections.sort(hjzzCases, new Comparator<HjzzCase>() {
            @Override
            public int compare(HjzzCase case1, HjzzCase case2) {
                // 根据circulationState的值比较
                int order1 = getOrder(Integer.valueOf(case1.getCirculationState()));
                int order2 = getOrder(Integer.valueOf(case2.getCirculationState()));

                // 返回比较结果
                return Integer.compare(order1, order2);
            }

            private int getOrder(int circulationState) {
                // 根据指定顺序返回相应的排序值
                switch (circulationState) {
                    case 0:
                        return 0;
                    case 2:
                        return 1;
                    case 1:
                        return 2;
                    case 3:
                        return 3;
                    case 4:
                        return 4;
                    default:
                        return Integer.MAX_VALUE; // 处理未知值的情况
                }
            }
        });
        return hjzzCases;
    }

    public HjzzCase handlePhoto(HjzzCase hjzzCase) {
        if (StrUtil.isNotEmpty(hjzzCase.getCheckPhoto())) {
            List<String> photoUrls = new ArrayList<>();
            String[] fileIds = hjzzCase.getCheckPhoto().split(",");
            for (String fileId : fileIds) {
                ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
                if(shcyFileInfo != null) {
                    photoUrls.add(shcyFileInfo.getFilePath());
                }
            }
            hjzzCase.setPhotoUrls(photoUrls);
        }
        return hjzzCase;
    }

    private String getM3u8Url(String alarmCode) {
        String m3u8Url = "";
        try {
            // JsonNode records = videoService.getAlarmRecords(alarmCode);
            // JsonNode videoRecord = records.get(0);
            // String channelId = videoRecord.get("channelId").asText();
            // String recordSource = videoRecord.get("recordSource").asText();
            // String startTime = videoRecord.get("startTime").asText();
            // String endTime = videoRecord.get("endTime").asText();
            // startTime = simpleDateFormat.format(new Date(Long.parseLong(startTime) * 1000));
            // endTime = simpleDateFormat.format(new Date(Long.parseLong(endTime) * 1000));
            // String url = videoService.replay(channelId, "1", "hls", recordSource, "2", startTime, endTime);
            String url = videoService.getAlarmVideoPlayback(alarmCode);
            String accessToken = iccService.getAccessToken();
            m3u8Url = url + "?token=" + accessToken;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return m3u8Url;
    }

    /**
     * 计算结案率
     *
     * @param total     总数
     * @param discarded 作废数量
     * @param completed 已完成数量
     * @return 结案率
     */
    public double calculateClosureRate(int total, int discarded, int completed) {
        return (total - discarded) == 0 ? 0.0D : (double) completed / (total - discarded);
    }

    @Override
    public List<HjzzCase> selectHjzzCaseCount(HjzzCase hjzzCase) {
        return hjzzCaseMapper.selectHjzzCaseCount(hjzzCase);
    }

    @Override
    public List<ChartDataVO> getIsFilingCount() {
        List<HjzzCase> hjzzCases = hjzzCaseMapper.selectCompletedList(new HjzzCase());
        List<ChartDataVO> chartDataVOS = new ArrayList<>();

        // 口头教育的 判断依据 HjzzCase中isFiling为否的合计
        int ktjyCount = (int) hjzzCases.stream()
                .filter(hjzzCase -> "生活垃圾".equals(hjzzCase.getDumpingGarbageType()))
                .filter(hjzzCase -> "否".equals(hjzzCase.getIsFiling())).count();
        ChartDataVO chartDataVO = new ChartDataVO();
        chartDataVO.setName("口头教育");
        chartDataVO.setValue(ktjyCount);
        chartDataVOS.add(chartDataVO);

        // 立案查处
        int lacdCount = (int) hjzzCases.stream()
                .filter(hjzzCase -> "生活垃圾".equals(hjzzCase.getDumpingGarbageType()))
                .filter(hjzzCase -> !"否".equals(hjzzCase.getIsFiling())).count();
        ChartDataVO chartDataVO2 = new ChartDataVO();
        chartDataVO2.setName("立案查处");
        chartDataVO2.setValue(lacdCount);
        chartDataVOS.add(chartDataVO2);

        return chartDataVOS;
    }
}
