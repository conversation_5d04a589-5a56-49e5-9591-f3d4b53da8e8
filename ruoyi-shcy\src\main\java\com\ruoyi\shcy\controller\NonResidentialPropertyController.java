package com.ruoyi.shcy.controller;

import java.util.Collections;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.NonResidentialProperty;
import com.ruoyi.shcy.service.INonResidentialPropertyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import static org.apache.commons.lang3.StringUtils.join;

/**
 * 物业管理处Controller
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/nonResidentialProperty")
public class NonResidentialPropertyController extends BaseController
{
    @Autowired
    private INonResidentialPropertyService nonResidentialPropertyService;

    /**
     * 查询物业管理处列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:nonResidentialProperty:list')")
    @GetMapping("/list")
    public TableDataInfo list(NonResidentialProperty nonResidentialProperty)
    {
        startPage();
        List<NonResidentialProperty> list = nonResidentialPropertyService.selectNonResidentialPropertyList(nonResidentialProperty);
        return getDataTable(list);
    }

    /**
     * 导出物业管理处列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:nonResidentialProperty:export')")
    @Log(title = "非住宅物业", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NonResidentialProperty nonResidentialProperty)
    {
        List<NonResidentialProperty> list = nonResidentialPropertyService.selectNonResidentialPropertyList(nonResidentialProperty);
        ExcelUtil<NonResidentialProperty> util = new ExcelUtil<NonResidentialProperty>(NonResidentialProperty.class);
        util.exportExcel(response, list, "非住宅物业数据");
    }

    /**
     * 获取物业管理处详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:nonResidentialProperty:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(nonResidentialPropertyService.selectNonResidentialPropertyById(id));
    }

    /**
     * 新增物业管理处
     */
    @PreAuthorize("@ss.hasPermi('shcy:nonResidentialProperty:add')")
    @Log(title = "非住宅物业", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NonResidentialProperty nonResidentialProperty)
    {


//        if(nonResidentialProperty.getResidentialId()!=null){
//            String[] depts = nonResidentialProperty.getResidentialId().split(",");
//
//            nonResidentialProperty.setResidentialId(join(depts, ","));
//        }
        return toAjax(nonResidentialPropertyService.insertNonResidentialProperty(nonResidentialProperty));
    }

    /**
     * 修改物业管理处
     */
    @PreAuthorize("@ss.hasPermi('shcy:nonResidentialProperty:edit')")
    @Log(title = "非住宅物业", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NonResidentialProperty nonResidentialProperty)
    {
//        System.out.println(nonResidentialProperty.getResidentialId());
//        if(nonResidentialProperty.getResidentialId()!=null){
//            String[] depts = nonResidentialProperty.getResidentialId().split(",");
//
//            nonResidentialProperty.setResidentialId(join(depts, ","));
//        }
        return toAjax(nonResidentialPropertyService.updateNonResidentialProperty(nonResidentialProperty));
    }

    /**
     * 删除物业管理处
     */
    @PreAuthorize("@ss.hasPermi('shcy:nonResidentialProperty:remove')")
    @Log(title = "非住宅物业", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(nonResidentialPropertyService.deleteNonResidentialPropertyByIds(ids));
    }
}
