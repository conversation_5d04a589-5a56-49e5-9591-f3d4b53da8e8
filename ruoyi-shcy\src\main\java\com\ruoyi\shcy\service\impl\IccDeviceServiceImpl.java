package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.IccDevice;
import com.ruoyi.shcy.mapper.IccDeviceMapper;
import com.ruoyi.shcy.service.IIccDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * icc监控设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
@Service
public class IccDeviceServiceImpl implements IIccDeviceService 
{
    @Autowired
    private IccDeviceMapper iccDeviceMapper;

    /**
     * 查询icc监控设备
     * 
     * @param id icc监控设备主键
     * @return icc监控设备
     */
    @Override
    public IccDevice selectIccDeviceById(Long id)
    {
        return iccDeviceMapper.selectIccDeviceById(id);
    }

    /**
     * 查询icc监控设备列表
     * 
     * @param iccDevice icc监控设备
     * @return icc监控设备
     */
    @Override
    public List<IccDevice> selectIccDeviceList(IccDevice iccDevice)
    {
        return iccDeviceMapper.selectIccDeviceList(iccDevice);
    }

    /**
     * 新增icc监控设备
     * 
     * @param iccDevice icc监控设备
     * @return 结果
     */
    @Override
    public int insertIccDevice(IccDevice iccDevice)
    {
        return iccDeviceMapper.insertIccDevice(iccDevice);
    }

    /**
     * 修改icc监控设备
     * 
     * @param iccDevice icc监控设备
     * @return 结果
     */
    @Override
    public int updateIccDevice(IccDevice iccDevice)
    {
        return iccDeviceMapper.updateIccDevice(iccDevice);
    }

    /**
     * 批量删除icc监控设备
     * 
     * @param ids 需要删除的icc监控设备主键
     * @return 结果
     */
    @Override
    public int deleteIccDeviceByIds(Long[] ids)
    {
        return iccDeviceMapper.deleteIccDeviceByIds(ids);
    }

    /**
     * 删除icc监控设备信息
     * 
     * @param id icc监控设备主键
     * @return 结果
     */
    @Override
    public int deleteIccDeviceById(Long id)
    {
        return iccDeviceMapper.deleteIccDeviceById(id);
    }

    @Override
    public List<String> selectHjzzAddressList() {
        return iccDeviceMapper.selectHjzzAddressList();
    }
}
