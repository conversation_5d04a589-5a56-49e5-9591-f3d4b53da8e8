package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.GridWorkforce;
import com.ruoyi.shcy.service.IGridWorkforceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 网格工作力量Controller
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@RestController
@RequestMapping("/shcy/gridWorkforce")
public class GridWorkforceController extends BaseController
{
    @Autowired
    private IGridWorkforceService gridWorkforceService;

    /**
     * 查询网格工作力量列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridWorkforce:list')")
    @GetMapping("/list")
    public TableDataInfo list(GridWorkforce gridWorkforce)
    {
        startPage();
        List<GridWorkforce> list = gridWorkforceService.selectGridWorkforceList(gridWorkforce);
        return getDataTable(list);
    }

    /**
     * 导出网格工作力量列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridWorkforce:export')")
    @Log(title = "网格工作力量", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, GridWorkforce gridWorkforce)
    {
        List<GridWorkforce> list = gridWorkforceService.selectGridWorkforceList(gridWorkforce);
        ExcelUtil<GridWorkforce> util = new ExcelUtil<GridWorkforce>(GridWorkforce.class);
        util.exportExcel(response, list, "网格工作力量数据");
    }

    /**
     * 获取网格工作力量详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridWorkforce:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(gridWorkforceService.selectGridWorkforceById(id));
    }

    /**
     * 新增网格工作力量
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridWorkforce:add')")
    @Log(title = "网格工作力量", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody GridWorkforce gridWorkforce)
    {
        return toAjax(gridWorkforceService.insertGridWorkforce(gridWorkforce));
    }

    /**
     * 修改网格工作力量
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridWorkforce:edit')")
    @Log(title = "网格工作力量", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody GridWorkforce gridWorkforce)
    {
        return toAjax(gridWorkforceService.updateGridWorkforce(gridWorkforce));
    }

    /**
     * 删除网格工作力量
     */
    @PreAuthorize("@ss.hasPermi('shcy:gridWorkforce:remove')")
    @Log(title = "网格工作力量", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(gridWorkforceService.deleteGridWorkforceByIds(ids));
    }
}
