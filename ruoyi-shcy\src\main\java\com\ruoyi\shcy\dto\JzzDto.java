package com.ruoyi.shcy.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

/**
 * “一街三长”汇总表
 */
@Data
public class JzzDto {

    @Excel(name = "序号")
    private Integer serialNumber;

    @Excel(name = "居委会")
    private String committee;

    @Excel(name = "商铺数")
    private Integer shopCount;

    @Excel(name = "社区街长数")
    private Integer communityStreetCount;

    @Excel(name = "巡查户次")
    private Integer inspectionTimes;

    @Excel(name = "覆盖率")
    private Double inspectionCompletionRate;

    @Excel(name = "合计处置数")
    private Integer totalDisposalCount;

    @Excel(name = "合计上报数")
    private Integer totalReportCount;

    @Excel(name = "是否存在“三合一”住人现象处置数")
    private Integer threeInOneDisposalCount;

    @Excel(name = "是否存在“三合一”住人现象上报数")
    private Integer threeInOneReportCount;

    @Excel(name = "广告牌存在安全隐患处置数")
    private Integer adSafetyHazardDisposalCount;

    @Excel(name = "广告牌存在安全隐患上报数")
    private Integer adSafetyHazardReportCount;

    @Excel(name = "证照公示处置数")
    private Integer licensePublicDisplayDisposalCount;

    @Excel(name = "证照公示上报数")
    private Integer licensePublicDisplayReportCount;

    @Excel(name = "沿街商铺施工安全处置数")
    private Integer streetShopConstructionSafetyDisposalCount;

    @Excel(name = "沿街商铺施工安全上报数")
    private Integer streetShopConstructionSafetyReportCount;

    @Excel(name = "沿街商铺文明施工处置数")
    private Integer streetShopCivilizedConstructionDisposalCount;

    @Excel(name = "沿街商铺文明施工上报数")
    private Integer streetShopCivilizedConstructionReportCount;
}
