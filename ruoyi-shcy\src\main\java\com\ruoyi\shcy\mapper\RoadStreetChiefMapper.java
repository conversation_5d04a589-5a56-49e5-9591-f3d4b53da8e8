package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.RoadStreetChief;

/**
 * 街长路名Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-23
 */
public interface RoadStreetChiefMapper 
{
    /**
     * 查询街长路名
     * 
     * @param id 街长路名主键
     * @return 街长路名
     */
    public RoadStreetChief selectRoadStreetChiefById(Long id);

    /**
     * 查询街长路名列表
     * 
     * @param roadStreetChief 街长路名
     * @return 街长路名集合
     */
    public List<RoadStreetChief> selectRoadStreetChiefList(RoadStreetChief roadStreetChief);

    /**
     * 新增街长路名
     * 
     * @param roadStreetChief 街长路名
     * @return 结果
     */
    public int insertRoadStreetChief(RoadStreetChief roadStreetChief);

    /**
     * 修改街长路名
     * 
     * @param roadStreetChief 街长路名
     * @return 结果
     */
    public int updateRoadStreetChief(RoadStreetChief roadStreetChief);

    /**
     * 删除街长路名
     * 
     * @param id 街长路名主键
     * @return 结果
     */
    public int deleteRoadStreetChiefById(Long id);

    /**
     * 批量删除街长路名
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRoadStreetChiefByIds(Long[] ids);
}
