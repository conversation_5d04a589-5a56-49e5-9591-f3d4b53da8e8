package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 隐患排查居委会对应表对象 shcy_flood_hazard_committee
 * 
 * <AUTHOR>
 * @date 2023-12-26
 */
public class ShcyFloodHazardCommittee extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 居委会名称 */
    @Excel(name = "居委会名称")
    private String communityName;

    /** 居委会书记 */
    @Excel(name = "居委会书记")
    private String communitySecretary;

    /** 卫生主任 */
    @Excel(name = "卫生主任")
    private String communityHygieneDirector;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCommunityName(String communityName) 
    {
        this.communityName = communityName;
    }

    public String getCommunityName() 
    {
        return communityName;
    }
    public void setCommunitySecretary(String communitySecretary) 
    {
        this.communitySecretary = communitySecretary;
    }

    public String getCommunitySecretary() 
    {
        return communitySecretary;
    }
    public void setCommunityHygieneDirector(String communityHygieneDirector) 
    {
        this.communityHygieneDirector = communityHygieneDirector;
    }

    public String getCommunityHygieneDirector() 
    {
        return communityHygieneDirector;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("communityName", getCommunityName())
            .append("communitySecretary", getCommunitySecretary())
            .append("communityHygieneDirector", getCommunityHygieneDirector())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
