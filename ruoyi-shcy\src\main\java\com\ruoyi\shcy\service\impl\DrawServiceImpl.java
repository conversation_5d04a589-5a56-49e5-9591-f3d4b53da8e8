package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.DrawMapper;
import com.ruoyi.shcy.domain.Draw;
import com.ruoyi.shcy.service.IDrawService;

/**
 * 地图绘制Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-10-08
 */
@Service
public class DrawServiceImpl implements IDrawService 
{
    @Autowired
    private DrawMapper drawMapper;

    /**
     * 查询地图绘制
     * 
     * @param id 地图绘制主键
     * @return 地图绘制
     */
    @Override
    public Draw selectDrawById(Long id)
    {
        return drawMapper.selectDrawById(id);
    }

    /**
     * 查询地图绘制列表
     * 
     * @param draw 地图绘制
     * @return 地图绘制
     */
    @Override
    public List<Draw> selectDrawList(Draw draw)
    {
        return drawMapper.selectDrawList(draw);
    }

    /**
     * 新增地图绘制
     * 
     * @param draw 地图绘制
     * @return 结果
     */
    @Override
    public int insertDraw(Draw draw)
    {
        return drawMapper.insertDraw(draw);
    }

    /**
     * 修改地图绘制
     * 
     * @param draw 地图绘制
     * @return 结果
     */
    @Override
    public int updateDraw(Draw draw)
    {
        return drawMapper.updateDraw(draw);
    }

    /**
     * 批量删除地图绘制
     * 
     * @param ids 需要删除的地图绘制主键
     * @return 结果
     */
    @Override
    public int deleteDrawByIds(Long[] ids)
    {
        return drawMapper.deleteDrawByIds(ids);
    }

    /**
     * 删除地图绘制信息
     * 
     * @param id 地图绘制主键
     * @return 结果
     */
    @Override
    public int deleteDrawById(Long id)
    {
        return drawMapper.deleteDrawById(id);
    }
}
