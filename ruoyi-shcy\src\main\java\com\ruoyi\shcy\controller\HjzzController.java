package com.ruoyi.shcy.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.shcy.domain.HjzzCase;
import com.ruoyi.shcy.service.IHjzzCaseService;
import com.ruoyi.shcy.service.IIccAlarmRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 环境整治案事件处置H5接口
 *
 * <AUTHOR>
 * @date 2023/11/29
 */
@RestController
@RequestMapping("/shcy/hjzz")
public class HjzzController extends BaseController {

    @Autowired
    private IHjzzCaseService hjzzCaseService;

    @Autowired
    private IIccAlarmRecordService iccAlarmRecordService;

    @GetMapping("/getPendingCaseCount")
    public AjaxResult getPendingCaseCount() {
        return AjaxResult.success("操作成功", hjzzCaseService.getPendingCaseCount());
    }

    @GetMapping("/getProcessingCaseCount")
    public AjaxResult getProcessingCaseCount() {
        return AjaxResult.success("操作成功", hjzzCaseService.getProcessingCaseCount());
    }

    @GetMapping("/getCaseCount")
    public AjaxResult getCaseCount(HjzzCase hjzzCase, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            hjzzCase.setParams(params);
        }
        return AjaxResult.success("操作成功", hjzzCaseService.getCaseCount(hjzzCase));
    }

    @GetMapping(value = "/getHjzzCase/{id}")
    public AjaxResult getHjzzCase(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hjzzCaseService.selectHjzzCaseById(id));
    }

    @GetMapping(value = "/getIccAlarmRecord/{id}")
    public AjaxResult getIccAlarmRecord(@PathVariable("id") Long id)
    {
        return AjaxResult.success(iccAlarmRecordService.selectIccAlarmRecordById(id));
    }

    @PostMapping("/invalidateCase")
    public AjaxResult invalidateCase(@RequestBody HjzzCase hjzzCase)
    {
        hjzzCase.setUpdateBy(getUsername());
        return toAjax(hjzzCaseService.invalidateCase(hjzzCase));
    }

    @PostMapping("/regressionCase")
    public AjaxResult regressionCase(@RequestBody HjzzCase hjzzCase)
    {
        hjzzCase.setUpdateBy(getUsername());
        return toAjax(hjzzCaseService.regressionCase(hjzzCase));
    }

    @GetMapping("/countHjzzCase")
    public AjaxResult countHjzzCase(HjzzCase hjzzCase)
    {
        // 统计偷倒垃圾类型的案件
        hjzzCase.setCaseType("1");
        return AjaxResult.success(hjzzCaseService.countHjzzCase(hjzzCase));
    }

    @GetMapping("/countHjzzCaseHistory")
    public AjaxResult countHjzzCaseHistory(HjzzCase hjzzCase, String yearMonth)
    {
        hjzzCase.setCaseType("1");
        return AjaxResult.success(hjzzCaseService.countHjzzCaseHistory(hjzzCase, yearMonth));
    }

    /**
     * 手动HLS转换MP4
     *
     * @param hjzzCase hjzz案例
     * @return {@link AjaxResult}
     */
    @PreAuthorize("@ss.hasPermi('shcy:hjzzCase:convert')")
    @PostMapping("/convertCase")
    public AjaxResult convertCase(@RequestBody HjzzCase hjzzCase)
    {
        return toAjax(hjzzCaseService.convertCase(hjzzCase));
    }

}
