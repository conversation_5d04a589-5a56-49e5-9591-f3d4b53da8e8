package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.FxftCase;

import java.util.List;

/**
 * 防汛防台案事件Service接口
 * 
 * <AUTHOR>
 * @date 2023-10-31
 */
public interface IFxftCaseService 
{
    /**
     * 查询防汛防台案事件
     * 
     * @param id 防汛防台案事件主键
     * @return 防汛防台案事件
     */
    public FxftCase selectFxftCaseById(Long id);

    /**
     * 查询防汛防台案事件列表
     * 
     * @param fxftCase 防汛防台案事件
     * @return 防汛防台案事件集合
     */
    public List<FxftCase> selectFxftCaseList(FxftCase fxftCase);

    /**
     * 新增防汛防台案事件
     * 
     * @param fxftCase 防汛防台案事件
     * @return 结果
     */
    public int insertFxftCase(FxftCase fxftCase);

    /**
     * 修改防汛防台案事件
     * 
     * @param fxftCase 防汛防台案事件
     * @return 结果
     */
    public int updateFxftCase(FxftCase fxftCase);

    /**
     * 批量删除防汛防台案事件
     * 
     * @param ids 需要删除的防汛防台案事件主键集合
     * @return 结果
     */
    public int deleteFxftCaseByIds(Long[] ids);

    /**
     * 删除防汛防台案事件信息
     * 
     * @param id 防汛防台案事件主键
     * @return 结果
     */
    public int deleteFxftCaseById(Long id);

    public List<FxftCase> selectFxftCaseListByCaseNumber(FxftCase fxftCase);

    public int handleFxftCase(FxftCase fxftCase);

    public FxftCase selectFxftCaseByAlarmRecordId(Long alarmRecordId);

    public long getCaseCount(FxftCase fxftCase);
}
