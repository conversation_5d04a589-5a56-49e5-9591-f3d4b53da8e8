<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.SupervisorLogMapper">

    <resultMap type="SupervisorLog" id="SupervisorLogResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="supervisorStatus"    column="supervisor_status"    />
        <result property="createDate"    column="create_date"    />
    </resultMap>

    <sql id="selectSupervisorLogVo">
        select id, shop_id, shop_name, create_time, update_time, supervisor_status, create_date from shcy_shop_supervisor_log
    </sql>

    <select id="selectSupervisorLogList" parameterType="SupervisorLog" resultMap="SupervisorLogResult">
        <include refid="selectSupervisorLogVo"/>
        <where>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="supervisorStatus != null  and supervisorStatus != ''"> and supervisor_status = #{supervisorStatus}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
        </where>
        order by create_date desc
    </select>

    <select id="selectSupervisorLogById" parameterType="Long" resultMap="SupervisorLogResult">
        <include refid="selectSupervisorLogVo"/>
        where id = #{id}
    </select>
    <select id="selectSupervisorLogByIdThree" parameterType="Long" resultMap="SupervisorLogResult">
        <include refid="selectSupervisorLogVo"/>
        where shop_id = #{id} order by create_date desc limit 3
    </select>

    <insert id="insertSupervisorLog" parameterType="SupervisorLog" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_shop_supervisor_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="supervisorStatus != null">supervisor_status,</if>
            <if test="createDate != null">create_date,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="supervisorStatus != null">#{supervisorStatus},</if>
            <if test="createDate != null">#{createDate},</if>
         </trim>
    </insert>

    <update id="updateSupervisorLog" parameterType="SupervisorLog">
        update shcy_shop_supervisor_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="supervisorStatus != null">supervisor_status = #{supervisorStatus},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupervisorLogById" parameterType="Long">
        delete from shcy_shop_supervisor_log where id = #{id}
    </delete>

    <delete id="deleteSupervisorLogByIds" parameterType="String">
        delete from shcy_shop_supervisor_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
