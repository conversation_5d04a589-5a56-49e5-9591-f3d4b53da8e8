package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.FactoryWarehouse;
import com.ruoyi.shcy.service.IFactoryWarehouseService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 厂房仓库Controller
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@RestController
@RequestMapping("/shcy/warehouse")
public class FactoryWarehouseController extends BaseController
{
    @Autowired
    private IFactoryWarehouseService factoryWarehouseService;

    /**
     * 查询厂房仓库列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:warehouse:list')")
    @GetMapping("/list")
    public TableDataInfo list(FactoryWarehouse factoryWarehouse)
    {
        startPage();
        List<FactoryWarehouse> list = factoryWarehouseService.selectFactoryWarehouseList(factoryWarehouse);
        return getDataTable(list);
    }

    /**
     * 导出厂房仓库列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:warehouse:export')")
    @Log(title = "厂房仓库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FactoryWarehouse factoryWarehouse)
    {
        List<FactoryWarehouse> list = factoryWarehouseService.selectFactoryWarehouseList(factoryWarehouse);
        ExcelUtil<FactoryWarehouse> util = new ExcelUtil<FactoryWarehouse>(FactoryWarehouse.class);
        util.exportExcel(response, list, "厂房仓库数据");
    }

    /**
     * 获取厂房仓库详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:warehouse:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(factoryWarehouseService.selectFactoryWarehouseById(id));
    }

    /**
     * 新增厂房仓库
     */
    @PreAuthorize("@ss.hasPermi('shcy:warehouse:add')")
    @Log(title = "厂房仓库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FactoryWarehouse factoryWarehouse)
    {
        return toAjax(factoryWarehouseService.insertFactoryWarehouse(factoryWarehouse));
    }

    /**
     * 修改厂房仓库
     */
    @PreAuthorize("@ss.hasPermi('shcy:warehouse:edit')")
    @Log(title = "厂房仓库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FactoryWarehouse factoryWarehouse)
    {
        return toAjax(factoryWarehouseService.updateFactoryWarehouse(factoryWarehouse));
    }

    /**
     * 删除厂房仓库
     */
    @PreAuthorize("@ss.hasPermi('shcy:warehouse:remove')")
    @Log(title = "厂房仓库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(factoryWarehouseService.deleteFactoryWarehouseByIds(ids));
    }
}
