package com.ruoyi.icc.service;


import com.ruoyi.icc.dto.CarRecordDTO;
import com.ruoyi.icc.vo.CarPageVO;

import java.util.List;

/**
 * 车辆卡口接口
 *
 * <AUTHOR>
 * @date 2024/01/30
 */
public interface EvoCarService {

    /**
     * 获取过车记录
     *
     * @param pageVO 分页参数
     * @param token 授权令牌
     * @return {@link List}<{@link CarRecordDTO}>
     */
    List<CarRecordDTO> getPicRecords(CarPageVO pageVO, String token);

    /**
     * 获取过车记录总数
     *
     * @param pageVO 分页参数
     * @param token  授权令牌
     * @return int 总数
     */
    int getPicRecordTotal(CarPageVO pageVO, String token);

    void syncCarRecord(CarRecordDTO carRecordDTO);
}
