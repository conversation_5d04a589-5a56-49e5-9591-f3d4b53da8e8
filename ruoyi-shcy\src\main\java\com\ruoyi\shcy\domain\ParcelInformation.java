package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 宗地信息对象 shcy_parcel_information
 *
 * <AUTHOR>
 * @date 2023-01-31
 */
public class ParcelInformation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 宗地编号 */
    @Excel(name = "宗地编号")
    private String parcelNo;

    /** 地块类型：宗地、非宗地 */
    @Excel(name = "地块类型：宗地、非宗地")
    private String lotNumber;

    /** 土地性质 */
    @Excel(name = "土地性质")
    private String landRights;

    /** 土地用途（试行） */
    @Excel(name = "土地用途", readConverterExp = "试=行")
    private String landUseTrial;

    /** 所属居委 */
    @Excel(name = "所属居委")
    private String belongCommittee;

    /** 权属单位 */
    @Excel(name = "权属单位")
    private String ownership;

    /** 是否国有资产 */
    @Excel(name = "是否国有资产")
    private String isGyzc;

    /** 权属联系人 */
    @Excel(name = "权属联系人")
    private String ownershipContact;

    /** 权属联系电话 */
    @Excel(name = "权属联系电话")
    private String ownershipPhone;

    /** 物业公司 */
    @Excel(name = "物业公司")
    private String propertyCompany;

    /** 物业联系人 */
    @Excel(name = "物业联系人")
    private String propertyContact;

    /** 物业联系方式 */
    @Excel(name = "物业联系方式")
    private String propertyPhone;

    /** 现状使用主体 */
    @Excel(name = "现状使用主体")
    private String rightToUse;

    /** 单位类型 */
    @Excel(name = "单位类型")
    private String unitType;

    /** 单位地址 */
    @Excel(name = "单位地址")
    private String unitAddress;

    /** 单位联系人 */
    @Excel(name = "单位联系人")
    private String unitContact;

    /** 单位联系电话 */
    @Excel(name = "单位联系电话")
    private String unitPhone;

    /** 现状使用主体1 */
    @Excel(name = "现状使用主体1")
    private String rightToUse1;

    /** 单位类型1 */
    @Excel(name = "单位类型1")
    private String unitType1;

    /** 单位地址1 */
    @Excel(name = "单位地址1")
    private String unitAddress1;

    /** 单位联系人1 */
    @Excel(name = "单位联系人1")
    private String unitContact1;

    /** 单位联系电话1 */
    @Excel(name = "单位联系电话1")
    private String unitPhone1;

    /** 现状使用主体2 */
    @Excel(name = "现状使用主体2")
    private String rightToUse2;

    /** 单位类型2 */
    @Excel(name = "单位类型2")
    private String unitType2;

    /** 单位地址2 */
    @Excel(name = "单位地址2")
    private String unitAddress2;

    /** 单位联系人2 */
    @Excel(name = "单位联系人2")
    private String unitContact2;

    /** 单位联系电话2 */
    @Excel(name = "单位联系电话2")
    private String unitPhone2;

    /** 现状使用主体3 */
    @Excel(name = "现状使用主体3")
    private String rightToUse3;

    /** 单位类型3 */
    @Excel(name = "单位类型3")
    private String unitType3;

    /** 单位地址3 */
    @Excel(name = "单位地址3")
    private String unitAddress3;

    /** 单位联系人3 */
    @Excel(name = "单位联系人3")
    private String unitContact3;

    /** 单位联系电话3 */
    @Excel(name = "单位联系电话3")
    private String unitPhone3;

    /** 现状使用主体4 */
    @Excel(name = "现状使用主体4")
    private String rightToUse4;

    /** 单位类型4 */
    @Excel(name = "单位类型4")
    private String unitType4;

    /** 单位地址4 */
    @Excel(name = "单位地址4")
    private String unitAddress4;

    /** 单位联系人4 */
    @Excel(name = "单位联系人4")
    private String unitContact4;

    /** 单位联系电话4 */
    @Excel(name = "单位联系电话4")
    private String unitPhone4;

    /** 现状使用主体5 */
    @Excel(name = "现状使用主体5")
    private String rightToUse5;

    /** 单位类型5 */
    @Excel(name = "单位类型5")
    private String unitType5;

    /** 单位地址5 */
    @Excel(name = "单位地址5")
    private String unitAddress5;

    /** 单位联系人5 */
    @Excel(name = "单位联系人5")
    private String unitContact5;

    /** 单位联系电话5 */
    @Excel(name = "单位联系电话5")
    private String unitPhone5;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 属性 */
    @Excel(name = "属性")
    private String attribute;

    /** 项目类型*/
    private String projectType;


    /** 土地面积（㎡） */
    @Excel(name = "土地面积", readConverterExp = "㎡=")
    private String shapeArea;



    public String getProjectType() {
        return projectType;
    }

    public void setProjectType(String projectType) {
        this.projectType = projectType;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setParcelNo(String parcelNo)
    {
        this.parcelNo = parcelNo;
    }

    public String getParcelNo()
    {
        return parcelNo;
    }
    public void setLotNumber(String lotNumber)
    {
        this.lotNumber = lotNumber;
    }

    public String getLotNumber()
    {
        return lotNumber;
    }
    public void setLandRights(String landRights)
    {
        this.landRights = landRights;
    }

    public String getLandRights()
    {
        return landRights;
    }
    public void setLandUseTrial(String landUseTrial)
    {
        this.landUseTrial = landUseTrial;
    }

    public String getLandUseTrial()
    {
        return landUseTrial;
    }
    public void setBelongCommittee(String belongCommittee)
    {
        this.belongCommittee = belongCommittee;
    }

    public String getBelongCommittee()
    {
        return belongCommittee;
    }
    public void setOwnership(String ownership)
    {
        this.ownership = ownership;
    }

    public String getOwnership()
    {
        return ownership;
    }
    public void setIsGyzc(String isGyzc)
    {
        this.isGyzc = isGyzc;
    }

    public String getIsGyzc()
    {
        return isGyzc;
    }
    public void setOwnershipContact(String ownershipContact)
    {
        this.ownershipContact = ownershipContact;
    }

    public String getOwnershipContact()
    {
        return ownershipContact;
    }
    public void setOwnershipPhone(String ownershipPhone)
    {
        this.ownershipPhone = ownershipPhone;
    }

    public String getOwnershipPhone()
    {
        return ownershipPhone;
    }
    public void setPropertyCompany(String propertyCompany)
    {
        this.propertyCompany = propertyCompany;
    }

    public String getPropertyCompany()
    {
        return propertyCompany;
    }
    public void setPropertyContact(String propertyContact)
    {
        this.propertyContact = propertyContact;
    }

    public String getPropertyContact()
    {
        return propertyContact;
    }
    public void setPropertyPhone(String propertyPhone)
    {
        this.propertyPhone = propertyPhone;
    }

    public String getPropertyPhone()
    {
        return propertyPhone;
    }
    public void setRightToUse(String rightToUse)
    {
        this.rightToUse = rightToUse;
    }

    public String getRightToUse()
    {
        return rightToUse;
    }
    public void setUnitType(String unitType)
    {
        this.unitType = unitType;
    }

    public String getUnitType()
    {
        return unitType;
    }
    public void setUnitAddress(String unitAddress)
    {
        this.unitAddress = unitAddress;
    }

    public String getUnitAddress()
    {
        return unitAddress;
    }
    public void setUnitContact(String unitContact)
    {
        this.unitContact = unitContact;
    }

    public String getUnitContact()
    {
        return unitContact;
    }
    public void setUnitPhone(String unitPhone)
    {
        this.unitPhone = unitPhone;
    }

    public String getUnitPhone()
    {
        return unitPhone;
    }
    public void setRightToUse1(String rightToUse1)
    {
        this.rightToUse1 = rightToUse1;
    }

    public String getRightToUse1()
    {
        return rightToUse1;
    }
    public void setUnitType1(String unitType1)
    {
        this.unitType1 = unitType1;
    }

    public String getUnitType1()
    {
        return unitType1;
    }
    public void setUnitAddress1(String unitAddress1)
    {
        this.unitAddress1 = unitAddress1;
    }

    public String getUnitAddress1()
    {
        return unitAddress1;
    }
    public void setUnitContact1(String unitContact1)
    {
        this.unitContact1 = unitContact1;
    }

    public String getUnitContact1()
    {
        return unitContact1;
    }
    public void setUnitPhone1(String unitPhone1)
    {
        this.unitPhone1 = unitPhone1;
    }

    public String getUnitPhone1()
    {
        return unitPhone1;
    }
    public void setRightToUse2(String rightToUse2)
    {
        this.rightToUse2 = rightToUse2;
    }

    public String getRightToUse2()
    {
        return rightToUse2;
    }
    public void setUnitType2(String unitType2)
    {
        this.unitType2 = unitType2;
    }

    public String getUnitType2()
    {
        return unitType2;
    }
    public void setUnitAddress2(String unitAddress2)
    {
        this.unitAddress2 = unitAddress2;
    }

    public String getUnitAddress2()
    {
        return unitAddress2;
    }
    public void setUnitContact2(String unitContact2)
    {
        this.unitContact2 = unitContact2;
    }

    public String getUnitContact2()
    {
        return unitContact2;
    }
    public void setUnitPhone2(String unitPhone2)
    {
        this.unitPhone2 = unitPhone2;
    }

    public String getUnitPhone2()
    {
        return unitPhone2;
    }
    public void setRightToUse3(String rightToUse3)
    {
        this.rightToUse3 = rightToUse3;
    }

    public String getRightToUse3()
    {
        return rightToUse3;
    }
    public void setUnitType3(String unitType3)
    {
        this.unitType3 = unitType3;
    }

    public String getUnitType3()
    {
        return unitType3;
    }
    public void setUnitAddress3(String unitAddress3)
    {
        this.unitAddress3 = unitAddress3;
    }

    public String getUnitAddress3()
    {
        return unitAddress3;
    }
    public void setUnitContact3(String unitContact3)
    {
        this.unitContact3 = unitContact3;
    }

    public String getUnitContact3()
    {
        return unitContact3;
    }
    public void setUnitPhone3(String unitPhone3)
    {
        this.unitPhone3 = unitPhone3;
    }

    public String getUnitPhone3()
    {
        return unitPhone3;
    }
    public void setRightToUse4(String rightToUse4)
    {
        this.rightToUse4 = rightToUse4;
    }

    public String getRightToUse4()
    {
        return rightToUse4;
    }
    public void setUnitType4(String unitType4)
    {
        this.unitType4 = unitType4;
    }

    public String getUnitType4()
    {
        return unitType4;
    }
    public void setUnitAddress4(String unitAddress4)
    {
        this.unitAddress4 = unitAddress4;
    }

    public String getUnitAddress4()
    {
        return unitAddress4;
    }
    public void setUnitContact4(String unitContact4)
    {
        this.unitContact4 = unitContact4;
    }

    public String getUnitContact4()
    {
        return unitContact4;
    }
    public void setUnitPhone4(String unitPhone4)
    {
        this.unitPhone4 = unitPhone4;
    }

    public String getUnitPhone4()
    {
        return unitPhone4;
    }
    public void setRightToUse5(String rightToUse5)
    {
        this.rightToUse5 = rightToUse5;
    }

    public String getRightToUse5()
    {
        return rightToUse5;
    }
    public void setUnitType5(String unitType5)
    {
        this.unitType5 = unitType5;
    }

    public String getUnitType5()
    {
        return unitType5;
    }
    public void setUnitAddress5(String unitAddress5)
    {
        this.unitAddress5 = unitAddress5;
    }

    public String getUnitAddress5()
    {
        return unitAddress5;
    }
    public void setUnitContact5(String unitContact5)
    {
        this.unitContact5 = unitContact5;
    }

    public String getUnitContact5()
    {
        return unitContact5;
    }
    public void setUnitPhone5(String unitPhone5)
    {
        this.unitPhone5 = unitPhone5;
    }

    public String getUnitPhone5()
    {
        return unitPhone5;
    }
    public void setShapeArea(String shapeArea)
    {
        this.shapeArea = shapeArea;
    }

    public String getShapeArea()
    {
        return shapeArea;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }

    @Override
    public String toString() {
        return "ParcelInformation{" +
                "id=" + id +
                ", parcelNo='" + parcelNo + '\'' +
                ", lotNumber='" + lotNumber + '\'' +
                ", landRights='" + landRights + '\'' +
                ", landUseTrial='" + landUseTrial + '\'' +
                ", belongCommittee='" + belongCommittee + '\'' +
                ", ownership='" + ownership + '\'' +
                ", isGyzc='" + isGyzc + '\'' +
                ", ownershipContact='" + ownershipContact + '\'' +
                ", ownershipPhone='" + ownershipPhone + '\'' +
                ", propertyCompany='" + propertyCompany + '\'' +
                ", propertyContact='" + propertyContact + '\'' +
                ", propertyPhone='" + propertyPhone + '\'' +
                ", rightToUse='" + rightToUse + '\'' +
                ", unitType='" + unitType + '\'' +
                ", unitAddress='" + unitAddress + '\'' +
                ", unitContact='" + unitContact + '\'' +
                ", unitPhone='" + unitPhone + '\'' +
                ", rightToUse1='" + rightToUse1 + '\'' +
                ", unitType1='" + unitType1 + '\'' +
                ", unitAddress1='" + unitAddress1 + '\'' +
                ", unitContact1='" + unitContact1 + '\'' +
                ", unitPhone1='" + unitPhone1 + '\'' +
                ", rightToUse2='" + rightToUse2 + '\'' +
                ", unitType2='" + unitType2 + '\'' +
                ", unitAddress2='" + unitAddress2 + '\'' +
                ", unitContact2='" + unitContact2 + '\'' +
                ", unitPhone2='" + unitPhone2 + '\'' +
                ", rightToUse3='" + rightToUse3 + '\'' +
                ", unitType3='" + unitType3 + '\'' +
                ", unitAddress3='" + unitAddress3 + '\'' +
                ", unitContact3='" + unitContact3 + '\'' +
                ", unitPhone3='" + unitPhone3 + '\'' +
                ", rightToUse4='" + rightToUse4 + '\'' +
                ", unitType4='" + unitType4 + '\'' +
                ", unitAddress4='" + unitAddress4 + '\'' +
                ", unitContact4='" + unitContact4 + '\'' +
                ", unitPhone4='" + unitPhone4 + '\'' +
                ", rightToUse5='" + rightToUse5 + '\'' +
                ", unitType5='" + unitType5 + '\'' +
                ", unitAddress5='" + unitAddress5 + '\'' +
                ", unitContact5='" + unitContact5 + '\'' +
                ", unitPhone5='" + unitPhone5 + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", attribute='" + attribute + '\'' +
                ", projectType='" + projectType + '\'' +
                ", shapeArea='" + shapeArea + '\'' +
                '}';
    }
}
