package com.ruoyi.shcy.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.Alias;

/**
 * 报警记录对象 nbiotyun_alarm_record
 * 
 * <AUTHOR>
 * @date 2023-09-04
 */
@Alias("NbiotyunAlarmRecord")
public class NbiotyunAlarmRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报警记录id */
    private Long id;

    /** 报警时间 */
    @Excel(name = "报警时间")
    private String alarmTime;

    /** 设备 imei */
    @Excel(name = "设备 imei")
    private String deviceImei;

    /** 设备所属单位名称 */
    @Excel(name = "设备所属单位名称")
    private String deptName;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceTypeName;

    /** 设备状态 */
    @Excel(name = "设备状态")
    private String deviceState;

    /** 设备型号名称 */
    @Excel(name = "设备型号名称")
    private String deviceVersionName;

    /** 事件名称 */
    @Excel(name = "事件名称")
    private String deviceEventName;

    /** 设备属性 */
    @Excel(name = "设备属性")
    private String deviceAttrValue;

    /** 处理时间 */
    @Excel(name = "处理时间")
    private String handleTime;

    /** 处理人 */
    @Excel(name = "处理人")
    private String handler;

    /** 人工确认报警原因 */
    private String alarmReason;

    /** 设备详细安装地址 */
    private String detailedLocation;

    /**
     * 状态
     */
    private String status;

    /**
     * 负责人
     */
    private String responsiblePerson;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAlarmTime(String alarmTime) 
    {
        this.alarmTime = alarmTime;
    }

    public String getAlarmTime() 
    {
        return alarmTime;
    }
    public void setDeviceImei(String deviceImei) 
    {
        this.deviceImei = deviceImei;
    }

    public String getDeviceImei() 
    {
        return deviceImei;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setDeviceTypeName(String deviceTypeName) 
    {
        this.deviceTypeName = deviceTypeName;
    }

    public String getDeviceTypeName() 
    {
        return deviceTypeName;
    }
    public void setDeviceState(String deviceState) 
    {
        this.deviceState = deviceState;
    }

    public String getDeviceState() 
    {
        return deviceState;
    }
    public void setDeviceVersionName(String deviceVersionName) 
    {
        this.deviceVersionName = deviceVersionName;
    }

    public String getDeviceVersionName() 
    {
        return deviceVersionName;
    }
    public void setDeviceEventName(String deviceEventName) 
    {
        this.deviceEventName = deviceEventName;
    }

    public String getDeviceEventName() 
    {
        return deviceEventName;
    }
    public void setDeviceAttrValue(String deviceAttrValue) 
    {
        this.deviceAttrValue = deviceAttrValue;
    }

    public String getDeviceAttrValue() 
    {
        return deviceAttrValue;
    }
    public void setHandleTime(String handleTime) 
    {
        this.handleTime = handleTime;
    }

    public String getHandleTime() 
    {
        return handleTime;
    }
    public void setHandler(String handler) 
    {
        this.handler = handler;
    }

    public String getHandler() 
    {
        return handler;
    }
    public void setAlarmReason(String alarmReason) 
    {
        this.alarmReason = alarmReason;
    }

    public String getAlarmReason() 
    {
        return alarmReason;
    }
    public void setDetailedLocation(String detailedLocation) 
    {
        this.detailedLocation = detailedLocation;
    }

    public String getDetailedLocation() 
    {
        return detailedLocation;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getResponsiblePerson() {
        return responsiblePerson;
    }

    public void setResponsiblePerson(String responsiblePerson) {
        this.responsiblePerson = responsiblePerson;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("alarmTime", getAlarmTime())
            .append("deviceImei", getDeviceImei())
            .append("deptName", getDeptName())
            .append("deviceTypeName", getDeviceTypeName())
            .append("deviceState", getDeviceState())
            .append("deviceVersionName", getDeviceVersionName())
            .append("deviceEventName", getDeviceEventName())
            .append("deviceAttrValue", getDeviceAttrValue())
            .append("handleTime", getHandleTime())
            .append("handler", getHandler())
            .append("alarmReason", getAlarmReason())
            .append("detailedLocation", getDetailedLocation())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("status", getStatus())
            .append("responsiblePerson", getResponsiblePerson())
            .toString();
    }
}
