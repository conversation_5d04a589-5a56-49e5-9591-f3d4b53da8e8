package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.FloodReserveInfo;

import java.util.List;

/**
 * 防汛物资储备信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface IFloodReserveInfoService 
{
    /**
     * 查询防汛物资储备信息
     * 
     * @param id 防汛物资储备信息主键
     * @return 防汛物资储备信息
     */
    public FloodReserveInfo selectFloodReserveInfoById(Long id);

    /**
     * 查询防汛物资储备信息列表
     * 
     * @param floodReserveInfo 防汛物资储备信息
     * @return 防汛物资储备信息集合
     */
    public List<FloodReserveInfo> selectFloodReserveInfoList(FloodReserveInfo floodReserveInfo);

    /**
     * 新增防汛物资储备信息
     * 
     * @param floodReserveInfo 防汛物资储备信息
     * @return 结果
     */
    public int insertFloodReserveInfo(FloodReserveInfo floodReserveInfo);

    /**
     * 修改防汛物资储备信息
     * 
     * @param floodReserveInfo 防汛物资储备信息
     * @return 结果
     */
    public int updateFloodReserveInfo(FloodReserveInfo floodReserveInfo);

    /**
     * 批量删除防汛物资储备信息
     * 
     * @param ids 需要删除的防汛物资储备信息主键集合
     * @return 结果
     */
    public int deleteFloodReserveInfoByIds(Long[] ids);

    /**
     * 删除防汛物资储备信息信息
     * 
     * @param id 防汛物资储备信息主键
     * @return 结果
     */
    public int deleteFloodReserveInfoById(Long id);
}
