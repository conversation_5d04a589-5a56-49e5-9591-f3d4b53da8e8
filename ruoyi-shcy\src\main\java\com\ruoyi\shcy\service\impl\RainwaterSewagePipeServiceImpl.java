package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.RainwaterSewagePipeMapper;
import com.ruoyi.shcy.domain.RainwaterSewagePipe;
import com.ruoyi.shcy.service.IRainwaterSewagePipeService;

/**
 * 雨污管道信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-21
 */
@Service
public class RainwaterSewagePipeServiceImpl implements IRainwaterSewagePipeService 
{
    @Autowired
    private RainwaterSewagePipeMapper rainwaterSewagePipeMapper;

    /**
     * 查询雨污管道信息
     * 
     * @param id 雨污管道信息主键
     * @return 雨污管道信息
     */
    @Override
    public RainwaterSewagePipe selectRainwaterSewagePipeById(Long id)
    {
        return rainwaterSewagePipeMapper.selectRainwaterSewagePipeById(id);
    }

    /**
     * 查询雨污管道信息列表
     * 
     * @param rainwaterSewagePipe 雨污管道信息
     * @return 雨污管道信息
     */
    @Override
    public List<RainwaterSewagePipe> selectRainwaterSewagePipeList(RainwaterSewagePipe rainwaterSewagePipe)
    {
        return rainwaterSewagePipeMapper.selectRainwaterSewagePipeList(rainwaterSewagePipe);
    }

    /**
     * 新增雨污管道信息
     * 
     * @param rainwaterSewagePipe 雨污管道信息
     * @return 结果
     */
    @Override
    public int insertRainwaterSewagePipe(RainwaterSewagePipe rainwaterSewagePipe)
    {
        rainwaterSewagePipe.setCreateTime(DateUtils.getNowDate());
        return rainwaterSewagePipeMapper.insertRainwaterSewagePipe(rainwaterSewagePipe);
    }

    /**
     * 修改雨污管道信息
     * 
     * @param rainwaterSewagePipe 雨污管道信息
     * @return 结果
     */
    @Override
    public int updateRainwaterSewagePipe(RainwaterSewagePipe rainwaterSewagePipe)
    {
        rainwaterSewagePipe.setUpdateTime(DateUtils.getNowDate());
        return rainwaterSewagePipeMapper.updateRainwaterSewagePipe(rainwaterSewagePipe);
    }

    /**
     * 批量删除雨污管道信息
     * 
     * @param ids 需要删除的雨污管道信息主键
     * @return 结果
     */
    @Override
    public int deleteRainwaterSewagePipeByIds(Long[] ids)
    {
        return rainwaterSewagePipeMapper.deleteRainwaterSewagePipeByIds(ids);
    }

    /**
     * 删除雨污管道信息信息
     * 
     * @param id 雨污管道信息主键
     * @return 结果
     */
    @Override
    public int deleteRainwaterSewagePipeById(Long id)
    {
        return rainwaterSewagePipeMapper.deleteRainwaterSewagePipeById(id);
    }
}
