package com.ruoyi.artemis;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.GisUtils;
import com.ruoyi.shcy.domain.Cameras;
import com.ruoyi.shcy.service.ICamerasService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class GisTest {

    @Autowired
    private ICamerasService camerasService;

    @Test
    public void contextLoads() {

    }

    @Test
    public void test() {
        String result = GisUtils.convertcc("121.337148", "30.717358");
    }

    /**
     * 水位传感器坐标转换：高德转城地
     */
    @Test
    public void testSensor() {

        // 864530052922045
        // "lon": 121.3394393,
        // "lat": 30.715823,
        String result = GisUtils.convert("121.3394393", "30.715823");
        System.out.println("864530052922045: " + result);

        // 862290055260115
        // "lon": 121.3386283814907,
        // "lat": 30.724170867096188,
        String result1 = GisUtils.convert("121.3386283814907", "30.724170867096188");
        System.out.println("862290055260115: " + result1);

        // 864530052922367
        // "lon": 121.348761767149,
        // "lat": 30.727858804679713,
        String result2 = GisUtils.convert("121.348761767149", "30.727858804679713");
        System.out.println("864530052922367: " + result2);
    }

    @Test
    public void testUpdateCameras() throws Exception{
        String[] array = {
                "31011601081320000935",
                "31011601081320000934",
                "31011601081320000933",
                "31011601081320000932",
                "31011601081320000478",
                "31011601081320000477",
                "31011601081320000481",
                "31011601081320000480",
                "31011601081320000479",
                "31011601081320000482",
                "31011601081320000483",
                "31011601081320000478",
                "31011601081320000478",
                "31011622001180004012",
                "31011622001180014003",
                "31011623051198000288",
                "31011623051198000287",
                "31011623051198000288",
                "31011622001180027014",
                "31011623001180021009",
                "31011623001180021017",
                "31011622001180003010",
                "31011622001180018012",
                "31011622001180019004",
                "31011622001180003009",
                "31011622001180002011",
                "31011622001180028012",
                "31011622001180002008",
                "31011622001180018006",
                "31011622001180029008",
                "31011622001180002004",
                "31011622001180002003",
                "31011622001180030013",
                "31011623001181030034",
                "31011622001180003004",
                "31011622001180018004",
                "31011622001180008006",
                "31011622001180003005",
                "31011623001180008007",
                "31011622001180001011",
                "31011622001180001012",
                "31011622001180001013",
                "31011622001180001014",
                "31011623001180017004"
        };

        int i = 0;
        // 遍历 array
        for (String indexCode : array) {
            Cameras cameras = camerasService.selectCamerasByCameraIndexCode(indexCode);
            String res = GisUtils.convertcc(cameras.getLongitude(), cameras.getLatitude());
            JSONObject resObject = JSONObject.parseObject(res);
            JSONObject dataObject = resObject.getJSONObject("data");
            String lng = dataObject.getString("lng");
            String lat = dataObject.getString("lat");
            cameras.setCoordinate(lng + "," + lat);
            camerasService.updateCameras(cameras);
            i++;
        }
        System.out.println("i = " + i);



    }

}
