package com.ruoyi.hikvision.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.hikvision.ArtemisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/artemis")
@RestController
@Slf4j
public class ArtemisController {

    @Autowired
    private ArtemisService artemisService;

    @GetMapping("/getUrl")
    public AjaxResult getUrl() throws Exception {
        String result = artemisService.callPostApiPreviewURLs("31011623601328000017");
        return AjaxResult.success(result);
    }

    /**
     * 获取预览url(HLS协议)
     *
     * @param cameraIndexCode 监控点编号
     * @return {@link AjaxResult}
     * @throws Exception 异常
     */
    @GetMapping("/getPreviewUrl/{cameraIndexCode}")
    public AjaxResult getPreviewUrl(@PathVariable("cameraIndexCode") String cameraIndexCode) throws Exception {
        String json = artemisService.callPostApiPreviewURLs(cameraIndexCode);
        JSONObject jsonObject = JSON.parseObject(json);
        JSONObject data = jsonObject.getJSONObject("data");
        String url = data.get("url").toString();
        return AjaxResult.success("操作成功", url);
    }

    /**
     * 获取预览url(WS协议)
     * @param cameraIndexCode 监控点编号
     * @return {@link AjaxResult}
     * @throws Exception 异常
     */
    @GetMapping("/getPreviewUrlWs/{cameraIndexCode}")
    public AjaxResult getPreviewUrlWs(@PathVariable("cameraIndexCode") String cameraIndexCode) throws Exception {
        String json = artemisService.callPostApiPreviewURLsWs(cameraIndexCode);
        JSONObject jsonObject = JSON.parseObject(json);
        JSONObject data = jsonObject.getJSONObject("data");
        // 判断data是否为空
        if (data == null) {
            return AjaxResult.success();
        }
        String url = data.get("url").toString();
        return AjaxResult.success("操作成功", url);
    }

}
