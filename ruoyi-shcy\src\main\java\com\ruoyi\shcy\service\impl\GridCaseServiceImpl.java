package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.GridCase;
import com.ruoyi.shcy.mapper.GridCaseMapper;
import com.ruoyi.shcy.service.IGridCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 网格化案事件Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-21
 */
@Service
public class GridCaseServiceImpl implements IGridCaseService 
{
    @Autowired
    private GridCaseMapper gridCaseMapper;

    /**
     * 查询网格化案事件
     * 
     * @param id 网格化案事件主键
     * @return 网格化案事件
     */
    @Override
    public GridCase selectGridCaseById(Long id)
    {
        return gridCaseMapper.selectGridCaseById(id);
    }

    /**
     * 查询网格化案事件列表
     * 
     * @param gridCase 网格化案事件
     * @return 网格化案事件
     */
    @Override
    public List<GridCase> selectGridCaseList(GridCase gridCase)
    {
        return gridCaseMapper.selectGridCaseList(gridCase);
    }

    /**
     * 新增网格化案事件
     * 
     * @param gridCase 网格化案事件
     * @return 结果
     */
    @Override
    public int insertGridCase(GridCase gridCase)
    {
        return gridCaseMapper.insertGridCase(gridCase);
    }

    /**
     * 修改网格化案事件
     * 
     * @param gridCase 网格化案事件
     * @return 结果
     */
    @Override
    public int updateGridCase(GridCase gridCase)
    {
        return gridCaseMapper.updateGridCase(gridCase);
    }

    /**
     * 批量删除网格化案事件
     * 
     * @param ids 需要删除的网格化案事件主键
     * @return 结果
     */
    @Override
    public int deleteGridCaseByIds(Long[] ids)
    {
        return gridCaseMapper.deleteGridCaseByIds(ids);
    }

    /**
     * 删除网格化案事件信息
     * 
     * @param id 网格化案事件主键
     * @return 结果
     */
    @Override
    public int deleteGridCaseById(Long id)
    {
        return gridCaseMapper.deleteGridCaseById(id);
    }
}
