package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.MapArea;

/**
 * 地图区域Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-07-08
 */
public interface MapAreaMapper 
{
    /**
     * 查询地图区域
     * 
     * @param areaId 地图区域主键
     * @return 地图区域
     */
    public MapArea selectMapAreaByAreaId(Long areaId);

    /**
     * 查询地图区域列表
     * 
     * @param mapArea 地图区域
     * @return 地图区域集合
     */
    public List<MapArea> selectMapAreaList(MapArea mapArea);

    /**
     * 新增地图区域
     * 
     * @param mapArea 地图区域
     * @return 结果
     */
    public int insertMapArea(MapArea mapArea);

    /**
     * 修改地图区域
     * 
     * @param mapArea 地图区域
     * @return 结果
     */
    public int updateMapArea(MapArea mapArea);

    /**
     * 删除地图区域
     * 
     * @param areaId 地图区域主键
     * @return 结果
     */
    public int deleteMapAreaByAreaId(Long areaId);

    /**
     * 批量删除地图区域
     * 
     * @param areaIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMapAreaByAreaIds(Long[] areaIds);
}
