package com.ruoyi.shcy.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.NucleicAcidLog;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.vo.EmployeesDetailVo;
import com.ruoyi.shcy.domain.vo.ShopDetailVo;
import com.ruoyi.shcy.mapper.NucleicAcidLogMapper;
import com.ruoyi.shcy.service.INucleicAcidLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 员工核酸情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@Service
public class NucleicAcidLogServiceImpl implements INucleicAcidLogService
{
    @Autowired
    private NucleicAcidLogMapper nucleicAcidLogMapper;

    /**
     * 查询员工核酸情况
     *
     * @param id 员工核酸情况主键
     * @return 员工核酸情况
     */
    @Override
    public NucleicAcidLog selectNucleicAcidLogById(Long id)
    {
        return nucleicAcidLogMapper.selectNucleicAcidLogById(id);
    }

    /**
     * 查询员工核酸情况列表
     *
     * @param nucleicAcidLog 员工核酸情况
     * @return 员工核酸情况
     */
    @Override
    public List<NucleicAcidLog> selectNucleicAcidLogList(NucleicAcidLog nucleicAcidLog)
    {
        return nucleicAcidLogMapper.selectNucleicAcidLogList(nucleicAcidLog);
    }

    /**
     * 新增员工核酸情况
     *
     * @param nucleicAcidLog 员工核酸情况
     * @return 结果
     */
    @Override
    public int insertNucleicAcidLog(NucleicAcidLog nucleicAcidLog)
    {
        nucleicAcidLog.setCreateTime(DateUtils.getNowDate());
        return nucleicAcidLogMapper.insertNucleicAcidLog(nucleicAcidLog);
    }

    /**
     * 修改员工核酸情况
     *
     * @param nucleicAcidLog 员工核酸情况
     * @return 结果
     */
    @Override
    public int updateNucleicAcidLog(NucleicAcidLog nucleicAcidLog)
    {
        nucleicAcidLog.setUpdateTime(DateUtils.getNowDate());
        return nucleicAcidLogMapper.updateNucleicAcidLog(nucleicAcidLog);
    }

    /**
     * 批量删除员工核酸情况
     *
     * @param ids 需要删除的员工核酸情况主键
     * @return 结果
     */
    @Override
    public int deleteNucleicAcidLogByIds(Long[] ids)
    {
        return nucleicAcidLogMapper.deleteNucleicAcidLogByIds(ids);
    }

    /**
     * 删除员工核酸情况信息
     *
     * @param id 员工核酸情况主键
     * @return 结果
     */
    @Override
    public int deleteNucleicAcidLogById(Long id)
    {
        return nucleicAcidLogMapper.deleteNucleicAcidLogById(id);
    }

    @Override
    public NucleicAcidLog selectNucleicAcidLogByEmployeeIdAndCheckDate(Long employeeId, String checkDate) {
        return nucleicAcidLogMapper.selectNucleicAcidLogByEmployeeIdAndCheckDate(employeeId, checkDate);
    }



//************************************************************日报-从业人员相关数据***************************************
//************************************************************日报-从业人员相关数据***************************************
    /**
     * 查询出来 所有从业人员
     *
     * @param shop
     */
    @Override
    public List<ShopDetailVo> selectEmployeesAll(Shop shop) {
        return  nucleicAcidLogMapper.selectEmployeesAll(shop);
    }

    /**
     * 今日巡查从业人员总数
     *
     * @param nucleicAcidLog
     **/
    @Override
    public List<ShopDetailVo> selectCheckEmployeesTotal(NucleicAcidLog nucleicAcidLog) {
        return  nucleicAcidLogMapper.selectCheckEmployeesTotal(nucleicAcidLog);
    }

    /**
     * 今日核酸正常人员总数
     *
     * @param nucleicAcidLog
     **/
    @Override
    public List<ShopDetailVo> selectCheckNormalEmployeesNum(NucleicAcidLog nucleicAcidLog) {
        return  nucleicAcidLogMapper.selectCheckNormalEmployeesNum(nucleicAcidLog);
    }

    /**
     * 今日核酸异常人员总数
     *
     * @param nucleicAcidLog
     **/
    @Override
    public List<ShopDetailVo> selectCheckAbnormalEmployeesNum(NucleicAcidLog nucleicAcidLog) {
        return nucleicAcidLogMapper.selectCheckAbnormalEmployeesNum(nucleicAcidLog);
    }

    /**
     * 今日离岗人员总数
     *
     * @param nucleicAcidLog
     **/
    @Override
    public List<ShopDetailVo> selectCheckRetiredEmployeesNum(NucleicAcidLog nucleicAcidLog) {
        return  nucleicAcidLogMapper.selectCheckRetiredEmployeesNum(nucleicAcidLog);
    }
}
