package com.ruoyi.shcy.service.impl;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.shcy.domain.WaterLevelRealtime;
import com.ruoyi.shcy.mapper.WaterLevelRealtimeMapper;
import com.ruoyi.shcy.service.IWaterLevelRealtimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@DataSource(value = DataSourceType.SLAVE2)
public class WaterLevelRealtimeServiceImpl implements IWaterLevelRealtimeService {


    @Autowired
    private WaterLevelRealtimeMapper waterLevelRealtimeMapper;

    @Override
    public WaterLevelRealtime selectWaterLevelRealtimeByWaterLevelId(Integer waterLevelId) {
        return waterLevelRealtimeMapper.selectWaterLevelRealtimeByWaterLevelId(waterLevelId);
    }
}
