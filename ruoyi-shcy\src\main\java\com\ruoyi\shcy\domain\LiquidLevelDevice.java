package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.ibatis.type.Alias;

/**
 * 液位超限感知设备对象 shcy_liquid_level_device
 *
 * <AUTHOR>
 * @date 2023-08-28
 */
@Alias("LiquidLevelDevice")
public class LiquidLevelDevice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 设备名称
     */
    @Excel(name = "设备名称")
    private String deviceName;

    /**
     * 设备 imei
     */
    @Excel(name = "设备imei")
    private String deviceImei;

    /**
     * 地址
     */
    @Excel(name = "地址")
    private String address;

    /**
     * 监测水体
     */
    @Excel(name = "监测水体")
    private String monitoredWaterBody;

    /**
     * 管网类型
     */
    @Excel(name = "管网类型")
    private String pipelineType;

    /**
     * 排水去向
     */
    @Excel(name = "排水去向")
    private String drainageDirection;

    /**
     * 负责单位
     */
    @Excel(name = "负责单位")
    private String responsibleUnit;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contactPerson;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String contactNumber;

    /**
     * 图形类型
     */
    private String type;

    /**
     * 坐标
     */
    private String coordinate;

    /**
     * 设备状态
     */
    private String deviceState;

    /**
     * 关联视频
     */
    private String cameras;

    /**
     * 图片
     */
    private String images;

    /**
     * 泵站区域
     */
    private String pumpStationArea;

    /**
     * 报警状态：0-未报警，1-报警
     */
    private String alarmStatus;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public String getDeviceImei() {
        return deviceImei;
    }

    public void setDeviceImei(String deviceImei) {
        this.deviceImei = deviceImei;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setMonitoredWaterBody(String monitoredWaterBody) {
        this.monitoredWaterBody = monitoredWaterBody;
    }

    public String getMonitoredWaterBody() {
        return monitoredWaterBody;
    }

    public void setPipelineType(String pipelineType) {
        this.pipelineType = pipelineType;
    }

    public String getPipelineType() {
        return pipelineType;
    }

    public String getDrainageDirection() {
        return drainageDirection;
    }

    public void setDrainageDirection(String drainageDirection) {
        this.drainageDirection = drainageDirection;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public String getResponsibleUnit() {
        return responsibleUnit;
    }

    public void setResponsibleUnit(String responsibleUnit) {
        this.responsibleUnit = responsibleUnit;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getDeviceState() {
        return deviceState;
    }

    public void setDeviceState(String deviceState) {
        this.deviceState = deviceState;
    }

    public String getCameras() {
        return cameras;
    }

    public void setCameras(String cameras) {
        this.cameras = cameras;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public String getPumpStationArea() {
        return pumpStationArea;
    }

    public void setPumpStationArea(String pumpStationArea) {
        this.pumpStationArea = pumpStationArea;
    }

    public String getAlarmStatus() {
        return alarmStatus;
    }

    public void setAlarmStatus(String alarmStatus) {
        this.alarmStatus = alarmStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("deviceName", getDeviceName())
                .append("deviceImei", getDeviceImei())
                .append("address", getAddress())
                .append("monitoredWaterBody", getMonitoredWaterBody())
                .append("pipelineType", getPipelineType())
                .append("drainageDirection", getDrainageDirection())
                .append("type", getType())
                .append("coordinate", getCoordinate())
                .append("responsibleUnit", getResponsibleUnit())
                .append("contactPerson", getContactPerson())
                .append("contactNumber", getContactNumber())
                .append("deviceState", getDeviceState())
                .append("cameras", getCameras())
                .append("images", getImages())
                .append("pumpStationArea", getPumpStationArea())
                .append("alarmStatus", getAlarmStatus())
                .toString();
    }
}
