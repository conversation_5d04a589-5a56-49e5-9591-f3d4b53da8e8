package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.Whitelist;

import java.util.List;

/**
 * 车牌白名单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
public interface WhitelistMapper 
{
    /**
     * 查询车牌白名单
     * 
     * @param id 车牌白名单主键
     * @return 车牌白名单
     */
    public Whitelist selectWhitelistById(Long id);

    /**
     * 查询车牌白名单列表
     * 
     * @param whitelist 车牌白名单
     * @return 车牌白名单集合
     */
    public List<Whitelist> selectWhitelistList(Whitelist whitelist);

    /**
     * 新增车牌白名单
     * 
     * @param whitelist 车牌白名单
     * @return 结果
     */
    public int insertWhitelist(Whitelist whitelist);

    /**
     * 修改车牌白名单
     * 
     * @param whitelist 车牌白名单
     * @return 结果
     */
    public int updateWhitelist(Whitelist whitelist);

    /**
     * 删除车牌白名单
     * 
     * @param id 车牌白名单主键
     * @return 结果
     */
    public int deleteWhitelistById(Long id);

    /**
     * 批量删除车牌白名单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWhitelistByIds(Long[] ids);
}
