package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.GridPartyMember;

/**
 * 网格党支部成员Service接口
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface IGridPartyMemberService 
{
    /**
     * 查询网格党支部成员
     * 
     * @param id 网格党支部成员主键
     * @return 网格党支部成员
     */
    public GridPartyMember selectGridPartyMemberById(Long id);

    /**
     * 查询网格党支部成员列表
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 网格党支部成员集合
     */
    public List<GridPartyMember> selectGridPartyMemberList(GridPartyMember gridPartyMember);

    /**
     * 新增网格党支部成员
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 结果
     */
    public int insertGridPartyMember(GridPartyMember gridPartyMember);

    /**
     * 修改网格党支部成员
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 结果
     */
    public int updateGridPartyMember(GridPartyMember gridPartyMember);

    /**
     * 批量删除网格党支部成员
     * 
     * @param ids 需要删除的网格党支部成员主键集合
     * @return 结果
     */
    public int deleteGridPartyMemberByIds(Long[] ids);

    /**
     * 删除网格党支部成员信息
     * 
     * @param id 网格党支部成员主键
     * @return 结果
     */
    public int deleteGridPartyMemberById(Long id);
}
