package com.ruoyi.shcy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.FxftCase;
import com.ruoyi.shcy.domain.LiquidLevelDevice;
import com.ruoyi.shcy.domain.NbiotyunAlarmRecord;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.mapper.FxftCaseMapper;
import com.ruoyi.shcy.mapper.LiquidLevelDeviceMapper;
import com.ruoyi.shcy.mapper.NbiotyunAlarmRecordMapper;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.service.IFxftCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 防汛防台案事件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
@Service
public class FxftCaseServiceImpl implements IFxftCaseService
{
    @Autowired
    private FxftCaseMapper fxftCaseMapper;

    @Autowired
    private NbiotyunAlarmRecordMapper nbiotyunAlarmRecordMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    @Autowired
    private LiquidLevelDeviceMapper liquidLevelDeviceMapper;

    /**
     * 查询防汛防台案事件
     *
     * @param id 防汛防台案事件主键
     * @return 防汛防台案事件
     */
    @Override
    public FxftCase selectFxftCaseById(Long id)
    {
        return handlePhoto(fxftCaseMapper.selectFxftCaseById(id));
    }

    /**
     * 查询防汛防台案事件列表
     *
     * @param fxftCase 防汛防台案事件
     * @return 防汛防台案事件
     */
    @Override
    public List<FxftCase> selectFxftCaseList(FxftCase fxftCase)
    {
        return fxftCaseMapper.selectFxftCaseList(fxftCase);
    }

    /**
     * 新增防汛防台案事件
     *
     * @param fxftCase 防汛防台案事件
     * @return 结果
     */
    @Override
    public int insertFxftCase(FxftCase fxftCase)
    {
        fxftCase.setCreateTime(DateUtils.getNowDate());
        return fxftCaseMapper.insertFxftCase(fxftCase);
    }

    /**
     * 修改防汛防台案事件
     *
     * @param fxftCase 防汛防台案事件
     * @return 结果
     */
    @Override
    public int updateFxftCase(FxftCase fxftCase)
    {
        fxftCase.setUpdateTime(DateUtils.getNowDate());
        return fxftCaseMapper.updateFxftCase(fxftCase);
    }

    /**
     * 批量删除防汛防台案事件
     *
     * @param ids 需要删除的防汛防台案事件主键
     * @return 结果
     */
    @Override
    public int deleteFxftCaseByIds(Long[] ids)
    {
        return fxftCaseMapper.deleteFxftCaseByIds(ids);
    }

    /**
     * 删除防汛防台案事件信息
     *
     * @param id 防汛防台案事件主键
     * @return 结果
     */
    @Override
    public int deleteFxftCaseById(Long id)
    {
        return fxftCaseMapper.deleteFxftCaseById(id);
    }

    @Override
    public List<FxftCase> selectFxftCaseListByCaseNumber(FxftCase fxftCase) {
        List<FxftCase> list=fxftCaseMapper.selectFxftCaseListByCaseNumber(fxftCase);
        list.stream().forEach(item->{
            if(StringUtils.isNotNull(item.getDeviceImei()))
            {
                LiquidLevelDevice theL=new LiquidLevelDevice();
                theL.setDeviceImei(item.getDeviceImei());
                List<LiquidLevelDevice> li =liquidLevelDeviceMapper.selectLiquidLevelDeviceList(theL);
                if(li.size()>0)
                {
                    item.setLiquidLevelDevice(li.get(0));
                }
                else {
                    item.setLiquidLevelDevice(new LiquidLevelDevice());
                }
            }
            NbiotyunAlarmRecord theI=nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordById(item.getAlarmRecordId());
            if(theI != null)
            {
                item.setNbiotyunAlarmRecord(theI);
            }
            else {
                item.setNbiotyunAlarmRecord(new NbiotyunAlarmRecord());
            }
        });

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int handleFxftCase(FxftCase fxftCase) {
        // 更新报警记录状态
        NbiotyunAlarmRecord alarmRecord = nbiotyunAlarmRecordMapper.selectNbiotyunAlarmRecordById(fxftCase.getAlarmRecordId());
        alarmRecord.setStatus(FxftConstants.PROCESSED);
        nbiotyunAlarmRecordMapper.updateNbiotyunAlarmRecord(alarmRecord);
        // 更新案件状态
        fxftCase.setCirculationState(FxftConstants.PROCESSED);
        fxftCase.setCaseFinishTime(DateUtils.getNowDate());
        // 判断当前时间是否小于fxftCase的caseEndTime
        if (DateUtils.getNowDate().compareTo(fxftCase.getCaseEndTime()) < 0) {
            fxftCase.setDealInTimeState(FxftConstants.OVERTIME_STATE_NORMAL);
        } else {
            fxftCase.setDealInTimeState(FxftConstants.OVERTIME_STATE_OVERTIME);
        }
        fxftCase.setCaseDealBy(SecurityUtils.getLoginUser().getUsername());
        fxftCase.setCheckUpdateBy(SecurityUtils.getLoginUser().getUsername());
        fxftCase.setCheckUpdateTime(DateUtils.getNowDate());
        return fxftCaseMapper.updateFxftCase(fxftCase);
    }

    @Override
    public FxftCase selectFxftCaseByAlarmRecordId(Long alarmRecordId) {
        return handlePhoto(fxftCaseMapper.selectFxftCaseByAlarmRecordId(alarmRecordId));
    }

    @Override
    public long getCaseCount(FxftCase fxftCase) {
        return fxftCaseMapper.getCaseCount(fxftCase);
    }

    public FxftCase handlePhoto(FxftCase fxftCase) {
        if (StrUtil.isNotEmpty(fxftCase.getCheckPhoto())) {
            List<String> photoUrls = new ArrayList<>();
            String[] fileIds = fxftCase.getCheckPhoto().split(",");
            for (String fileId : fileIds) {
                ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
                // 判断shcyFileInfo是否为空
                if (shcyFileInfo != null) {
                    photoUrls.add(shcyFileInfo.getFilePath());
                }
            }
            fxftCase.setPhotoUrls(photoUrls);
        }
        // if (StrUtil.isNotEmpty(fxftCase.getRescuePersonnelStatus())) {
        //     List<String> rescuePersonnelUrls = new ArrayList<>();
        //     String[] fileIds = fxftCase.getRescuePersonnelStatus().split(",");
        //     for (String fileId : fileIds) {
        //         ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
        //         rescuePersonnelUrls.add(shcyFileInfo.getFilePath());
        //     }
        //     fxftCase.setRescuePersonnelUrls(rescuePersonnelUrls);
        // }
        return fxftCase;
    }

}
