package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 公寓租赁房对象 shcy_apartment_rental
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public class ApartmentRental extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属居委会 */
    @Excel(name = "所属居委会")
    private String committee;

    /** 所属居委会id */
    @Excel(name = "所属居委会id")
    private Long committeeId;

    /** 所属小区 */
    @Excel(name = "所属小区")
    private String residential;

    /** 所属小区id */
    @Excel(name = "所属小区id")
    private Long residentialId;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 名称 */
    @Excel(name = "名称")
    private String name;


    /** 营业执照名称 */
    @Excel(name = "营业执照名称")
    private String licenseName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contacts;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactNumber;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String detailAddress;

    /** 房间数量 */
    @Excel(name = "房间数量")
    private Integer roomNumber;

    /** 从业人员数量 */
    @Excel(name = "从业人员数量")
    private Integer employeeNumber;


    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getDetailAddress() {
        return detailAddress;
    }

    public void setDetailAddress(String detailAddress) {
        this.detailAddress = detailAddress;
    }

    public Integer getRoomNumber() {
        return roomNumber;
    }

    public void setRoomNumber(Integer roomNumber) {
        this.roomNumber = roomNumber;
    }

    public Integer getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(Integer employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCommittee(String committee)
    {
        this.committee = committee;
    }

    public String getCommittee()
    {
        return committee;
    }
    public void setCommitteeId(Long committeeId)
    {
        this.committeeId = committeeId;
    }

    public Long getCommitteeId()
    {
        return committeeId;
    }
    public void setResidential(String residential)
    {
        this.residential = residential;
    }

    public String getResidential()
    {
        return residential;
    }
    public void setResidentialId(Long residentialId)
    {
        this.residentialId = residentialId;
    }

    public Long getResidentialId()
    {
        return residentialId;
    }
    public void setLongitude(String longitude)
    {
        this.longitude = longitude;
    }

    public String getLongitude()
    {
        return longitude;
    }
    public void setLatitude(String latitude)
    {
        this.latitude = latitude;
    }

    public String getLatitude()
    {
        return latitude;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "ApartmentRental{" +
                "id=" + id +
                ", committee='" + committee + '\'' +
                ", committeeId=" + committeeId +
                ", residential='" + residential + '\'' +
                ", residentialId=" + residentialId +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", name='" + name + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", contacts='" + contacts + '\'' +
                ", contactNumber='" + contactNumber + '\'' +
                ", detailAddress='" + detailAddress + '\'' +
                ", roomNumber=" + roomNumber +
                ", employeeNumber=" + employeeNumber +
                '}';
    }

}
