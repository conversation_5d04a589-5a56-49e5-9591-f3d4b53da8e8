package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 两类人员网格化对象 shcy_personnel_grid
 * 
 * <AUTHOR>
 * @date 2023-07-18
 */
public class PersonnelGrid extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String xm;

    /** 罪名 */
    @Excel(name = "罪名")
    private String zm;

    /** 期满日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "期满日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date qmrq;

    /** 户籍地地址 */
    @Excel(name = "户籍地地址")
    private String hxdd;

    /** 居住地地址 */
    @Excel(name = "居住地地址")
    private String jzdd;

    /** 居住地村(居)委 */
    private String jzdcjw;

    /** 社区矫正重点对象/社区矫正重要对象/重点关注安置帮教对象 */
    private String sqjzcbdx;

    /** 颜色等级 */
    private String ysdj;

    /** 纳入网格化管理原因 */
    private String nrwghglyy;

    /** 村(居)联络员姓名 */
    private String cjllxyxm;

    /** 村(居)联络员联系方式 */
    private String cjllxylxfs;

    /** 网格信息员姓名 */
    private String wgxxyxm;

    /** 网格信息员联系方式 */
    private String wgxxylxfs;

    /** 备注 */
    private String bz;

    /** 类型 */
    private String type;

    /** 坐标 */
    private String coordinate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setXm(String xm) 
    {
        this.xm = xm;
    }

    public String getXm() 
    {
        return xm;
    }
    public void setZm(String zm) 
    {
        this.zm = zm;
    }

    public String getZm() 
    {
        return zm;
    }
    public void setQmrq(Date qmrq) 
    {
        this.qmrq = qmrq;
    }

    public Date getQmrq() 
    {
        return qmrq;
    }
    public void setHxdd(String hxdd) 
    {
        this.hxdd = hxdd;
    }

    public String getHxdd() 
    {
        return hxdd;
    }
    public void setJzdd(String jzdd) 
    {
        this.jzdd = jzdd;
    }

    public String getJzdd() 
    {
        return jzdd;
    }
    public void setJzdcjw(String jzdcjw) 
    {
        this.jzdcjw = jzdcjw;
    }

    public String getJzdcjw() 
    {
        return jzdcjw;
    }
    public void setSqjzcbdx(String sqjzcbdx) 
    {
        this.sqjzcbdx = sqjzcbdx;
    }

    public String getSqjzcbdx() 
    {
        return sqjzcbdx;
    }
    public void setYsdj(String ysdj) 
    {
        this.ysdj = ysdj;
    }

    public String getYsdj() 
    {
        return ysdj;
    }
    public void setNrwghglyy(String nrwghglyy) 
    {
        this.nrwghglyy = nrwghglyy;
    }

    public String getNrwghglyy() 
    {
        return nrwghglyy;
    }
    public void setCjllxyxm(String cjllxyxm) 
    {
        this.cjllxyxm = cjllxyxm;
    }

    public String getCjllxyxm() 
    {
        return cjllxyxm;
    }
    public void setCjllxylxfs(String cjllxylxfs) 
    {
        this.cjllxylxfs = cjllxylxfs;
    }

    public String getCjllxylxfs() 
    {
        return cjllxylxfs;
    }
    public void setWgxxyxm(String wgxxyxm) 
    {
        this.wgxxyxm = wgxxyxm;
    }

    public String getWgxxyxm() 
    {
        return wgxxyxm;
    }
    public void setWgxxylxfs(String wgxxylxfs) 
    {
        this.wgxxylxfs = wgxxylxfs;
    }

    public String getWgxxylxfs() 
    {
        return wgxxylxfs;
    }
    public void setBz(String bz) 
    {
        this.bz = bz;
    }

    public String getBz() 
    {
        return bz;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setCoordinate(String coordinate) 
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate() 
    {
        return coordinate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("xm", getXm())
            .append("zm", getZm())
            .append("qmrq", getQmrq())
            .append("hxdd", getHxdd())
            .append("jzdd", getJzdd())
            .append("jzdcjw", getJzdcjw())
            .append("sqjzcbdx", getSqjzcbdx())
            .append("ysdj", getYsdj())
            .append("nrwghglyy", getNrwghglyy())
            .append("cjllxyxm", getCjllxyxm())
            .append("cjllxylxfs", getCjllxylxfs())
            .append("wgxxyxm", getWgxxyxm())
            .append("wgxxylxfs", getWgxxylxfs())
            .append("bz", getBz())
            .append("type", getType())
            .append("coordinate", getCoordinate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
