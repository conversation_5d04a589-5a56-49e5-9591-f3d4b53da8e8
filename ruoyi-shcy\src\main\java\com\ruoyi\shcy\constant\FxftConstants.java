package com.ruoyi.shcy.constant;

/**
 * fxft常数
 *
 * <AUTHOR>
 * @date 2023/11/02
 */
public class FxftConstants {

    // 已处理
    public static final String PROCESSED = "0";
    // 待处理
    public static final String UNPROCESSED = "1";
    // 误报
    public static final String FALSE_ALARM = "2";

    // 超时状态 正常
    public static final String OVERTIME_STATE_NORMAL = "0";
    // 超时状态 超时
    public static final String OVERTIME_STATE_OVERTIME = "1";

    // 报警状态 待处理
    public static final String ALARM_STATE_UNPROCESSED = "0";
    // 报警状态 处理中
    public static final String ALARM_STATE_PROCESSING = "1";
    // 报警状态 已处理
    public static final String ALARM_STATE_PROCESSED = "2";


}
