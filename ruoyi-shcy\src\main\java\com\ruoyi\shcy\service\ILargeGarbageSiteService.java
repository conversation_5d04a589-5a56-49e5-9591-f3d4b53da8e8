package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.LargeGarbageSite;

/**
 * 小区大件垃圾堆放点Service接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface ILargeGarbageSiteService 
{
    /**
     * 查询小区大件垃圾堆放点
     * 
     * @param id 小区大件垃圾堆放点主键
     * @return 小区大件垃圾堆放点
     */
    public LargeGarbageSite selectLargeGarbageSiteById(Long id);

    /**
     * 查询小区大件垃圾堆放点列表
     * 
     * @param largeGarbageSite 小区大件垃圾堆放点
     * @return 小区大件垃圾堆放点集合
     */
    public List<LargeGarbageSite> selectLargeGarbageSiteList(LargeGarbageSite largeGarbageSite);

    /**
     * 新增小区大件垃圾堆放点
     * 
     * @param largeGarbageSite 小区大件垃圾堆放点
     * @return 结果
     */
    public int insertLargeGarbageSite(LargeGarbageSite largeGarbageSite);

    /**
     * 修改小区大件垃圾堆放点
     * 
     * @param largeGarbageSite 小区大件垃圾堆放点
     * @return 结果
     */
    public int updateLargeGarbageSite(LargeGarbageSite largeGarbageSite);

    /**
     * 批量删除小区大件垃圾堆放点
     * 
     * @param ids 需要删除的小区大件垃圾堆放点主键集合
     * @return 结果
     */
    public int deleteLargeGarbageSiteByIds(Long[] ids);

    /**
     * 删除小区大件垃圾堆放点信息
     * 
     * @param id 小区大件垃圾堆放点主键
     * @return 结果
     */
    public int deleteLargeGarbageSiteById(Long id);
}
