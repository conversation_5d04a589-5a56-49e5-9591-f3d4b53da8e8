package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.IccDevice;
import com.ruoyi.shcy.service.IIccDeviceService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * icc监控设备Controller
 * 
 * <AUTHOR>
 * @date 2023-09-21
 */
@RestController
@RequestMapping("/shcy/iccDevice")
public class IccDeviceController extends BaseController
{
    @Autowired
    private IIccDeviceService iccDeviceService;

    /**
     * 查询icc监控设备列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccDevice:list')")
    @GetMapping("/list")
    public TableDataInfo list(IccDevice iccDevice)
    {
        startPage();
        List<IccDevice> list = iccDeviceService.selectIccDeviceList(iccDevice);
        return getDataTable(list);
    }

    /**
     * 导出icc监控设备列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccDevice:export')")
    @Log(title = "icc监控设备", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, IccDevice iccDevice)
    {
        List<IccDevice> list = iccDeviceService.selectIccDeviceList(iccDevice);
        ExcelUtil<IccDevice> util = new ExcelUtil<IccDevice>(IccDevice.class);
        util.exportExcel(response, list, "icc监控设备数据");
    }

    /**
     * 获取icc监控设备详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccDevice:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(iccDeviceService.selectIccDeviceById(id));
    }

    /**
     * 新增icc监控设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccDevice:add')")
    @Log(title = "icc监控设备", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody IccDevice iccDevice)
    {
        return toAjax(iccDeviceService.insertIccDevice(iccDevice));
    }

    /**
     * 修改icc监控设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccDevice:edit')")
    @Log(title = "icc监控设备", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody IccDevice iccDevice)
    {
        return toAjax(iccDeviceService.updateIccDevice(iccDevice));
    }

    /**
     * 删除icc监控设备
     */
    @PreAuthorize("@ss.hasPermi('shcy:iccDevice:remove')")
    @Log(title = "icc监控设备", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(iccDeviceService.deleteIccDeviceByIds(ids));
    }
}
