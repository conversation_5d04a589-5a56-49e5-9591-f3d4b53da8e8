package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.ShcyAfforest;
import com.ruoyi.shcy.service.IShcyAfforestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 绿化信息Controller
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
@RestController
@RequestMapping("/shcy/afforest")
public class ShcyAfforestController extends BaseController
{
    @Autowired
    private IShcyAfforestService shcyAfforestService;

    /**
     * 查询绿化信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforest:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyAfforest shcyAfforest)
    {
        startPage();
        List<ShcyAfforest> list = shcyAfforestService.selectShcyAfforestList(shcyAfforest);
        return getDataTable(list);
    }

    /**
     * 导出绿化信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforest:export')")
    @Log(title = "绿化信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyAfforest shcyAfforest)
    {
        List<ShcyAfforest> list = shcyAfforestService.selectShcyAfforestList(shcyAfforest);
        ExcelUtil<ShcyAfforest> util = new ExcelUtil<ShcyAfforest>(ShcyAfforest.class);
        util.exportExcel(response, list, "绿化信息数据");
    }

    /**
     * 获取绿化信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforest:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyAfforestService.selectShcyAfforestById(id));
    }

    /**
     * 新增绿化信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforest:add')")
    @Log(title = "绿化信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyAfforest shcyAfforest)
    {
        shcyAfforest.setCreateBy(getUsername());
        return toAjax(shcyAfforestService.insertShcyAfforest(shcyAfforest));
    }

    /**
     * 修改绿化信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforest:edit')")
    @Log(title = "绿化信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyAfforest shcyAfforest)
    {
        shcyAfforest.setUpdateBy(getUsername());
        return toAjax(shcyAfforestService.updateShcyAfforest(shcyAfforest));
    }

    /**
     * 删除绿化信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:afforest:remove')")
    @Log(title = "绿化信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyAfforestService.deleteShcyAfforestByIds(ids));
    }
}
