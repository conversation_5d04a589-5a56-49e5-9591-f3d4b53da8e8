package com.ruoyi.common.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class FfmpegUtils {

    public static boolean useCmd(String[] cmdLine){
        StringBuilder stringBuilder = new StringBuilder();
        Process process = null;
        int retval = -1;
        int exitval= -1;
        try {
            process = Runtime.getRuntime().exec(cmdLine);
            final InputStream is1 = process.getInputStream();
            new Thread(new Runnable() {
                public void run() {
                    BufferedReader bufferedReader = null;
                    String line = null;
                    try {
                        bufferedReader = new BufferedReader(new InputStreamReader(is1, "GBK"));
                        while ((line = bufferedReader.readLine()) != null) {
                            stringBuilder.append(line + "\n");
                        }
                        is1.close();
                    } catch (Exception e) {
                        // TODO Auto-generated catch block
                        e.printStackTrace();
                    }
                }
            }).start(); // 启动单独的线程来清空p.getInputStream()的缓冲区;
            InputStream is2 = process.getErrorStream();
            BufferedReader br2 = new BufferedReader(new InputStreamReader(is2));
            StringBuilder buf = new StringBuilder(); // 保存输出结果流
            String line2 = null;
            while((line2 = br2.readLine()) != null) buf.append(line2); //
            System.out.println("----res:----" + stringBuilder + "&" + buf);
            //return stringBuilder + "&" + buf;
            retval = process.waitFor();
            exitval = process.exitValue();

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
        if (retval == 0 && exitval == 0) {
            return true;
        }else{
            return false;
        }
    }
}
