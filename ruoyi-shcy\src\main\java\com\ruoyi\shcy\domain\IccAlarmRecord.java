package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.ibatis.type.Alias;

/**
 * ICC报警记录对象 icc_alarm_record
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
@Alias("IccAlarmRecord")
public class IccAlarmRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据库 ID */
    private Long id;

    /** 报警发生时间 */
    @Excel(name = "报警发生时间")
    private String alarmDate;

    /** 报警发生地点 */
    @Excel(name = "报警发生地点")
    private String alarmPosition;

    /** 报警事件名称 */
    @Excel(name = "报警事件名称")
    private String alarmTypeName;

    /** 报警事件类型 */
    @Excel(name = "报警事件类型")
    private Long alarmType;

    /** 报警所属组织 */
    @Excel(name = "报警所属组织")
    private String orgName;

    /** 报警状态 */
    @Excel(name = "报警状态")
    private Long alarmStat;

    /** 处理状态 */
    @Excel(name = "处理状态")
    private Long handleStat;

    /** 处理人 */
    @Excel(name = "处理人")
    private String handleUser;

    /** 处理时间 */
    @Excel(name = "处理时间")
    private String handleDate;

    /** 报警编号 */
    private String alarmCode;

    /** 联动能力 */
    private String linkNames;

    /** 报警等级 */
    private Long alarmGrade;

    /** 报警源编码 */
    private String nodeCode;

    /** 报警图片地址 */
    private String alarmPicture;

    /** 任务详情 */
    private String taskWebName;

    /** 参数 */
    private String alarmWebUrl;

    /** 参数 */
    private String alarmAppUrl;

    /** 参数 */
    private String taskWebUrl;

    /** 参数 */
    private String taskAppUrl;

    /**
     * 状态
     */
    private String status;

    /**
     * 牌照
     */
    private String licensePlate;

    /**
     * 车辆类型
     */
    private String vehicleType;

    /**
     * 负责人
     */
    private String responsiblePerson;

    private Long alarmRecordId;

    private Long carRecordId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAlarmDate(String alarmDate) 
    {
        this.alarmDate = alarmDate;
    }

    public String getAlarmDate() 
    {
        return alarmDate;
    }
    public void setAlarmPosition(String alarmPosition) 
    {
        this.alarmPosition = alarmPosition;
    }

    public String getAlarmPosition() 
    {
        return alarmPosition;
    }
    public void setAlarmTypeName(String alarmTypeName) 
    {
        this.alarmTypeName = alarmTypeName;
    }

    public String getAlarmTypeName() 
    {
        return alarmTypeName;
    }
    public void setAlarmType(Long alarmType) 
    {
        this.alarmType = alarmType;
    }

    public Long getAlarmType() 
    {
        return alarmType;
    }
    public void setOrgName(String orgName) 
    {
        this.orgName = orgName;
    }

    public String getOrgName() 
    {
        return orgName;
    }
    public void setAlarmStat(Long alarmStat) 
    {
        this.alarmStat = alarmStat;
    }

    public Long getAlarmStat() 
    {
        return alarmStat;
    }
    public void setHandleStat(Long handleStat) 
    {
        this.handleStat = handleStat;
    }

    public Long getHandleStat() 
    {
        return handleStat;
    }
    public void setHandleUser(String handleUser) 
    {
        this.handleUser = handleUser;
    }

    public String getHandleUser() 
    {
        return handleUser;
    }
    public void setHandleDate(String handleDate) 
    {
        this.handleDate = handleDate;
    }

    public String getHandleDate() 
    {
        return handleDate;
    }
    public void setAlarmCode(String alarmCode) 
    {
        this.alarmCode = alarmCode;
    }

    public String getAlarmCode() 
    {
        return alarmCode;
    }
    public void setLinkNames(String linkNames) 
    {
        this.linkNames = linkNames;
    }

    public String getLinkNames() 
    {
        return linkNames;
    }
    public void setAlarmGrade(Long alarmGrade) 
    {
        this.alarmGrade = alarmGrade;
    }

    public Long getAlarmGrade() 
    {
        return alarmGrade;
    }
    public void setNodeCode(String nodeCode) 
    {
        this.nodeCode = nodeCode;
    }

    public String getNodeCode() 
    {
        return nodeCode;
    }
    public void setAlarmPicture(String alarmPicture) 
    {
        this.alarmPicture = alarmPicture;
    }

    public String getAlarmPicture() 
    {
        return alarmPicture;
    }
    public void setTaskWebName(String taskWebName) 
    {
        this.taskWebName = taskWebName;
    }

    public String getTaskWebName() 
    {
        return taskWebName;
    }
    public void setAlarmWebUrl(String alarmWebUrl) 
    {
        this.alarmWebUrl = alarmWebUrl;
    }

    public String getAlarmWebUrl() 
    {
        return alarmWebUrl;
    }
    public void setAlarmAppUrl(String alarmAppUrl) 
    {
        this.alarmAppUrl = alarmAppUrl;
    }

    public String getAlarmAppUrl() 
    {
        return alarmAppUrl;
    }
    public void setTaskWebUrl(String taskWebUrl) 
    {
        this.taskWebUrl = taskWebUrl;
    }

    public String getTaskWebUrl() 
    {
        return taskWebUrl;
    }
    public void setTaskAppUrl(String taskAppUrl) 
    {
        this.taskAppUrl = taskAppUrl;
    }

    public String getTaskAppUrl() 
    {
        return taskAppUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getResponsiblePerson() {
        return responsiblePerson;
    }

    public void setResponsiblePerson(String responsiblePerson) {
        this.responsiblePerson = responsiblePerson;
    }

    public Long getAlarmRecordId() {
        return alarmRecordId;
    }

    public void setAlarmRecordId(Long alarmRecordId) {
        this.alarmRecordId = alarmRecordId;
    }

    public Long getCarRecordId() {
        return carRecordId;
    }

    public void setCarRecordId(Long carRecordId) {
        this.carRecordId = carRecordId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("alarmDate", getAlarmDate())
            .append("alarmPosition", getAlarmPosition())
            .append("alarmTypeName", getAlarmTypeName())
            .append("alarmType", getAlarmType())
            .append("orgName", getOrgName())
            .append("alarmStat", getAlarmStat())
            .append("handleStat", getHandleStat())
            .append("handleUser", getHandleUser())
            .append("handleDate", getHandleDate())
            .append("alarmCode", getAlarmCode())
            .append("linkNames", getLinkNames())
            .append("alarmGrade", getAlarmGrade())
            .append("nodeCode", getNodeCode())
            .append("alarmPicture", getAlarmPicture())
            .append("taskWebName", getTaskWebName())
            .append("alarmWebUrl", getAlarmWebUrl())
            .append("alarmAppUrl", getAlarmAppUrl())
            .append("taskWebUrl", getTaskWebUrl())
            .append("taskAppUrl", getTaskAppUrl())
            .append("status", getStatus())
            .append("licensePlate", getLicensePlate())
            .append("vehicleType", getVehicleType())
            .append("responsiblePerson", getResponsiblePerson())
            .append("alarmRecordId", getAlarmRecordId())
            .append("carRecordId", getCarRecordId())
            .toString();
    }
}
