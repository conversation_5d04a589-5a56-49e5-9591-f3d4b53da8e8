package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.LiquidLevelDevice;
import com.ruoyi.shcy.mapper.LiquidLevelDeviceMapper;
import com.ruoyi.shcy.service.ILiquidLevelDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 液位超限感知设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-28
 */
@Service
public class LiquidLevelDeviceServiceImpl implements ILiquidLevelDeviceService 
{
    @Autowired
    private LiquidLevelDeviceMapper liquidLevelDeviceMapper;

    /**
     * 查询液位超限感知设备
     * 
     * @param id 液位超限感知设备主键
     * @return 液位超限感知设备
     */
    @Override
    public LiquidLevelDevice selectLiquidLevelDeviceById(Long id)
    {
        return liquidLevelDeviceMapper.selectLiquidLevelDeviceById(id);
    }

    /**
     * 查询液位超限感知设备列表
     * 
     * @param liquidLevelDevice 液位超限感知设备
     * @return 液位超限感知设备
     */
    @Override
    public List<LiquidLevelDevice> selectLiquidLevelDeviceList(LiquidLevelDevice liquidLevelDevice)
    {
        return liquidLevelDeviceMapper.selectLiquidLevelDeviceList(liquidLevelDevice);
    }

    /**
     * 新增液位超限感知设备
     * 
     * @param liquidLevelDevice 液位超限感知设备
     * @return 结果
     */
    @Override
    public int insertLiquidLevelDevice(LiquidLevelDevice liquidLevelDevice)
    {
        return liquidLevelDeviceMapper.insertLiquidLevelDevice(liquidLevelDevice);
    }

    /**
     * 修改液位超限感知设备
     * 
     * @param liquidLevelDevice 液位超限感知设备
     * @return 结果
     */
    @Override
    public int updateLiquidLevelDevice(LiquidLevelDevice liquidLevelDevice)
    {
        return liquidLevelDeviceMapper.updateLiquidLevelDevice(liquidLevelDevice);
    }

    /**
     * 批量删除液位超限感知设备
     * 
     * @param ids 需要删除的液位超限感知设备主键
     * @return 结果
     */
    @Override
    public int deleteLiquidLevelDeviceByIds(Long[] ids)
    {
        return liquidLevelDeviceMapper.deleteLiquidLevelDeviceByIds(ids);
    }

    /**
     * 删除液位超限感知设备信息
     * 
     * @param id 液位超限感知设备主键
     * @return 结果
     */
    @Override
    public int deleteLiquidLevelDeviceById(Long id)
    {
        return liquidLevelDeviceMapper.deleteLiquidLevelDeviceById(id);
    }

    @Override
    public List<String> fxftAddress() {
        return liquidLevelDeviceMapper.fxftAddress();
    }
}
