package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.FxftCase;

import java.util.List;

/**
 * 防汛防台案事件Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-31
 */
public interface FxftCaseMapper 
{
    /**
     * 查询防汛防台案事件
     * 
     * @param id 防汛防台案事件主键
     * @return 防汛防台案事件
     */
    public FxftCase selectFxftCaseById(Long id);

    /**
     * 查询防汛防台案事件列表
     * 
     * @param fxftCase 防汛防台案事件
     * @return 防汛防台案事件集合
     */
    public List<FxftCase> selectFxftCaseList(FxftCase fxftCase);

    /**
     * 新增防汛防台案事件
     * 
     * @param fxftCase 防汛防台案事件
     * @return 结果
     */
    public int insertFxftCase(FxftCase fxftCase);

    /**
     * 修改防汛防台案事件
     * 
     * @param fxftCase 防汛防台案事件
     * @return 结果
     */
    public int updateFxftCase(FxftCase fxftCase);

    /**
     * 删除防汛防台案事件
     * 
     * @param id 防汛防台案事件主键
     * @return 结果
     */
    public int deleteFxftCaseById(Long id);

    /**
     * 批量删除防汛防台案事件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFxftCaseByIds(Long[] ids);

    public List<FxftCase> selectFxftCaseListByCaseNumber(FxftCase fxftCase);

    public FxftCase selectFxftCaseByAlarmRecordId(Long alarmRecordId);

    public long getCaseCount(FxftCase fxftCase);
}
