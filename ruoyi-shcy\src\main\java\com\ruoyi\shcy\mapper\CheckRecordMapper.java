package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.CheckRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 商铺巡查通过记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
public interface CheckRecordMapper
{
    /**
     * 查询商铺巡查通过记录
     *
     * @param id 商铺巡查通过记录主键
     * @return 商铺巡查通过记录
     */
    public CheckRecord selectCheckRecordById(Long id);

    /**
     * 查询商铺巡查通过记录列表
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 商铺巡查通过记录集合
     */
    public List<CheckRecord> selectCheckRecordList(CheckRecord checkRecord);

    /**
     * 新增商铺巡查通过记录
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 结果
     */
    public int insertCheckRecord(CheckRecord checkRecord);

    /**
     * 修改商铺巡查通过记录
     *
     * @param checkRecord 商铺巡查通过记录
     * @return 结果
     */
    public int updateCheckRecord(CheckRecord checkRecord);

    /**
     * 删除商铺巡查通过记录
     *
     * @param id 商铺巡查通过记录主键
     * @return 结果
     */
    public int deleteCheckRecordById(Long id);

    /**
     * 批量删除商铺巡查通过记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCheckRecordByIds(Long[] ids);



    CheckRecord selectCheckRecordByShopIdAndCheckDate(@Param("shopId") Long shopId, @Param("checkDate") Date checkDate);



    /**
     * 查询出 已巡查商铺数量
     * **/
    public List<CheckRecord> selectCheckedShopNum(CheckRecord checkRecord);

    /**
     * 查询出检查通过商铺数量
     *
     */
    public List<CheckRecord> selectCheckAndPassShop(CheckRecord checkRecord);

    /**
     * 查询检查未通过商铺数量
     *
     * **/
    public List<CheckRecord> selectCheckAndNoPassShop(CheckRecord checkRecord);

    /**
     * 查询出未检查商铺数量
     *
     * **/
    public List<CheckRecord> selectNoCheckShop(CheckRecord checkRecord);


    List<CheckRecord> selectCheckRecordShopList();
}
