package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyFileInfo;

/**
 * 文件信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-05-19
 */
public interface IShcyFileInfoService 
{
    /**
     * 查询文件信息
     * 
     * @param fileId 文件信息主键
     * @return 文件信息
     */
    public ShcyFileInfo selectShcyFileInfoByFileId(Long fileId);

    /**
     * 查询文件信息列表
     * 
     * @param shcyFileInfo 文件信息
     * @return 文件信息集合
     */
    public List<ShcyFileInfo> selectShcyFileInfoList(ShcyFileInfo shcyFileInfo);

    /**
     * 新增文件信息
     * 
     * @param shcyFileInfo 文件信息
     * @return 结果
     */
    public int insertShcyFileInfo(ShcyFileInfo shcyFileInfo);

    /**
     * 修改文件信息
     * 
     * @param shcyFileInfo 文件信息
     * @return 结果
     */
    public int updateShcyFileInfo(ShcyFileInfo shcyFileInfo);

    /**
     * 批量删除文件信息
     * 
     * @param fileIds 需要删除的文件信息主键集合
     * @return 结果
     */
    public int deleteShcyFileInfoByFileIds(Long[] fileIds);

    /**
     * 删除文件信息信息
     * 
     * @param fileId 文件信息主键
     * @return 结果
     */
    public int deleteShcyFileInfoByFileId(Long fileId);
}
