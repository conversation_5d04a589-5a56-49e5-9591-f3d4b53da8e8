package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ResidentialEntranceMapper;
import com.ruoyi.shcy.domain.ResidentialEntrance;
import com.ruoyi.shcy.service.IResidentialEntranceService;

/**
 * 小区出入门口Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class ResidentialEntranceServiceImpl implements IResidentialEntranceService 
{
    @Autowired
    private ResidentialEntranceMapper residentialEntranceMapper;

    /**
     * 查询小区出入门口
     * 
     * @param id 小区出入门口主键
     * @return 小区出入门口
     */
    @Override
    public ResidentialEntrance selectResidentialEntranceById(Long id)
    {
        return residentialEntranceMapper.selectResidentialEntranceById(id);
    }

    /**
     * 查询小区出入门口列表
     * 
     * @param residentialEntrance 小区出入门口
     * @return 小区出入门口
     */
    @Override
    public List<ResidentialEntrance> selectResidentialEntranceList(ResidentialEntrance residentialEntrance)
    {
        return residentialEntranceMapper.selectResidentialEntranceList(residentialEntrance);
    }

    /**
     * 新增小区出入门口
     * 
     * @param residentialEntrance 小区出入门口
     * @return 结果
     */
    @Override
    public int insertResidentialEntrance(ResidentialEntrance residentialEntrance)
    {
        return residentialEntranceMapper.insertResidentialEntrance(residentialEntrance);
    }

    /**
     * 修改小区出入门口
     * 
     * @param residentialEntrance 小区出入门口
     * @return 结果
     */
    @Override
    public int updateResidentialEntrance(ResidentialEntrance residentialEntrance)
    {
        return residentialEntranceMapper.updateResidentialEntrance(residentialEntrance);
    }

    /**
     * 批量删除小区出入门口
     * 
     * @param ids 需要删除的小区出入门口主键
     * @return 结果
     */
    @Override
    public int deleteResidentialEntranceByIds(Long[] ids)
    {
        return residentialEntranceMapper.deleteResidentialEntranceByIds(ids);
    }

    /**
     * 删除小区出入门口信息
     * 
     * @param id 小区出入门口主键
     * @return 结果
     */
    @Override
    public int deleteResidentialEntranceById(Long id)
    {
        return residentialEntranceMapper.deleteResidentialEntranceById(id);
    }
}
