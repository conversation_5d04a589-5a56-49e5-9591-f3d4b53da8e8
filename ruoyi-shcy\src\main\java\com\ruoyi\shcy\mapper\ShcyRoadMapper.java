package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyRoad;

/**
 * 路段名Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-02-09
 */
public interface ShcyRoadMapper 
{
    /**
     * 查询路段名
     * 
     * @param id 路段名主键
     * @return 路段名
     */
    public ShcyRoad selectShcyRoadById(Long id);

    /**
     * 查询路段名列表
     * 
     * @param shcyRoad 路段名
     * @return 路段名集合
     */
    public List<ShcyRoad> selectShcyRoadList(ShcyRoad shcyRoad);

    /**
     * 新增路段名
     * 
     * @param shcyRoad 路段名
     * @return 结果
     */
    public int insertShcyRoad(ShcyRoad shcyRoad);

    /**
     * 修改路段名
     * 
     * @param shcyRoad 路段名
     * @return 结果
     */
    public int updateShcyRoad(ShcyRoad shcyRoad);

    /**
     * 删除路段名
     * 
     * @param id 路段名主键
     * @return 结果
     */
    public int deleteShcyRoadById(Long id);

    /**
     * 批量删除路段名
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyRoadByIds(Long[] ids);
}
