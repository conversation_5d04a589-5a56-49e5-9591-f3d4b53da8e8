package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.RainwaterSewagePipe;

/**
 * 雨污管道信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-21
 */
public interface RainwaterSewagePipeMapper 
{
    /**
     * 查询雨污管道信息
     * 
     * @param id 雨污管道信息主键
     * @return 雨污管道信息
     */
    public RainwaterSewagePipe selectRainwaterSewagePipeById(Long id);

    /**
     * 查询雨污管道信息列表
     * 
     * @param rainwaterSewagePipe 雨污管道信息
     * @return 雨污管道信息集合
     */
    public List<RainwaterSewagePipe> selectRainwaterSewagePipeList(RainwaterSewagePipe rainwaterSewagePipe);

    /**
     * 新增雨污管道信息
     * 
     * @param rainwaterSewagePipe 雨污管道信息
     * @return 结果
     */
    public int insertRainwaterSewagePipe(RainwaterSewagePipe rainwaterSewagePipe);

    /**
     * 修改雨污管道信息
     * 
     * @param rainwaterSewagePipe 雨污管道信息
     * @return 结果
     */
    public int updateRainwaterSewagePipe(RainwaterSewagePipe rainwaterSewagePipe);

    /**
     * 删除雨污管道信息
     * 
     * @param id 雨污管道信息主键
     * @return 结果
     */
    public int deleteRainwaterSewagePipeById(Long id);

    /**
     * 批量删除雨污管道信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRainwaterSewagePipeByIds(Long[] ids);
}
