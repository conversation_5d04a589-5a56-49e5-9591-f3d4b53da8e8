package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 防汛防台案事件对象 shcy_fxft_case
 *
 * <AUTHOR>
 * @date 2023-10-31
 */
public class FxftCase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 事件名称 */
    @Excel(name = "事件名称")
    private String caseName;

    /** 事件类型 */
    @Excel(name = "事件类型")
    private String caseType;

    /** 事件描述 */
    @Excel(name = "事件描述")
    private String caseDescription;

    /** 事件处理人 */
    @Excel(name = "事件处理人")
    private String caseDealBy;

    /** 处理照片信息 */
    @Excel(name = "处理照片信息")
    private String caseDealPhoto;

    /** 处置状态 */
    @Excel(name = "处置状态")
    private String circulationState;

    /** 按时完成状态 */
    private String dealInTimeState;

    /** 地址 */
    private String address;

    /** 报警记录id */
    private Long alarmRecordId;

    private NbiotyunAlarmRecord nbiotyunAlarmRecord;

    private LiquidLevelDevice liquidLevelDevice;

    /** 事件截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseEndTime;

    /** 事件完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseFinishTime;

    /** 事件建议 */
    private String checkOthers;

    /** 抢险作业照片 */
    private String checkPhoto;

    /** 事件修改人 */
    private String checkUpdateBy;

    /** 事件修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkUpdateTime;

    /** 设备imei */
    @Excel(name = "设备imei")
    private String deviceImei;

    /** 事件编号 */
    @Excel(name = "事件编号")
    private String caseNumber;

    /** 情况是否属实 */
    private String rescuePersonnelStatus;

    /** 抢险措施 */
    private String rescueAction;

    private List<String> photoUrls;

    private List<String> rescuePersonnelUrls;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCaseName(String caseName)
    {
        this.caseName = caseName;
    }

    public String getCaseName()
    {
        return caseName;
    }
    public void setCaseType(String caseType)
    {
        this.caseType = caseType;
    }

    public String getCaseType()
    {
        return caseType;
    }
    public void setCaseDescription(String caseDescription)
    {
        this.caseDescription = caseDescription;
    }

    public String getCaseDescription()
    {
        return caseDescription;
    }
    public void setCaseDealBy(String caseDealBy)
    {
        this.caseDealBy = caseDealBy;
    }

    public String getCaseDealBy()
    {
        return caseDealBy;
    }
    public void setCaseDealPhoto(String caseDealPhoto)
    {
        this.caseDealPhoto = caseDealPhoto;
    }

    public String getCaseDealPhoto()
    {
        return caseDealPhoto;
    }
    public void setCirculationState(String circulationState)
    {
        this.circulationState = circulationState;
    }

    public String getCirculationState()
    {
        return circulationState;
    }
    public void setDealInTimeState(String dealInTimeState)
    {
        this.dealInTimeState = dealInTimeState;
    }

    public String getDealInTimeState()
    {
        return dealInTimeState;
    }
    public void setAddress(String address)
    {
        this.address = address;
    }

    public String getAddress()
    {
        return address;
    }
    public void setAlarmRecordId(Long alarmRecordId)
    {
        this.alarmRecordId = alarmRecordId;
    }

    public Long getAlarmRecordId()
    {
        return alarmRecordId;
    }
    public void setCaseEndTime(Date caseEndTime)
    {
        this.caseEndTime = caseEndTime;
    }

    public Date getCaseEndTime()
    {
        return caseEndTime;
    }
    public void setCaseFinishTime(Date caseFinishTime)
    {
        this.caseFinishTime = caseFinishTime;
    }

    public Date getCaseFinishTime()
    {
        return caseFinishTime;
    }
    public void setCheckOthers(String checkOthers)
    {
        this.checkOthers = checkOthers;
    }

    public String getCheckOthers()
    {
        return checkOthers;
    }
    public void setCheckPhoto(String checkPhoto)
    {
        this.checkPhoto = checkPhoto;
    }

    public String getCheckPhoto()
    {
        return checkPhoto;
    }
    public void setCheckUpdateBy(String checkUpdateBy)
    {
        this.checkUpdateBy = checkUpdateBy;
    }

    public String getCheckUpdateBy()
    {
        return checkUpdateBy;
    }
    public void setCheckUpdateTime(Date checkUpdateTime)
    {
        this.checkUpdateTime = checkUpdateTime;
    }

    public Date getCheckUpdateTime()
    {
        return checkUpdateTime;
    }

    public String getDeviceImei() {
        return deviceImei;
    }

    public void setDeviceImei(String deviceImei) {
        this.deviceImei = deviceImei;
    }

    public String getCaseNumber() {
        return caseNumber;
    }

    public void setCaseNumber(String caseNumber) {
        this.caseNumber = caseNumber;
    }

    public List<String> getPhotoUrls() {
        return photoUrls;
    }

    public void setPhotoUrls(List<String> photoUrls) {
        this.photoUrls = photoUrls;
    }

    public String getRescuePersonnelStatus() {
        return rescuePersonnelStatus;
    }

    public void setRescuePersonnelStatus(String rescuePersonnelStatus) {
        this.rescuePersonnelStatus = rescuePersonnelStatus;
    }

    public List<String> getRescuePersonnelUrls() {
        return rescuePersonnelUrls;
    }

    public void setRescuePersonnelUrls(List<String> rescuePersonnelUrls) {
        this.rescuePersonnelUrls = rescuePersonnelUrls;
    }

    public NbiotyunAlarmRecord getNbiotyunAlarmRecord() {
        return nbiotyunAlarmRecord;
    }

    public void setNbiotyunAlarmRecord(NbiotyunAlarmRecord nbiotyunAlarmRecord) {
        this.nbiotyunAlarmRecord = nbiotyunAlarmRecord;
    }

    public LiquidLevelDevice getLiquidLevelDevice() {
        return liquidLevelDevice;
    }

    public void setLiquidLevelDevice(LiquidLevelDevice liquidLevelDevice) {
        this.liquidLevelDevice = liquidLevelDevice;
    }

    public String getRescueAction() {
        return rescueAction;
    }

    public void setRescueAction(String rescueAction) {
        this.rescueAction = rescueAction;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("caseName", getCaseName())
            .append("caseType", getCaseType())
            .append("caseDescription", getCaseDescription())
            .append("caseDealBy", getCaseDealBy())
            .append("caseDealPhoto", getCaseDealPhoto())
            .append("circulationState", getCirculationState())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("dealInTimeState", getDealInTimeState())
            .append("address", getAddress())
            .append("alarmRecordId", getAlarmRecordId())
            .append("caseEndTime", getCaseEndTime())
            .append("caseFinishTime", getCaseFinishTime())
            .append("checkOthers", getCheckOthers())
            .append("checkPhoto", getCheckPhoto())
            .append("checkUpdateBy", getCheckUpdateBy())
            .append("checkUpdateTime", getCheckUpdateTime())
            .append("deviceImei", getDeviceImei())
            .append("caseNumber", getCaseNumber())
            .append("rescuePersonnelStatus", getRescuePersonnelStatus())
            .append("nbiotyunAlarmRecord", getNbiotyunAlarmRecord())
            .append("liquidLevelDevice", getLiquidLevelDevice())
            .append("rescueAction", getRescueAction())
            .toString();
    }
}
