package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.FloodShelterInfo;
import com.ruoyi.shcy.service.IFloodShelterInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 防汛安置点信息Controller
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
@RestController
@RequestMapping("/shcy/floodShelterInfo")
public class FloodShelterInfoController extends BaseController
{
    @Autowired
    private IFloodShelterInfoService floodShelterInfoService;

    /**
     * 查询防汛安置点信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodShelterInfo:list')")
    @GetMapping("/list")
    public TableDataInfo list(FloodShelterInfo floodShelterInfo)
    {
        startPage();
        List<FloodShelterInfo> list = floodShelterInfoService.selectFloodShelterInfoList(floodShelterInfo);
        return getDataTable(list);
    }

    /**
     * 导出防汛安置点信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodShelterInfo:export')")
    @Log(title = "防汛安置点信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FloodShelterInfo floodShelterInfo)
    {
        List<FloodShelterInfo> list = floodShelterInfoService.selectFloodShelterInfoList(floodShelterInfo);
        ExcelUtil<FloodShelterInfo> util = new ExcelUtil<FloodShelterInfo>(FloodShelterInfo.class);
        util.exportExcel(response, list, "防汛安置点信息数据");
    }

    /**
     * 获取防汛安置点信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodShelterInfo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(floodShelterInfoService.selectFloodShelterInfoById(id));
    }

    /**
     * 新增防汛安置点信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodShelterInfo:add')")
    @Log(title = "防汛安置点信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FloodShelterInfo floodShelterInfo)
    {
        return toAjax(floodShelterInfoService.insertFloodShelterInfo(floodShelterInfo));
    }

    /**
     * 修改防汛安置点信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodShelterInfo:edit')")
    @Log(title = "防汛安置点信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FloodShelterInfo floodShelterInfo)
    {
        return toAjax(floodShelterInfoService.updateFloodShelterInfo(floodShelterInfo));
    }

    /**
     * 删除防汛安置点信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodShelterInfo:remove')")
    @Log(title = "防汛安置点信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(floodShelterInfoService.deleteFloodShelterInfoByIds(ids));
    }
}
