package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 短信发送记录对象 shcy_sms_record
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public class SmsRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 短信记录ID */
    private Long smsId;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phoneNumber;

    /** 短信内容 */
    @Excel(name = "短信内容")
    private String smsContent;

    /** 模板Code */
    @Excel(name = "模板Code")
    private String templateCode;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /** 发送状态（0成功 1失败） */
    private String status;

    public void setSmsId(Long smsId) 
    {
        this.smsId = smsId;
    }

    public Long getSmsId() 
    {
        return smsId;
    }
    public void setPhoneNumber(String phoneNumber) 
    {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumber() 
    {
        return phoneNumber;
    }
    public void setSmsContent(String smsContent) 
    {
        this.smsContent = smsContent;
    }

    public String getSmsContent() 
    {
        return smsContent;
    }
    public void setTemplateCode(String templateCode) 
    {
        this.templateCode = templateCode;
    }

    public String getTemplateCode() 
    {
        return templateCode;
    }
    public void setSendTime(Date sendTime) 
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime() 
    {
        return sendTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("smsId", getSmsId())
            .append("phoneNumber", getPhoneNumber())
            .append("smsContent", getSmsContent())
            .append("templateCode", getTemplateCode())
            .append("sendTime", getSendTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
