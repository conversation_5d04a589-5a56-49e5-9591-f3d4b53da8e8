package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.Institutions;

/**
 * 企事业单位Service接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface IInstitutionsService 
{
    /**
     * 查询企事业单位
     * 
     * @param id 企事业单位主键
     * @return 企事业单位
     */
    public Institutions selectInstitutionsById(Long id);

    /**
     * 查询企事业单位列表
     * 
     * @param institutions 企事业单位
     * @return 企事业单位集合
     */
    public List<Institutions> selectInstitutionsList(Institutions institutions);

    /**
     * 新增企事业单位
     * 
     * @param institutions 企事业单位
     * @return 结果
     */
    public int insertInstitutions(Institutions institutions);

    /**
     * 修改企事业单位
     * 
     * @param institutions 企事业单位
     * @return 结果
     */
    public int updateInstitutions(Institutions institutions);

    /**
     * 批量删除企事业单位
     * 
     * @param ids 需要删除的企事业单位主键集合
     * @return 结果
     */
    public int deleteInstitutionsByIds(Long[] ids);

    /**
     * 删除企事业单位信息
     * 
     * @param id 企事业单位主键
     * @return 结果
     */
    public int deleteInstitutionsById(Long id);
}
