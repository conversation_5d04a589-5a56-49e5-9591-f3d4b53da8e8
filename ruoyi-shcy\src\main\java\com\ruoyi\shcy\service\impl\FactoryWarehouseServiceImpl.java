package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.FactoryWarehouseMapper;
import com.ruoyi.shcy.domain.FactoryWarehouse;
import com.ruoyi.shcy.service.IFactoryWarehouseService;

/**
 * 厂房仓库Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
@Service
public class FactoryWarehouseServiceImpl implements IFactoryWarehouseService 
{
    @Autowired
    private FactoryWarehouseMapper factoryWarehouseMapper;

    /**
     * 查询厂房仓库
     * 
     * @param id 厂房仓库主键
     * @return 厂房仓库
     */
    @Override
    public FactoryWarehouse selectFactoryWarehouseById(Long id)
    {
        return factoryWarehouseMapper.selectFactoryWarehouseById(id);
    }

    /**
     * 查询厂房仓库列表
     * 
     * @param factoryWarehouse 厂房仓库
     * @return 厂房仓库
     */
    @Override
    public List<FactoryWarehouse> selectFactoryWarehouseList(FactoryWarehouse factoryWarehouse)
    {
        return factoryWarehouseMapper.selectFactoryWarehouseList(factoryWarehouse);
    }

    /**
     * 新增厂房仓库
     * 
     * @param factoryWarehouse 厂房仓库
     * @return 结果
     */
    @Override
    public int insertFactoryWarehouse(FactoryWarehouse factoryWarehouse)
    {
        return factoryWarehouseMapper.insertFactoryWarehouse(factoryWarehouse);
    }

    /**
     * 修改厂房仓库
     * 
     * @param factoryWarehouse 厂房仓库
     * @return 结果
     */
    @Override
    public int updateFactoryWarehouse(FactoryWarehouse factoryWarehouse)
    {
        return factoryWarehouseMapper.updateFactoryWarehouse(factoryWarehouse);
    }

    /**
     * 批量删除厂房仓库
     * 
     * @param ids 需要删除的厂房仓库主键
     * @return 结果
     */
    @Override
    public int deleteFactoryWarehouseByIds(Long[] ids)
    {
        return factoryWarehouseMapper.deleteFactoryWarehouseByIds(ids);
    }

    /**
     * 删除厂房仓库信息
     * 
     * @param id 厂房仓库主键
     * @return 结果
     */
    @Override
    public int deleteFactoryWarehouseById(Long id)
    {
        return factoryWarehouseMapper.deleteFactoryWarehouseById(id);
    }
}
