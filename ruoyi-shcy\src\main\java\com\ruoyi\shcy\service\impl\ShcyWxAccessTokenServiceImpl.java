package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyWxAccessTokenMapper;
import com.ruoyi.shcy.domain.ShcyWxAccessToken;
import com.ruoyi.shcy.service.IShcyWxAccessTokenService;

/**
 * 【请填写功能名称】Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-18
 */
@Service
public class ShcyWxAccessTokenServiceImpl implements IShcyWxAccessTokenService 
{
    @Autowired
    private ShcyWxAccessTokenMapper shcyWxAccessTokenMapper;

    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ShcyWxAccessToken selectShcyWxAccessTokenById(Long id)
    {
        return shcyWxAccessTokenMapper.selectShcyWxAccessTokenById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param shcyWxAccessToken 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ShcyWxAccessToken> selectShcyWxAccessTokenList(ShcyWxAccessToken shcyWxAccessToken)
    {
        return shcyWxAccessTokenMapper.selectShcyWxAccessTokenList(shcyWxAccessToken);
    }

    /**
     * 新增【请填写功能名称】
     * 
     * @param shcyWxAccessToken 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertShcyWxAccessToken(ShcyWxAccessToken shcyWxAccessToken)
    {
        return shcyWxAccessTokenMapper.insertShcyWxAccessToken(shcyWxAccessToken);
    }

    /**
     * 修改【请填写功能名称】
     * 
     * @param shcyWxAccessToken 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateShcyWxAccessToken(ShcyWxAccessToken shcyWxAccessToken)
    {
        return shcyWxAccessTokenMapper.updateShcyWxAccessToken(shcyWxAccessToken);
    }

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteShcyWxAccessTokenByIds(Long[] ids)
    {
        return shcyWxAccessTokenMapper.deleteShcyWxAccessTokenByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteShcyWxAccessTokenById(Long id)
    {
        return shcyWxAccessTokenMapper.deleteShcyWxAccessTokenById(id);
    }
}
