package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.SpecialTopicMapper;
import com.ruoyi.shcy.domain.SpecialTopic;
import com.ruoyi.shcy.service.ISpecialTopicService;

/**
 * 重点专项Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-16
 */
@Service
public class SpecialTopicServiceImpl implements ISpecialTopicService 
{
    @Autowired
    private SpecialTopicMapper specialTopicMapper;

    /**
     * 查询重点专项
     * 
     * @param id 重点专项主键
     * @return 重点专项
     */
    @Override
    public SpecialTopic selectSpecialTopicById(Long id)
    {
        return specialTopicMapper.selectSpecialTopicById(id);
    }

    /**
     * 查询重点专项列表
     * 
     * @param specialTopic 重点专项
     * @return 重点专项
     */
    @Override
    public List<SpecialTopic> selectSpecialTopicList(SpecialTopic specialTopic)
    {
        return specialTopicMapper.selectSpecialTopicList(specialTopic);
    }

    /**
     * 新增重点专项
     * 
     * @param specialTopic 重点专项
     * @return 结果
     */
    @Override
    public int insertSpecialTopic(SpecialTopic specialTopic)
    {
        return specialTopicMapper.insertSpecialTopic(specialTopic);
    }

    /**
     * 修改重点专项
     * 
     * @param specialTopic 重点专项
     * @return 结果
     */
    @Override
    public int updateSpecialTopic(SpecialTopic specialTopic)
    {
        return specialTopicMapper.updateSpecialTopic(specialTopic);
    }

    /**
     * 批量删除重点专项
     * 
     * @param ids 需要删除的重点专项主键
     * @return 结果
     */
    @Override
    public int deleteSpecialTopicByIds(Long[] ids)
    {
        return specialTopicMapper.deleteSpecialTopicByIds(ids);
    }

    /**
     * 删除重点专项信息
     * 
     * @param id 重点专项主键
     * @return 结果
     */
    @Override
    public int deleteSpecialTopicById(Long id)
    {
        return specialTopicMapper.deleteSpecialTopicById(id);
    }
}
