package com.ruoyi.shcy.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.dahuatech.hutool.core.bean.BeanUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.constant.HjzzConstants;
import com.ruoyi.shcy.domain.JsDbcenter;
import com.ruoyi.shcy.domain.ShcyWgh;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.domain.vo.DbCenterRxJmqVO;
import com.ruoyi.shcy.domain.vo.DbCenterRxMyVO;
import com.ruoyi.shcy.domain.vo.RxReportCfgdVO;
import com.ruoyi.shcy.domain.vo.RxReportVO;
import com.ruoyi.shcy.mapper.TaskDbcenterMapper;
import com.ruoyi.shcy.service.IJsDbcenterService;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import javafx.collections.transformation.SortedList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 网格化案件信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-13
 */
@Service
public class TaskDbcenterServiceImpl implements ITaskDbcenterService 
{
    @Autowired
    private TaskDbcenterMapper taskDbcenterMapper;

    @Autowired
    private IJsDbcenterService jsDbcenterService;

    /**
     * 查询网格化案件信息
     * 
     * @param id 网格化案件信息主键
     * @return 网格化案件信息
     */
    @Override
    public TaskDbcenter selectTaskDbcenterById(Long id)
    {
        return taskDbcenterMapper.selectTaskDbcenterById(id);
    }

    /**
     * 查询网格化案件信息列表
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 网格化案件信息
     */
    @Override
    public List<TaskDbcenter> selectTaskDbcenterList(TaskDbcenter taskDbcenter)
    {
        return taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
    }

    /**
     * 新增网格化案件信息
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 结果
     */
    @Override
    public int insertTaskDbcenter(TaskDbcenter taskDbcenter)
    {
        return taskDbcenterMapper.insertTaskDbcenter(taskDbcenter);
    }

    /**
     * 修改网格化案件信息
     * 
     * @param taskDbcenter 网格化案件信息
     * @return 结果
     */
    @Override
    public int updateTaskDbcenter(TaskDbcenter taskDbcenter)
    {
        return taskDbcenterMapper.updateTaskDbcenter(taskDbcenter);
    }

    /**
     * 批量删除网格化案件信息
     * 
     * @param ids 需要删除的网格化案件信息主键
     * @return 结果
     */
    @Override
    public int deleteTaskDbcenterByIds(Long[] ids)
    {
        return taskDbcenterMapper.deleteTaskDbcenterByIds(ids);
    }

    /**
     * 删除网格化案件信息信息
     * 
     * @param id 网格化案件信息主键
     * @return 结果
     */
    @Override
    public int deleteTaskDbcenterById(Long id)
    {
        return taskDbcenterMapper.deleteTaskDbcenterById(id);
    }

    @Override
    public List<Long> selectTaskDbcenterIdList() {
        return taskDbcenterMapper.selectTaskDbcenterIdList();
    }

    @Override
    public List<Long> selectTaskDbcenterIdListBySynctime(String synctime) {
        return taskDbcenterMapper.selectTaskDbcenterIdListBySynctime(synctime);
    }

    @Override
    public TaskDbcenter selectTaskDbcenterByHotlinesn(String hotlinesn) {
        return taskDbcenterMapper.selectTaskDbcenterByHotlinesn(hotlinesn);
    }

    @Override
    public void handleRelatedTaskDbcenter(TaskDbcenter taskDbcenter) {

        // 判断最近的工单是否未空
        if (StrUtil.isEmpty(taskDbcenter.getRecenthotlinesn())) {
            return;
        }

        // 前继工单
        TaskDbcenter preDbcenter = taskDbcenterMapper.selectTaskDbcenterByHotlinesn(taskDbcenter.getRecenthotlinesn());
        if (preDbcenter == null) {
            taskDbcenter.setRelatedhotlinesn(IdUtil.simpleUUID());
        } else {
            if (StrUtil.isNotEmpty(preDbcenter.getRelatedhotlinesn())) {
                taskDbcenter.setRelatedhotlinesn(preDbcenter.getRelatedhotlinesn());
            } else {
                preDbcenter.setRelatedhotlinesn(IdUtil.simpleUUID());
                taskDbcenterMapper.updateTaskDbcenter(preDbcenter);
                taskDbcenter.setRelatedhotlinesn(preDbcenter.getRelatedhotlinesn());
            }
        }
        // 后继工单
        handleSubsequent(taskDbcenter);
    }

    @Override
    public List<TaskDbcenter> selectTaskDbcenterSyncList() {
        return taskDbcenterMapper.selectTaskDbcenterSyncList();
    }

    @Override
    public List<Long> selectTaskDbcenterSyncIdList() {
        return taskDbcenterMapper.selectTaskDbcenterSyncIdList();
    }

    @Override
    public List<String> selectTaskDbcenterSyncTaskidList() {
        return taskDbcenterMapper.selectTaskDbcenterSyncTaskidList();
    }

    @Override
    public int deleteTaskDbcenterNotShjd() {
        return taskDbcenterMapper.deleteTaskDbcenterNotShjd();
    }

    @Override
    public int syncDbcenterRxByTaskids(String[] taskids) {
        List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenterSyncListByTaskid(taskids);
        for (JsDbcenter jsDbcenter : jsDbcenters) {
            TaskDbcenter taskDbcenter = new TaskDbcenter();
            BeanUtil.copyProperties(jsDbcenter, taskDbcenter);
            taskDbcenterMapper.updateTaskDbcenter(taskDbcenter);
        }
        return 1;
    }

    @Override
    public int syncDbcenter(String[] dataList) {
        // dataList[0]
        String startTime = dataList[0];
        // dataList[1]的字符串格式化后向后推一天
        String endTime = DateUtil.offsetDay(DateUtil.parse(dataList[1]), 1).toString();
        List<Long> ids = taskDbcenterMapper.selectTaskDbcenterIdList();
        List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenterCaseList(startTime, endTime);
        //遍历jsDbcenters判断JsDbcenter.getId()是否在ids中，如果不在则插入
        for (JsDbcenter jsDbcenter : jsDbcenters) {
            TaskDbcenter dbcenter = new TaskDbcenter();
            BeanUtil.copyProperties(jsDbcenter, dbcenter);
            if (!ids.contains(jsDbcenter.getId())) {
                taskDbcenterMapper.insertTaskDbcenter(dbcenter);
            } else {
                taskDbcenterMapper.updateTaskDbcenter(dbcenter);
            }
        }
        return 1;
    }

    @Override
    public List<TaskDbcenter> selectTaskDbcenterRxList(TaskDbcenter taskDbcenter) {
        return taskDbcenterMapper.selectTaskDbcenterRxList(taskDbcenter);
    }

    @Override
    public List<TaskDbcenter> selectTaskDbcenterListDesc(TaskDbcenter taskDbcenter) {
        return taskDbcenterMapper.selectTaskDbcenterListDesc(taskDbcenter);
    }

    @Override
    public List<DbCenterRxJmqVO> selectTaskDbcenterListJmq(TaskDbcenter taskDbcenter) {
        String[] subexecutedeptnameMh={"三村居民区","四村居民区","柳城居民区","七村居民区","九村居民区","十村居民区","合浦居民区","十二村居民区",
                "十三村居民区","梅州居民区", "海棠居民区","临蒙居民区","临三居民区","东礁一居民区","东礁二居民区","东泉居民区",
                "辰凯居民区","山鑫居民区","滨一居民区","滨二居民区","东村居民区","山龙居民区","桥园居民区","卫清居民区","紫卫居民区","合生居民区"};

        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxJmqVO> list=new ArrayList<DbCenterRxJmqVO>();
        for(int i= 0;i<subexecutedeptnameMh.length;i++)
        {
            DbCenterRxJmqVO theD=new DbCenterRxJmqVO();
            theD.setDeptName(subexecutedeptnameMh[i]);
            String dept=subexecutedeptnameMh[i];
            int zbNum = (int) allRelateds.stream().filter(item -> dept.equals(item.getSubexecutedeptnameMh())).count();
            theD.setZbNum(zbNum);
            list.add(theD);
        }
        //超期
        taskDbcenter.setOverdue("超期");
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int cqNum = (int) allRelateds1.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setCqNum(cqNum);
            List<TaskDbcenter> cqList=  allRelateds1.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).collect(Collectors.toList());
            StringBuilder sb=new StringBuilder();
            for(TaskDbcenter theTaskDbcenter:cqList)
            {
                sb.append(theTaskDbcenter.getHotlinesn());
            }
            item.setCqGdh(sb.toString());
        });


        int lastyear= 0;
        int month =0;
        try {
            month=getMonthFromDate(taskDbcenter.getParams().get("beginTime").toString())+1;
            if(month >= 11)
            {
                lastyear= getYearFromDate(taskDbcenter.getParams().get("beginTime").toString());
            }
            else
            {
                lastyear = getYearFromDate(taskDbcenter.getParams().get("beginTime").toString())-1;
            }

        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("beginTime",lastyear+"-11-01 00:00:00");
        taskDbcenter.setOverdue(null);
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int zbNum1 = (int) allRelateds2.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setZbNum1(zbNum1);

        });

        //超期
        taskDbcenter.setOverdue("超期");
        taskDbcenter.getParams().put("cqFlag",null);
        List<TaskDbcenter> cqlist = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int cqNum1 = (int) cqlist.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setCqNum1(cqNum1);
        });
        return list;
    }

    @Override
    public List<DbCenterRxJmqVO> selectTaskDbcenterListKs(TaskDbcenter taskDbcenter) {
        String[] subexecutedeptnameMh={"石化街道党政办公室","石化街道社区党群办公室","石化街道社区管理办","石化街道服务办","石化街道社区平安办公室",
                "石化街道社会工作办公室","石化街道干部人事办公室","石化街道营商环境办公室","石化街道社区事务受理中心","石化街道建管中心",
                "石化街道综合行政执法队","石化街道城运中心（网格化）","石化街道城运中心（应急）","石化街道管违办","昭缘经济发展有限公司",
                 "石化街道社区平安办隔离点", "石化街道社区党群服务中心"};
        taskDbcenter.setOverdue(null);
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxJmqVO> list=new ArrayList<DbCenterRxJmqVO>();
        for(int i= 0;i<subexecutedeptnameMh.length;i++)
        {
            DbCenterRxJmqVO theD=new DbCenterRxJmqVO();
            theD.setDeptName(subexecutedeptnameMh[i]);
            String dept=subexecutedeptnameMh[i];
            int zbNum;
            if("石化街道综合行政执法队".equals(dept)) {
                // 统计执法队和联勤联动的数据
                zbNum = (int) allRelateds.stream()
                        .filter(item -> dept.equals(item.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item.getSubexecutedeptnameMh()))
                        .count();
            } else {
                zbNum = (int) allRelateds.stream()
                        .filter(item -> dept.equals(item.getSubexecutedeptnameMh()))
                        .count();
            }
            theD.setZbNum(zbNum);
            list.add(theD);
        }

        int lastyear= 0;
        int month =0;
        try {
            month=getMonthFromDate(taskDbcenter.getParams().get("beginTime").toString())+1;
            if(month >= 11)
            {
                lastyear= getYearFromDate(taskDbcenter.getParams().get("beginTime").toString());
            }
            else
            {
                lastyear = getYearFromDate(taskDbcenter.getParams().get("beginTime").toString())-1;
            }

        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("beginTime",lastyear+"-11-01 00:00:00");
//        taskDbcenter.getParams().put("cqFlag",false);
        taskDbcenter.setOverdue(null);
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int zbNum1;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                // 统计执法队和联勤联动的数据
                zbNum1 = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                zbNum1 = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setZbNum1(zbNum1);

            if("石化街道党政办公室".equals(item.getDeptName()))
            {
                item.setDeptName("党政办");
            }
            else  if("石化街道社区党群办公室".equals(item.getDeptName()))
            {
                item.setDeptName("党群办");
            }
            else  if("石化街道社区管理办".equals(item.getDeptName()))
            {
                item.setDeptName("管理办");
            }
            else  if("石化街道服务办".equals(item.getDeptName()))
            {
                item.setDeptName("服务办");
            }
            else  if("石化街道社区平安办公室".equals(item.getDeptName()))
            {
                item.setDeptName("平安办");
            }
            else  if("石化街道社会工作办公室".equals(item.getDeptName()))
            {
                item.setDeptName("社会工作办");
            }
            else  if("石化街道干部人事办公室".equals(item.getDeptName()))
            {
                item.setDeptName("干部人事办");
            }
            else  if("石化街道营商环境办公室".equals(item.getDeptName()))
            {
                item.setDeptName("营商办");
            }
            else  if("石化街道社区事务受理中心".equals(item.getDeptName()))
            {
                item.setDeptName("受理中心");
            }
            else  if("石化街道建管中心".equals(item.getDeptName()))
            {
                item.setDeptName("建管中心");
            }
            else  if("石化街道综合行政执法队".equals(item.getDeptName()))
            {
                item.setDeptName("综合执法队");
            }
            else  if("石化街道城运中心(网格化)".equals(item.getDeptName()))
            {
                item.setDeptName("城运中心(网格)");
            }
            else  if("石化街道城运中心(应急）".equals(item.getDeptName()))
            {
                item.setDeptName("城运中心(应急)");
            }
            else  if("石化街道管违办".equals(item.getDeptName()))
            {
                item.setDeptName("管违办");
            }
            else  if("昭缘经济发展有限公司".equals(item.getDeptName()))
            {
                item.setDeptName("昭缘经发公司");
            }
            else  if("石化街道社区平安办隔离点".equals(item.getDeptName()))
            {
                item.setDeptName("平安办隔离点");
            }
            else  if("石化街道社区党群服务中心".equals(item.getDeptName()))
            {
                item.setDeptName("党群服务中心");
            }

        });

        return list;
    }

    @Override
    public DbCenterRxMyVO selectTaskDbcenterListMy(TaskDbcenter taskDbcenter) {
        String da = null;
        try {
            da = getYear(taskDbcenter.getParams().get("beginTime").toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("evaBeginTime",da);
        taskDbcenter.getParams().put("evaEndTime",da);
        taskDbcenter.getParams().put("beginTime",null);
        taskDbcenter.getParams().put("endTime",null);
        taskDbcenter.setSatisfaction("满意");
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        DbCenterRxMyVO theDbCenterRxMyVO=new DbCenterRxMyVO();
        theDbCenterRxMyVO.setMy(allRelateds.size());
        taskDbcenter.setSatisfaction("基本满意");
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        theDbCenterRxMyVO.setJbmy(allRelateds1.size());
        taskDbcenter.setSatisfaction("一般");
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        theDbCenterRxMyVO.setYb(allRelateds2.size());
        taskDbcenter.setSatisfaction("不满意");
        List<TaskDbcenter> allRelateds3 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        theDbCenterRxMyVO.setBmy(allRelateds3.size());
        return theDbCenterRxMyVO;
    }

    @Override
    public List<DbCenterRxMyVO> selectTaskDbcenterListMyQk(TaskDbcenter taskDbcenter) {
        String da = null;
        String da1 = null;
        try {
            da = getYear(taskDbcenter.getParams().get("endTime").toString());
            da1 = getYear(taskDbcenter.getParams().get("beginTime").toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("evaBeginTime",da1);
        taskDbcenter.getParams().put("evaEndTime",da);
        taskDbcenter.getParams().put("beginTime",null);
        taskDbcenter.getParams().put("endTime",null);
        //获取三级主责部门信息,满意度不为空
        List<TaskDbcenter> depts = taskDbcenterMapper.selectDeptList(taskDbcenter);
        Map<String,TaskDbcenter> d=new HashMap<>();
        for(TaskDbcenter theTaskDbcenter:depts)
        {
            if(StringUtils.isNotEmpty(theTaskDbcenter.getSubexecutedeptnameMh()))
            {
                // 跳过联勤联动
                if(!"联勤联动".equals(theTaskDbcenter.getSubexecutedeptnameMh())) {
                    d.put(theTaskDbcenter.getSubexecutedeptnameMh(),theTaskDbcenter);
                }
            }
        }

        taskDbcenter.setSatisfaction("满意");
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxMyVO> list=new ArrayList<DbCenterRxMyVO>();
        for(String key: d.keySet())
        {
            DbCenterRxMyVO theD=new DbCenterRxMyVO();
            theD.setDeptName(key);
            int my;
            if("石化街道综合行政执法队".equals(key)) {
                // 统计执法队和联勤联动的数据
                my = (int) allRelateds.stream()
                        .filter(item1 -> key.equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                my = (int) allRelateds.stream()
                        .filter(item1 -> key.equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            theD.setMy(my);
            list.add(theD);
        }

        taskDbcenter.setSatisfaction("不满意");
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int bmy;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                bmy = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                bmy = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setBmy(bmy);
        });

        taskDbcenter.setSatisfaction("基本满意");
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int jbmy;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                jbmy = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                jbmy = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setJbmy(jbmy);
        });

        taskDbcenter.setSatisfaction("一般");
        List<TaskDbcenter> allRelateds3= taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int yb;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                yb = (int) allRelateds3.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                yb = (int) allRelateds3.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setYb(yb);
        });
        DecimalFormat df = new DecimalFormat("#0.00");
        list.stream().forEach(item->{
            item.setHj(item.getBmy()+item.getJbmy()+item.getMy()+item.getYb());
            if(item.getHj() != 0)
            {
                item.setMyl( df.format(((float)item.getMy()+(float)item.getJbmy()*0.8+(float)item.getYb()*0.6)/item.getHj()*100));
            }
            else {
                item.setMyl("0");
            }
        });

        return list;
    }


    @Override
    public List<DbCenterRxMyVO> selectTaskDbcenterListMyQs(TaskDbcenter taskDbcenter) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

        LocalDate start = DateUtil.date(formatter.parse(taskDbcenter.getParams().get("beginTime").toString())).toLocalDateTime().toLocalDate();
        LocalDate end = DateUtil.date(formatter.parse(taskDbcenter.getParams().get("endTime").toString())).toLocalDateTime().toLocalDate();
        List<String> yearMonths = getYearMonths(start, end);
        List<DbCenterRxMyVO>  list=new ArrayList<DbCenterRxMyVO>();
        for(String month:yearMonths)
        {
            DbCenterRxMyVO theD=new DbCenterRxMyVO();
            theD.setNianyue(month);
            String da = month;
            String da1 = month;
            taskDbcenter.getParams().put("evaBeginTime",da1);
            taskDbcenter.getParams().put("evaEndTime",da);
            taskDbcenter.getParams().put("beginTime",null);
            taskDbcenter.getParams().put("endTime",null);
            taskDbcenter.setSatisfaction("满意");
            List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
            DbCenterRxMyVO theDbCenterRxMyVO=new DbCenterRxMyVO();
            theD.setMy(allRelateds.size());
            taskDbcenter.setSatisfaction("基本满意");
            List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
            theD.setJbmy(allRelateds1.size());
            taskDbcenter.setSatisfaction("一般");
            List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
            theD.setYb(allRelateds2.size());
            taskDbcenter.setSatisfaction("不满意");
            List<TaskDbcenter> allRelateds3 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
            theD.setBmy(allRelateds3.size());
            list.add(theD);
        }

        return list;
    }

    @Override
    public List<String> selectTaskDbcenterHotlinesnList() {
        return taskDbcenterMapper.selectTaskDbcenterHotlinesnList();
    }

    @Override
    public List<TaskDbcenter> selectSimpleTaskDbcenterList(TaskDbcenter taskDbcenter) {
        return taskDbcenterMapper.selectSimpleTaskDbcenterList(taskDbcenter);
    }


    @Override
    public List<DbCenterRxMyVO> selectTaskDbcenterListMyQkJmq(TaskDbcenter taskDbcenter) {
        String da = null;
        try {
            da = getYear(taskDbcenter.getParams().get("beginTime").toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("evaBeginTime",da);
        taskDbcenter.getParams().put("evaEndTime",da);
        taskDbcenter.getParams().put("beginTime",null);
        taskDbcenter.getParams().put("endTime",null);
        //获取三级主责部门信息,满意度不为空
        List<TaskDbcenter> depts = taskDbcenterMapper.selectDeptList(taskDbcenter);
        Map<String,TaskDbcenter> d=new HashMap<>();
        for(TaskDbcenter theTaskDbcenter:depts)
        {
            if(StringUtils.isNotEmpty(theTaskDbcenter.getSubexecutedeptnameMh()) && theTaskDbcenter.getSubexecutedeptnameMh().contains("居民区") )
            {
                d.put(theTaskDbcenter.getSubexecutedeptnameMh(),theTaskDbcenter);
            }

        }
        taskDbcenter.setSatisfaction("满意");
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxMyVO> list=new ArrayList<DbCenterRxMyVO>();
        for(String key: d.keySet())
        {
            DbCenterRxMyVO theD=new DbCenterRxMyVO();
            theD.setDeptName(key);
            int my = (int) allRelateds.stream().filter(item1 -> key.equals(item1.getSubexecutedeptnameMh())).count();
            theD.setMy(my);
            list.add(theD);
        }
        taskDbcenter.setSatisfaction("不满意");
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int bmy = (int) allRelateds1.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setBmy(bmy);
        });

        taskDbcenter.setSatisfaction("基本满意");
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int jbmy = (int) allRelateds2.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setJbmy(jbmy);
        });
        taskDbcenter.setSatisfaction("一般");
        List<TaskDbcenter> allRelateds3= taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int yb = (int) allRelateds3.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setYb(yb);
        });
        DecimalFormat df = new DecimalFormat("#0.00");
        list.stream().forEach(item->{
            item.setHj(item.getBmy()+item.getJbmy()+item.getMy()+item.getYb());
            if(item.getHj() != 0)
            {
                item.setMyl( df.format(((float)item.getMy()+(float)item.getJbmy()*0.8+(float)item.getYb()*0.6)/item.getHj()*100));
            }
            else {
                item.setMyl("0");
            }
        });

        return list;
    }

    @Override
    public List<DbCenterRxMyVO> selectTaskDbcenterListMyQkKs(TaskDbcenter taskDbcenter) {
        String da = null;
        try {
            da = getYear(taskDbcenter.getParams().get("beginTime").toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("evaBeginTime",da);
        taskDbcenter.getParams().put("evaEndTime",da);
        taskDbcenter.getParams().put("beginTime",null);
        taskDbcenter.getParams().put("endTime",null);
        //获取三级主责部门信息,满意度不为空
        List<TaskDbcenter> depts = taskDbcenterMapper.selectDeptList(taskDbcenter);
        Map<String,TaskDbcenter> d=new HashMap<>();
        for(TaskDbcenter theTaskDbcenter:depts)
        {
            if(StringUtils.isNotEmpty(theTaskDbcenter.getSubexecutedeptnameMh()) && !theTaskDbcenter.getSubexecutedeptnameMh().contains("居民区") )
            {
                // 跳过联勤联动
                if(!"联勤联动".equals(theTaskDbcenter.getSubexecutedeptnameMh())) {
                    d.put(theTaskDbcenter.getSubexecutedeptnameMh(),theTaskDbcenter);
                }
            }

        }
        taskDbcenter.setSatisfaction("满意");
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxMyVO> list=new ArrayList<DbCenterRxMyVO>();
        for(String key: d.keySet())
        {
            DbCenterRxMyVO theD=new DbCenterRxMyVO();
            theD.setDeptName(key);
            int my;
            if("石化街道综合行政执法队".equals(key)) {
                // 统计执法队和联勤联动的数据
                my = (int) allRelateds.stream()
                        .filter(item1 -> key.equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                my = (int) allRelateds.stream()
                        .filter(item1 -> key.equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            theD.setMy(my);
            list.add(theD);
        }
        taskDbcenter.setSatisfaction("不满意");
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int bmy;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                bmy = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                bmy = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setBmy(bmy);
        });

        taskDbcenter.setSatisfaction("基本满意");
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int jbmy;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                jbmy = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                jbmy = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setJbmy(jbmy);
        });
        taskDbcenter.setSatisfaction("一般");
        List<TaskDbcenter> allRelateds3= taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int yb;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                yb = (int) allRelateds3.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                yb = (int) allRelateds3.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setYb(yb);
        });
        DecimalFormat df = new DecimalFormat("#0.00");
        list.stream().forEach(item->{
            item.setHj(item.getBmy()+item.getJbmy()+item.getMy()+item.getYb());
            if(item.getHj() != 0)
            {
                item.setMyl( df.format(((float)item.getMy()+(float)item.getJbmy()*0.8+(float)item.getYb()*0.6)/item.getHj()*100));
            }
            else {
                item.setMyl("0");
            }
        });

        return list;
    }

    @Override
    public List<RxReportVO> selectListByParentappealclassification(TaskDbcenter taskDbcenter) {
        return taskDbcenterMapper.selectListByParentappealclassification(taskDbcenter);
    }

    @Override
    public List<RxReportVO> selectXlByParentappealclassification(TaskDbcenter taskDbcenter) {
        return taskDbcenterMapper.selectXlByParentappealclassification(taskDbcenter);
    }

    @Override
    public List<RxReportVO> selectJmqByParentappealclassification(TaskDbcenter taskDbcenter) {
        return taskDbcenterMapper.selectJmqByParentappealclassification(taskDbcenter);
    }

    @Override
    public List<RxReportVO> selectTaskDbcenterListGroupByResidentialarea(TaskDbcenter taskDbcenter) {
        return taskDbcenterMapper.selectTaskDbcenterListGroupByResidentialarea(taskDbcenter);
    }

    @Override
    public List<DbCenterRxMyVO> selectTaskDbcenterListMyd(TaskDbcenter taskDbcenter) {
        int lastyear= 0;
        int month =0;
        String da=null;
        try {
            month=getMonthFromDate(taskDbcenter.getParams().get("beginTime").toString())+1;
            if(month >= 11)
            {
                lastyear= getYearFromDate(taskDbcenter.getParams().get("beginTime").toString());
            }
            else
            {
                lastyear = getYearFromDate(taskDbcenter.getParams().get("beginTime").toString())-1;
            }
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        try {
            da = getYear(taskDbcenter.getParams().get("endTime").toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("evaBeginTime",lastyear+"-11");
        taskDbcenter.getParams().put("evaEndTime",da);
        taskDbcenter.getParams().put("beginTime",null);
        taskDbcenter.getParams().put("endTime",null);
        List<TaskDbcenter> depts = taskDbcenterMapper.selectDeptList(taskDbcenter);
        DecimalFormat df = new DecimalFormat("#0.00");

        Map<String,TaskDbcenter> d=new HashMap<>();
        for(TaskDbcenter theTaskDbcenter:depts)
        {
            if(StringUtils.isNotEmpty(theTaskDbcenter.getSubexecutedeptnameMh()))
            {
                // 跳过联勤联动
                if(!"联勤联动".equals(theTaskDbcenter.getSubexecutedeptnameMh())) {
                    d.put(theTaskDbcenter.getSubexecutedeptnameMh(),theTaskDbcenter);
                }
            }
        }

        taskDbcenter.setSatisfaction("满意");
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxMyVO> list=new ArrayList<DbCenterRxMyVO>();
        for(String key: d.keySet())
        {
            DbCenterRxMyVO theD=new DbCenterRxMyVO();
            theD.setDeptName(key);
            int my;
            if("石化街道综合行政执法队".equals(key)) {
                // 统计执法队和联勤联动的数据
                my = (int) allRelateds.stream()
                        .filter(item1 -> key.equals(item1.getSubexecutedeptnameMh()) || 
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                my = (int) allRelateds.stream()
                        .filter(item1 -> key.equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            theD.setMy(my);
            list.add(theD);
        }

        taskDbcenter.setSatisfaction("不满意");
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int bmy;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                bmy = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) || 
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                bmy = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setBmy(bmy);
        });

        taskDbcenter.setSatisfaction("基本满意");
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int jbmy;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                jbmy = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) || 
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                jbmy = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setJbmy(jbmy);
        });

        taskDbcenter.setSatisfaction("一般");
        List<TaskDbcenter> allRelateds3= taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int yb;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                yb = (int) allRelateds3.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) || 
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                yb = (int) allRelateds3.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setYb(yb);
        });
        list.stream().forEach(item->{
            item.setHj(item.getBmy()+item.getJbmy()+item.getMy()+item.getYb());
            if(item.getHj() != 0)
            {
                item.setMyl( df.format(((float)item.getMy()+(float)item.getJbmy()*0.8+(float)item.getYb()*0.6)/item.getHj()*100));
            }
            else {
                item.setMyl("0");
            }
        });
        Comparator<DbCenterRxMyVO> byMyl=Comparator.comparing(DbCenterRxMyVO::getMyl);
        List<DbCenterRxMyVO> li=list.stream().sorted(byMyl.reversed()).collect(Collectors.toList());

        return li;
    }

    @Override
    public List<DbCenterRxMyVO> selectTaskDbcenterListMydJmq(TaskDbcenter taskDbcenter) {
        int lastyear= 0;
        int month =0;
        String da=null;
        try {
            month=getMonthFromDate(taskDbcenter.getParams().get("beginTime").toString())+1;
            if(month >= 11)
            {
                lastyear= getYearFromDate(taskDbcenter.getParams().get("beginTime").toString());
            }
            else
            {
                lastyear = getYearFromDate(taskDbcenter.getParams().get("beginTime").toString())-1;
            }

        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        try {
            da = getYear(taskDbcenter.getParams().get("endTime").toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("evaBeginTime",lastyear+"-11");
        taskDbcenter.getParams().put("evaEndTime",da);
        taskDbcenter.getParams().put("beginTime",null);
        taskDbcenter.getParams().put("endTime",null);
        List<TaskDbcenter> depts = taskDbcenterMapper.selectDeptList(taskDbcenter);
        DecimalFormat df = new DecimalFormat("#0.00");

        Map<String,TaskDbcenter> d=new HashMap<>();
        for(TaskDbcenter theTaskDbcenter:depts)
        {
            if(StringUtils.isNotEmpty(theTaskDbcenter.getSubexecutedeptnameMh()) && theTaskDbcenter.getSubexecutedeptnameMh().contains("居民区"))
            {
                d.put(theTaskDbcenter.getSubexecutedeptnameMh(),theTaskDbcenter);
            }

        }
        taskDbcenter.setSatisfaction("满意");
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxMyVO> list=new ArrayList<DbCenterRxMyVO>();
        for(String key: d.keySet())
        {
            DbCenterRxMyVO theD=new DbCenterRxMyVO();
            theD.setDeptName(key);
            int my = (int) allRelateds.stream().filter(item1 -> key.equals(item1.getSubexecutedeptnameMh())).count();
            theD.setMy(my);
            list.add(theD);
        }
        taskDbcenter.setSatisfaction("不满意");
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int bmy = (int) allRelateds1.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setBmy(bmy);
        });

        taskDbcenter.setSatisfaction("基本满意");
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int jbmy = (int) allRelateds2.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setJbmy(jbmy);
        });
        taskDbcenter.setSatisfaction("一般");
        List<TaskDbcenter> allRelateds3= taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int yb = (int) allRelateds3.stream().filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh())).count();
            item.setYb(yb);
        });
        list.stream().forEach(item->{
            item.setHj(item.getBmy()+item.getJbmy()+item.getMy()+item.getYb());
            if(item.getHj() != 0)
            {
                item.setMyl( df.format(((float)item.getMy()+(float)item.getJbmy()*0.8+(float)item.getYb()*0.6)/item.getHj()*100));
            }
            else {
                item.setMyl("0");
            }
        });
        Comparator<DbCenterRxMyVO> byMyl=Comparator.comparing(DbCenterRxMyVO::getMyl);
        List<DbCenterRxMyVO> li=list.stream().sorted(byMyl.reversed()).collect(Collectors.toList());

        return li;
    }

    @Override
    public List<DbCenterRxMyVO> selectTaskDbcenterListMydKs(TaskDbcenter taskDbcenter) {
        int lastyear= 0;
        String da=null;
        int month =0;
        try {
            month=getMonthFromDate(taskDbcenter.getParams().get("beginTime").toString())+1;
            if(month >= 11)
            {
                lastyear= getYearFromDate(taskDbcenter.getParams().get("beginTime").toString());
            }
            else
            {
                lastyear = getYearFromDate(taskDbcenter.getParams().get("beginTime").toString())-1;
            }

        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        try {
            da = getYear(taskDbcenter.getParams().get("endTime").toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        taskDbcenter.getParams().put("evaBeginTime",lastyear+"-11");
        taskDbcenter.getParams().put("evaEndTime",da);
        taskDbcenter.getParams().put("beginTime",null);
        taskDbcenter.getParams().put("endTime",null);
        List<TaskDbcenter> depts = taskDbcenterMapper.selectDeptList(taskDbcenter);
        DecimalFormat df = new DecimalFormat("#0.00");

        Map<String,TaskDbcenter> d=new HashMap<>();
        for(TaskDbcenter theTaskDbcenter:depts)
        {
            if(StringUtils.isNotEmpty(theTaskDbcenter.getSubexecutedeptnameMh()) && !theTaskDbcenter.getSubexecutedeptnameMh().contains("居民区"))
            {
                // 跳过联勤联动
                if(!"联勤联动".equals(theTaskDbcenter.getSubexecutedeptnameMh())) {
                    d.put(theTaskDbcenter.getSubexecutedeptnameMh(),theTaskDbcenter);
                }
            }

        }
        taskDbcenter.setSatisfaction("满意");
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxMyVO> list=new ArrayList<DbCenterRxMyVO>();
        for(String key: d.keySet())
        {
            DbCenterRxMyVO theD=new DbCenterRxMyVO();
            theD.setDeptName(key);
            int my;
            if("石化街道综合行政执法队".equals(key)) {
                my = (int) allRelateds.stream()
                        .filter(item1 -> key.equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                my = (int) allRelateds.stream()
                        .filter(item1 -> key.equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            theD.setMy(my);
            list.add(theD);
        }
        taskDbcenter.setSatisfaction("不满意");
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int bmy;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                bmy = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                bmy = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setBmy(bmy);
        });

        taskDbcenter.setSatisfaction("基本满意");
        List<TaskDbcenter> allRelateds2 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int jbmy;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                jbmy = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                jbmy = (int) allRelateds2.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setJbmy(jbmy);
        });
        taskDbcenter.setSatisfaction("一般");
        List<TaskDbcenter> allRelateds3= taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int yb;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                yb = (int) allRelateds3.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                yb = (int) allRelateds3.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setYb(yb);
        });
        list.stream().forEach(item->{
            item.setHj(item.getBmy()+item.getJbmy()+item.getMy()+item.getYb());
            if(item.getHj() != 0)
            {
                item.setMyl( df.format(((float)item.getMy()+(float)item.getJbmy()*0.8+(float)item.getYb()*0.6)/item.getHj()*100));
            }
            else {
                item.setMyl("0");
            }
        });
        Comparator<DbCenterRxMyVO> byMyl=Comparator.comparing(DbCenterRxMyVO::getMyl);
        List<DbCenterRxMyVO> li=list.stream().sorted(byMyl.reversed()).collect(Collectors.toList());

        return li;
    }

    @Override
    public List<DbCenterRxJmqVO> selectTaskDbcenterListJas(TaskDbcenter taskDbcenter) {
        String[] subexecutedeptnameMh={"石化街道综合行政执法队","石化街道社区管理办","金欣环卫","石化街道建管中心","石化街道社区党群办公室",
        "锦石市政","象州派出所","石化街道管违办","石化街道服务办"};

        taskDbcenter.getParams().put("sourceFlag",false);
        List<TaskDbcenter> allRelateds = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        List<DbCenterRxJmqVO> list=new ArrayList<DbCenterRxJmqVO>();
        for(int i= 0;i<subexecutedeptnameMh.length;i++)
        {
            DbCenterRxJmqVO theD=new DbCenterRxJmqVO();
            theD.setDeptName(subexecutedeptnameMh[i]);
            String dept=subexecutedeptnameMh[i];
            int zbNum;
            if("石化街道综合行政执法队".equals(dept)) {
                zbNum = (int) allRelateds.stream()
                        .filter(item -> dept.equals(item.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item.getSubexecutedeptnameMh()))
                        .count();
            } else {
                zbNum = (int) allRelateds.stream()
                        .filter(item -> dept.equals(item.getSubexecutedeptnameMh()))
                        .count();
            }
            theD.setZbNum(zbNum);
            list.add(theD);
        }
        try {
            taskDbcenter.getParams().put("jieanTime",getMonthlyDeadline(taskDbcenter.getParams().get("endTime").toString()));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        List<TaskDbcenter> allRelateds1 = taskDbcenterMapper.selectTaskDbcenterList(taskDbcenter);
        list.stream().forEach(item->{
            int yb;
            if("石化街道综合行政执法队".equals(item.getDeptName())) {
                yb = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()) ||
                                "联勤联动".equals(item1.getSubexecutedeptnameMh()))
                        .count();
            } else {
                yb = (int) allRelateds1.stream()
                        .filter(item1 -> item.getDeptName().equals(item1.getSubexecutedeptnameMh()))
                        .count();
            }
            item.setCqNum(yb);
            if("石化街道综合行政执法队".equals(item.getDeptName()))
            {
                item.setDeptName("综合行政执法队");
            }
            else  if("石化街道社区管理办".equals(item.getDeptName()))
            {
                item.setDeptName("社区管理办");
            }
            else  if("石化街道建管中心".equals(item.getDeptName()))
            {
                item.setDeptName("建管中心");
            }
            else  if("石化街道社区党群办公室".equals(item.getDeptName()))
            {
                item.setDeptName("党群中心");
            }
            else  if("石化街道管违办".equals(item.getDeptName()))
            {
                item.setDeptName("管违办");
            }
            else  if("石化街道服务办".equals(item.getDeptName()))
            {
                item.setDeptName("服务办");
            }

        });
        return list;
    }

    @Override
    public int deleteTaskDbcenterReturnOther() {
        return taskDbcenterMapper.deleteTaskDbcenterReturnOther();
    }

    @Override
    public int syncData(String[] dataList) {
        // dataList[0]
        String startTime = dataList[0];
        // dataList[1]的字符串格式化后向后推一天
        String endTime = DateUtil.offsetDay(DateUtil.parse(dataList[1]), 1).toString();
        List<Long> ids = taskDbcenterMapper.selectTaskDbcenterIdList();
        List<String> hotlinesns = taskDbcenterMapper.selectTaskDbcenterHotlinesnList();
        List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenter12345List(startTime, endTime);
        //遍历jsDbcenters判断JsDbcenter.getId()是否在ids中，如果不在则插入
        for (JsDbcenter jsDbcenter : jsDbcenters) {
            TaskDbcenter dbcenter = new TaskDbcenter();
            BeanUtil.copyProperties(jsDbcenter, dbcenter);
            if (!ids.contains(jsDbcenter.getId())) {
                if (hotlinesns.contains(jsDbcenter.getHotlinesn())) {
                    dbcenter.setIscheck("是");
                }
                taskDbcenterMapper.insertTaskDbcenter(dbcenter);
            } else {
                taskDbcenterMapper.updateTaskDbcenter(dbcenter);
            }
        }
        return 1;
    }

    public static int getYearFromDate(String date) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatter.parse(date));
        return calendar.get(Calendar.YEAR);
    }

    public static int getMonthFromDate(String date) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatter.parse(date));
        return calendar.get(Calendar.MONTH);
    }


    public static String getYear(String date) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatter.parse(date));
        String month=String.format("%02d",calendar.get(Calendar.MONTH)+1);
        return calendar.get(Calendar.YEAR)+"-"+month;
    }


    /**
     * 递归处理后继工单
     * @param taskDbcenter
     */
    public void handleSubsequent(TaskDbcenter taskDbcenter) {
        TaskDbcenter subsequent = taskDbcenterMapper.selectTaskDbcenterByRecenthotlinesn(taskDbcenter.getHotlinesn());
        if (subsequent != null) {
            subsequent.setRelatedhotlinesn(taskDbcenter.getRelatedhotlinesn());
            taskDbcenterMapper.updateTaskDbcenter(subsequent);
            handleSubsequent(subsequent);
        }
    }

    public static String getMonthlyDeadline(String date) throws ParseException {
         date=date+" 23:59:59";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatter.parse(date));
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return formatter.format(calendar.getTime());
    }

    public static List<String> getYearMonths(LocalDate start, LocalDate end) {
        List<String> yearMonths = new ArrayList<>();
        LocalDate current = start;
        while (current.isBefore(end) || current.isEqual(end)) {
            int year = current.getYear();
            int month = current.getMonthValue();
            yearMonths.add(String.format("%d-%02d", year, month));
            current = current.plus(1, ChronoUnit.MONTHS);
        }
        return yearMonths;
    }
}
