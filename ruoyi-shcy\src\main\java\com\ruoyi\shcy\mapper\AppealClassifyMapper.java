package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.AppealClassify;

/**
 * 诉求归类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
public interface AppealClassifyMapper 
{
    /**
     * 查询诉求归类
     * 
     * @param id 诉求归类主键
     * @return 诉求归类
     */
    public AppealClassify selectAppealClassifyById(Long id);

    /**
     * 查询诉求归类列表
     * 
     * @param appealClassify 诉求归类
     * @return 诉求归类集合
     */
    public List<AppealClassify> selectAppealClassifyList(AppealClassify appealClassify);

    /**
     * 新增诉求归类
     * 
     * @param appealClassify 诉求归类
     * @return 结果
     */
    public int insertAppealClassify(AppealClassify appealClassify);

    /**
     * 修改诉求归类
     * 
     * @param appealClassify 诉求归类
     * @return 结果
     */
    public int updateAppealClassify(AppealClassify appealClassify);

    /**
     * 删除诉求归类
     * 
     * @param id 诉求归类主键
     * @return 结果
     */
    public int deleteAppealClassifyById(Long id);

    /**
     * 批量删除诉求归类
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAppealClassifyByIds(Long[] ids);
}
