package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.FactoryWarehouse;

/**
 * 厂房仓库Service接口
 * 
 * <AUTHOR>
 * @date 2024-12-31
 */
public interface IFactoryWarehouseService 
{
    /**
     * 查询厂房仓库
     * 
     * @param id 厂房仓库主键
     * @return 厂房仓库
     */
    public FactoryWarehouse selectFactoryWarehouseById(Long id);

    /**
     * 查询厂房仓库列表
     * 
     * @param factoryWarehouse 厂房仓库
     * @return 厂房仓库集合
     */
    public List<FactoryWarehouse> selectFactoryWarehouseList(FactoryWarehouse factoryWarehouse);

    /**
     * 新增厂房仓库
     * 
     * @param factoryWarehouse 厂房仓库
     * @return 结果
     */
    public int insertFactoryWarehouse(FactoryWarehouse factoryWarehouse);

    /**
     * 修改厂房仓库
     * 
     * @param factoryWarehouse 厂房仓库
     * @return 结果
     */
    public int updateFactoryWarehouse(FactoryWarehouse factoryWarehouse);

    /**
     * 批量删除厂房仓库
     * 
     * @param ids 需要删除的厂房仓库主键集合
     * @return 结果
     */
    public int deleteFactoryWarehouseByIds(Long[] ids);

    /**
     * 删除厂房仓库信息
     * 
     * @param id 厂房仓库主键
     * @return 结果
     */
    public int deleteFactoryWarehouseById(Long id);
}
