package com.ruoyi.shcy.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.*;
import com.ruoyi.shcy.domain.vo.DbCenterRxJmqVO;
import com.ruoyi.shcy.domain.vo.DbCenterRxMyVO;
import com.ruoyi.shcy.dto.TaskDbcenterRxDTO;
import com.ruoyi.shcy.service.IAppealClassifyService;
import com.ruoyi.shcy.service.INonResidentialPropertyService;
import com.ruoyi.shcy.service.ISpecialTopicService;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 12345热线案件信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-26
 */
@RestController
@RequestMapping("/shcy/dbcenterRx")
public class TaskDbcenterRxController extends BaseController
{
    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    @Autowired
    private IAppealClassifyService appealClassifyService;

    @Autowired
    private ISpecialTopicService specialTopicService;

    @Autowired
    private INonResidentialPropertyService nonResidentialPropertyService;

    /**
     * 查询12345热线案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRx:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskDbcenter taskDbcenter)
    {
        if ("空缺".equals(taskDbcenter.getResidentialarea())) {
            taskDbcenter.getParams().put("vacancy", true);
            taskDbcenter.setResidentialarea(null);
        }
        if ("空缺".equals(taskDbcenter.getStreetarea())) {
            taskDbcenter.getParams().put("streetvacancy", true);
            taskDbcenter.setStreetarea(null);
        }
        if ("空缺".equals(taskDbcenter.getCommunity())) {
            taskDbcenter.getParams().put("communityvacancy", true);
            taskDbcenter.setCommunity(null);
        }
        if ("空缺".equals(taskDbcenter.getProperty())) {
            taskDbcenter.getParams().put("propertyvacancy", true);
            taskDbcenter.setProperty(null);
        }
        // 诉求大类空缺
        if ("空缺".equals(taskDbcenter.getParentappealclassification())) {
            taskDbcenter.getParams().put("parentappealclassificationvacancy", true);
            taskDbcenter.setParentappealclassification(null);
        }
        // 诉求小类空缺
        if ("空缺".equals(taskDbcenter.getParams().get("appealclassifications").toString())) {
            taskDbcenter.getParams().put("appealclassificationsvacancy", true);
            taskDbcenter.getParams().put("appealclassifications", null);
        } else {
            if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("appealclassifications").toString())) {
                taskDbcenter.getParams().put("appealclassifications", taskDbcenter.getParams().get("appealclassifications").toString().split(","));
            }
        }

        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("satisfactions").toString())) {
            taskDbcenter.getParams().put("satisfactions", taskDbcenter.getParams().get("satisfactions").toString().split(","));
        }

        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("specialtopics").toString())) {
            taskDbcenter.getParams().put("specialtopics", taskDbcenter.getParams().get("specialtopics").toString().split(","));
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("queryTrackingtypes").toString())) {
            taskDbcenter.getParams().put("queryTrackingtypes", taskDbcenter.getParams().get("queryTrackingtypes").toString().split(","));
        }
        taskDbcenter.setInfosourcename("12345上报");
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        return getDataTable(list);
    }

    /**
     * 查询12345热线案件信息列表
     */
    @GetMapping("/listRelated")
    public TableDataInfo listRelated(TaskDbcenter taskDbcenter)
    {
        TaskDbcenter taskDbcenter1 = taskDbcenterService.selectTaskDbcenterByHotlinesn(taskDbcenter.getHotlinesn());
        if (StringUtils.isNull(taskDbcenter1)) {
            return getDataTable(new ArrayList<TaskDbcenter>());
        }
        if (StringUtils.isEmpty(taskDbcenter1.getRelatedhotlinesn())) {
            List<TaskDbcenter> dbcenters = new ArrayList<TaskDbcenter>();
            dbcenters.add(taskDbcenter1);
            return getDataTable(dbcenters);
        }
        taskDbcenter.setHotlinesn(null);
        taskDbcenter.setRelatedhotlinesn(taskDbcenter1.getRelatedhotlinesn());
        startPage();
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        return getDataTable(list);
    }

    /**
     * 导出12345热线案件信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRx:export')")
    @Log(title = "12345热线案件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskDbcenter taskDbcenter)
    {
        if ("空缺".equals(taskDbcenter.getResidentialarea())) {
            taskDbcenter.getParams().put("vacancy", true);
            taskDbcenter.setResidentialarea(null);
        }
        if ("空缺".equals(taskDbcenter.getStreetarea())) {
            taskDbcenter.getParams().put("streetvacancy", true);
            taskDbcenter.setStreetarea(null);
        }
        if ("空缺".equals(taskDbcenter.getCommunity())) {
            taskDbcenter.getParams().put("communityvacancy", true);
            taskDbcenter.setCommunity(null);
        }
        if ("空缺".equals(taskDbcenter.getProperty())) {
            taskDbcenter.getParams().put("propertyvacancy", true);
            taskDbcenter.setProperty(null);
        }
        // 诉求大类空缺
        if ("空缺".equals(taskDbcenter.getParentappealclassification())) {
            taskDbcenter.getParams().put("parentappealclassificationvacancy", true);
            taskDbcenter.setParentappealclassification(null);
        }
        // 诉求小类空缺
        if ("空缺".equals(taskDbcenter.getParams().get("appealclassifications").toString())) {
            taskDbcenter.getParams().put("appealclassificationsvacancy", true);
            taskDbcenter.getParams().put("appealclassifications", null);
        } else {
            if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("appealclassifications").toString())) {
                taskDbcenter.getParams().put("appealclassifications", taskDbcenter.getParams().get("appealclassifications").toString().split(","));
            }
        }

        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("satisfactions").toString())) {
            taskDbcenter.getParams().put("satisfactions", taskDbcenter.getParams().get("satisfactions").toString().split(","));
        }

        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("specialtopics").toString())) {
            taskDbcenter.getParams().put("specialtopics", taskDbcenter.getParams().get("specialtopics").toString().split(","));
        }
        if (StrUtil.isNotEmpty(taskDbcenter.getParams().get("queryTrackingtypes").toString())) {
            taskDbcenter.getParams().put("queryTrackingtypes", taskDbcenter.getParams().get("queryTrackingtypes").toString().split(","));
        }
        taskDbcenter.setInfosourcename("12345上报");
        List<TaskDbcenter> list = taskDbcenterService.selectTaskDbcenterRxList(taskDbcenter);
        List<TaskDbcenterRxDTO> exportList = BeanUtil.copyToList(list, TaskDbcenterRxDTO.class);
        ExcelUtil<TaskDbcenterRxDTO> util = new ExcelUtil<TaskDbcenterRxDTO>(TaskDbcenterRxDTO.class);
        util.exportExcel(response, exportList, "12345热线案件信息数据");
    }

    /**
     * 获取12345热线案件信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRx:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(taskDbcenterService.selectTaskDbcenterById(id));
    }

    @GetMapping(value = "/getByHotlinesn/{hotlinesn}")
    public AjaxResult getByHotlinesn(@PathVariable("hotlinesn") String hotlinesn)
    {
        return AjaxResult.success(taskDbcenterService.selectTaskDbcenterByHotlinesn(hotlinesn));
    }

    /**
     * 修改12345热线案件信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRx:edit')")
    @Log(title = "12345热线案件信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskDbcenter taskDbcenter)
    {
        // 关联工单处理
        taskDbcenterService.handleRelatedTaskDbcenter(taskDbcenter);
        return toAjax(taskDbcenterService.updateTaskDbcenter(taskDbcenter));
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRx:sync')")
    @Log(title = "12345热线案件信息", businessType = BusinessType.UPDATE)
    @PostMapping("/syncDbcenterRx/{taskids}")
    public AjaxResult syncDbcenterRx(@PathVariable String[] taskids)
    {
        return toAjax(taskDbcenterService.syncDbcenterRxByTaskids(taskids));
    }

    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRx:sync')")
    @Log(title = "12345热线案件信息", businessType = BusinessType.UPDATE)
    @PostMapping("/syncData")
    public AjaxResult syncData(@RequestBody String[] dataList)
    {
        return toAjax(taskDbcenterService.syncData(dataList));
    }

    @GetMapping("/countDbcenterRx")
    public AjaxResult countDbcenterRx(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<TaskDbcenter> list=taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        return AjaxResult.success(list.size());
    }

    @GetMapping("/countDbcenterRxJmq")
    public AjaxResult countDbcenterRxJmq(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<TaskDbcenter> list=taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        int total = (int) list.stream()
            .filter(item1 -> item1.getSubexecutedeptnameMh() != null && item1.getSubexecutedeptnameMh().contains("居民区"))
            .count();
        return AjaxResult.success(total);
    }

    @GetMapping("/countDbcenterRxKs")
    public AjaxResult countDbcenterRxKs(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<TaskDbcenter> list=taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        int total = (int) list.stream()
            .filter(item1 -> item1.getSubexecutedeptnameMh() != null && !item1.getSubexecutedeptnameMh().contains("居民区"))
            .count();
        return AjaxResult.success(total);
    }

    @GetMapping("/jmqList")
    public AjaxResult jmqList(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<DbCenterRxJmqVO> list=taskDbcenterService.selectTaskDbcenterListJmq(taskDbcenter);
        return AjaxResult.success(list);
    }

    @GetMapping("/ksList")
    public AjaxResult ksList(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<DbCenterRxJmqVO> list=taskDbcenterService.selectTaskDbcenterListKs(taskDbcenter);
        return AjaxResult.success(list);
    }

    @GetMapping("/manyiDuPxList")
    public AjaxResult manyiDuPxList(TaskDbcenter taskDbcenter)
    {
        if(StringUtils.isNotEmpty(taskDbcenter.getParams().get("beginTime").toString())) {
            taskDbcenter.setInfosourcename("12345上报");
            List<DbCenterRxMyVO> list = taskDbcenterService.selectTaskDbcenterListMyd(taskDbcenter);
            Collections.sort(list, new Comparator<DbCenterRxMyVO>() {
                @Override
                public int compare(DbCenterRxMyVO o1, DbCenterRxMyVO o2) {
                    return Double.compare(Double.parseDouble(o2.getMyl()), Double.parseDouble(o1.getMyl()));
                }
            });
            return AjaxResult.success(list);
        }
        return AjaxResult.success(null);
    }

    @GetMapping("/manyiDuPxListJmq")
    public AjaxResult manyiDuPxListJmq(TaskDbcenter taskDbcenter)
    {
        if(StringUtils.isNotEmpty(taskDbcenter.getParams().get("beginTime").toString())) {
            taskDbcenter.setInfosourcename("12345上报");
            List<DbCenterRxMyVO> list = taskDbcenterService.selectTaskDbcenterListMydJmq(taskDbcenter);
            Collections.sort(list, new Comparator<DbCenterRxMyVO>() {
                @Override
                public int compare(DbCenterRxMyVO o1, DbCenterRxMyVO o2) {
                    return Double.compare(Double.parseDouble(o2.getMyl()), Double.parseDouble(o1.getMyl()));
                }
            });
            return AjaxResult.success(list);
        }
        return AjaxResult.success(null);
    }

    @GetMapping("/manyiDuPxListKs")
    public AjaxResult manyiDuPxListKs(TaskDbcenter taskDbcenter)
    {
        if(StringUtils.isNotEmpty(taskDbcenter.getParams().get("beginTime").toString())) {
            taskDbcenter.setInfosourcename("12345上报");
            List<DbCenterRxMyVO> list = taskDbcenterService.selectTaskDbcenterListMydKs(taskDbcenter);
            Collections.sort(list, new Comparator<DbCenterRxMyVO>() {
                @Override
                public int compare(DbCenterRxMyVO o1, DbCenterRxMyVO o2) {
                    return Double.compare(Double.parseDouble(o2.getMyl()), Double.parseDouble(o1.getMyl()));
                }
            });
            return AjaxResult.success(list);
        }
        return AjaxResult.success(null);
    }

    @GetMapping("/myList")
    public AjaxResult myList(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        DbCenterRxMyVO list=taskDbcenterService.selectTaskDbcenterListMy(taskDbcenter);
        return AjaxResult.success(list);
    }

    @GetMapping("/manyiqkList")
    public AjaxResult manyiqkList(TaskDbcenter taskDbcenter)
    {
            taskDbcenter.setInfosourcename("12345上报");
            List<DbCenterRxMyVO> list=taskDbcenterService.selectTaskDbcenterListMyQk(taskDbcenter);
            Collections.sort(list, new Comparator<DbCenterRxMyVO>() {
                @Override
                public int compare(DbCenterRxMyVO o1, DbCenterRxMyVO o2) {
                    return Double.compare(Double.parseDouble(o2.getMyl()), Double.parseDouble(o1.getMyl()));
                }
            });
            return AjaxResult.success(list);
    }

    @GetMapping("/manyiqsList")
    public AjaxResult manyiqsList(TaskDbcenter taskDbcenter) throws ParseException {
        taskDbcenter.setInfosourcename("12345上报");
        List<DbCenterRxMyVO> list=taskDbcenterService.selectTaskDbcenterListMyQs(taskDbcenter);
        return AjaxResult.success(list);
    }

    @GetMapping("/manyiqkListJmq")
    public AjaxResult manyiqkListJmq(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        List<DbCenterRxMyVO> list=taskDbcenterService.selectTaskDbcenterListMyQkJmq(taskDbcenter);
        Collections.sort(list, new Comparator<DbCenterRxMyVO>() {
            @Override
            public int compare(DbCenterRxMyVO o1, DbCenterRxMyVO o2) {
                return Double.compare(Double.parseDouble(o2.getMyl()), Double.parseDouble(o1.getMyl()));
            }
        });
        return AjaxResult.success(list);
    }

    @GetMapping("/manyiqkListKs")
    public AjaxResult manyiqkListKs(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        List<DbCenterRxMyVO> list=taskDbcenterService.selectTaskDbcenterListMyQkKs(taskDbcenter);
        Collections.sort(list, new Comparator<DbCenterRxMyVO>() {
            @Override
            public int compare(DbCenterRxMyVO o1, DbCenterRxMyVO o2) {
                return Double.compare(Double.parseDouble(o2.getMyl()), Double.parseDouble(o1.getMyl()));
            }
        });
        return AjaxResult.success(list);
    }


    @GetMapping("/laqkList")
    public AjaxResult laqkList(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("兼职监督员上报");
        taskDbcenter.getParams().put("jmqflag","居民区");
        taskDbcenter.getParams().put("cfFlag",true);
        List<TaskDbcenter> list1=taskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        Map<String,TaskDbcenter> d=new HashMap<>();
        for(TaskDbcenter theTaskDbcenter:list1)
        {
            d.put(theTaskDbcenter.getSubexecutedeptnameMh(),theTaskDbcenter);
        }
        List<DbCenterRxMyVO> list=new ArrayList<DbCenterRxMyVO>();
        for(String key: d.keySet())
        {
            DbCenterRxMyVO theD=new DbCenterRxMyVO();
            theD.setDeptName(key);
            int las = (int) list1.stream().filter(item1 -> key.equals(item1.getSubexecutedeptnameMh())).count();
            theD.setzNum(las);
            list.add(theD);
        }

        Comparator<DbCenterRxMyVO> byMyl=Comparator.comparing(DbCenterRxMyVO::getzNum);
        List<DbCenterRxMyVO> li=list.stream().sorted(byMyl).collect(Collectors.toList());

        return AjaxResult.success(li);
    }

    @GetMapping("/jasList")
    public AjaxResult jasList(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.getParams().put("jaFlag",true);
        taskDbcenter.getParams().put("cfFlag",true);
        List<DbCenterRxJmqVO> list1=taskDbcenterService.selectTaskDbcenterListJas(taskDbcenter);
        return AjaxResult.success(list1);

    }

    /**
     * 查询诉求归类列表
     */
    @GetMapping("/listAppealClassify")
    public AjaxResult listAppealClassify()
    {
        List<AppealClassify> appealClassifies = appealClassifyService.selectAppealClassifyList(null);
        // 定义List<String> list 用于存放appealClassifies的name
        // List<String> list = new ArrayList<>();
        // for (AppealClassify appealClassify : appealClassifies) {
        //     list.add(appealClassify.getName());
        // }
        return AjaxResult.success(appealClassifies);
    }

    /**
     * 查询重点专项列表
     */
    @GetMapping("/listSpecialTopic")
    public AjaxResult listSpecialTopic()
    {
        List<SpecialTopic> specialTopicList = specialTopicService.selectSpecialTopicList(null);
        // 使用stream将specialTopicList转化为List<String> list，用于存放specialTopicList的name
        List<String> list = specialTopicList.stream().map(SpecialTopic::getName).collect(Collectors.toList());
        return AjaxResult.success(list);
    }

    /**
     * 查询物业列表
     */
    @GetMapping("/listProperty")
    public AjaxResult listProperty()
    {
        List<NonResidentialProperty> nonResidentialProperties = nonResidentialPropertyService.selectNonResidentialPropertyList(null);
        return AjaxResult.success(nonResidentialProperties);
    }
}
