package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.JsDbcenter;

import java.util.List;

public interface IJsDbcenterService {

    public JsDbcenter selectJsDbcenterById(Long id);

    public JsDbcenter selectJsDbcenterByTaskid(String taskid);

    public List<JsDbcenter> selectJsDbcenterList(JsDbcenter jsDbcenter);

    public List<JsDbcenter> selectJsDbcenterSyncList(Long[] ids);

    public List<JsDbcenter> selectJsDbcenterSyncListByTaskid(String[] taskids);

    public List<JsDbcenter> selectJsDbcenter12345List(String startTime, String endTime);

    public List<JsDbcenter> selectJsDbcenterCaseList(String startTime, String endTime);

}
