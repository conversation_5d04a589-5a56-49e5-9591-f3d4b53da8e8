package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 隐患排查子表对象 shcy_flood_hazard_check_list_extend
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
public class ShcyFloodHazardCheckListExtend extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 隐患表id */
    @Excel(name = "隐患表id")
    private Long checkId;

    /** 检查项目父类 */
    @Excel(name = "检查项目父类")
    private Long checkItemParent;

    /** 检查项目子类 */
    @Excel(name = "检查项目子类")
    private Long checkItemChild;

    /** 发现隐患数量 */
    @Excel(name = "发现隐患数量")
    private Integer foundHazardCount;

    /** 整改隐患数量 */
    @Excel(name = "整改隐患数量")
    private Integer rectifiedHazardCount;

    private  String checkItemParentName;

    private  String checkItemChildName;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCheckId(Long checkId)
    {
        this.checkId = checkId;
    }

    public Long getCheckId()
    {
        return checkId;
    }
    public void setCheckItemParent(Long checkItemParent)
    {
        this.checkItemParent = checkItemParent;
    }

    public Long getCheckItemParent()
    {
        return checkItemParent;
    }
    public void setCheckItemChild(Long checkItemChild)
    {
        this.checkItemChild = checkItemChild;
    }

    public Long getCheckItemChild()
    {
        return checkItemChild;
    }
    public void setFoundHazardCount(Integer foundHazardCount)
    {
        this.foundHazardCount = foundHazardCount;
    }

    public Integer getFoundHazardCount()
    {
        return foundHazardCount;
    }
    public void setRectifiedHazardCount(Integer rectifiedHazardCount)
    {
        this.rectifiedHazardCount = rectifiedHazardCount;
    }

    public Integer getRectifiedHazardCount()
    {
        return rectifiedHazardCount;
    }

    public String getCheckItemParentName() {
        return checkItemParentName;
    }

    public void setCheckItemParentName(String checkItemParentName) {
        this.checkItemParentName = checkItemParentName;
    }

    public String getCheckItemChildName() {
        return checkItemChildName;
    }

    public void setCheckItemChildName(String checkItemChildName) {
        this.checkItemChildName = checkItemChildName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("checkId", getCheckId())
            .append("checkItemParent", getCheckItemParent())
            .append("checkItemChild", getCheckItemChild())
            .append("foundHazardCount", getFoundHazardCount())
            .append("rectifiedHazardCount", getRectifiedHazardCount())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
