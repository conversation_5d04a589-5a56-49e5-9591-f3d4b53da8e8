package com.ruoyi.shcy.controller;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.Category;
import com.ruoyi.shcy.domain.NucleicAcidLog;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.ShopCheckLog;
import com.ruoyi.shcy.domain.vo.CategoryDetailVo;
import com.ruoyi.shcy.domain.vo.CommitteeDetailVo;
import com.ruoyi.shcy.domain.vo.ShopDailyVo;
import com.ruoyi.shcy.domain.vo.ShopDetailVo;
import com.ruoyi.shcy.service.ICategoryService;
import com.ruoyi.shcy.service.ICheckRecordService;
import com.ruoyi.shcy.service.INucleicAcidLogService;
import com.ruoyi.shcy.service.IShopService;
import com.ruoyi.system.service.ISysDeptService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 店铺信息Controller
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@RestController
@RequestMapping("/shcy/shop")
public class ShopController extends BaseController
{
    @Autowired
    private IShopService shopService;

    @Autowired
    private ISysDeptService iSysDeptService;

    @Autowired
    private ICategoryService iCategoryService;


    @Autowired
    private INucleicAcidLogService nucleicAcidLogService;




    @Autowired
    private ICheckRecordService checkRecordService;




    /**
     * 查询所有店铺信息列表（正常、关停）
     */
    @PreAuthorize("@ss.hasPermi('shcy:shop:list')")
    @GetMapping("/list")
    public TableDataInfo list(Shop shop)
    {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();
        Long roleId = user.getRoleId();
        String roleName  =  user.getRoles().get(0).getRoleName();
        if(Objects.equals(roleName, "超级管理员")){
            // 获取当前的用户名称
            if(StringUtils.isNotEmpty(shop.getRoadSectionStart())&&StringUtils.isNotEmpty(shop.getRoadSectionEnd())){
                shop.setRoadFlag("1");
            }else{
                shop.setRoadFlag("0");
            }
            startPage();
            List<Shop> list = shopService.selectShopList(shop);
            return getDataTable(list);
        }
        if(Objects.equals(roleName, "营商办")){
            // 获取当前的用户名称
            if(StringUtils.isNotEmpty(shop.getRoadSectionStart())&&StringUtils.isNotEmpty(shop.getRoadSectionEnd())){
                shop.setRoadFlag("1");
            }else{
                shop.setRoadFlag("0");
            }
            startPage();
            List<Shop> list = shopService.selectShopList(shop);
            return getDataTable(list);
        }
        if(Objects.equals(roleName, "城管")){
            // 获取当前的用户名称
            if(StringUtils.isNotEmpty(shop.getRoadSectionStart())&&StringUtils.isNotEmpty(shop.getRoadSectionEnd())){
                shop.setRoadFlag("1");
            }else{
                shop.setRoadFlag("0");
            }
            startPage();
            List<Shop> list = shopService.selectShopList(shop);
            return getDataTable(list);
        }

        if(Objects.equals(roleName, "居委会联络员")) {
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if (StringUtils.isNotEmpty(sysDept.getIsCommittee()) && sysDept.getIsCommittee().equals("1")) {     //获取居委会对应查看的店铺列表

                shop.setShopDeparmentId(deptId);
                if (StringUtils.isNotEmpty(shop.getRoadSectionStart()) && StringUtils.isNotEmpty(shop.getRoadSectionEnd())) {
                    shop.setRoadFlag("1");
                } else {
                    shop.setRoadFlag("0");
                }
                startPage();
                List<Shop> list = shopService.selectShopList(shop);
                return getDataTable(list);
            }
        }
        if(Objects.equals(roleName, "网格长")){
            TableDataInfo tableDataInfo = selectWanggezhangShopList(user, shop, shopService);
            return tableDataInfo;
        }
        // 获取当前的用户名称
        startPage();
        List<Shop> list = new ArrayList<>();
        return getDataTable(list);

    }
    public static TableDataInfo   selectWanggezhangShopList(SysUser user,Shop shop,IShopService shopService){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        if(StringUtils.isNotEmpty(shop.getRoadSectionStart())&&StringUtils.isNotEmpty(shop.getRoadSectionEnd())){
            shop.setRoadFlag("1");
        }else{
            shop.setRoadFlag("0");
        }
        List<Shop> shopDeptList = shopService.selectShopList(shop).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getShopDeparmentId())).collect(Collectors.toList());

        int num = shopDeptList.size();
        shopDeptList = shopDeptList.stream()
                .skip((pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(shopDeptList);
        rspData.setTotal(num);
        return rspData;
    }



    @Log(title = "商铺管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('shcy:shop:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<Shop> util = new ExcelUtil<Shop>(Shop.class);
        List<Shop> shopList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = shopService.importShop(shopList, updateSupport, operName);
        return AjaxResult.success(message);
    }


    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<Shop> util = new ExcelUtil<Shop>(Shop.class);
        util.importTemplateExcel(response, "商铺数据");
    }



    /***
     * 查询店铺列表（正常营业的）
     * */
    @GetMapping("/normalList")
    public TableDataInfo listWithNormal(Shop shop){


        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();
        Long roleId = user.getRoleId();
        String roleName  =  user.getRoles().get(0).getRoleName();
        if(Objects.equals(roleName, "超级管理员") || Objects.equals(roleName, "营商办")){
            // 获取当前的用户名称
            if(StringUtils.isNotEmpty(shop.getRoadSectionStart())&&StringUtils.isNotEmpty(shop.getRoadSectionEnd())){
                shop.setRoadFlag("1");
            }else{
                shop.setRoadFlag("0");
            }
            startPage();
            List<Shop> list = shopService.selectShopListWithNoram(shop);
            return getDataTable(list);
        }
        if(Objects.equals(roleName, "城管")){
            // 获取当前的用户名称
            if(StringUtils.isNotEmpty(shop.getRoadSectionStart())&&StringUtils.isNotEmpty(shop.getRoadSectionEnd())){
                shop.setRoadFlag("1");
            }else{
                shop.setRoadFlag("0");
            }

            List<Shop> list = new ArrayList<>();
            startPage();

            ShopCheckLog shopCheckLog1 = new ShopCheckLog();
            shopCheckLog1.setCheckCrossDoorOperation("是");
            shopCheckLog1.setCheckFfhx("是");

            List<Shop> cg1 = shopService.selectCheckedShopListWithNormalCg(shopCheckLog1);

            ShopCheckLog shopCheckLog2 = new ShopCheckLog();
            shopCheckLog2.setCheckCrossDoorOperation("是");
            shopCheckLog2.setCheckFfhx("否");
            List<Shop> cg2 = shopService.selectCheckedShopListWithNormalCg(shopCheckLog2);

            ShopCheckLog shopCheckLog3 = new ShopCheckLog();
            shopCheckLog3.setCheckCrossDoorOperation("否");
            shopCheckLog3.setCheckFfhx("否");
            List<Shop> cg3 = shopService.selectCheckedShopListWithNormalCg(shopCheckLog3);
            list.addAll(cg1);
            list.addAll(cg2);
            list.addAll(cg3);
//            List<Shop> list = shopService.selectShopListWithNoram(shop);
            return getDataTable(list);
        }

        if(Objects.equals(roleName, "居委会联络员")) {
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if (StringUtils.isNotEmpty(sysDept.getIsCommittee()) && sysDept.getIsCommittee().equals("1")) {     //获取居委会对应查看的店铺列表

                shop.setShopDeparmentId(deptId);
                if (StringUtils.isNotEmpty(shop.getRoadSectionStart()) && StringUtils.isNotEmpty(shop.getRoadSectionEnd())) {
                    shop.setRoadFlag("1");
                } else {
                    shop.setRoadFlag("0");
                }
                startPage();
                List<Shop> list = shopService.selectShopListWithNoram(shop);
                return getDataTable(list);
            }
        }
        if(Objects.equals(roleName, "网格长")){
            TableDataInfo tableDataInfo = selectWanggezhangShopWithNormalList(user, shop, shopService);
            return tableDataInfo;
        }
        // 获取当前的用户名称
        startPage();
        List<Shop> list = new ArrayList<>();
        return getDataTable(list);

    }
    public static TableDataInfo   selectWanggezhangShopWithNormalList(SysUser user,Shop shop,IShopService shopService){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        shop.setIsOpen("营业");

        List<Shop> shopDeptList = shopService.selectShopListWithNoram(shop).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getShopDeparmentId())).collect(Collectors.toList());

        int num = shopDeptList.size();
        shopDeptList = shopDeptList.stream()
                .skip((pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(shopDeptList);
        rspData.setTotal(num);
        return rspData;
    }


    /**
     * 查询今日巡查店铺详细信息
     * **/
    @GetMapping("/detail/{curDate}")
    public AjaxResult shopData(@PathVariable("curDate") String curDate) throws ParseException {
        ShopDetailVo shopDetailVo = new ShopDetailVo();

        ShopCheckLog shopCheckLog = new ShopCheckLog();
//        Date check_date = DateUtils.parseDate(curDatel);
        Date check_date = DateUtils.parseDate(curDate, "yyyy-MM-dd");
        shopCheckLog.setCheckDate(check_date);




        //巡查日期
        shopDetailVo.setCurDate(check_date);

        //************************************************************店铺具体详细信息*************************************
        //************************************************************店铺具体详细信息*************************************
        //************************************************************店铺具体详细信息*************************************

        //查询 总店铺数
        Shop emptyShop = new Shop();
        List<ShopDetailVo> shopDetailVos = shopService.selectShopTotal(emptyShop);
        if(shopDetailVos.size()!=0){
            shopDetailVo.setShopTotal(shopDetailVos.get(0).getShopTotal());
        }else{
            shopDetailVo.setShopTotal("0");
        }
        //查询每日巡店总数
        List<ShopDetailVo> shopDetailVo1 = shopService.selectTodayCheckShopTotal(shopCheckLog);
        if(shopDetailVo1.size()!=0){
            shopDetailVo.setCheckShopTotal(shopDetailVo1.get(0).getCheckShopTotal());
        }else{
            shopDetailVo.setCheckShopTotal("0");
        }

        //查询每日巡查店铺正常数量
        List<ShopDetailVo> shopDetailVo2= shopService.selectTodayCheckShopNomalNum(shopCheckLog);
        if(shopDetailVo2.size()!=0){
            shopDetailVo.setShopNormalNum(shopDetailVo2.get(0).getShopNormalNum());
        }else{
            shopDetailVo.setShopNormalNum("0");
        }

        //查询每日巡查歇店总数
        List<ShopDetailVo> shopDetailVo3= shopService.selectTodayCheckShopRestNum(shopCheckLog);
        if(shopDetailVo3.size()!=0){
            shopDetailVo.setShopRestNum(shopDetailVo3.get(0).getShopRestNum());
        }else{
            shopDetailVo.setShopRestNum("0");
        }

        //查询每日巡查关停店总数
        Shop shop = new Shop();
        shop.setIsOpen("关停");
        List<ShopDetailVo> shopDetailVo4 = shopService.selectTodayCheckShopCloseNum(shop);
        if(shopDetailVo4.size()!=0){
            shopDetailVo.setShopCloseNum(shopDetailVo4.get(0).getShopCloseNum());
        }else{
            shopDetailVo.setShopCloseNum("0");
        }

        //查询每日重点监管商铺数量
        Shop shop1 = new Shop();
        List<ShopDetailVo> shopDetailVo5 = shopService.selectSupervisorShop(shop1);
        if(shopDetailVo5.size()!=0){
            shopDetailVo.setShopSupervisorNum(shopDetailVo5.get(0).getShopSupervisorNum());
        }else{
            shopDetailVo.setShopSupervisorNum("0");
        }

        //查询每日谈话商铺数量
        List<ShopDetailVo> shopDetailVo6 = shopService.selectTalkShop(shop1);
        if(shopDetailVo6.size()!=0){
            shopDetailVo.setShopTalkNum(shopDetailVo6.get(0).getShopTalkNum());
        }else{
            shopDetailVo.setShopTalkNum("0");
        }

        //查询每日督办商铺数量
        List<ShopDetailVo>  shopDetailVo7 = shopService.selectHandleShop(shop1);
        if(shopDetailVo7.size()!=0){
            shopDetailVo.setShopHandleNum(shopDetailVo7.get(0).getShopHandleNum());
        }else{
            shopDetailVo.setShopHandleNum("0");
        }

        //查询每日黄牌商铺数量
        List<ShopDetailVo>  shopDetailVo8 = shopService.selectYellowCardShop(shop1);
        if(shopDetailVo8.size()!=0){
            shopDetailVo.setShopYellowCardNum(shopDetailVo8.get(0).getShopYellowCardNum());
        }else{
            shopDetailVo.setShopYellowCardNum("0");
        }



        //*********************************************从业人员相关信息***************************************
        //*********************************************从业人员相关信息***************************************
        //*********************************************从业人员相关信息***************************************

        //从业人员总人数
        Shop shop2 = new Shop();
        List<ShopDetailVo> shopDetailVoss = nucleicAcidLogService.selectEmployeesAll(shop2);
        int num =0;
        if(shopDetailVoss.size()!=0){
            for (ShopDetailVo detailVo : shopDetailVoss) {
                num+=Integer.valueOf(detailVo.getEmployeeNum());

            }
            shopDetailVo.setEmployeesTotal(String.valueOf(num));
        }

        NucleicAcidLog nucleicAcidLog = new NucleicAcidLog();
        nucleicAcidLog.setCheckDate(check_date);

        //每日巡查从业人员总人数
        List<ShopDetailVo> employeesDetailVo1 = nucleicAcidLogService.selectCheckEmployeesTotal(nucleicAcidLog);
        if(employeesDetailVo1.size()!=0){
            shopDetailVo.setCheckEmployeesTotal(employeesDetailVo1.get(0).getCheckEmployeesTotal());
        }

        //每日核酸正常人员数
        List<ShopDetailVo> employeesDetailVo2 = nucleicAcidLogService.selectCheckNormalEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo2.size()!=0){
            shopDetailVo.setCheckNoramEmployeesNum(employeesDetailVo2.get(0).getCheckNoramEmployeesNum());
        }

        //每日核酸异常人员数
        List<ShopDetailVo> employeesDetailVo3 = nucleicAcidLogService.selectCheckAbnormalEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo3.size()!=0){
            shopDetailVo.setCheckAbnormalEmployeesNum(employeesDetailVo3.get(0).getCheckAbnormalEmployeesNum());
        }
        //每日离岗人员数
        List<ShopDetailVo> employeesDetailVo4 = nucleicAcidLogService.selectCheckRetiredEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo4.size()!=0){
            shopDetailVo.setCheckRetiredEmployeesNum(employeesDetailVo4.get(0).getCheckRetiredEmployeesNum());
        }


        return AjaxResult.success(shopDetailVo);
    }

    @Log(title = "日报信息", businessType = BusinessType.EXPORT)
    @PostMapping("/dailyExport/{curDate}")
    public void exportDailyData(HttpServletResponse response,@PathVariable("curDate") String curDate) throws ParseException {
        ShopDetailVo shopDetailVo = new ShopDetailVo();

        ShopCheckLog shopCheckLog = new ShopCheckLog();
        //        Date check_date = DateUtils.parseDate(curDatel);
        Date check_date = DateUtils.parseDate(curDate, "yyyy-MM-dd");
        shopCheckLog.setCheckDate(check_date);




        //巡查日期
        shopDetailVo.setCurDate(check_date);

        //************************************************************店铺具体详细信息*************************************
        //************************************************************店铺具体详细信息*************************************
        //************************************************************店铺具体详细信息*************************************

        //查询 总店铺数
        Shop emptyShop = new Shop();
        List<ShopDetailVo> shopDetailVos = shopService.selectShopTotal(emptyShop);
        if(shopDetailVos.size()!=0){
            shopDetailVo.setShopTotal(shopDetailVos.get(0).getShopTotal());
        }else{
            shopDetailVo.setShopTotal("0");
        }
        //查询每日巡店总数
        List<ShopDetailVo> shopDetailVo1 = shopService.selectTodayCheckShopTotal(shopCheckLog);
        if(shopDetailVo1.size()!=0){
            shopDetailVo.setCheckShopTotal(shopDetailVo1.get(0).getCheckShopTotal());
        }else{
            shopDetailVo.setCheckShopTotal("0");
        }

        //查询每日巡查店铺正常数量
        List<ShopDetailVo> shopDetailVo2= shopService.selectTodayCheckShopNomalNum(shopCheckLog);
        if(shopDetailVo2.size()!=0){
            shopDetailVo.setShopNormalNum(shopDetailVo2.get(0).getShopNormalNum());
        }else{
            shopDetailVo.setShopNormalNum("0");
        }

        //查询每日巡查歇店总数
        List<ShopDetailVo> shopDetailVo3= shopService.selectTodayCheckShopRestNum(shopCheckLog);
        if(shopDetailVo3.size()!=0){
            shopDetailVo.setShopRestNum(shopDetailVo3.get(0).getShopRestNum());
        }else{
            shopDetailVo.setShopRestNum("0");
        }

        //查询每日巡查关停店总数
        Shop shop = new Shop();
        shop.setIsOpen("关停");
        List<ShopDetailVo> shopDetailVo4 = shopService.selectTodayCheckShopCloseNum(shop);
        if(shopDetailVo4.size()!=0){
            shopDetailVo.setShopCloseNum(shopDetailVo4.get(0).getShopCloseNum());
        }else{
            shopDetailVo.setShopCloseNum("0");
        }

        //查询每日重点监管商铺数量
        Shop shop1 = new Shop();
        List<ShopDetailVo> shopDetailVo5 = shopService.selectSupervisorShop(shop1);
        if(shopDetailVo5.size()!=0){
            shopDetailVo.setShopSupervisorNum(shopDetailVo5.get(0).getShopSupervisorNum());
        }else{
            shopDetailVo.setShopSupervisorNum("0");
        }

        //查询每日谈话商铺数量
        List<ShopDetailVo> shopDetailVo6 = shopService.selectTalkShop(shop1);
        if(shopDetailVo6.size()!=0){
            shopDetailVo.setShopTalkNum(shopDetailVo6.get(0).getShopTalkNum());
        }else{
            shopDetailVo.setShopTalkNum("0");
        }

        //查询每日督办商铺数量
        List<ShopDetailVo>  shopDetailVo7 = shopService.selectHandleShop(shop1);
        if(shopDetailVo7.size()!=0){
            shopDetailVo.setShopHandleNum(shopDetailVo7.get(0).getShopHandleNum());
        }else{
            shopDetailVo.setShopHandleNum("0");
        }

        //查询每日黄牌商铺数量
        List<ShopDetailVo>  shopDetailVo8 = shopService.selectYellowCardShop(shop1);
        if(shopDetailVo8.size()!=0){
            shopDetailVo.setShopYellowCardNum(shopDetailVo8.get(0).getShopYellowCardNum());
        }else{
            shopDetailVo.setShopYellowCardNum("0");
        }



        //*********************************************从业人员相关信息***************************************
        //*********************************************从业人员相关信息***************************************
        //*********************************************从业人员相关信息***************************************

        //从业人员总人数
        Shop shop2 = new Shop();
        List<ShopDetailVo> shopDetailVoss = nucleicAcidLogService.selectEmployeesAll(shop2);
        int num =0;
        if(shopDetailVoss.size()!=0){
            for (ShopDetailVo detailVo : shopDetailVoss) {
                num+=Integer.valueOf(detailVo.getEmployeeNum());

            }
            shopDetailVo.setEmployeesTotal(String.valueOf(num));
        }

        NucleicAcidLog nucleicAcidLog = new NucleicAcidLog();
        nucleicAcidLog.setCheckDate(check_date);

        //每日巡查从业人员总人数
        List<ShopDetailVo> employeesDetailVo1 = nucleicAcidLogService.selectCheckEmployeesTotal(nucleicAcidLog);
        if(employeesDetailVo1.size()!=0){
            shopDetailVo.setCheckEmployeesTotal(employeesDetailVo1.get(0).getCheckEmployeesTotal());
        }

        //每日核酸正常人员数
        List<ShopDetailVo> employeesDetailVo2 = nucleicAcidLogService.selectCheckNormalEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo2.size()!=0){
            shopDetailVo.setCheckNoramEmployeesNum(employeesDetailVo2.get(0).getCheckNoramEmployeesNum());
        }

        //每日核酸异常人员数
        List<ShopDetailVo> employeesDetailVo3 = nucleicAcidLogService.selectCheckAbnormalEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo3.size()!=0){
            shopDetailVo.setCheckAbnormalEmployeesNum(employeesDetailVo3.get(0).getCheckAbnormalEmployeesNum());
        }
        //每日离岗人员数
        List<ShopDetailVo> employeesDetailVo4 = nucleicAcidLogService.selectCheckRetiredEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo4.size()!=0){
            shopDetailVo.setCheckRetiredEmployeesNum(employeesDetailVo4.get(0).getCheckRetiredEmployeesNum());
        }
        List<ShopDetailVo>  exportDailyData = new ArrayList<>();
        exportDailyData.add(shopDetailVo);

        ExcelUtil<ShopDetailVo> util = new ExcelUtil<ShopDetailVo>(ShopDetailVo.class);
        util.exportExcel(response, exportDailyData, curDate+"信息数据");

    }




    /**
     * 查询出来居委会负责的全部店铺
     * */
    @GetMapping("/committee")
    public AjaxResult committeeShopData(Shop shop){
//        Date check_date = DateUtils.parseDate(curDate);


        if(StringUtils.isEmpty(shop.getParams())){
            String today= DateUtil.today();
            Map params = new HashMap<>();
            params.put("beginTime",today);
            params.put("endTime",today);
            shop.setParams(params);
        }
        List<CommitteeDetailVo> committeeDetailVo = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("##.##%");
        //
        SysDept sysDept = new SysDept();
        List<SysDept> depts = iSysDeptService.selectAllCommittee(sysDept);

        Integer committeeCheckShopTotal =0;
        Integer committeeCheckedShopNum =0;
        String committeeCheckedRatio = "0%";
        for (SysDept dept : depts) {
            CommitteeDetailVo committeeDetailVo1 = new CommitteeDetailVo();
//            committeeDetailVo1.setCurDate(check_date);

            //查询出来对应居委管理的居委信息
            SysDept dept1 =new SysDept();
            dept1.setDeptId(dept.getDeptId());
            List<CommitteeDetailVo> committeeDetailVo2 = shopService.selectCommitteeCheckShopTotal(dept1);
            if(committeeDetailVo2.size()!=0){
                committeeDetailVo1.setCommittee(committeeDetailVo2.get(0).getCommittee());
                committeeDetailVo1.setCommitteeCheckShopTotal(committeeDetailVo2.get(0).getCommitteeCheckShopTotal());
                committeeCheckShopTotal = Integer.valueOf(committeeDetailVo2.get(0).getCommitteeCheckShopTotal());
            }
            //查询出来对应居委会巡查商铺数量
            shop.setDeptId(dept.getDeptId());
            List<CommitteeDetailVo> committeeDetailVo3 = shopService.selectCommitteeCheckedShopNum(shop);
            if(committeeDetailVo3.size()!=0){
                committeeDetailVo1.setCommitteeCheckedShopNum(committeeDetailVo3.get(0).getCommitteeCheckedShopNum());
                committeeCheckedShopNum= Integer.valueOf(committeeDetailVo3.get(0).getCommitteeCheckedShopNum());
            }
            //拿到各个居委会巡查商铺比例
            committeeCheckedRatio = df.format((float)committeeCheckedShopNum/committeeCheckShopTotal);
            committeeDetailVo1.setCommitteeCheckedRatio(committeeCheckedRatio);
            committeeDetailVo.add(committeeDetailVo1);

        }

        return AjaxResult.success(committeeDetailVo);
    }

    @Log(title = "居委会负责店铺数据", businessType = BusinessType.EXPORT)
    @PostMapping("/committeeExport")
    public void exportCommitteeData(HttpServletResponse response,Shop shop) throws ParseException {

//        Date check_date = DateUtils.parseDate(curDate, "yyyy-MM-dd");
        List<CommitteeDetailVo> committeeDetailVo = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("##.##%");
        //
        SysDept sysDept = new SysDept();
        List<SysDept> depts = iSysDeptService.selectAllCommittee(sysDept);

        Integer committeeCheckShopTotal =0;
        Integer committeeCheckedShopNum =0;
        String committeeCheckedRatio = "0%";
        for (SysDept dept : depts) {
            CommitteeDetailVo committeeDetailVo1 = new CommitteeDetailVo();
//            committeeDetailVo1.setCurDate(check_date);

            //查询出来对应居委管理的居委信息
            SysDept dept1 =new SysDept();
            dept1.setDeptId(dept.getDeptId());
            List<CommitteeDetailVo> committeeDetailVo2 = shopService.selectCommitteeCheckShopTotal(dept1);
            if(committeeDetailVo2.size()!=0){
                committeeDetailVo1.setCommittee(committeeDetailVo2.get(0).getCommittee());
                committeeDetailVo1.setCommitteeCheckShopTotal(committeeDetailVo2.get(0).getCommitteeCheckShopTotal());
                committeeCheckShopTotal = Integer.valueOf(committeeDetailVo2.get(0).getCommitteeCheckShopTotal());
            }
            //查询出来对应居委会巡查商铺数量
            shop.setDeptId(dept.getDeptId());
            List<CommitteeDetailVo> committeeDetailVo3 = shopService.selectCommitteeCheckedShopNum(shop);
            if(committeeDetailVo3.size()!=0){
                committeeDetailVo1.setCommitteeCheckedShopNum(committeeDetailVo3.get(0).getCommitteeCheckedShopNum());
                committeeCheckedShopNum= Integer.valueOf(committeeDetailVo3.get(0).getCommitteeCheckedShopNum());
            }
            //拿到各个居委会巡查商铺比例
            committeeCheckedRatio = df.format((float)committeeCheckedShopNum/committeeCheckShopTotal);
            committeeDetailVo1.setCommitteeCheckedRatio(committeeCheckedRatio);
            committeeDetailVo.add(committeeDetailVo1);
        }
        ExcelUtil<CommitteeDetailVo> util = new ExcelUtil<CommitteeDetailVo>(CommitteeDetailVo.class);
        util.exportExcel(response, committeeDetailVo, "居委会管理下巡查商铺数据");

    }

    /**
     * 查询出来行业负责的全部店铺信息
     * **/

//    public AjaxResult categoryShopData(@PathVariable("curDate") String curDate){
    @GetMapping("/category")
    public AjaxResult categoryShopData(Shop shop){
//        Date checkDate = DateUtils.parseDate(curDate);

        if(StringUtils.isEmpty(shop.getParams())){
            String today= DateUtil.today();
            Map params = new HashMap<>();
            params.put("beginTime",today);
            params.put("endTime",today);
            shop.setParams(params);
        }
        List<CategoryDetailVo> categoryDetailVo = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("##.##%");  //##.##%

        List<Category> categories = iCategoryService.selectCategoryList(new Category());
        Integer categoryCheckShopTotal=0;
        Integer categoryCheckedShopNum=0;
        String categoryCheckedRatio="0%";
        for (Category categorys : categories) {
            CategoryDetailVo categoryDetailVos = new CategoryDetailVo();


            //获取当前各个行业所需要管理的店铺数据
            String shopCategory = categorys.getCategory();
            List<CategoryDetailVo> categoryDetailVo1 = shopService.selectCategoryShopNum(shopCategory);
            if(categoryDetailVo1.size()!=0){
                categoryDetailVos.setCategory(categorys.getCategory());
                categoryDetailVos.setCategoryCheckShopTotal(categoryDetailVo1.get(0).getCategoryCheckShopTotal());
                categoryCheckShopTotal =Integer.valueOf(categoryDetailVo1.get(0).getCategoryCheckShopTotal());
            }

            //获取当前各个行业已经巡查的店铺数据
            String category = categorys.getCategory();
            shop.setCategory(category);
            List<CategoryDetailVo> categoryDetailVo2 = shopService.selectCategoryCheckedShopNum(shop);
            if(categoryDetailVo2.size()!=0){
                categoryDetailVos.setCategoryCheckedShopNum(categoryDetailVo2.get(0).getCategoryCheckedShopNum());
                categoryCheckedShopNum = Integer.valueOf(categoryDetailVo2.get(0).getCategoryCheckedShopNum());

            }

            if(categoryDetailVo1.get(0).getCategoryCheckShopTotal().equals("0")){   //如果行业管理的店铺数据为0
                categoryDetailVos.setCategoryCheckedRatio("0.00");
            }else{
                categoryCheckedRatio = df.format((float) categoryCheckedShopNum / categoryCheckShopTotal);
                categoryDetailVos.setCategoryCheckedRatio(categoryCheckedRatio);
            }


//            categoryDetailVos.setCurDate(checkDate);
            categoryDetailVo.add(categoryDetailVos);

        }

        return AjaxResult.success(categoryDetailVo);
    }

    @Log(title = "行业负责店铺数据", businessType = BusinessType.EXPORT)
    @PostMapping("/categoryExport")
    public void exportCategoryData(HttpServletResponse response,Shop shop) throws ParseException {

//        Date checkDate = DateUtils.parseDate(curDate, "yyyy-MM-dd");

        List<CategoryDetailVo> categoryDetailVo = new ArrayList<>();
        DecimalFormat df = new DecimalFormat("##.##%");  //##.##%

        List<Category> categories = iCategoryService.selectCategoryList(new Category());
        Integer categoryCheckShopTotal=0;
        Integer categoryCheckedShopNum=0;
        String categoryCheckedRatio="0%";
        for (Category categorys : categories) {
            CategoryDetailVo categoryDetailVos = new CategoryDetailVo();


            //获取当前各个行业所需要管理的店铺数据
            String shopCategory = categorys.getCategory();
            List<CategoryDetailVo> categoryDetailVo1 = shopService.selectCategoryShopNum(shopCategory);
            if(categoryDetailVo1.size()!=0){
                categoryDetailVos.setCategory(categorys.getCategory());
                categoryDetailVos.setCategoryCheckShopTotal(categoryDetailVo1.get(0).getCategoryCheckShopTotal());
                categoryCheckShopTotal =Integer.valueOf(categoryDetailVo1.get(0).getCategoryCheckShopTotal());
            }

            //获取当前各个行业已经巡查的店铺数据
            String category = categorys.getCategory();
            shop.setCategory(category);
            List<CategoryDetailVo> categoryDetailVo2 = shopService.selectCategoryCheckedShopNum(shop);
            if(categoryDetailVo2.size()!=0){
                categoryDetailVos.setCategoryCheckedShopNum(categoryDetailVo2.get(0).getCategoryCheckedShopNum());
                categoryCheckedShopNum = Integer.valueOf(categoryDetailVo2.get(0).getCategoryCheckedShopNum());

            }

            if(categoryDetailVo1.get(0).getCategoryCheckShopTotal().equals("0")){   //如果行业管理的店铺数据为0
                categoryDetailVos.setCategoryCheckedRatio("0.00");
            }
            categoryCheckedRatio = df.format((float) categoryCheckedShopNum / categoryCheckShopTotal);
            categoryDetailVos.setCategoryCheckedRatio(categoryCheckedRatio);
//            categoryDetailVos.setCurDate(checkDate);
            categoryDetailVo.add(categoryDetailVos);

        }

        ExcelUtil<CategoryDetailVo> util = new ExcelUtil<CategoryDetailVo>(CategoryDetailVo.class);
        util.exportExcel(response, categoryDetailVo, "行业类别下巡查商铺数据");
    }


    @GetMapping("/alllist")
    public AjaxResult shopList(Shop shop){

        List<Shop> allShop  =  shopService.selectAllShops(shop);
        if(allShop.size()==0){
            List<Shop> shopList = new ArrayList<>();
            return AjaxResult.success(shopList);
        }
        return AjaxResult.success(allShop);
    }

    /**
     * 导出店铺信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:shop:export')")
    @Log(title = "店铺信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Shop shop)
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();

        if(user.getUserName().equals("admin") ){
            // 获取当前的用户名称
            List<Shop> list = shopService.selectShopList(shop);
            ExcelUtil<Shop> util = new ExcelUtil<Shop>(Shop.class);
            util.exportExcel(response, list, "店铺信息数据");
        }

        if(deptId!=null && !Objects.equals(user.getUserName(), "admin")){
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if(StringUtils.isNotEmpty(sysDept.getIsCommittee())&&sysDept.getIsCommittee().equals("1")) {     //获取居委会对应查看的店铺列表
                shop.setShopDeparmentId(deptId);
                List<Shop> list = shopService.selectShopList(shop);
                ExcelUtil<Shop> util = new ExcelUtil<Shop>(Shop.class);
                util.exportExcel(response, list, "店铺信息数据");

            }else if(StringUtils.isNotEmpty(sysDept.getIsCommittee())
                    &&sysDept.getIsCommittee().equals("2")){ //获取网格长查看多个居委会数据
                List<Shop> shopDeptList = shopService.selectShopList(shop).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getShopDeparmentId())).collect(Collectors.toList());
                ExcelUtil<Shop> util = new ExcelUtil<Shop>(Shop.class);
                util.exportExcel(response, shopDeptList, "店铺信息数据");
            }
        }
    }



    /**
     *
     * 新版日报数据  （商铺总数、歇业数、关停店铺数）
     * **/
    @GetMapping(value="/dailyData")
    public AjaxResult shopDaily(ShopDailyVo shopDailyVo){
        ShopDailyVo shopDaily = new ShopDailyVo();
        List<ShopDailyVo> shopOpenlist = new ArrayList<>();


        Shop shop = new Shop();
        //查询商铺总数
        List<Shop> totalShop = shopService.selectShopList(shop);
        shopDaily.setShopTotal(String.valueOf(totalShop.size()));

        //查询正常商铺总数
        Shop normalShop = new Shop();
        List<Shop> openNormal = shopService.selectOpenNormalShopNum(normalShop);
        if(openNormal.size()!=0){
            shopDaily.setShopNormalNum(String.valueOf(openNormal.size()));
        }else{
            shopDaily.setShopNormalNum("0");
        }

        //查询歇业商铺总数
        Shop restShop = new Shop();
        List<Shop> openRest = shopService.selectOpenRestShopNum(restShop);
        if(openRest.size()!=0){
            shopDaily.setShopRestNum(String.valueOf(openRest.size()));
        }else{
            shopDaily.setShopRestNum("0");
        }

        //查询关停商铺总数
        Shop stopShop = new Shop();
        List<Shop> openStop = shopService.selectOpenStopShopNum(stopShop);
        if(openStop.size()!=0){
            shopDaily.setShopCloseNum(String.valueOf(openStop.size()));
        }else{
            shopDaily.setShopCloseNum("0");
        }
        shopOpenlist.add(shopDaily);


        return AjaxResult.success(shopOpenlist);
    }



    /**
     * 获取店铺信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:shop:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shopService.selectShopById(id));
    }


    /**
     * 新增店铺信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:shop:add')")
    @Log(title = "店铺信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Shop shop)
    {
        // SysUser user = SecurityUtils.getLoginUser().getUser();
        // Long deptId = user.getDeptId();
        // SysDept sysDept = iSysDeptService.selectDeptById(deptId);
        // shop.setShopDeparmentId(sysDept.getDeptId());

        Boolean is_completed = StringUtils.isNotEmpty(shop.getShopName()) && StringUtils.isNotEmpty(shop.getShopAddress()) && StringUtils.isNotEmpty(shop.getShopLicense())
                && StringUtils.isNotEmpty(shop.getShopCreditCode()) &&StringUtils.isNotEmpty(shop.getShopContract()) && StringUtils.isNotEmpty(shop.getShopContactPhone())
                && StringUtils.isNotEmpty(shop.getLegalPerson()) && StringUtils.isNotEmpty(shop.getLegalPersonContact()) && StringUtils.isNotEmpty(shop.getStreetChief())
                && StringUtils.isNotEmpty(shop.getChiefPhone()) && shop.getShopDeparmentId()!=null && StringUtils.isNotEmpty(shop.getShopEmployeeNum())
                && StringUtils.isNotEmpty(shop.getShopCategory()) && StringUtils.isNotEmpty(shop.getShopSubcategory()) && StringUtils.isNotEmpty(shop.getShopLittlecategory())
                && StringUtils.isNotEmpty(shop.getCoordinate()) && StringUtils.isNotEmpty(shop.getIsAccommodation()) && StringUtils.isNotEmpty(shop.getIsOpen())
                && StringUtils.isNotEmpty(shop.getShopHouseOwnership()) && StringUtils.isNotEmpty(shop.getShoplAndlordPhone()) && StringUtils.isNotEmpty(shop.getShopLandlordContract())
                && StringUtils.isNotEmpty(shop.getIsStateAssets()) && StringUtils.isNotEmpty(shop.getShopGoverningProperty()) && StringUtils.isNotEmpty(shop.getIsSameCommunityProperty())
                && StringUtils.isNotEmpty(shop.getShopPropertyContact()) && StringUtils.isNotEmpty(shop.getShopPropertyPhone()) &&StringUtils.isNotEmpty(shop.getRoadSection())
                && StringUtils.isNotEmpty(shop.getRoadSectionStart()) && StringUtils.isNotEmpty(shop.getRoadSectionEnd());


        if(is_completed){
            shop.setInformationStatus("已完善");
        }else{
            shop.setInformationStatus("未完善");
        }

        if(StringUtils.isNotEmpty(shop.getInsuranceLiabilityContact())||StringUtils.isNotEmpty(shop.getInsuranceLiabilityPhone())){
            shop.setInsuranceLiabilityFlag("1");
        }else{
            shop.setInsuranceLiabilityFlag("0");
        }

        return toAjax(shopService.insertShop(shop));
    }

    /**
     * 修改店铺信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:shop:edit')")
    @Log(title = "店铺信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Shop shop)
    {

        Boolean is_completed = StringUtils.isNotEmpty(shop.getShopName())&&StringUtils.isNotEmpty(shop.getShopAddress())&&StringUtils.isNotEmpty(shop.getShopLicense())
                &&StringUtils.isNotEmpty(shop.getShopCreditCode())&&StringUtils.isNotEmpty(shop.getShopContract())&&StringUtils.isNotEmpty(shop.getShopContactPhone())
                &&StringUtils.isNotEmpty(shop.getLegalPerson())&&StringUtils.isNotEmpty(shop.getLegalPersonContact())&&StringUtils.isNotEmpty(shop.getStreetChief())
                &&StringUtils.isNotEmpty(shop.getChiefPhone())&&shop.getShopDeparmentId()!=null&&StringUtils.isNotEmpty(shop.getShopEmployeeNum())
                &&StringUtils.isNotEmpty(shop.getShopCategory())&&StringUtils.isNotEmpty(shop.getShopSubcategory())&&StringUtils.isNotEmpty(shop.getShopLittlecategory())
                &&StringUtils.isNotEmpty(shop.getCoordinate())&&StringUtils.isNotEmpty(shop.getIsAccommodation())&&StringUtils.isNotEmpty(shop.getIsOpen())
                &&StringUtils.isNotEmpty(shop.getShopHouseOwnership())&&StringUtils.isNotEmpty(shop.getShoplAndlordPhone())&&StringUtils.isNotEmpty(shop.getShopLandlordContract())
                &&StringUtils.isNotEmpty(shop.getIsStateAssets())&&StringUtils.isNotEmpty(shop.getShopGoverningProperty())&&StringUtils.isNotEmpty(shop.getIsSameCommunityProperty())
                &&StringUtils.isNotEmpty(shop.getShopPropertyContact())&&StringUtils.isNotEmpty(shop.getShopPropertyPhone())&&StringUtils.isNotEmpty(shop.getRoadSection())
                && StringUtils.isNotEmpty(shop.getRoadSectionStart()) && StringUtils.isNotEmpty(shop.getRoadSectionEnd());


        if(is_completed){
            shop.setInformationStatus("已完善");
        }else{
            shop.setInformationStatus("未完善");
        }
        if(StringUtils.isNotEmpty(shop.getInsuranceLiabilityContact())||StringUtils.isNotEmpty(shop.getInsuranceLiabilityPhone())){
            shop.setInsuranceLiabilityFlag("1");
        }else{
            shop.setInsuranceLiabilityFlag("0");
        }

        return toAjax(shopService.updateShop(shop));
    }

    /**
     * 删除店铺信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:shop:remove')")
    @Log(title = "店铺信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shopService.deleteShopByIds(ids));
    }


    @GetMapping("/chief/{road}")
    public AjaxResult chiefShop(@PathVariable("road") String road){
        List<Shop> list= shopService.selectShopByChiefRoad(road);
        return AjaxResult.success(list);
    }
}
