package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyRoad;

/**
 * 路段名Service接口
 * 
 * <AUTHOR>
 * @date 2023-02-09
 */
public interface IShcyRoadService 
{
    /**
     * 查询路段名
     * 
     * @param id 路段名主键
     * @return 路段名
     */
    public ShcyRoad selectShcyRoadById(Long id);

    /**
     * 查询路段名列表
     * 
     * @param shcyRoad 路段名
     * @return 路段名集合
     */
    public List<ShcyRoad> selectShcyRoadList(ShcyRoad shcyRoad);

    /**
     * 新增路段名
     * 
     * @param shcyRoad 路段名
     * @return 结果
     */
    public int insertShcyRoad(ShcyRoad shcyRoad);

    /**
     * 修改路段名
     * 
     * @param shcyRoad 路段名
     * @return 结果
     */
    public int updateShcyRoad(ShcyRoad shcyRoad);

    /**
     * 批量删除路段名
     * 
     * @param ids 需要删除的路段名主键集合
     * @return 结果
     */
    public int deleteShcyRoadByIds(Long[] ids);

    /**
     * 删除路段名信息
     * 
     * @param id 路段名主键
     * @return 结果
     */
    public int deleteShcyRoadById(Long id);
}
