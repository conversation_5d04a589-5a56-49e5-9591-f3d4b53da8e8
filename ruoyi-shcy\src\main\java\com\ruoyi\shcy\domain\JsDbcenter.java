package com.ruoyi.shcy.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网格化案件信息
 * Oracle数据库表名：JSDBCENTER.T_TASK_DBCENTER
 * Oracle数据库版本：11.2.0.4.0
 */
public class JsDbcenter extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @JSONField(name = "ID")
    private Long id;

    /**
     * 是否市级标准大小类（1-是，0-否）
     */
    @JSONField(name = "ISSTANDARD")
    private Long isstandard;

    /**
     * 核查照片
     */
    @JSONField(name = "CHECKIMAGE")
    private String checkimage;

    /**
     * 业务类型名
     */
    @JSONField(name = "SERVICETYPENAME")
    private String servicetypename;

    /**
     * 12345业务类型
     */
    @JSONField(name = "WP_TYPE")
    private String wpType;

    /**
     * 是否现行联系
     */
    @JSONField(name = "ISFIRSTCONTACT")
    private Long isfirstcontact;

    /**
     * $column.columnComment
     */
    @JSONField(name = "HYNAME")
    private String hyname;

    /**
     * 上报监督员编号
     */
    @JSONField(name = "UPKEEPERNAME")
    private String upkeepername;

    /**
     * 立案部门名称
     */
    @JSONField(name = "DEPTNAME")
    private String deptname;

    /**
     * 状态
     */
    @JSONField(name = "STATUSNAME")
    private String statusname;

    /**
     * --
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "SYNCTIME")
    private Date synctime;

    /**
     * 首次联系截止时间（最后主责）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "LASTCONTACTTIME")
    private Date lastcontacttime;

    /**
     * 接单时间（最后一次主责部门处理完成时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "ACCEPTTIME")
    private Date accepttime;

    /**
     * 作废时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "CANCLETIME")
    private Date cancletime;

    /**
     * 处理阶段红灯开始时间（处理截止时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "LASTSOLVINGTIME")
    private Date lastsolvingtime;

    /**
     * 处理阶段橙灯开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "IMPORTANTSOLVINGTIME")
    private Date importantsolvingtime;

    /**
     * 处理阶段黄灯开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "MIDDLESOLVINGTIME")
    private Date middlesolvingtime;

    /**
     * 整体案卷截止时间（红灯开始时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "ALLENDTIME")
    private Date allendtime;

    /**
     * 整体案卷橙灯开始时间（根据紧急程度important计算的）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "ALLIMPORTANTTIME")
    private Date allimportanttime;

    /**
     * 整体案卷黄灯开始时间（根据紧急程度middle计算的）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "ALLMIDDLETIME")
    private Date allmiddletime;

    /**
     * 结案时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "ENDTIME")
    private Date endtime;

    /**
     * 回访时间（最后一次回访时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "TELASKTIME")
    private Date telasktime;

    /**
     * 处理完成时间（最后一次主责部门处理完成时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "SOLVINGTIME")
    private Date solvingtime;

    /**
     * 派遣时间（最后一次派遣时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "DISPATCHTIME")
    private Date dispatchtime;

    /**
     * 立案时间（最后一次立案时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "CREATETIME")
    private Date createtime;

    /**
     * 受理时间（最后一次受理时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "PERCREATETIME")
    private Date percreatetime;

    /**
     * 发现时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(name = "DISCOVERTIME")
    private Date discovertime;

    /**
     * 最后一次主责部门
     */
    @JSONField(name = "EXECUTEDEPTNAME")
    private String executedeptname;

    /**
     * 责任网格
     */
    @JSONField(name = "WORKGRIDCODE")
    private String workgridcode;

    /**
     * 村，居
     */
    @JSONField(name = "COMMUNITYNAME")
    private String communityname;

    /**
     * 街镇名
     */
    @JSONField(name = "STREETNAME")
    private String streetname;

    /**
     * 工作网格
     */
    @JSONField(name = "WORKGRID")
    private String workgrid;

    /**
     * 发生地址
     */
    @JSONField(name = "ADDRESS")
    private String address;

    /**
     * y坐标
     */
    @JSONField(name = "COORDY")
    private double coordy;

    /**
     * X坐标
     */
    @JSONField(name = "COORDX")
    private double coordx;

    /**
     * 万米网格编码
     */
    @JSONField(name = "GRIDCODE")
    private String gridcode;

    /**
     * 村、居村编码
     */
    @JSONField(name = "COMMUNITYCODE")
    private String communitycode;

    /**
     * 街道编号
     */
    @JSONField(name = "STREETCODE")
    private String streetcode;

    /**
     * 现场查看
     */
    @JSONField(name = "VIEWINFO")
    private String viewinfo;

    /**
     * 是否满意
     */
    @JSONField(name = "CASEVALUATION12345")
    private Long casevaluation12345;

    /**
     * 未联原因
     */
    @JSONField(name = "NOT_REASON")
    private String notReason;

    /**
     * 反馈结论
     */
    @JSONField(name = "DESCRIPTION12345")
    private String description12345;

    /**
     * 诉求认定说明
     */
    @JSONField(name = "APPEAL_EXPLAIN")
    private String appealExplain;

    /**
     * 办理结果
     */
    @JSONField(name = "BANLIRESULT12345")
    private Long banliresult12345;

    /**
     * 工单来源
     */
    @JSONField(name = "WP_SOURCE")
    private String wpSource;

    /**
     * 渠道来源
     */
    @JSONField(name = "REPORTDEPTNAME")
    private String reportdeptname;

    /**
     * 12319编号，延伸为外系统管理单号
     */
    @JSONField(name = "HOTLINESN")
    private String hotlinesn;

    /**
     * 12345办理结果 (0:解决，1:未解决，2:部分解决，3:不办理退单)
     */
    @JSONField(name = "BANLIRESULT")
    private Long banliresult;

    /**
     * 12345督办时限(天)
     */
    @JSONField(name = "DULIMIT")
    private Long duLimit;

    /**
     * 12345催单次数（12345催单时带过来的催单次数）
     */
    @JSONField(name = "URGECOUNT")
    private Long urgeCount;

    /**
     * 12345回访复核单状态标识(0：否，1：是)
     */
    @JSONField(name = "CALLBACKFLAG")
    private Long callbackFlag;

    /**
     * 用户评价/满意度(0:满意;1:基本满意;2:不满意)
     */
    @JSONField(name = "USEREVALUATE")
    private Long userevaluate;

    /**
     * 是否匿名
     */
    @JSONField(name = "ISANONYMITY")
    private Long isanonymity;

    /**
     * 业务类型
     */
    @JSONField(name = "SERVICETYPE")
    private String servicetype;

    /**
     * 相关案件编号
     */
    @JSONField(name = "SIMILARCASESN")
    private String similarcasesn;

    /**
     * 12345工单处理方式/案卷类型（0：转办;1：督办;2：回访复核）
     */
    @JSONField(name = "CASETYPE")
    private Long approach;

    /**
     * 紧急程度（0或者null：一般；1：紧急；2：次紧急）
     */
    @JSONField(name = "URGENTDEGREE")
    private Long urgentdegree;

    /**
     * 部件编号
     */
    @JSONField(name = "PARTSN")
    private String partsn;

    /**
     * 结案备注
     */
    @JSONField(name = "ENDNOTE")
    private String endnote;

    /**
     * 派遣备注
     */
    @JSONField(name = "DISPATCHNOTE")
    private String dispatchnote;

    /**
     * 反映人
     */
    @JSONField(name = "REPORTER")
    private String reporter;

    /**
     * 子类
     */
    @JSONField(name = "INFOZCNAME")
    private String infozcname;

    /**
     * 小类
     */
    @JSONField(name = "INFOSCNAME")
    private String infoscname;

    /**
     * 大类
     */
    @JSONField(name = "INFOBCNAME")
    private String infobcname;

    /**
     * 案件属性
     */
    @JSONField(name = "INFOTYPENAME")
    private String infotypename;

    /**
     * 问题来源
     */
    @JSONField(name = "INFOSOURCENAME")
    private String infosourcename;

    /**
     * 联系方式
     */
    @JSONField(name = "CONTACTINFO")
    private String contactinfo;

    /**
     * 催办过的次数
     */
    @JSONField(name = "URGECOUNT12345")
    private Long hastentypecount;

    /**
     * 领导督办次数
     */
    @JSONField(name = "HASLEADTYPECOUNT")
    private Long hasleadtypecount;

    /**
     * 回访次数
     */
    @JSONField(name = "HUIFANGCOUNT")
    private Long huifangcount;

    /**
     * 核查次数
     */
    @JSONField(name = "HECHACOUNT")
    private Long hechacount;

    /**
     * 核实次数
     */
    @JSONField(name = "HESHICOUNT")
    private Long heshicount;

    /**
     * 反映人联系方式
     */
    @JSONField(name = "CONTACTMODE")
    private String contactmode;

    /**
     * 来电号码（环境热线）
     */
    @JSONField(name = "CALLNUMBER")
    private String callnumber;

    /**
     * 重要区域
     */
    @JSONField(name = "PRIORITYAREA")
    private String priorityarea;

    /**
     * 审核标志0：未审核；1：审核通过；2：审核未通过
     */
    @JSONField(name = "CHECKFLAG")
    private Long checkresult;

    /**
     * 最后的核实结果 （1:属实，0:不属实）
     */
    @JSONField(name = "VERIFYRESULT")
    private Long verifyresult;

    /**
     * 结案评价
     */
    @JSONField(name = "CASEVALUATION")
    private Long endresult;

    /**
     * 是否简易流程：1-简易流程，0或空一般流程
     */
    @JSONField(name = "ISJYLC")
    private Long caseend;

    /**
     * 记录添加操作员（收集人）
     */
    @JSONField(name = "INSERTUSER")
    private String insertuser;

    /**
     * 上报监督员编号
     */
    @JSONField(name = "UPKEEPER")
    private String keepersn;

    /**
     * 收集部门
     */
    @JSONField(name = "INSERTDEPTCODE")
    private String insertdeptcode;

    /**
     * 最后一次主责部门
     */
    @JSONField(name = "EXECUTEDEPTCODE")
    private String executedeptcode;

    /**
     * 立案部门
     */
    @JSONField(name = "DEPTCODE")
    private String deptcode;

    /**
     * T_INFO_MAIN表状态
     */
    @JSONField(name = "STATUS")
    private Long status;

    /**
     * 问题描述
     */
    @JSONField(name = "DESCRIPTION")
    private String description;

    /**
     * 问题管理要点编码
     */
    @JSONField(name = "INFOATCODE")
    private String infoatcode;

    /**
     * 问题子类编号
     */
    @JSONField(name = "INFOZCCODE")
    private String infozccode;

    /**
     * 问题大类编号
     */
    @JSONField(name = "INFOSCCODE")
    private String infosccode;

    /**
     * 案件大类编号
     */
    @JSONField(name = "INFOBCCODE")
    private String infobccode;

    /**
     * 问题类型编码(0:部件；1：事件；...)
     */
    @JSONField(name = "INFOTYPECODE")
    private Long infotypeid;

    /**
     * 问题来源
     */
    @JSONField(name = "INFOSOURCECODE")
    private Long infosourceid;

    /**
     * 案卷编号
     */
    @JSONField(name = "CASESN")
    private String casesn;

    /**
     * 任务编号
     */
    @JSONField(name = "TASKID")
    private String taskid;

    /**
     * 三级部门
     */
    @JSONField(name = "SUBEXECUTEDEPTNAME_MH")
    private String subexecutedeptnameMh;

    /**
     * 上传图片
     */
    @JSONField(name = "IMAGEFILENAME")
    private String imagefilename;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setIsstandard(Long isstandard) {
        this.isstandard = isstandard;
    }

    public Long getIsstandard() {
        return isstandard;
    }

    public void setCheckimage(String checkimage) {
        this.checkimage = checkimage;
    }

    public String getCheckimage() {
        return checkimage;
    }

    public void setServicetypename(String servicetypename) {
        this.servicetypename = servicetypename;
    }

    public String getServicetypename() {
        return servicetypename;
    }

    public void setWpType(String wpType) {
        this.wpType = wpType;
    }

    public String getWpType() {
        return wpType;
    }

    public void setIsfirstcontact(Long isfirstcontact) {
        this.isfirstcontact = isfirstcontact;
    }

    public Long getIsfirstcontact() {
        return isfirstcontact;
    }

    public void setHyname(String hyname) {
        this.hyname = hyname;
    }

    public String getHyname() {
        return hyname;
    }

    public void setUpkeepername(String upkeepername) {
        this.upkeepername = upkeepername;
    }

    public String getUpkeepername() {
        return upkeepername;
    }

    public void setDeptname(String deptname) {
        this.deptname = deptname;
    }

    public String getDeptname() {
        return deptname;
    }

    public void setStatusname(String statusname) {
        this.statusname = statusname;
    }

    public String getStatusname() {
        return statusname;
    }

    public void setSynctime(Date synctime) {
        this.synctime = synctime;
    }

    public Date getSynctime() {
        return synctime;
    }

    public void setLastcontacttime(Date lastcontacttime) {
        this.lastcontacttime = lastcontacttime;
    }

    public Date getLastcontacttime() {
        return lastcontacttime;
    }

    public void setAccepttime(Date accepttime) {
        this.accepttime = accepttime;
    }

    public Date getAccepttime() {
        return accepttime;
    }

    public void setCancletime(Date cancletime) {
        this.cancletime = cancletime;
    }

    public Date getCancletime() {
        return cancletime;
    }

    public void setLastsolvingtime(Date lastsolvingtime) {
        this.lastsolvingtime = lastsolvingtime;
    }

    public Date getLastsolvingtime() {
        return lastsolvingtime;
    }

    public void setImportantsolvingtime(Date importantsolvingtime) {
        this.importantsolvingtime = importantsolvingtime;
    }

    public Date getImportantsolvingtime() {
        return importantsolvingtime;
    }

    public void setMiddlesolvingtime(Date middlesolvingtime) {
        this.middlesolvingtime = middlesolvingtime;
    }

    public Date getMiddlesolvingtime() {
        return middlesolvingtime;
    }

    public void setAllendtime(Date allendtime) {
        this.allendtime = allendtime;
    }

    public Date getAllendtime() {
        return allendtime;
    }

    public void setAllimportanttime(Date allimportanttime) {
        this.allimportanttime = allimportanttime;
    }

    public Date getAllimportanttime() {
        return allimportanttime;
    }

    public void setAllmiddletime(Date allmiddletime) {
        this.allmiddletime = allmiddletime;
    }

    public Date getAllmiddletime() {
        return allmiddletime;
    }

    public void setEndtime(Date endtime) {
        this.endtime = endtime;
    }

    public Date getEndtime() {
        return endtime;
    }

    public void setTelasktime(Date telasktime) {
        this.telasktime = telasktime;
    }

    public Date getTelasktime() {
        return telasktime;
    }

    public void setSolvingtime(Date solvingtime) {
        this.solvingtime = solvingtime;
    }

    public Date getSolvingtime() {
        return solvingtime;
    }

    public void setDispatchtime(Date dispatchtime) {
        this.dispatchtime = dispatchtime;
    }

    public Date getDispatchtime() {
        return dispatchtime;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public void setPercreatetime(Date percreatetime) {
        this.percreatetime = percreatetime;
    }

    public Date getPercreatetime() {
        return percreatetime;
    }

    public void setDiscovertime(Date discovertime) {
        this.discovertime = discovertime;
    }

    public Date getDiscovertime() {
        return discovertime;
    }

    public void setExecutedeptname(String executedeptname) {
        this.executedeptname = executedeptname;
    }

    public String getExecutedeptname() {
        return executedeptname;
    }

    public void setWorkgridcode(String workgridcode) {
        this.workgridcode = workgridcode;
    }

    public String getWorkgridcode() {
        return workgridcode;
    }

    public void setCommunityname(String communityname) {
        this.communityname = communityname;
    }

    public String getCommunityname() {
        return communityname;
    }

    public void setStreetname(String streetname) {
        this.streetname = streetname;
    }

    public String getStreetname() {
        return streetname;
    }

    public void setWorkgrid(String workgrid) {
        this.workgrid = workgrid;
    }

    public String getWorkgrid() {
        return workgrid;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setCoordy(double coordy) {
        this.coordy = coordy;
    }

    public double getCoordy() {
        return coordy;
    }

    public void setCoordx(double coordx) {
        this.coordx = coordx;
    }

    public double getCoordx() {
        return coordx;
    }

    public void setGridcode(String gridcode) {
        this.gridcode = gridcode;
    }

    public String getGridcode() {
        return gridcode;
    }

    public void setCommunitycode(String communitycode) {
        this.communitycode = communitycode;
    }

    public String getCommunitycode() {
        return communitycode;
    }

    public void setStreetcode(String streetcode) {
        this.streetcode = streetcode;
    }

    public String getStreetcode() {
        return streetcode;
    }

    public void setViewinfo(String viewinfo) {
        this.viewinfo = viewinfo;
    }

    public String getViewinfo() {
        return viewinfo;
    }

    public void setCasevaluation12345(Long casevaluation12345) {
        this.casevaluation12345 = casevaluation12345;
    }

    public Long getCasevaluation12345() {
        return casevaluation12345;
    }

    public void setNotReason(String notReason) {
        this.notReason = notReason;
    }

    public String getNotReason() {
        return notReason;
    }

    public void setDescription12345(String description12345) {
        this.description12345 = description12345;
    }

    public String getDescription12345() {
        return description12345;
    }

    public void setAppealExplain(String appealExplain) {
        this.appealExplain = appealExplain;
    }

    public String getAppealExplain() {
        return appealExplain;
    }

    public void setBanliresult12345(Long banliresult12345) {
        this.banliresult12345 = banliresult12345;
    }

    public Long getBanliresult12345() {
        return banliresult12345;
    }

    public void setWpSource(String wpSource) {
        this.wpSource = wpSource;
    }

    public String getWpSource() {
        return wpSource;
    }

    public void setReportdeptname(String reportdeptname) {
        this.reportdeptname = reportdeptname;
    }

    public String getReportdeptname() {
        return reportdeptname;
    }

    public void setHotlinesn(String hotlinesn) {
        this.hotlinesn = hotlinesn;
    }

    public String getHotlinesn() {
        return hotlinesn;
    }

    public void setBanliresult(Long banliresult) {
        this.banliresult = banliresult;
    }

    public Long getBanliresult() {
        return banliresult;
    }

    public void setDuLimit(Long duLimit) {
        this.duLimit = duLimit;
    }

    public Long getDuLimit() {
        return duLimit;
    }

    public void setUrgeCount(Long urgeCount) {
        this.urgeCount = urgeCount;
    }

    public Long getUrgeCount() {
        return urgeCount;
    }

    public void setCallbackFlag(Long callbackFlag) {
        this.callbackFlag = callbackFlag;
    }

    public Long getCallbackFlag() {
        return callbackFlag;
    }

    public void setUserevaluate(Long userevaluate) {
        this.userevaluate = userevaluate;
    }

    public Long getUserevaluate() {
        return userevaluate;
    }

    public void setIsanonymity(Long isanonymity) {
        this.isanonymity = isanonymity;
    }

    public Long getIsanonymity() {
        return isanonymity;
    }

    public void setServicetype(String servicetype) {
        this.servicetype = servicetype;
    }

    public String getServicetype() {
        return servicetype;
    }

    public void setSimilarcasesn(String similarcasesn) {
        this.similarcasesn = similarcasesn;
    }

    public String getSimilarcasesn() {
        return similarcasesn;
    }

    public void setApproach(Long approach) {
        this.approach = approach;
    }

    public Long getApproach() {
        return approach;
    }

    public void setUrgentdegree(Long urgentdegree) {
        this.urgentdegree = urgentdegree;
    }

    public Long getUrgentdegree() {
        return urgentdegree;
    }

    public void setPartsn(String partsn) {
        this.partsn = partsn;
    }

    public String getPartsn() {
        return partsn;
    }

    public void setEndnote(String endnote) {
        this.endnote = endnote;
    }

    public String getEndnote() {
        return endnote;
    }

    public void setDispatchnote(String dispatchnote) {
        this.dispatchnote = dispatchnote;
    }

    public String getDispatchnote() {
        return dispatchnote;
    }

    public void setReporter(String reporter) {
        this.reporter = reporter;
    }

    public String getReporter() {
        return reporter;
    }

    public void setInfozcname(String infozcname) {
        this.infozcname = infozcname;
    }

    public String getInfozcname() {
        return infozcname;
    }

    public void setInfoscname(String infoscname) {
        this.infoscname = infoscname;
    }

    public String getInfoscname() {
        return infoscname;
    }

    public void setInfobcname(String infobcname) {
        this.infobcname = infobcname;
    }

    public String getInfobcname() {
        return infobcname;
    }

    public void setInfotypename(String infotypename) {
        this.infotypename = infotypename;
    }

    public String getInfotypename() {
        return infotypename;
    }

    public void setInfosourcename(String infosourcename) {
        this.infosourcename = infosourcename;
    }

    public String getInfosourcename() {
        return infosourcename;
    }

    public void setContactinfo(String contactinfo) {
        this.contactinfo = contactinfo;
    }

    public String getContactinfo() {
        return contactinfo;
    }

    public void setHastentypecount(Long hastentypecount) {
        this.hastentypecount = hastentypecount;
    }

    public Long getHastentypecount() {
        return hastentypecount;
    }

    public void setHasleadtypecount(Long hasleadtypecount) {
        this.hasleadtypecount = hasleadtypecount;
    }

    public Long getHasleadtypecount() {
        return hasleadtypecount;
    }

    public void setHuifangcount(Long huifangcount) {
        this.huifangcount = huifangcount;
    }

    public Long getHuifangcount() {
        return huifangcount;
    }

    public void setHechacount(Long hechacount) {
        this.hechacount = hechacount;
    }

    public Long getHechacount() {
        return hechacount;
    }

    public void setHeshicount(Long heshicount) {
        this.heshicount = heshicount;
    }

    public Long getHeshicount() {
        return heshicount;
    }

    public void setContactmode(String contactmode) {
        this.contactmode = contactmode;
    }

    public String getContactmode() {
        return contactmode;
    }

    public void setCallnumber(String callnumber) {
        this.callnumber = callnumber;
    }

    public String getCallnumber() {
        return callnumber;
    }

    public void setPriorityarea(String priorityarea) {
        this.priorityarea = priorityarea;
    }

    public String getPriorityarea() {
        return priorityarea;
    }

    public void setCheckresult(Long checkresult) {
        this.checkresult = checkresult;
    }

    public Long getCheckresult() {
        return checkresult;
    }

    public void setVerifyresult(Long verifyresult) {
        this.verifyresult = verifyresult;
    }

    public Long getVerifyresult() {
        return verifyresult;
    }

    public void setEndresult(Long endresult) {
        this.endresult = endresult;
    }

    public Long getEndresult() {
        return endresult;
    }

    public void setCaseend(Long caseend) {
        this.caseend = caseend;
    }

    public Long getCaseend() {
        return caseend;
    }

    public void setInsertuser(String insertuser) {
        this.insertuser = insertuser;
    }

    public String getInsertuser() {
        return insertuser;
    }

    public void setKeepersn(String keepersn) {
        this.keepersn = keepersn;
    }

    public String getKeepersn() {
        return keepersn;
    }

    public void setInsertdeptcode(String insertdeptcode) {
        this.insertdeptcode = insertdeptcode;
    }

    public String getInsertdeptcode() {
        return insertdeptcode;
    }

    public void setExecutedeptcode(String executedeptcode) {
        this.executedeptcode = executedeptcode;
    }

    public String getExecutedeptcode() {
        return executedeptcode;
    }

    public void setDeptcode(String deptcode) {
        this.deptcode = deptcode;
    }

    public String getDeptcode() {
        return deptcode;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Long getStatus() {
        return status;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setInfoatcode(String infoatcode) {
        this.infoatcode = infoatcode;
    }

    public String getInfoatcode() {
        return infoatcode;
    }

    public void setInfozccode(String infozccode) {
        this.infozccode = infozccode;
    }

    public String getInfozccode() {
        return infozccode;
    }

    public void setInfosccode(String infosccode) {
        this.infosccode = infosccode;
    }

    public String getInfosccode() {
        return infosccode;
    }

    public void setInfobccode(String infobccode) {
        this.infobccode = infobccode;
    }

    public String getInfobccode() {
        return infobccode;
    }

    public void setInfotypeid(Long infotypeid) {
        this.infotypeid = infotypeid;
    }

    public Long getInfotypeid() {
        return infotypeid;
    }

    public void setInfosourceid(Long infosourceid) {
        this.infosourceid = infosourceid;
    }

    public Long getInfosourceid() {
        return infosourceid;
    }

    public void setCasesn(String casesn) {
        this.casesn = casesn;
    }

    public String getCasesn() {
        return casesn;
    }

    public void setTaskid(String taskid) {
        this.taskid = taskid;
    }

    public String getTaskid() {
        return taskid;
    }

    public String getSubexecutedeptnameMh() {
        return subexecutedeptnameMh;
    }

    public void setSubexecutedeptnameMh(String subexecutedeptnameMh) {
        this.subexecutedeptnameMh = subexecutedeptnameMh;
    }

    public String getImagefilename() {
        return imagefilename;
    }

    public void setImagefilename(String imagefilename) {
        this.imagefilename = imagefilename;
    }


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("isstandard", getIsstandard())
                .append("checkimage", getCheckimage())
                .append("servicetypename", getServicetypename())
                .append("wpType", getWpType())
                .append("isfirstcontact", getIsfirstcontact())
                .append("hyname", getHyname())
                .append("upkeepername", getUpkeepername())
                .append("deptname", getDeptname())
                .append("statusname", getStatusname())
                .append("synctime", getSynctime())
                .append("lastcontacttime", getLastcontacttime())
                .append("accepttime", getAccepttime())
                .append("cancletime", getCancletime())
                .append("lastsolvingtime", getLastsolvingtime())
                .append("importantsolvingtime", getImportantsolvingtime())
                .append("middlesolvingtime", getMiddlesolvingtime())
                .append("allendtime", getAllendtime())
                .append("allimportanttime", getAllimportanttime())
                .append("allmiddletime", getAllmiddletime())
                .append("endtime", getEndtime())
                .append("telasktime", getTelasktime())
                .append("solvingtime", getSolvingtime())
                .append("dispatchtime", getDispatchtime())
                .append("createtime", getCreatetime())
                .append("percreatetime", getPercreatetime())
                .append("discovertime", getDiscovertime())
                .append("executedeptname", getExecutedeptname())
                .append("workgridcode", getWorkgridcode())
                .append("communityname", getCommunityname())
                .append("streetname", getStreetname())
                .append("workgrid", getWorkgrid())
                .append("address", getAddress())
                .append("coordy", getCoordy())
                .append("coordx", getCoordx())
                .append("gridcode", getGridcode())
                .append("communitycode", getCommunitycode())
                .append("streetcode", getStreetcode())
                .append("viewinfo", getViewinfo())
                .append("casevaluation12345", getCasevaluation12345())
                .append("notReason", getNotReason())
                .append("description12345", getDescription12345())
                .append("appealExplain", getAppealExplain())
                .append("banliresult12345", getBanliresult12345())
                .append("wpSource", getWpSource())
                .append("reportdeptname", getReportdeptname())
                .append("hotlinesn", getHotlinesn())
                .append("banliresult", getBanliresult())
                .append("duLimit", getDuLimit())
                .append("urgeCount", getUrgeCount())
                .append("callbackFlag", getCallbackFlag())
                .append("userevaluate", getUserevaluate())
                .append("isanonymity", getIsanonymity())
                .append("servicetype", getServicetype())
                .append("similarcasesn", getSimilarcasesn())
                .append("approach", getApproach())
                .append("urgentdegree", getUrgentdegree())
                .append("partsn", getPartsn())
                .append("endnote", getEndnote())
                .append("dispatchnote", getDispatchnote())
                .append("reporter", getReporter())
                .append("infozcname", getInfozcname())
                .append("infoscname", getInfoscname())
                .append("infobcname", getInfobcname())
                .append("infotypename", getInfotypename())
                .append("infosourcename", getInfosourcename())
                .append("contactinfo", getContactinfo())
                .append("hastentypecount", getHastentypecount())
                .append("hasleadtypecount", getHasleadtypecount())
                .append("huifangcount", getHuifangcount())
                .append("hechacount", getHechacount())
                .append("heshicount", getHeshicount())
                .append("contactmode", getContactmode())
                .append("callnumber", getCallnumber())
                .append("priorityarea", getPriorityarea())
                .append("checkresult", getCheckresult())
                .append("verifyresult", getVerifyresult())
                .append("endresult", getEndresult())
                .append("caseend", getCaseend())
                .append("insertuser", getInsertuser())
                .append("keepersn", getKeepersn())
                .append("insertdeptcode", getInsertdeptcode())
                .append("executedeptcode", getExecutedeptcode())
                .append("deptcode", getDeptcode())
                .append("status", getStatus())
                .append("description", getDescription())
                .append("infoatcode", getInfoatcode())
                .append("infozccode", getInfozccode())
                .append("infosccode", getInfosccode())
                .append("infobccode", getInfobccode())
                .append("infotypeid", getInfotypeid())
                .append("infosourceid", getInfosourceid())
                .append("casesn", getCasesn())
                .append("taskid", getTaskid())
                .append("subexecutedeptnameMh", getSubexecutedeptnameMh())
                .append("imagefilename", getImagefilename())
                .toString();
    }
}
