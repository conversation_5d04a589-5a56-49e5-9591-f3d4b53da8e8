package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthlyExtend;
import com.ruoyi.shcy.service.IShcyDbcenterRxMonthlyExtendService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthly;
import com.ruoyi.shcy.service.IShcyDbcenterRxMonthlyService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 12345热线分析Controller
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@RestController
@RequestMapping("/shcy/dbcenterRxMonthly")
public class ShcyDbcenterRxMonthlyController extends BaseController
{
    @Autowired
    private IShcyDbcenterRxMonthlyService shcyDbcenterRxMonthlyService;

    @Autowired
    private IShcyDbcenterRxMonthlyExtendService shcyDbcenterRxMonthlyExtendService;

    /**
     * 查询12345热线分析列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthly:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyDbcenterRxMonthly shcyDbcenterRxMonthly)
    {
        startPage();
        List<ShcyDbcenterRxMonthly> list = shcyDbcenterRxMonthlyService.selectShcyDbcenterRxMonthlyList(shcyDbcenterRxMonthly);
        return getDataTable(list);
    }

    /**
     * 导出12345热线分析列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthly:export')")
    @Log(title = "12345热线分析", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyDbcenterRxMonthly shcyDbcenterRxMonthly)
    {
        List<ShcyDbcenterRxMonthly> list = shcyDbcenterRxMonthlyService.selectShcyDbcenterRxMonthlyList(shcyDbcenterRxMonthly);
        ExcelUtil<ShcyDbcenterRxMonthly> util = new ExcelUtil<ShcyDbcenterRxMonthly>(ShcyDbcenterRxMonthly.class);
        util.exportExcel(response, list, "12345热线分析数据");
    }

    /**
     * 获取12345热线分析详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthly:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyDbcenterRxMonthlyService.selectShcyDbcenterRxMonthlyById(id));
    }

    /**
     * 新增12345热线分析
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthly:add')")
    @Log(title = "12345热线分析", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyDbcenterRxMonthly shcyDbcenterRxMonthly)
    {
        shcyDbcenterRxMonthly.setCreateBy(getUsername());
        shcyDbcenterRxMonthlyService.insertShcyDbcenterRxMonthly(shcyDbcenterRxMonthly);
        List<ShcyDbcenterRxMonthlyExtend> list=shcyDbcenterRxMonthly.getDbcenterRxMonthlyExtendList();
        list.stream().forEach(item -> {
            item.setCreateBy(getUsername());
            item.setPid(shcyDbcenterRxMonthly.getId());
            shcyDbcenterRxMonthlyExtendService.insertShcyDbcenterRxMonthlyExtend(item);
        });


        return AjaxResult.success();
    }

    /**
     * 修改12345热线分析
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthly:edit')")
    @Log(title = "12345热线分析", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyDbcenterRxMonthly shcyDbcenterRxMonthly)
    {
        shcyDbcenterRxMonthly.setUpdateBy(getUsername());
        List<ShcyDbcenterRxMonthlyExtend> list=shcyDbcenterRxMonthly.getDbcenterRxMonthlyExtendList();
        list.stream().forEach(item -> {
            item.setUpdateBy(getUsername());
            shcyDbcenterRxMonthlyExtendService.updateShcyDbcenterRxMonthlyExtend(item);
        });
        return toAjax(shcyDbcenterRxMonthlyService.updateShcyDbcenterRxMonthly(shcyDbcenterRxMonthly));
    }

    /**
     * 删除12345热线分析
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthly:remove')")
    @Log(title = "12345热线分析", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyDbcenterRxMonthlyService.deleteShcyDbcenterRxMonthlyByIds(ids));
    }
}
