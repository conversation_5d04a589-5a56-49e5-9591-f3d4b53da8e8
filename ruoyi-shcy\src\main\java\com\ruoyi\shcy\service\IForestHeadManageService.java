package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.ForestHeadManage;

/**
 * 林长管理Service接口
 * 
 * <AUTHOR>
 * @date 2022-08-05
 */
public interface IForestHeadManageService 
{
    /**
     * 查询林长管理
     * 
     * @param id 林长管理主键
     * @return 林长管理
     */
    public ForestHeadManage selectForestHeadManageById(Long id);

    /**
     * 查询林长管理列表
     * 
     * @param forestHeadManage 林长管理
     * @return 林长管理集合
     */
    public List<ForestHeadManage> selectForestHeadManageList(ForestHeadManage forestHeadManage);

    /**
     * 新增林长管理
     * 
     * @param forestHeadManage 林长管理
     * @return 结果
     */
    public int insertForestHeadManage(ForestHeadManage forestHeadManage);

    /**
     * 修改林长管理
     * 
     * @param forestHeadManage 林长管理
     * @return 结果
     */
    public int updateForestHeadManage(ForestHeadManage forestHeadManage);

    /**
     * 批量删除林长管理
     * 
     * @param ids 需要删除的林长管理主键集合
     * @return 结果
     */
    public int deleteForestHeadManageByIds(Long[] ids);

    /**
     * 删除林长管理信息
     * 
     * @param id 林长管理主键
     * @return 结果
     */
    public int deleteForestHeadManageById(Long id);
}
