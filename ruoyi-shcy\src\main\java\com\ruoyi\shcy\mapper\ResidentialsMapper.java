package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.Residentials;

/**
 * 小区信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface ResidentialsMapper 
{
    /**
     * 查询小区信息
     * 
     * @param id 小区信息主键
     * @return 小区信息
     */
    public Residentials selectResidentialsById(Long id);

    /**
     * 查询小区信息列表
     * 
     * @param residentials 小区信息
     * @return 小区信息集合
     */
    public List<Residentials> selectResidentialsList(Residentials residentials);

    /**
     * 新增小区信息
     * 
     * @param residentials 小区信息
     * @return 结果
     */
    public int insertResidentials(Residentials residentials);

    /**
     * 修改小区信息
     * 
     * @param residentials 小区信息
     * @return 结果
     */
    public int updateResidentials(Residentials residentials);

    /**
     * 删除小区信息
     * 
     * @param id 小区信息主键
     * @return 结果
     */
    public int deleteResidentialsById(Long id);

    /**
     * 批量删除小区信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteResidentialsByIds(Long[] ids);
}
