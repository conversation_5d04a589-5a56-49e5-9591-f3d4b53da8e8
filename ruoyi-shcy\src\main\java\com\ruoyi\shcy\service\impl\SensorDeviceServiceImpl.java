package com.ruoyi.shcy.service.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.SensorDevice;
import com.ruoyi.shcy.mapper.SensorDeviceMapper;
import com.ruoyi.shcy.service.ISensorDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 传感器设备Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
@Service
public class SensorDeviceServiceImpl implements ISensorDeviceService 
{
    @Autowired
    private SensorDeviceMapper sensorDeviceMapper;

    /**
     * 查询传感器设备
     * 
     * @param id 传感器设备主键
     * @return 传感器设备
     */
    @Override
    public SensorDevice selectSensorDeviceById(Long id)
    {
        return sensorDeviceMapper.selectSensorDeviceById(id);
    }

    /**
     * 查询传感器设备列表
     * 
     * @param sensorDevice 传感器设备
     * @return 传感器设备
     */
    @Override
    public List<SensorDevice> selectSensorDeviceList(SensorDevice sensorDevice)
    {
        return sensorDeviceMapper.selectSensorDeviceList(sensorDevice);
    }

    /**
     * 新增传感器设备
     * 
     * @param sensorDevice 传感器设备
     * @return 结果
     */
    @Override
    public int insertSensorDevice(SensorDevice sensorDevice)
    {
        return sensorDeviceMapper.insertSensorDevice(sensorDevice);
    }

    /**
     * 修改传感器设备
     * 
     * @param sensorDevice 传感器设备
     * @return 结果
     */
    @Override
    public int updateSensorDevice(SensorDevice sensorDevice)
    {
        sensorDevice.setUpdateTime(DateUtils.getNowDate());
        return sensorDeviceMapper.updateSensorDevice(sensorDevice);
    }

    /**
     * 批量删除传感器设备
     * 
     * @param ids 需要删除的传感器设备主键
     * @return 结果
     */
    @Override
    public int deleteSensorDeviceByIds(Long[] ids)
    {
        return sensorDeviceMapper.deleteSensorDeviceByIds(ids);
    }

    /**
     * 删除传感器设备信息
     * 
     * @param id 传感器设备主键
     * @return 结果
     */
    @Override
    public int deleteSensorDeviceById(Long id)
    {
        return sensorDeviceMapper.deleteSensorDeviceById(id);
    }

    @Override
    public SensorDevice selectSensorDeviceByImei(String imei) {
        return sensorDeviceMapper.selectSensorDeviceByImei(imei);
    }
}
