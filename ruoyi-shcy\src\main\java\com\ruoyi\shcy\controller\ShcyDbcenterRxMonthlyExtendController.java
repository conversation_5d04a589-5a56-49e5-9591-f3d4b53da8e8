package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthlyExtend;
import com.ruoyi.shcy.service.IShcyDbcenterRxMonthlyExtendService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 12345热线分析拓展Controller
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@RestController
@RequestMapping("/shcy/dbcenterRxMonthlyExtend")
public class ShcyDbcenterRxMonthlyExtendController extends BaseController
{
    @Autowired
    private IShcyDbcenterRxMonthlyExtendService shcyDbcenterRxMonthlyExtendService;

    /**
     * 查询12345热线分析拓展列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthlyExtend:list')")
    @GetMapping("/list")
    public TableDataInfo list(ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend)
    {
        startPage();
        List<ShcyDbcenterRxMonthlyExtend> list = shcyDbcenterRxMonthlyExtendService.selectShcyDbcenterRxMonthlyExtendList(shcyDbcenterRxMonthlyExtend);
        return getDataTable(list);
    }

    /**
     * 导出12345热线分析拓展列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthlyExtend:export')")
    @Log(title = "12345热线分析拓展", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend)
    {
        List<ShcyDbcenterRxMonthlyExtend> list = shcyDbcenterRxMonthlyExtendService.selectShcyDbcenterRxMonthlyExtendList(shcyDbcenterRxMonthlyExtend);
        ExcelUtil<ShcyDbcenterRxMonthlyExtend> util = new ExcelUtil<ShcyDbcenterRxMonthlyExtend>(ShcyDbcenterRxMonthlyExtend.class);
        util.exportExcel(response, list, "12345热线分析拓展数据");
    }

    /**
     * 获取12345热线分析拓展详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthlyExtend:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyDbcenterRxMonthlyExtendService.selectShcyDbcenterRxMonthlyExtendById(id));
    }

    @GetMapping(value = "/pid/{pid}")
    public AjaxResult getInfoByPid(@PathVariable("pid") Long pid)
    {
        ShcyDbcenterRxMonthlyExtend theI=new ShcyDbcenterRxMonthlyExtend();
        theI.setPid(pid);
        List<ShcyDbcenterRxMonthlyExtend> list=shcyDbcenterRxMonthlyExtendService.selectShcyDbcenterRxMonthlyExtendList(theI);
        return AjaxResult.success(list);
    }


    /**
     * 新增12345热线分析拓展
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthlyExtend:add')")
    @Log(title = "12345热线分析拓展", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend)
    {
        return toAjax(shcyDbcenterRxMonthlyExtendService.insertShcyDbcenterRxMonthlyExtend(shcyDbcenterRxMonthlyExtend));
    }

    /**
     * 修改12345热线分析拓展
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthlyExtend:edit')")
    @Log(title = "12345热线分析拓展", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ShcyDbcenterRxMonthlyExtend shcyDbcenterRxMonthlyExtend)
    {
        return toAjax(shcyDbcenterRxMonthlyExtendService.updateShcyDbcenterRxMonthlyExtend(shcyDbcenterRxMonthlyExtend));
    }

    /**
     * 删除12345热线分析拓展
     */
    @PreAuthorize("@ss.hasPermi('shcy:dbcenterRxMonthlyExtend:remove')")
    @Log(title = "12345热线分析拓展", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(shcyDbcenterRxMonthlyExtendService.deleteShcyDbcenterRxMonthlyExtendByIds(ids));
    }
}
