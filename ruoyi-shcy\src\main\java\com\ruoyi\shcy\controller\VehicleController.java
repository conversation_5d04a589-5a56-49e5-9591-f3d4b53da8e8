package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.Vehicle;
import com.ruoyi.shcy.service.IVehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 过车记录Controller
 * 
 * <AUTHOR>
 * @date 2024-08-01
 */
@RestController
@RequestMapping("/shcy/vehicle")
public class VehicleController extends BaseController
{
    @Autowired
    private IVehicleService vehicleService;

    /**
     * 查询过车记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:vehicle:list')")
    @GetMapping("/list")
    public TableDataInfo list(Vehicle vehicle)
    {
        startPage();
        List<Vehicle> list = vehicleService.selectVehicleList(vehicle);
        return getDataTable(list);
    }

    /**
     * 导出过车记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:vehicle:export')")
    @Log(title = "过车记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Vehicle vehicle)
    {
        List<Vehicle> list = vehicleService.selectVehicleList(vehicle);
        ExcelUtil<Vehicle> util = new ExcelUtil<Vehicle>(Vehicle.class);
        util.exportExcel(response, list, "过车记录数据");
    }

    /**
     * 获取过车记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:vehicle:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(vehicleService.selectVehicleById(id));
    }

    /**
     * 新增过车记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:vehicle:add')")
    @Log(title = "过车记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Vehicle vehicle)
    {
        return toAjax(vehicleService.insertVehicle(vehicle));
    }

    /**
     * 修改过车记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:vehicle:edit')")
    @Log(title = "过车记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Vehicle vehicle)
    {
        return toAjax(vehicleService.updateVehicle(vehicle));
    }

    /**
     * 删除过车记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:vehicle:remove')")
    @Log(title = "过车记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(vehicleService.deleteVehicleByIds(ids));
    }
}
