package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.Hotel;
import com.ruoyi.shcy.service.IHotelService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 宾旅馆信息Controller
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@RestController
@RequestMapping("/shcy/hotel")
public class HotelController extends BaseController
{
    @Autowired
    private IHotelService hotelService;

    /**
     * 查询宾旅馆信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hotel:list')")
    @GetMapping("/list")
    public TableDataInfo list(Hotel hotel)
    {
        startPage();
        List<Hotel> list = hotelService.selectHotelList(hotel);
        return getDataTable(list);
    }

    /**
     * 导出宾旅馆信息列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:hotel:export')")
    @Log(title = "宾旅馆信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Hotel hotel)
    {
        List<Hotel> list = hotelService.selectHotelList(hotel);
        ExcelUtil<Hotel> util = new ExcelUtil<Hotel>(Hotel.class);
        util.exportExcel(response, list, "宾旅馆信息数据");
    }

    /**
     * 获取宾旅馆信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hotel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(hotelService.selectHotelById(id));
    }

    /**
     * 新增宾旅馆信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hotel:add')")
    @Log(title = "宾旅馆信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Hotel hotel)
    {
        return toAjax(hotelService.insertHotel(hotel));
    }

    /**
     * 修改宾旅馆信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hotel:edit')")
    @Log(title = "宾旅馆信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Hotel hotel)
    {
        return toAjax(hotelService.updateHotel(hotel));
    }

    /**
     * 删除宾旅馆信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:hotel:remove')")
    @Log(title = "宾旅馆信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(hotelService.deleteHotelByIds(ids));
    }
}
