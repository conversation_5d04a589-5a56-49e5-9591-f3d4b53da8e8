package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ApartmentRentalMapper;
import com.ruoyi.shcy.domain.ApartmentRental;
import com.ruoyi.shcy.service.IApartmentRentalService;

/**
 * 公寓租赁房Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
@Service
public class ApartmentRentalServiceImpl implements IApartmentRentalService 
{
    @Autowired
    private ApartmentRentalMapper apartmentRentalMapper;

    /**
     * 查询公寓租赁房
     * 
     * @param id 公寓租赁房主键
     * @return 公寓租赁房
     */
    @Override
    public ApartmentRental selectApartmentRentalById(Long id)
    {
        return apartmentRentalMapper.selectApartmentRentalById(id);
    }

    /**
     * 查询公寓租赁房列表
     * 
     * @param apartmentRental 公寓租赁房
     * @return 公寓租赁房
     */
    @Override
    public List<ApartmentRental> selectApartmentRentalList(ApartmentRental apartmentRental)
    {
        return apartmentRentalMapper.selectApartmentRentalList(apartmentRental);
    }

    /**
     * 新增公寓租赁房
     * 
     * @param apartmentRental 公寓租赁房
     * @return 结果
     */
    @Override
    public int insertApartmentRental(ApartmentRental apartmentRental)
    {
        apartmentRental.setCreateTime(DateUtils.getNowDate());
        return apartmentRentalMapper.insertApartmentRental(apartmentRental);
    }

    /**
     * 修改公寓租赁房
     * 
     * @param apartmentRental 公寓租赁房
     * @return 结果
     */
    @Override
    public int updateApartmentRental(ApartmentRental apartmentRental)
    {
        apartmentRental.setUpdateTime(DateUtils.getNowDate());
        return apartmentRentalMapper.updateApartmentRental(apartmentRental);
    }

    /**
     * 批量删除公寓租赁房
     * 
     * @param ids 需要删除的公寓租赁房主键
     * @return 结果
     */
    @Override
    public int deleteApartmentRentalByIds(Long[] ids)
    {
        return apartmentRentalMapper.deleteApartmentRentalByIds(ids);
    }

    /**
     * 删除公寓租赁房信息
     * 
     * @param id 公寓租赁房主键
     * @return 结果
     */
    @Override
    public int deleteApartmentRentalById(Long id)
    {
        return apartmentRentalMapper.deleteApartmentRentalById(id);
    }
}
