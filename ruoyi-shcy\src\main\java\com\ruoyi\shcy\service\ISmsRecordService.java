package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.SmsRecord;

/**
 * 短信发送记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
public interface ISmsRecordService 
{
    /**
     * 查询短信发送记录
     * 
     * @param smsId 短信发送记录主键
     * @return 短信发送记录
     */
    public SmsRecord selectSmsRecordBySmsId(Long smsId);

    /**
     * 查询短信发送记录列表
     * 
     * @param smsRecord 短信发送记录
     * @return 短信发送记录集合
     */
    public List<SmsRecord> selectSmsRecordList(SmsRecord smsRecord);

    /**
     * 新增短信发送记录
     * 
     * @param smsRecord 短信发送记录
     * @return 结果
     */
    public int insertSmsRecord(SmsRecord smsRecord);

    /**
     * 修改短信发送记录
     * 
     * @param smsRecord 短信发送记录
     * @return 结果
     */
    public int updateSmsRecord(SmsRecord smsRecord);

    /**
     * 批量删除短信发送记录
     * 
     * @param smsIds 需要删除的短信发送记录主键集合
     * @return 结果
     */
    public int deleteSmsRecordBySmsIds(Long[] smsIds);

    /**
     * 删除短信发送记录信息
     * 
     * @param smsId 短信发送记录主键
     * @return 结果
     */
    public int deleteSmsRecordBySmsId(Long smsId);
}
