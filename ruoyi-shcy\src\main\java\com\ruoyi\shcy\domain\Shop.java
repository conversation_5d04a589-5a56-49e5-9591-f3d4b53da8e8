package com.ruoyi.shcy.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 店铺信息对象 shcy_shop
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
public class Shop extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 店铺名称
     */
    @Excel(name = "店铺名称")
    private String shopName;

    /**
     * 店铺地址
     */
    @Excel(name = "店铺地址")
    private String shopAddress;

    /**
     * 营业执照名称
     */
    @Excel(name = "营业执照名称")
    private String shopLicense;

    /**
     * 统一信用社会代码
     */
    @Excel(name = "统一信用社会代码")
    private String shopCreditCode;

    /**
     * 注册地址
     */
    @Excel(name = "注册地址")
    private String shopRegisterAddress;

    /**
     * 经营地址
     */
    @Excel(name = "经营地址")
    private String shopOperatingAddress;

    /**
     * 所属居委
     */
    @Excel(name = "所属居委")
    private String shopCommittee;

    /**
     * 行业类别
     */
    @Excel(name = "行业大类")
    private String shopCategory;

    /**
     * 行业
     */
    @Excel(name = "行业中类")
    private String shopSubcategory;

    /**
     * 行业小类
     */
    @Excel(name = "行业小类")
    private String shopLittlecategory;

    /**
     * 商铺联系人
     */
    @Excel(name = "商铺联系人")
    private String shopContract;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    private String shopContactPhone;

    /**
     * 房屋权属
     */
    @Excel(name = "房屋权属")
    private String shopHouseOwnership;

    /**
     * 是否国有资产
     */
    @Excel(name = "是否国有资产")
    private String isStateAssets;

    /**
     * 房东联系人
     */
    @Excel(name = "房东联系人")
    private String shopLandlordContract;

    /**
     * 房东联系电话
     */
    @Excel(name = "房东联系电话")
    private String shoplAndlordPhone;

    /**
     * 管辖物业
     */
    @Excel(name = "管辖物业")
    private String shopGoverningProperty;

    /**
     * 是否与小区物业一致
     */
    @Excel(name = "是否与小区物业一致")
    private String isSameCommunityProperty;

    /**
     * 物业联系人
     */
    @Excel(name = "物业联系人")
    private String shopPropertyContact;

    /**
     * 物业联系电话
     */
    @Excel(name = "物业联系电话")
    private String shopPropertyPhone;

    /**
     * 是否有场所码
     */
    @Excel(name = "是否有场所码")
    private String isSiteCode;

    /**
     * 从业人员
     */
    @Excel(name = "从业人员")
    private String shopEmployeeNum;

    /**
     * 巡查状态
     */
    @Excel(name = "巡查状态")
    private String shopStatus;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String shopRemark;

    /**
     * 居委会id
     */
    @Excel(name = "居委会id")
    private Long shopDeparmentId;
    /**
     * 是否开业
     */
    @Excel(name = "是否开业")
    private String isOpen;


    /**
     * 单双号
     */
    @Excel(name = "单双号")
    private String oddNumber;

    /**
     * 商铺区域
     */
    @Excel(name = "商铺区域")
    private String shopArea;

    /**
     * 监管类型
     */
    @Excel(name = "监管类型")
    private String supervisionStatus;

    /**
     * 经度
     */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /**
     * 通告类型
     */
    @Excel(name = "通告类型")
    private String noticeType;

    /**
     * 居委会下店铺数量
     */
    private String shopNum;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 坐标
     */
    @Excel(name = "坐标")
    private String coordinate;

    /**
     * 是否住店
     */
    @Excel(name = "是否住店")
    private String isAccommodation;

    /**
     * 法人
     */
    @Excel(name = "法人")
    private String legalPerson;

    /**
     * 法人联系电话
     */
    @Excel(name = "法人联系电话")
    private String legalPersonContact;

    /**
     * 所在路段
     */
    @Excel(name = "所在路段")
    private String roadSection;

    /**
     * 路段起
     */
    @Excel(name = "路段起")
    private String roadSectionStart;

    /**
     * 路段止
     */
    @Excel(name = "路段止")
    private String roadSectionEnd;


    /**
     * 路段搜索标志
     */
    private String roadFlag;


    /**
     * 信息完善状态
     */
    @Excel(name = "信息完善状态")
    private String informationStatus;

    /**
     * 街道街长
     */
    @Excel(name = "街道街长")
    private String streetChief;

    /**
     * 街道街长电话
     */
    @Excel(name = "街道街长电话")
    private String chiefPhone;

    /**
     * 社区街长
     */
    private String communityStreetChief;

    /**
     * 社区街长电话
     */
    private String communityStreetChiefPhone;

    /**
     * 商户街长
     */
    private String merchantStreetChief;

    /**
     * 商户街长电话
     */
    private String merchantStreetChiefPhone;

    /**
     * 包保制责任人
     */
    @Excel(name = "包保制责任人")
    private String insuranceLiabilityContact;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式")
    private String insuranceLiabilityPhone;

    /**
     * 包保制flag
     */
    @Excel(name = "包保制flag")
    private String insuranceLiabilityFlag;

    /**
     * 街长路段
     **/
    @Excel(name = "街长路段")
    private String chiefRoad;

    /**
     * 是否排查到房屋信息
     */
    private String houseInvestigated;

    /**
     * 是否排查到物业信息
     */
    private String propertyInvestigated;


    // 一些参数

    // 分类
    private String category;


    // 居委会id
    private Long deptId;


    // 巡查时间
    private Date checkDate;

    private String checkUpdateByCg;

    private String dianmianPhoto;
    private String yinyezhizhaoPhoto;
    private String jingyingxukezhengPhoto;

    public String getDianmianPhoto() {
        return dianmianPhoto;
    }

    public void setDianmianPhoto(String dianmianPhoto) {
        this.dianmianPhoto = dianmianPhoto;
    }

    public String getYinyezhizhaoPhoto() {
        return yinyezhizhaoPhoto;
    }

    public void setYinyezhizhaoPhoto(String yinyezhizhaoPhoto) {
        this.yinyezhizhaoPhoto = yinyezhizhaoPhoto;
    }

    public String getJingyingxukezhengPhoto() {
        return jingyingxukezhengPhoto;
    }

    public void setJingyingxukezhengPhoto(String jingyingxukezhengPhoto) {
        this.jingyingxukezhengPhoto = jingyingxukezhengPhoto;
    }

    public String getChiefRoad() {
        return chiefRoad;
    }

    public void setChiefRoad(String chiefRoad) {
        this.chiefRoad = chiefRoad;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }

    public String getInsuranceLiabilityContact() {
        return insuranceLiabilityContact;
    }

    public void setInsuranceLiabilityContact(String insuranceLiabilityContact) {
        this.insuranceLiabilityContact = insuranceLiabilityContact;
    }

    public String getInsuranceLiabilityPhone() {
        return insuranceLiabilityPhone;
    }

    public void setInsuranceLiabilityPhone(String insuranceLiabilityPhone) {
        this.insuranceLiabilityPhone = insuranceLiabilityPhone;
    }

    public String getInsuranceLiabilityFlag() {
        return insuranceLiabilityFlag;
    }

    public void setInsuranceLiabilityFlag(String insuranceLiabilityFlag) {
        this.insuranceLiabilityFlag = insuranceLiabilityFlag;
    }

    public String getStreetChief() {
        return streetChief;
    }

    public void setStreetChief(String streetChief) {
        this.streetChief = streetChief;
    }

    public String getChiefPhone() {
        return chiefPhone;
    }

    public void setChiefPhone(String chiefPhone) {
        this.chiefPhone = chiefPhone;
    }

    public String getCommunityStreetChief() {
        return communityStreetChief;
    }

    public void setCommunityStreetChief(String communityStreetChief) {
        this.communityStreetChief = communityStreetChief;
    }

    public String getCommunityStreetChiefPhone() {
        return communityStreetChiefPhone;
    }

    public void setCommunityStreetChiefPhone(String communityStreetChiefPhone) {
        this.communityStreetChiefPhone = communityStreetChiefPhone;
    }

    public String getMerchantStreetChief() {
        return merchantStreetChief;
    }

    public void setMerchantStreetChief(String merchantStreetChief) {
        this.merchantStreetChief = merchantStreetChief;
    }

    public String getMerchantStreetChiefPhone() {
        return merchantStreetChiefPhone;
    }

    public void setMerchantStreetChiefPhone(String merchantStreetChiefPhone) {
        this.merchantStreetChiefPhone = merchantStreetChiefPhone;
    }

    public String getRoadFlag() {
        return roadFlag;
    }

    public void setRoadFlag(String roadFlag) {
        this.roadFlag = roadFlag;
    }

    public String getIsAccommodation() {
        return isAccommodation;
    }

    public void setIsAccommodation(String isAccommodation) {
        this.isAccommodation = isAccommodation;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getLegalPersonContact() {
        return legalPersonContact;
    }

    public void setLegalPersonContact(String legalPersonContact) {
        this.legalPersonContact = legalPersonContact;
    }

    public String getRoadSection() {
        return roadSection;
    }

    public void setRoadSection(String roadSection) {
        this.roadSection = roadSection;
    }

    public String getRoadSectionStart() {
        return roadSectionStart;
    }

    public void setRoadSectionStart(String roadSectionStart) {
        this.roadSectionStart = roadSectionStart;
    }

    public String getRoadSectionEnd() {
        return roadSectionEnd;
    }

    public void setRoadSectionEnd(String roadSectionEnd) {
        this.roadSectionEnd = roadSectionEnd;
    }

    public String getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(String noticeType) {
        this.noticeType = noticeType;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public String getSupervisionStatus() {
        return supervisionStatus;
    }

    public void setSupervisionStatus(String supervisionStatus) {
        this.supervisionStatus = supervisionStatus;
    }

    public String getShopNum() {
        return shopNum;
    }

    public void setShopNum(String shopNum) {
        this.shopNum = shopNum;
    }

    public String getShopArea() {
        return shopArea;
    }

    public void setShopArea(String shopArea) {
        this.shopArea = shopArea;
    }

    public String getShopLittlecategory() {
        return shopLittlecategory;
    }

    public void setShopLittlecategory(String shopLittlecategory) {
        this.shopLittlecategory = shopLittlecategory;
    }

    public String getIsOpen() {
        return isOpen;
    }

    public void setIsOpen(String isOpen) {
        this.isOpen = isOpen;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopAddress(String shopAddress) {
        this.shopAddress = shopAddress;
    }

    public String getShopAddress() {
        return shopAddress;
    }

    public void setShopLicense(String shopLicense) {
        this.shopLicense = shopLicense;
    }

    public String getShopLicense() {
        return shopLicense;
    }

    public void setShopCreditCode(String shopCreditCode) {
        this.shopCreditCode = shopCreditCode;
    }

    public String getShopCreditCode() {
        return shopCreditCode;
    }

    public void setShopRegisterAddress(String shopRegisterAddress) {
        this.shopRegisterAddress = shopRegisterAddress;
    }

    public String getShopRegisterAddress() {
        return shopRegisterAddress;
    }

    public void setShopOperatingAddress(String shopOperatingAddress) {
        this.shopOperatingAddress = shopOperatingAddress;
    }

    public String getShopOperatingAddress() {
        return shopOperatingAddress;
    }

    public void setShopCommittee(String shopCommittee) {
        this.shopCommittee = shopCommittee;
    }

    public String getShopCommittee() {
        return shopCommittee;
    }

    public void setShopCategory(String shopCategory) {
        this.shopCategory = shopCategory;
    }

    public String getShopCategory() {
        return shopCategory;
    }

    public void setShopSubcategory(String shopSubcategory) {
        this.shopSubcategory = shopSubcategory;
    }

    public String getShopSubcategory() {
        return shopSubcategory;
    }

    public void setShopContract(String shopContract) {
        this.shopContract = shopContract;
    }

    public String getShopContract() {
        return shopContract;
    }

    public void setShopContactPhone(String shopContactPhone) {
        this.shopContactPhone = shopContactPhone;
    }

    public String getShopContactPhone() {
        return shopContactPhone;
    }

    public void setShopHouseOwnership(String shopHouseOwnership) {
        this.shopHouseOwnership = shopHouseOwnership;
    }

    public String getShopHouseOwnership() {
        return shopHouseOwnership;
    }

    public void setIsStateAssets(String isStateAssets) {
        this.isStateAssets = isStateAssets;
    }

    public String getIsStateAssets() {
        return isStateAssets;
    }

    public void setShopLandlordContract(String shopLandlordContract) {
        this.shopLandlordContract = shopLandlordContract;
    }

    public String getShopLandlordContract() {
        return shopLandlordContract;
    }

    public void setShoplAndlordPhone(String shoplAndlordPhone) {
        this.shoplAndlordPhone = shoplAndlordPhone;
    }

    public String getShoplAndlordPhone() {
        return shoplAndlordPhone;
    }

    public void setShopGoverningProperty(String shopGoverningProperty) {
        this.shopGoverningProperty = shopGoverningProperty;
    }

    public String getShopGoverningProperty() {
        return shopGoverningProperty;
    }

    public void setIsSameCommunityProperty(String isSameCommunityProperty) {
        this.isSameCommunityProperty = isSameCommunityProperty;
    }

    public String getIsSameCommunityProperty() {
        return isSameCommunityProperty;
    }

    public void setShopPropertyContact(String shopPropertyContact) {
        this.shopPropertyContact = shopPropertyContact;
    }

    public String getShopPropertyContact() {
        return shopPropertyContact;
    }

    public void setShopPropertyPhone(String shopPropertyPhone) {
        this.shopPropertyPhone = shopPropertyPhone;
    }

    public String getShopPropertyPhone() {
        return shopPropertyPhone;
    }

    public void setIsSiteCode(String isSiteCode) {
        this.isSiteCode = isSiteCode;
    }

    public String getIsSiteCode() {
        return isSiteCode;
    }

    public void setShopEmployeeNum(String shopEmployeeNum) {
        this.shopEmployeeNum = shopEmployeeNum;
    }

    public String getShopEmployeeNum() {
        return shopEmployeeNum;
    }

    public void setShopStatus(String shopStatus) {
        this.shopStatus = shopStatus;
    }

    public String getShopStatus() {
        return shopStatus;
    }

    public void setShopRemark(String shopRemark) {
        this.shopRemark = shopRemark;
    }

    public String getShopRemark() {
        return shopRemark;
    }

    public void setShopDeparmentId(Long shopDeparmentId) {
        this.shopDeparmentId = shopDeparmentId;
    }

    public Long getShopDeparmentId() {
        return shopDeparmentId;
    }

    public String getOddNumber() {
        return oddNumber;
    }

    public void setOddNumber(String oddNumber) {
        this.oddNumber = oddNumber;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getInformationStatus() {
        return informationStatus;
    }

    public void setInformationStatus(String informationStatus) {
        this.informationStatus = informationStatus;
    }

    public String getCheckUpdateByCg() {
        return checkUpdateByCg;
    }

    public void setCheckUpdateByCg(String checkUpdateByCg) {
        this.checkUpdateByCg = checkUpdateByCg;
    }

    public String getHouseInvestigated() {
        return houseInvestigated;
    }

    public void setHouseInvestigated(String houseInvestigated) {
        this.houseInvestigated = houseInvestigated;
    }

    public String getPropertyInvestigated() {
        return propertyInvestigated;
    }

    public void setPropertyInvestigated(String propertyInvestigated) {
        this.propertyInvestigated = propertyInvestigated;
    }

    @Override
    public String toString() {
        return "Shop{" +
                "id=" + id +
                ", shopName='" + shopName + '\'' +
                ", shopAddress='" + shopAddress + '\'' +
                ", shopLicense='" + shopLicense + '\'' +
                ", shopCreditCode='" + shopCreditCode + '\'' +
                ", shopRegisterAddress='" + shopRegisterAddress + '\'' +
                ", shopOperatingAddress='" + shopOperatingAddress + '\'' +
                ", shopCommittee='" + shopCommittee + '\'' +
                ", shopCategory='" + shopCategory + '\'' +
                ", shopSubcategory='" + shopSubcategory + '\'' +
                ", shopLittlecategory='" + shopLittlecategory + '\'' +
                ", shopContract='" + shopContract + '\'' +
                ", shopContactPhone='" + shopContactPhone + '\'' +
                ", shopHouseOwnership='" + shopHouseOwnership + '\'' +
                ", isStateAssets='" + isStateAssets + '\'' +
                ", shopLandlordContract='" + shopLandlordContract + '\'' +
                ", shoplAndlordPhone='" + shoplAndlordPhone + '\'' +
                ", shopGoverningProperty='" + shopGoverningProperty + '\'' +
                ", isSameCommunityProperty='" + isSameCommunityProperty + '\'' +
                ", shopPropertyContact='" + shopPropertyContact + '\'' +
                ", shopPropertyPhone='" + shopPropertyPhone + '\'' +
                ", isSiteCode='" + isSiteCode + '\'' +
                ", shopEmployeeNum='" + shopEmployeeNum + '\'' +
                ", shopStatus='" + shopStatus + '\'' +
                ", shopRemark='" + shopRemark + '\'' +
                ", shopDeparmentId=" + shopDeparmentId +
                ", isOpen='" + isOpen + '\'' +
                ", oddNumber='" + oddNumber + '\'' +
                ", shopArea='" + shopArea + '\'' +
                ", supervisionStatus='" + supervisionStatus + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", noticeType='" + noticeType + '\'' +
                ", shopNum='" + shopNum + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", isAccommodation='" + isAccommodation + '\'' +
                ", legalPerson='" + legalPerson + '\'' +
                ", legalPersonContact='" + legalPersonContact + '\'' +
                ", roadSection='" + roadSection + '\'' +
                ", roadSectionStart='" + roadSectionStart + '\'' +
                ", roadSectionEnd='" + roadSectionEnd + '\'' +
                ", roadFlag='" + roadFlag + '\'' +
                ", informationStatus='" + informationStatus + '\'' +
                ", streetChief='" + streetChief + '\'' +
                ", chiefPhone='" + chiefPhone + '\'' +
                ", communityStreetChief='" + communityStreetChief + '\'' +
                ", communityStreetChiefPhone='" + communityStreetChiefPhone + '\'' +
                ", merchantStreetChief='" + merchantStreetChief + '\'' +
                ", merchantStreetChiefPhone='" + merchantStreetChiefPhone + '\'' +
                ", insuranceLiabilityContact='" + insuranceLiabilityContact + '\'' +
                ", insuranceLiabilityPhone='" + insuranceLiabilityPhone + '\'' +
                ", insuranceLiabilityFlag='" + insuranceLiabilityFlag + '\'' +
                ", chiefRoad='" + chiefRoad + '\'' +
                ", category='" + category + '\'' +
                ", deptId=" + deptId +
                ", checkDate=" + checkDate +
                ", checkUpdateByCg='" + checkUpdateByCg + '\'' +
                ", houseInvestigated='" + houseInvestigated + '\'' +
                ", propertyInvestigated='" + propertyInvestigated + '\'' +
                '}';
    }
}
