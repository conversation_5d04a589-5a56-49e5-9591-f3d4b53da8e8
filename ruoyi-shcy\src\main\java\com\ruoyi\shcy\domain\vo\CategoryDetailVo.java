package com.ruoyi.shcy.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/11/8 15:24
 * @Version 1.0
 */

//各个行业巡查商铺数详细信息
public class CategoryDetailVo  extends BaseEntity {

    /***行业名**/
    @Excel(name="行业名")
    private  String category;

    /***各个行业下的店铺总数**/
    @Excel(name="各个行业下的店铺总数")
    private String categoryCheckShopTotal;

    /**各个行业巡查商铺的数量 */
    @Excel(name="各个行业巡查商铺的数量")
    private String categoryCheckedShopNum;

    /** 各个行业巡查商铺的比例*/
    @Excel(name="各个行业巡查商铺的比例")
    private String categoryCheckedRatio;




    /**巡查日期*/
    @Excel(name = "巡查日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date curDate;


    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryCheckShopTotal() {
        return categoryCheckShopTotal;
    }

    public void setCategoryCheckShopTotal(String categoryCheckShopTotal) {
        this.categoryCheckShopTotal = categoryCheckShopTotal;
    }

    public String getCategoryCheckedShopNum() {
        return categoryCheckedShopNum;
    }

    public void setCategoryCheckedShopNum(String categoryCheckedShopNum) {
        this.categoryCheckedShopNum = categoryCheckedShopNum;
    }

    public String getCategoryCheckedRatio() {
        return categoryCheckedRatio;
    }

    public void setCategoryCheckedRatio(String categoryCheckedRatio) {
        this.categoryCheckedRatio = categoryCheckedRatio;
    }

    public Date getCurDate() {
        return curDate;
    }

    public void setCurDate(Date curDate) {
        this.curDate = curDate;
    }
}
