package com.ruoyi.icc.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 车辆卡口-过车记录
 *
 * <AUTHOR>
 * @date 2024/01/30
 */
@Data
@NoArgsConstructor
public class CarRecordDTO {

    private Long id;
    private String devId;
    private int devChnnum;
    private String devName;
    /**
     * 抓拍地点
     */
    private String devChnname;
    /**
     * 车牌号码
     */
    private String carNum;
    private int carNumtype;
    private String carNumTypeStr;
    private int carNumcolor;
    private String carNumcolorStr;
    private int carSpeed;
    private int carType;

    /**
     * 车辆类型
     */
    private String carTypeStr;
    private int carColor;
    private String carColorStr;
    private int carLength;
    private String carDirect;
    private String carWayCode;
    private String carWayCodeStr;
    private int capTime;
    private String capTimeStr;
    private long capDate;
    /**
     * 抓拍时间
     */
    private String capDateStr;
    private String infNote;
    private Integer maxSpeed;
    private Integer minSpeed;
    /**
     * 抓拍图片
     */
    private String carImgUrl;
    private String carImg1Url;
    private String carImg2Url;
    private String carImg3Url;
    private String carImg4Url;
    private String carImg5Url;
    private int carImgPlateBottom;
    private int carImgPlateRight;
    private int carImgPlateLeft;
    private int carImgPlateTop;
    private int dcCleanFlag;
    private int bindStat;
    private Integer carImgCount;
    private int recStat;
    private String devChnid;
    private int saveFlag;
    private String carNumPic;
    private String combinedPicUrl;
    private String verifyMemo;
    private Integer picId;
    private int carBrand;
    private String carBrandStr;
    private int issafetybelt;
    private int isvisor;
    private int cascadeImgType;

}
