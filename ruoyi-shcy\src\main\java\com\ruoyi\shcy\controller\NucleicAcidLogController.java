package com.ruoyi.shcy.controller;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.PageDomain;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.domain.Employees;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.vo.EmployeesDetailVo;
import com.ruoyi.shcy.domain.vo.ShopDetailVo;
import com.ruoyi.shcy.service.IEmployeesService;
import com.ruoyi.system.service.ISysDeptService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.NucleicAcidLog;
import com.ruoyi.shcy.service.INucleicAcidLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 员工核酸情况Controller
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@RestController
@RequestMapping("/shcy/nucleicAcidLog")
public class NucleicAcidLogController extends BaseController
{
    @Autowired
    private INucleicAcidLogService nucleicAcidLogService;

    @Autowired
    private ISysDeptService iSysDeptService;

    /**
     * 查询员工核酸情况列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:nucleicAcidLog:list')")
    @GetMapping("/list")
    public TableDataInfo list(NucleicAcidLog nucleicAcidLog)
    {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();
        if(Objects.equals(user.getUserName(), "admin")){
            // 获取当前的用户名称
            startPage();
            List<NucleicAcidLog> list = nucleicAcidLogService.selectNucleicAcidLogList(nucleicAcidLog);
            return getDataTable(list);
        }

        if(deptId!=null&& !Objects.equals(user.getUserName(), "admin")){
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if(StringUtils.isNotEmpty(sysDept.getIsCommittee()) &&sysDept.getIsCommittee().equals("1")) {      //获取居委会对应查看的店铺列表
                startPage();
                nucleicAcidLog.setDeptId(sysDept.getDeptId());
                List<NucleicAcidLog> list = nucleicAcidLogService.selectNucleicAcidLogList(nucleicAcidLog);
                return getDataTable(list);
            }else if(StringUtils.isNotEmpty(sysDept.getIsCommittee()) &&sysDept.getIsCommittee().equals("2")){ //获取网格长查看多个居委会数据

                TableDataInfo tableDataInfo = selectWanggezhangShopNucleicAcidLogList(user, nucleicAcidLog, nucleicAcidLogService);
                return tableDataInfo;
            }
        }
        // 获取当前的用户名称
        startPage();
//        List<NucleicAcidLog> list = nucleicAcidLogService.selectNucleicAcidLogList(nucleicAcidLog);
        List<NucleicAcidLog> list = new ArrayList<>();
        return getDataTable(list);
    }

    public static TableDataInfo  selectWanggezhangShopNucleicAcidLogList(SysUser user, NucleicAcidLog nucleicAcidLog, INucleicAcidLogService nucleicAcidLogService){
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        List<NucleicAcidLog> shopNucleicAcidLogList = nucleicAcidLogService.selectNucleicAcidLogList(nucleicAcidLog).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getDeptId())).collect(Collectors.toList());

        int num = shopNucleicAcidLogList.size();
        shopNucleicAcidLogList = shopNucleicAcidLogList.stream()
                .skip((pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(shopNucleicAcidLogList);
        rspData.setTotal(num);
        return rspData;
    }

    /**
     * 导出员工核酸情况列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:nucleicAcidLog:export')")
    @Log(title = "员工核酸情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NucleicAcidLog nucleicAcidLog)
    {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDeptId();

        if(user.getUserName().equals("admin") ){
            // 获取当前的用户名称
            List<NucleicAcidLog> list = nucleicAcidLogService.selectNucleicAcidLogList(nucleicAcidLog);
            ExcelUtil<NucleicAcidLog> util = new ExcelUtil<NucleicAcidLog>(NucleicAcidLog.class);
            util.exportExcel(response, list, "员工核酸情况数据");
        }

        if(deptId!=null && !Objects.equals(user.getUserName(), "admin")){
            SysDept sysDept = iSysDeptService.selectDeptById(deptId);
            if(StringUtils.isNotEmpty(sysDept.getIsCommittee())&&sysDept.getIsCommittee().equals("1")) {     //获取居委会对应查看的店铺列表
                nucleicAcidLog.setDeptId(deptId);
                List<NucleicAcidLog> list = nucleicAcidLogService.selectNucleicAcidLogList(nucleicAcidLog);
                ExcelUtil<NucleicAcidLog> util = new ExcelUtil<NucleicAcidLog>(NucleicAcidLog.class);
                util.exportExcel(response, list, "员工核酸情况数据");

            }else if(StringUtils.isNotEmpty(sysDept.getIsCommittee()) &&sysDept.getIsCommittee().equals("2")){ //获取网格长查看多个居委会数据
                List<NucleicAcidLog> nucleicAcidLogList = nucleicAcidLogService.selectNucleicAcidLogList(nucleicAcidLog).stream().filter(x -> ArrayUtils.contains(Convert.toLongArray(user.getChargeDept()), x.getDeptId())).collect(Collectors.toList());
                ExcelUtil<NucleicAcidLog> util = new ExcelUtil<NucleicAcidLog>(NucleicAcidLog.class);
                util.exportExcel(response, nucleicAcidLogList, "员工核酸情况数据");
            }
        }
    }

    /**
     * 获取员工核酸情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:nucleicAcidLog:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(nucleicAcidLogService.selectNucleicAcidLogById(id));
    }

    /**
     * 获取所有从业人员核酸的详细信息
     * **/
    @GetMapping("/detail")
    public AjaxResult getEmployeeNucleicAcidDetail(NucleicAcidLog nucleicAcidLog){
        ShopDetailVo shopDetailVo = new ShopDetailVo();
        Date date = new Date();
        SimpleDateFormat dtf = new SimpleDateFormat("yyyy-MM-dd");
        String now_date = dtf.format(date);  //2022-11-08
        Date check_date = DateUtils.parseDate(now_date);

        //巡查日期
        nucleicAcidLog.setCheckDate(check_date);


        //从业人员总人数
        Shop shop1 = new Shop();
        List<ShopDetailVo> shopDetailVos = nucleicAcidLogService.selectEmployeesAll(shop1);
        int num =0;
        if(shopDetailVos.size()!=0){
            for (ShopDetailVo detailVo : shopDetailVos) {
                num+=Integer.valueOf(detailVo.getEmployeeNum());

            }
            shopDetailVo.setEmployeesTotal(String.valueOf(num));
        }

        //每日巡查从业人员总人数
        List<ShopDetailVo> employeesDetailVo1 = nucleicAcidLogService.selectCheckEmployeesTotal(nucleicAcidLog);
        if(employeesDetailVo1.size()!=0){
            shopDetailVo.setCheckEmployeesTotal(employeesDetailVo1.get(0).getCheckEmployeesTotal());
        }

        //每日核酸正常人员数
        List<ShopDetailVo> employeesDetailVo2 = nucleicAcidLogService.selectCheckNormalEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo2.size()!=0){
            shopDetailVo.setCheckNoramEmployeesNum(employeesDetailVo2.get(0).getCheckNoramEmployeesNum());
        }

        //每日核酸异常人员数
        List<ShopDetailVo> employeesDetailVo3 = nucleicAcidLogService.selectCheckAbnormalEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo3.size()!=0){
            shopDetailVo.setCheckAbnormalEmployeesNum(employeesDetailVo3.get(0).getCheckAbnormalEmployeesNum());
        }
        //每日离岗人员数
        List<ShopDetailVo> employeesDetailVo4 = nucleicAcidLogService.selectCheckRetiredEmployeesNum(nucleicAcidLog);
        if(employeesDetailVo4.size()!=0){
            shopDetailVo.setCheckRetiredEmployeesNum(employeesDetailVo4.get(0).getCheckRetiredEmployeesNum());
        }

        return AjaxResult.success(shopDetailVo);

    }

    /**
     * 新增员工核酸情况
     */
    @PreAuthorize("@ss.hasPermi('shcy:nucleicAcidLog:add')")
    @Log(title = "员工核酸情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NucleicAcidLog nucleicAcidLog)
    {
        return toAjax(nucleicAcidLogService.insertNucleicAcidLog(nucleicAcidLog));
    }

    /**
     * 修改员工核酸情况
     */
    @PreAuthorize("@ss.hasPermi('shcy:nucleicAcidLog:edit')")
    @Log(title = "员工核酸情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NucleicAcidLog nucleicAcidLog)
    {
        return toAjax(nucleicAcidLogService.updateNucleicAcidLog(nucleicAcidLog));
    }

    /**
     * 删除员工核酸情况
     */
    @PreAuthorize("@ss.hasPermi('shcy:nucleicAcidLog:remove')")
    @Log(title = "员工核酸情况", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(nucleicAcidLogService.deleteNucleicAcidLogByIds(ids));
    }
}
