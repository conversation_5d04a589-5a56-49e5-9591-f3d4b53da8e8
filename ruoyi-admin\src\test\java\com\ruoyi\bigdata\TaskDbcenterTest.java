package com.ruoyi.bigdata;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.bigdata.service.BigdataService;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class TaskDbcenterTest {

    @Autowired
    private BigdataService bigdataService;

    @Autowired
    private ITaskDbcenterService taskDbcenterService;


    @Test
    public void contextLoads() {
        // 这里没有任何代码，只是确保应用程序上下文能够成功加载
    }

    @Test
    public void testSync() {
        System.out.println("同步启动~");
        String token = bigdataService.getToken();
        String res = bigdataService.getGridCaseInfo(0, 5, "2024-02-20 00:00:00", token);
        JSONObject responseObj = JSONObject.parseObject(res);
        List<TaskDbcenter> responseDataList = new ArrayList<>();
        if (responseObj.getInteger("code") == 1) {
            JSONObject dataObj = responseObj.getJSONObject("data");
            String dataArray = dataObj.getString("content");
            JSONArray jsonArray = JSON.parseArray(dataArray);
            if (jsonArray == null) {
                System.out.println("没有数据");
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject itemObj = jsonArray.getJSONObject(i);
                System.out.println("itemObj: " + itemObj);
                TaskDbcenter responseData = JSON.toJavaObject(itemObj, TaskDbcenter.class);
                System.out.println(responseData);
                taskDbcenterService.insertTaskDbcenter(responseData);
                responseDataList.add(responseData);
            }
        }
        System.out.println("同步结束~");
    }

    @Test
    public void testGetGridCaseInfos() {
        List<Long> ids = taskDbcenterService.selectTaskDbcenterIdList();
        String token = bigdataService.getToken();

        int page = 0;
        int size = 500;

        String discoverTime = "2024-02-01 00:00:00";

        // 定义一个计数器
        int count = 0;

        while (true) {
            String res = bigdataService.getGridCaseInfosByDiscoverTime(page, size, discoverTime, token);
            JSONObject responseObj = JSONObject.parseObject(res);
            if (responseObj.getInteger("code") == 1) {
                JSONObject dataObj = responseObj.getJSONObject("data");
                String dataArray = dataObj.getString("content");
                JSONArray jsonArray = JSON.parseArray(dataArray);
                if (jsonArray.size() > 0) {
                    List<TaskDbcenter> responseDataList = jsonArray.toJavaList(TaskDbcenter.class);
                    for (TaskDbcenter dbcenter : responseDataList) {
                        // 判断dbcenter案件来源infosourcename只保留专职监督员上报和12345上报
                        if (!"专职监督员上报".equals(dbcenter.getInfosourcename()) && !"12345上报".equals(dbcenter.getInfosourcename())) {
                            continue;
                        }

                        // 如果是12345上报，判断executedeptname是否是石化街道
                        if ("12345上报".equals(dbcenter.getInfosourcename())) {
                            if (!"石化街道".equals(dbcenter.getExecutedeptname())) {
                                continue;
                            }
                        }

                        // 判断responseData.getId()是否在collect中
                        if (!ids.contains(dbcenter.getId())) {
                            taskDbcenterService.insertTaskDbcenter(dbcenter);
                        } else {
                            taskDbcenterService.updateTaskDbcenter(dbcenter);
                        }

                        count++;
                    }
                    page++;
                } else {
                    break;
                }
            } else {
                break;
            }
        }
        System.out.println("网格化案件信息初始化完成, 总共" + count + "条数据");
    }

    @Test
    public void testSelectTaskDbcenterIdList() {

        String synctime = "2024-02-20 00:00:00";
        List<Long> ids = taskDbcenterService.selectTaskDbcenterIdList();

        System.out.println("ids: " + ids);

    }

}
