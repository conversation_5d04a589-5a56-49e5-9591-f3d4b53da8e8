package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.Employees;

/**
 * 从业人员基本信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-10-27
 */
public interface EmployeesMapper 
{
    /**
     * 查询从业人员基本信息
     * 
     * @param id 从业人员基本信息主键
     * @return 从业人员基本信息
     */
    public Employees selectEmployeesById(Long id);

    /**
     * 查询从业人员基本信息列表
     * 
     * @param employees 从业人员基本信息
     * @return 从业人员基本信息集合
     */
    public List<Employees> selectEmployeesList(Employees employees);

    /**
     * 新增从业人员基本信息
     * 
     * @param employees 从业人员基本信息
     * @return 结果
     */
    public int insertEmployees(Employees employees);

    /**
     * 修改从业人员基本信息
     * 
     * @param employees 从业人员基本信息
     * @return 结果
     */
    public int updateEmployees(Employees employees);

    /**
     * 删除从业人员基本信息
     * 
     * @param id 从业人员基本信息主键
     * @return 结果
     */
    public int deleteEmployeesById(Long id);

    /**
     * 批量删除从业人员基本信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteEmployeesByIds(Long[] ids);
}
