package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyFloodHazardCheckListExtend;

/**
 * 隐患排查子表Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
public interface ShcyFloodHazardCheckListExtendMapper
{
    /**
     * 查询隐患排查子表
     *
     * @param id 隐患排查子表主键
     * @return 隐患排查子表
     */
    public ShcyFloodHazardCheckListExtend selectShcyFloodHazardCheckListExtendById(Long id);

    /**
     * 查询隐患排查子表列表
     *
     * @param shcyFloodHazardCheckListExtend 隐患排查子表
     * @return 隐患排查子表集合
     */
    public List<ShcyFloodHazardCheckListExtend> selectShcyFloodHazardCheckListExtendList(ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend);

    /**
     * 新增隐患排查子表
     *
     * @param shcyFloodHazardCheckListExtend 隐患排查子表
     * @return 结果
     */
    public int insertShcyFloodHazardCheckListExtend(ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend);

    /**
     * 修改隐患排查子表
     *
     * @param shcyFloodHazardCheckListExtend 隐患排查子表
     * @return 结果
     */
    public int updateShcyFloodHazardCheckListExtend(ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend);

    /**
     * 删除隐患排查子表
     *
     * @param id 隐患排查子表主键
     * @return 结果
     */
    public int deleteShcyFloodHazardCheckListExtendById(Long id);

    /**
     * 批量删除隐患排查子表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyFloodHazardCheckListExtendByIds(Long[] ids);

    ShcyFloodHazardCheckListExtend sumCountByCheckId(Long id);
}
