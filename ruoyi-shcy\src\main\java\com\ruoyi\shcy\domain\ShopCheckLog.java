package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 检查日志对象 shcy_shop_check_log
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
public class ShopCheckLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 店铺id */
    @Excel(name = "店铺id")
    private Long shopId;

    /** 是否规范佩戴口罩 */
    @Excel(name = "是否规范佩戴口罩")
    private String checkMask;

    /** 员工每日核酸 */
    @Excel(name = "员工每日核酸")
    private String checkNucleicAcid;

    /** 门口扫码落实一带三查 */
    @Excel(name = "门口扫码落实一带三查")
    private String checkScanCode;

    /** 每日消杀记录 */
    @Excel(name = "每日消杀记录")
    private String checkDisinfection;

    /** 其他问题 */
    @Excel(name = "其他问题")
    private String checkOthers;

    /** 店铺名称*/
    @Excel(name="店铺名称")
    private String shopName;

    /** 所属居委会*/
    @Excel(name= "所属居委会")
    private String deptName;

    /** 所属居委会id*/
    private Long deptId;

    /** 检查日期 */
    @Excel(name ="检查日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkDate;

    /** 店铺状态 */
    @Excel(name = "店铺状态")
    private String shopStatus;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 巡检距离*/
    @Excel(name = "巡检距离状态")
    private String distanceStatus;

    /** 广告牌存在安全隐患 */
    @Excel(name = "广告牌存在安全隐患")
    private String checkCrossDoorOperation;

    /** 证照公示 */
    @Excel(name = "证照公示'")
    private String checkFfhx;

    /** 门责制是否落实 */
    @Excel(name = "门责制是否落实")
    private String checkMqsbls;

    /** 是否存在“三合一”住人现象 */
    @Excel(name = "是否存在“三合一”住人现象")
    private String checkThreeOnePlace;

    /** 沿街商铺是否装修 */
    @Excel(name = "沿街商铺是否装修")
    private String checkShopDecorate;

    /** 沿街商铺施工安全 */
    @Excel(name = "沿街商铺施工安全")
    private String checkHealthClean;

    /** 沿街商铺文明施工 */
    @Excel(name = "沿街商铺文明施工")
    private String checkFalsePropaganda;

    /** 场所环境是否整洁 */
    @Excel(name = "场所环境是否整洁")
    private String checkEnvironmentNeat;

    /** 及时清理变质或者超过保质期的食品 */
    @Excel(name = "及时清理变质或者超过保质期的食品")
    private String checkCleanUpFood;

    /** 城管修改 */
    @Excel(name = "城管修改")
    private String checkUpdateByCg;

    /** 城管建议 */
    @Excel(name = "城管建议")
    private String checkOthersCg;

    /** 城管拍照 */
    @Excel(name = "城管拍照")
    private String checkPhoto;

    /** 城管修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTimeCg;

    private String fileId;
    // 街长巡查照片
    private String jzCheckPhoto;


    /** 照片 url**/
    private List<String> photoUrls;

    private List<String> jzPhotoUrls;

    private List<String> fileIds;

    private Shop shop;


    public List<String> getJzPhotoUrls() {
        return jzPhotoUrls;
    }

    public void setJzPhotoUrls(List<String> jzPhotoUrls) {
        this.jzPhotoUrls = jzPhotoUrls;
    }

    public String getJzCheckPhoto() {
        return jzCheckPhoto;
    }

    public void setJzCheckPhoto(String jzCheckPhoto) {
        this.jzCheckPhoto = jzCheckPhoto;
    }

    public List<String> getFileIds() {
        return fileIds;
    }

    public void setFileIds(List<String> fileIds) {
        this.fileIds = fileIds;
    }

    public Shop getShop() {
        return shop;
    }

    public void setShop(Shop shop) {
        this.shop = shop;
    }

    public String getDistanceStatus() {
        return distanceStatus;
    }

    public void setDistanceStatus(String distanceStatus) {
        this.distanceStatus = distanceStatus;
    }


    public String getShopStatus() {
        return shopStatus;
    }

    public void setShopStatus(String shopStatus) {
        this.shopStatus = shopStatus;
    }

    public Date getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(Date checkDate) {
        this.checkDate = checkDate;
    }


    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public void setCheckMask(String checkMask)
    {
        this.checkMask = checkMask;
    }

    public String getCheckMask()
    {
        return checkMask;
    }

    public void setCheckNucleicAcid(String checkNucleicAcid)
    {
        this.checkNucleicAcid = checkNucleicAcid;
    }

    public String getCheckNucleicAcid()
    {
        return checkNucleicAcid;
    }

    public void setCheckScanCode(String checkScanCode)
    {
        this.checkScanCode = checkScanCode;
    }

    public String getCheckScanCode()
    {
        return checkScanCode;
    }

    public void setCheckDisinfection(String checkDisinfection)
    {
        this.checkDisinfection = checkDisinfection;
    }

    public String getCheckDisinfection()
    {
        return checkDisinfection;
    }

    public void setCheckOthers(String checkOthers)
    {
        this.checkOthers = checkOthers;
    }

    public String getCheckOthers()
    {
        return checkOthers;
    }

    public void setLongitude(BigDecimal longitude)
    {
        this.longitude = longitude;
    }

    public BigDecimal getLongitude()
    {
        return longitude;
    }

    public void setLatitude(BigDecimal latitude)
    {
        this.latitude = latitude;
    }

    public BigDecimal getLatitude()
    {
        return latitude;
    }

    public String getCheckCrossDoorOperation() {
        return checkCrossDoorOperation;
    }

    public void setCheckCrossDoorOperation(String checkCrossDoorOperation) {
        this.checkCrossDoorOperation = checkCrossDoorOperation;
    }

    public String getCheckMqsbls() {
        return checkMqsbls;
    }

    public void setCheckMqsbls(String checkMqsbls) {
        this.checkMqsbls = checkMqsbls;
    }

    public String getCheckThreeOnePlace() {
        return checkThreeOnePlace;
    }

    public void setCheckThreeOnePlace(String checkThreeOnePlace) {
        this.checkThreeOnePlace = checkThreeOnePlace;
    }

    public String getCheckFfhx() {
        return checkFfhx;
    }

    public void setCheckFfhx(String checkFfhx) {
        this.checkFfhx = checkFfhx;
    }

    public String getCheckShopDecorate() {
        return checkShopDecorate;
    }

    public void setCheckShopDecorate(String checkShopDecorate) {
        this.checkShopDecorate = checkShopDecorate;
    }

    public String getCheckHealthClean() {
        return checkHealthClean;
    }

    public void setCheckHealthClean(String checkHealthClean) {
        this.checkHealthClean = checkHealthClean;
    }

    public String getCheckFalsePropaganda() {
        return checkFalsePropaganda;
    }

    public void setCheckFalsePropaganda(String checkFalsePropaganda) {
        this.checkFalsePropaganda = checkFalsePropaganda;
    }

    public String getCheckEnvironmentNeat() {
        return checkEnvironmentNeat;
    }

    public void setCheckEnvironmentNeat(String checkEnvironmentNeat) {
        this.checkEnvironmentNeat = checkEnvironmentNeat;
    }

    public String getCheckCleanUpFood() {
        return checkCleanUpFood;
    }

    public void setCheckCleanUpFood(String checkCleanUpFood) {
        this.checkCleanUpFood = checkCleanUpFood;
    }


    public String getCheckUpdateByCg() {
        return checkUpdateByCg;
    }

    public void setCheckUpdateByCg(String checkUpdateByCg) {
        this.checkUpdateByCg = checkUpdateByCg;
    }

    public String getCheckOthersCg() {
        return checkOthersCg;
    }

    public void setCheckOthersCg(String checkOthersCg) {
        this.checkOthersCg = checkOthersCg;
    }

    public String getCheckPhoto() {
        return checkPhoto;
    }

    public void setCheckPhoto(String checkPhoto) {
        this.checkPhoto = checkPhoto;
    }

    public Date getUpdateTimeCg() {
        return updateTimeCg;
    }

    public void setUpdateTimeCg(Date updateTimeCg) {
        this.updateTimeCg = updateTimeCg;
    }

    public List<String> getPhotoUrls() {
        return photoUrls;
    }

    public void setPhotoUrls(List<String> photoUrls) {
        this.photoUrls = photoUrls;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    @Override
    public String toString() {
        return "ShopCheckLog{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", checkMask='" + checkMask + '\'' +
                ", checkNucleicAcid='" + checkNucleicAcid + '\'' +
                ", checkScanCode='" + checkScanCode + '\'' +
                ", checkDisinfection='" + checkDisinfection + '\'' +
                ", checkOthers='" + checkOthers + '\'' +
                ", shopName='" + shopName + '\'' +
                ", deptName='" + deptName + '\'' +
                ", deptId=" + deptId +
                ", checkDate=" + checkDate +
                ", shopStatus='" + shopStatus + '\'' +
                ", longitude=" + longitude +
                ", latitude=" + latitude +
                ", distanceStatus='" + distanceStatus + '\'' +
                ", checkCrossDoorOperation='" + checkCrossDoorOperation + '\'' +
                ", checkFfhx='" + checkFfhx + '\'' +
                ", checkMqsbls='" + checkMqsbls + '\'' +
                ", checkThreeOnePlace='" + checkThreeOnePlace + '\'' +
                ", checkShopDecorate='" + checkShopDecorate + '\'' +
                ", checkHealthClean='" + checkHealthClean + '\'' +
                ", checkFalsePropaganda='" + checkFalsePropaganda + '\'' +
                ", checkEnvironmentNeat='" + checkEnvironmentNeat + '\'' +
                ", checkCleanUpFood='" + checkCleanUpFood + '\'' +
                ", checkUpdateByCg='" + checkUpdateByCg + '\'' +
                ", checkOthersCg='" + checkOthersCg + '\'' +
                ", checkPhoto='" + checkPhoto + '\'' +
                ", updateTimeCg=" + updateTimeCg +
                ", shop=" + shop +
                '}';
    }
}
