package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.Cameras;
import com.ruoyi.shcy.service.ICamerasService;
import com.ruoyi.shcy.service.IMonitoringTypesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 监控资源Controller
 * 
 * <AUTHOR>
 * @date 2023-03-28
 */
@RestController
@RequestMapping("/shcy/cameras")
public class CamerasController extends BaseController
{
    @Autowired
    private ICamerasService camerasService;

    @Autowired
    private IMonitoringTypesService monitoringTypesService;

    /**
     * 查询监控资源列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:cameras:list')")
    @GetMapping("/list")
    public TableDataInfo list(Cameras cameras)
    {
        startPage();
        List<Cameras> list = camerasService.selectCamerasList(cameras);
        return getDataTable(list);
    }

    /**
     * 导出监控资源列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:cameras:export')")
    @Log(title = "监控资源", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Cameras cameras)
    {
        List<Cameras> list = camerasService.selectCamerasList(cameras);
        ExcelUtil<Cameras> util = new ExcelUtil<Cameras>(Cameras.class);
        util.exportExcel(response, list, "监控资源数据");
    }

    /**
     * 获取监控资源详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:cameras:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(camerasService.selectCamerasById(id));
    }

    /**
     * 新增监控资源
     */
    @PreAuthorize("@ss.hasPermi('shcy:cameras:add')")
    @Log(title = "监控资源", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Cameras cameras)
    {
        return toAjax(camerasService.insertCameras(cameras));
    }

    /**
     * 修改监控资源
     */
    @PreAuthorize("@ss.hasPermi('shcy:cameras:edit')")
    @Log(title = "监控资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Cameras cameras)
    {
        return toAjax(camerasService.updateCameras(cameras));
    }

    /**
     * 删除监控资源
     */
    @PreAuthorize("@ss.hasPermi('shcy:cameras:remove')")
    @Log(title = "监控资源", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(camerasService.deleteCamerasByIds(ids));
    }

    /**
     * 获取所有监控类型名称
     *
     * @return {@link AjaxResult}
     */
    @GetMapping("/getAllMonitoringTypeName")
    public AjaxResult getAllMonitoringTypeName()
    {
        return AjaxResult.success(monitoringTypesService.getAllMonitoringTypeName());
    }

    @GetMapping("/getGroupCamera")
    public AjaxResult getGroupCamera()
    {
        return AjaxResult.success(camerasService.getGroupCamera());
    }

    @GetMapping("/getGroupCameras/{groupType}")
    public AjaxResult getGroupCameras(@PathVariable("groupType") String groupType) throws Exception
    {
        return AjaxResult.success(camerasService.selectGroupCameras(groupType));
    }

    @GetMapping("/getLinkCameras/{cameras}")
    public AjaxResult getLinkCameras(@PathVariable("cameras") String cameras) throws Exception
    {
        return AjaxResult.success(camerasService.selectLinkCameras(cameras));
    }

}
