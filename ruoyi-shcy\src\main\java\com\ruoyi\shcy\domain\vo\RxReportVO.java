package com.ruoyi.shcy.domain.vo;

import com.ruoyi.shcy.domain.TaskDbcenter;

import java.util.List;

public class RxReportVO {

    private String num;

    private String lastNum;

    private String hotlinesn;

    private String lalastNum;

    private String gbflNum;

    private String parentappealclassification;

    private String appealclassification;

    private String residentialarea;

    private String subexecutedeptnameMh;

    List<RxReportVO> xlList;

    List<RxReportVO> xlAllList;

    List<RxReportCfgdVO> cfgdList;

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getParentappealclassification() {
        return parentappealclassification;
    }

    public void setParentappealclassification(String parentappealclassification) {
        this.parentappealclassification = parentappealclassification;
    }

    public String getAppealclassification() {
        return appealclassification;
    }

    public void setAppealclassification(String appealclassification) {
        this.appealclassification = appealclassification;
    }

    public List<RxReportVO> getXlList() {
        return xlList;
    }

    public void setXlList(List<RxReportVO> xlList) {
        this.xlList = xlList;
    }

    public String getResidentialarea() {
        return residentialarea;
    }

    public void setResidentialarea(String residentialarea) {
        this.residentialarea = residentialarea;
    }

    public String getLastNum() {
        return lastNum;
    }

    public void setLastNum(String lastNum) {
        this.lastNum = lastNum;
    }

    public String getGbflNum() {
        return gbflNum;
    }

    public void setGbflNum(String gbflNum) {
        this.gbflNum = gbflNum;
    }

    public List<RxReportCfgdVO> getCfgdList() {
        return cfgdList;
    }

    public void setCfgdList(List<RxReportCfgdVO> cfgdList) {
        this.cfgdList = cfgdList;
    }

    public String getSubexecutedeptnameMh() {
        return subexecutedeptnameMh;
    }

    public void setSubexecutedeptnameMh(String subexecutedeptnameMh) {
        this.subexecutedeptnameMh = subexecutedeptnameMh;
    }

    public String getLalastNum() {
        return lalastNum;
    }

    public void setLalastNum(String lalastNum) {
        this.lalastNum = lalastNum;
    }

    public String getHotlinesn() {
        return hotlinesn;
    }

    public void setHotlinesn(String hotlinesn) {
        this.hotlinesn = hotlinesn;
    }

    public List<RxReportVO> getXlAllList() {
        return xlAllList;
    }

    public void setXlAllList(List<RxReportVO> xlAllList) {
        this.xlAllList = xlAllList;
    }
}
