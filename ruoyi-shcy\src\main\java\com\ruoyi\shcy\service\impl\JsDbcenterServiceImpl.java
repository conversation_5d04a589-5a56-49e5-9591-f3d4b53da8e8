package com.ruoyi.shcy.service.impl;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.shcy.domain.JsDbcenter;
import com.ruoyi.shcy.mapper.JsDbcenterMapper;
import com.ruoyi.shcy.service.IJsDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@DataSource(value = DataSourceType.SLAVE)
public class JsDbcenterServiceImpl implements IJsDbcenterService {

    @Autowired
    private JsDbcenterMapper jsDbcenterMapper;

    @Override
    public JsDbcenter selectJsDbcenterById(Long id) {
        return jsDbcenterMapper.selectJsDbcenterById(id);
    }

    @Override
    public JsDbcenter selectJsDbcenterByTaskid(String taskid) {
        return jsDbcenterMapper.selectJsDbcenterByTaskid(taskid);
    }

    @Override
    public List<JsDbcenter> selectJsDbcenterList(JsDbcenter jsDbcenter)
    {
        return jsDbcenterMapper.selectJsDbcenterList(jsDbcenter);
    }

    @Override
    public List<JsDbcenter> selectJsDbcenterSyncList(Long[] ids) {
        return jsDbcenterMapper.selectJsDbcenterSyncList(ids);
    }

    @Override
    public List<JsDbcenter> selectJsDbcenterSyncListByTaskid(String[] taskids) {
        return jsDbcenterMapper.selectJsDbcenterSyncListByTaskid(taskids);
    }

    @Override
    public List<JsDbcenter> selectJsDbcenter12345List(String startTime, String endTime) {
        return jsDbcenterMapper.selectJsDbcenter12345List(startTime, endTime);
    }

    @Override
    public List<JsDbcenter> selectJsDbcenterCaseList(String startTime, String endTime) {
        return jsDbcenterMapper.selectJsDbcenterCaseList(startTime, endTime);
    }

}
