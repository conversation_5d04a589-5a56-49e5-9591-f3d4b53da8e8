package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.JsDbcenter;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface JsDbcenterMapper {

    public JsDbcenter selectJsDbcenterById(Long id);

    public List<JsDbcenter> selectJsDbcenterList(JsDbcenter jsDbcenter);

    public List<JsDbcenter> selectJsDbcenterSyncList(Long[] ids);

    public List<JsDbcenter> selectJsDbcenter12345List(@Param("startTime") String startTime, @Param("endTime") String endTime);

    public List<JsDbcenter> selectJsDbcenterCaseList(@Param("startTime") String startTime, @Param("endTime") String endTime);

    public JsDbcenter selectJsDbcenterByTaskid(String taskid);

    public List<JsDbcenter> selectJsDbcenterSyncListByTaskid(String[] taskids);
}
