package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 网格化案件信息对象 shcy_wgh
 * 
 * <AUTHOR>
 * @date 2024-04-10
 */
public class ShcyWgh extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 三高网格化表id */
    @Excel(name = "三高网格化表id")
    private Long wghId;

    /** 案件大类 */
    @Excel(name = "案件大类")
    private String infobcName;

    /** 案件小类 */
    @Excel(name = "案件小类")
    private String infoscName;

    /** 发生时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发生时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date discoverTime;

    /** 发生地址 */
    @Excel(name = "发生地址")
    private String address;

    /** 问题描述 */
    @Excel(name = "问题描述")
    private String description;

    /** 处置人 */
    @Excel(name = "处置人")
    private String dealBy;

    /** 处置照片 */
    @Excel(name = "处置照片")
    private String dealPhoto;

    /** 流转状态 */
    @Excel(name = "流转状态", readConverterExp = "0=待结案,1=处置中,2=延期处置中,3=待分派,4=已上报至三高平台,5=已作废,6=已结案")
    private String circulationState;

    /** 按时完成状态 */
    @Excel(name = "按时完成状态")
    private String dealInTimeState;

    /** 事件截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "事件截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date caseEndTime;

    /** 事件完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date caseFinishTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "立案时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lianTime;

    @Excel(name = "监督人姓名")
    private String reporter;

    /** 任务号 */
    @Excel(name = "任务号")
    private String taskId;

    /** 延期原因 */
    @Excel(name = "延期原因")
    private String yqReason;

    /** 退单原因 */
    @Excel(name = "退单原因")
    private String tdReason;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    private List<String> dealPhotos;

    public List<String> getDealPhotos() {
        return dealPhotos;
    }

    private String imagefilename;

    private String ddFlag;
    /**
     * 上传图片
     */
    private List<String> imagefilenames;

    public List<String> getImagefilenames() {
        if (StringUtils.isNotEmpty(imagefilename)) {
            // imagefilename以逗号分隔, 拼上前缀http://
            imagefilenames = Arrays.asList(imagefilename.split(",")).stream().map(s -> "http://10.233.49.253:10110/file-service/resource/"+s).collect(Collectors.toList());
        }
        return imagefilenames;
    }

    public String getImagefilename() {
        return imagefilename;
    }

    public void setImagefilename(String imagefilename) {
        this.imagefilename = imagefilename;
    }


    public void setDealPhotos(List<String> dealPhotos) {
        this.dealPhotos = dealPhotos;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public Long getWghId() {
        return wghId;
    }

    public void setWghId(Long wghId) {
        this.wghId = wghId;
    }

    public void setInfobcName(String infobcName)
    {
        this.infobcName = infobcName;
    }

    public String getInfobcName() 
    {
        return infobcName;
    }
    public void setInfoscName(String infoscName) 
    {
        this.infoscName = infoscName;
    }

    public String getInfoscName() 
    {
        return infoscName;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setDealBy(String dealBy) 
    {
        this.dealBy = dealBy;
    }

    public String getDealBy() 
    {
        return dealBy;
    }
    public void setDealPhoto(String dealPhoto) 
    {
        this.dealPhoto = dealPhoto;
    }

    public String getDealPhoto() 
    {
        return dealPhoto;
    }
    public void setCirculationState(String circulationState) 
    {
        this.circulationState = circulationState;
    }

    public String getCirculationState() 
    {
        return circulationState;
    }
    public void setDealInTimeState(String dealInTimeState) 
    {
        this.dealInTimeState = dealInTimeState;
    }

    public String getDealInTimeState() 
    {
        return dealInTimeState;
    }
    public void setCaseEndTime(Date caseEndTime) 
    {
        this.caseEndTime = caseEndTime;
    }

    public Date getCaseEndTime() 
    {
        return caseEndTime;
    }
    public void setCaseFinishTime(Date caseFinishTime) 
    {
        this.caseFinishTime = caseFinishTime;
    }

    public Date getCaseFinishTime() 
    {
        return caseFinishTime;
    }

    public String getYqReason() {
        return yqReason;
    }

    public void setYqReason(String yqReason) {
        this.yqReason = yqReason;
    }

    public String getTdReason() {
        return tdReason;
    }

    public void setTdReason(String tdReason) {
        this.tdReason = tdReason;
    }

    public Date getDiscoverTime() {
        return discoverTime;
    }

    public void setDiscoverTime(Date discoverTime) {
        this.discoverTime = discoverTime;
    }

    public Date getLianTime() {
        return lianTime;
    }

    public void setLianTime(Date lianTime) {
        this.lianTime = lianTime;
    }

    public String getReporter() {
        return reporter;
    }

    public void setReporter(String reporter) {
        this.reporter = reporter;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getDdFlag(){
        // 根据statusname不是'已作废', '已结案', '已退回其他平台'并且discovertime超过7天的返回true
        if (this.circulationState != null && !this.circulationState.equals("4") ) {
            if (this.lianTime != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(this.lianTime);
                calendar.add(Calendar.DATE, 10);
                Calendar calendar1 = Calendar.getInstance();
                calendar1.setTime(this.lianTime);
                calendar1.add(Calendar.DATE, 30);
               if (new Date().after(calendar1.getTime())) {
                    return "2";
               } else if (new Date().after(calendar.getTime())) {
                   return "1";
               }
            }
        }
        return "0";
    }
    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("wghId", getWghId())
            .append("infobcName", getInfobcName())
            .append("infoscName", getInfoscName())
            .append("address", getAddress())
            .append("description", getDescription())
            .append("dealBy", getDealBy())
            .append("dealPhoto", getDealPhoto())
            .append("circulationState", getCirculationState())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("dealInTimeState", getDealInTimeState())
            .append("createTime", getCreateTime())
            .append("caseEndTime", getCaseEndTime())
            .append("updateTime", getUpdateTime())
            .append("caseFinishTime", getCaseFinishTime())
            .append("taskId", getTaskId())
            .append("yqReason",getYqReason())
            .append("tdReason",getTdReason())
            .append("discoverTime",getDiscoverTime())
            .append("lianTime",getLianTime())
            .append("reporter",getReporter())
            .append("imagefilename",getImagefilename())
            .append("memo",getMemo())
            .toString();
    }
}
