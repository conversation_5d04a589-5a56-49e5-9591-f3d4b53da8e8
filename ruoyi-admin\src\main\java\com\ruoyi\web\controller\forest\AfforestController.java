package com.ruoyi.web.controller.forest;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.domain.ShcyAfforestCase;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.service.IShcyAfforestCaseService;
import com.ruoyi.shcy.service.IShcyFileInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @Date 2023/5/19 15:36
 * @Version 1.0
 */


@RestController
@RequestMapping("/shcy/afforestCheck")
public class AfforestController extends BaseController {


    @Autowired
    private IShcyAfforestCaseService shcyAfforestCaseService;

    @Autowired
    private IShcyFileInfoService shcyFileInfoService;

    @PostMapping("/confirm")
    @Log(title = "林长确认巡检", businessType = BusinessType.INSERT)
    public AjaxResult check(@RequestBody ShcyAfforestCase shcyAfforestCase){

        shcyAfforestCase.setDealStatus("1");
        shcyAfforestCase.setDealInTimeState("0");
        shcyAfforestCaseService.insertShcyAfforestCase(shcyAfforestCase);
        return AjaxResult.success("新增成功");
    }


    @GetMapping(value = "/{forestId}")
    public AjaxResult getShcyAfforestCase(@PathVariable("forestId") String forestId){
        String checkDate = DateUtil.formatDate(new Date());
        ShcyAfforestCase shcyAfforestCase = shcyAfforestCaseService.selectShcyAfforestCaseByForestId(Long.valueOf(forestId));

        if(shcyAfforestCase!=null){
            if(StringUtils.isNotEmpty(shcyAfforestCase.getAppendix())){
                String[] ids  = shcyAfforestCase.getAppendix().split(",");

                if(ids.length!=0){     //上传照片信息
                    List<String> imgurls = new ArrayList<>();
                    for (String id : ids) {
                        ShcyFileInfo shcyFileInfo = shcyFileInfoService.selectShcyFileInfoByFileId(Long.valueOf(id));
                        imgurls.add(shcyFileInfo.getFilePath());
                    }
                    shcyAfforestCase.setPhotoUrls(imgurls);

                }
            }
            if(StringUtils.isNotEmpty(shcyAfforestCase.getDealPhoto())){  //处理照片信息
                String[] ids  = shcyAfforestCase.getDealPhoto().split(",");

                if(ids.length!=0){
                    List<String> imgurls = new ArrayList<>();
                    for (String id : ids) {
                        ShcyFileInfo shcyFileInfo = shcyFileInfoService.selectShcyFileInfoByFileId(Long.valueOf(id));
                        imgurls.add(shcyFileInfo.getFilePath());
                    }
                    shcyAfforestCase.setDealPhotoUrls(imgurls);
                }
            }
        }

        return AjaxResult.success(shcyAfforestCase);
    }


}

