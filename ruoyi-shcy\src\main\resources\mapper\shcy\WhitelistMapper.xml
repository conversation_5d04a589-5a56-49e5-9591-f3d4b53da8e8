<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.WhitelistMapper">
    
    <resultMap type="Whitelist" id="WhitelistResult">
        <result property="id"    column="id"    />
        <result property="licensePlate"    column="license_plate"    />
    </resultMap>

    <sql id="selectWhitelistVo">
        select id, license_plate from shcy_whitelist
    </sql>

    <select id="selectWhitelistList" parameterType="Whitelist" resultMap="WhitelistResult">
        <include refid="selectWhitelistVo"/>
        <where>  
            <if test="licensePlate != null  and licensePlate != ''"> and license_plate = #{licensePlate}</if>
        </where>
    </select>
    
    <select id="selectWhitelistById" parameterType="Long" resultMap="WhitelistResult">
        <include refid="selectWhitelistVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertWhitelist" parameterType="Whitelist" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_whitelist
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="licensePlate != null">license_plate,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="licensePlate != null">#{licensePlate},</if>
         </trim>
    </insert>

    <update id="updateWhitelist" parameterType="Whitelist">
        update shcy_whitelist
        <trim prefix="SET" suffixOverrides=",">
            <if test="licensePlate != null">license_plate = #{licensePlate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWhitelistById" parameterType="Long">
        delete from shcy_whitelist where id = #{id}
    </delete>

    <delete id="deleteWhitelistByIds" parameterType="String">
        delete from shcy_whitelist where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>