package com.ruoyi.shcy.domain.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;

/**
 * @Description: 店铺日常数据（总数，正常数量，歇业数量，关停数量）
 * <AUTHOR>
 * @Date 2023/2/15 10:25
 * @Version 1.0
 */


public class ShopDailyVo extends BaseEntity {

    /** 店铺总数*/
    @Excel(name="商铺总数量")
    private String ShopTotal;

    /** 正常商铺总数*/
    @Excel(name="正常商铺数量")
    private String ShopNormalNum;

    /** 歇业商铺数量*/
    @Excel(name="歇业商铺数量")
    private String ShopRestNum;

    /** 关停商铺数量 */
    @Excel(name="关停商铺数量")
    private String ShopCloseNum;

    /**巡查日期*/
    @Excel(name = "巡查日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date curDate;

    public String getShopTotal() {
        return ShopTotal;
    }

    public void setShopTotal(String shopTotal) {
        ShopTotal = shopTotal;
    }

    public String getShopNormalNum() {
        return ShopNormalNum;
    }

    public void setShopNormalNum(String shopNormalNum) {
        ShopNormalNum = shopNormalNum;
    }

    public String getShopRestNum() {
        return ShopRestNum;
    }

    public void setShopRestNum(String shopRestNum) {
        ShopRestNum = shopRestNum;
    }

    public String getShopCloseNum() {
        return ShopCloseNum;
    }

    public void setShopCloseNum(String shopCloseNum) {
        ShopCloseNum = shopCloseNum;
    }

    public Date getCurDate() {
        return curDate;
    }

    public void setCurDate(Date curDate) {
        this.curDate = curDate;
    }


    @Override
    public String toString() {
        return "ShopDailyVo{" +
                "ShopTotal='" + ShopTotal + '\'' +
                ", ShopNormalNum='" + ShopNormalNum + '\'' +
                ", ShopRestNum='" + ShopRestNum + '\'' +
                ", ShopCloseNum='" + ShopCloseNum + '\'' +
                ", curDate=" + curDate +
                '}';
    }
}
