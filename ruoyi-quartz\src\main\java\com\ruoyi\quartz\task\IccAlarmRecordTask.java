package com.ruoyi.quartz.task;

import cn.hutool.core.date.DateUtil;
import com.dahuatech.icc.exception.ClientException;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import com.ruoyi.shcy.domain.Whitelist;
import com.ruoyi.shcy.service.IWhitelistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * icc报警记录任务
 *
 * <AUTHOR>
 * @date 2023/12/11
 */
@Component("iccAlarmRecordTask")
public class IccAlarmRecordTask {

    @Autowired
    private IccService iccService;

    @Autowired
    private IWhitelistService whitelistService;

    /**
     * 同步
     */
    public void sync() throws ClientException {
        String today = DateUtil.today() + " 00:00:00";
        String tomorrow = DateUtil.formatDate(DateUtil.tomorrow())+ " 00:00:00";
        int pageNum = 1;
        int pageSize = 1000;
        List<IccAlarmRecord> iccAlarmRecords = iccService.alarmRecordPage(pageNum, pageSize, today, tomorrow, 15591);

        // 过滤白名单
        List<Whitelist> whitelists = whitelistService.selectWhitelistList(null);
        for (Whitelist whitelist : whitelists) {
            iccAlarmRecords.removeIf(iccAlarmRecord -> whitelist.getLicensePlate().equals(iccAlarmRecord.getLicensePlate()));
        }

        for (IccAlarmRecord iccAlarmRecord : iccAlarmRecords) {
            iccService.syncIccAlarmRecord(iccAlarmRecord);
        }

    }

}
