package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.NbiotyunAlarmRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报警记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-04
 */
public interface NbiotyunAlarmRecordMapper 
{
    /**
     * 查询报警记录
     * 
     * @param id 报警记录主键
     * @return 报警记录
     */
    public NbiotyunAlarmRecord selectNbiotyunAlarmRecordById(Long id);

    /**
     * 查询报警记录列表
     * 
     * @param nbiotyunAlarmRecord 报警记录
     * @return 报警记录集合
     */
    public List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordList(NbiotyunAlarmRecord nbiotyunAlarmRecord);

    /**
     * 新增报警记录
     * 
     * @param nbiotyunAlarmRecord 报警记录
     * @return 结果
     */
    public int insertNbiotyunAlarmRecord(NbiotyunAlarmRecord nbiotyunAlarmRecord);

    /**
     * 修改报警记录
     * 
     * @param nbiotyunAlarmRecord 报警记录
     * @return 结果
     */
    public int updateNbiotyunAlarmRecord(NbiotyunAlarmRecord nbiotyunAlarmRecord);

    /**
     * 删除报警记录
     * 
     * @param id 报警记录主键
     * @return 结果
     */
    public int deleteNbiotyunAlarmRecordById(Long id);

    /**
     * 批量删除报警记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNbiotyunAlarmRecordByIds(Long[] ids);

    /**
     * 按列表选择nbiotyun报警记录组
     *
     * @param nbiotyunAlarmRecord nbiotyun报警记录
     * @return {@link List}<{@link NbiotyunAlarmRecord}>
     */
    List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordGroupByList(NbiotyunAlarmRecord nbiotyunAlarmRecord);

    /**
     * 通过imei获取报警记录
     *
     * @param deviceImei 设备imei
     * @return {@link NbiotyunAlarmRecord}
     */
    NbiotyunAlarmRecord getAlarmRecordByImei(@Param("deviceImei") String deviceImei);

    /**
     * 更新nbiotyun报警记录状态
     *
     * @param nbiotyunAlarmRecord nbiotyun报警记录
     * @return int
     */
    int updateNbiotyunAlarmRecordStatus(NbiotyunAlarmRecord nbiotyunAlarmRecord);

    List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordUnprocessedList(NbiotyunAlarmRecord nbiotyunAlarmRecord);

    List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordProcessingList(NbiotyunAlarmRecord nbiotyunAlarmRecord);

    List<NbiotyunAlarmRecord> selectNbiotyunAlarmRecordProcessedList(NbiotyunAlarmRecord nbiotyunAlarmRecord);
}
