package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 汛期隐患排查对象 shcy_flood_hazard_check_list
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
public class FloodHazardCheckList extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 事件编号 */
    @Excel(name = "事件编号")
    private String eventNo;

    /** 检查地点 */
    @Excel(name = "检查地点")
    private String checkLocation;

    /** 检查负责人 */
    @Excel(name = "检查负责人")
    private String checkPerson;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactNumber;

    /** 检查人数 */
    @Excel(name = "检查人数")
    private Integer checkPeople;

    /** 检查时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "检查时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /** 检查项目 */
    @Excel(name = "检查项目")
    private String checkItem;

    /** 发现隐患数量 */
    @Excel(name = "发现隐患数量")
    private Integer foundHazardCount;

    /** 整改隐患数量 */
    @Excel(name = "整改隐患数量")
    private Integer rectifiedHazardCount;

    /** 居委会名称 */
    @Excel(name = "居委会名称")
    private String residentCommunityName;

    /** 小区名称 */
    @Excel(name = "小区名称")
    private String communityName;

    /** 居委会书记 */
    @Excel(name = "居委会书记")
    private String communitySecretary;

    /** 卫生主任 */
    @Excel(name = "卫生主任")
    private String communityHygieneDirector;

    /** 解决率 */
    @Excel(name = "解决率")
    private BigDecimal solveRate;

    /** 发布时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /** 截止完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "截止完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date caseEndTime;

    /** 流转状态 */
    @Excel(name = "流转状态")
    private String circulationState;

    /** 事件完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date caseFinishTime;

    /** 按时完成状态 */
    private String dealInTimeState;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCheckLocation(String checkLocation)
    {
        this.checkLocation = checkLocation;
    }

    public String getCheckLocation()
    {
        return checkLocation;
    }
    public void setCheckPerson(String checkPerson)
    {
        this.checkPerson = checkPerson;
    }

    public String getCheckPerson()
    {
        return checkPerson;
    }
    public void setContactNumber(String contactNumber)
    {
        this.contactNumber = contactNumber;
    }

    public String getContactNumber()
    {
        return contactNumber;
    }
    public void setCheckPeople(Integer checkPeople)
    {
        this.checkPeople = checkPeople;
    }

    public Integer getCheckPeople()
    {
        return checkPeople;
    }
    public void setCheckTime(Date checkTime)
    {
        this.checkTime = checkTime;
    }

    public Date getCheckTime()
    {
        return checkTime;
    }
    public void setCheckItem(String checkItem)
    {
        this.checkItem = checkItem;
    }

    public String getCheckItem()
    {
        return checkItem;
    }
    public void setFoundHazardCount(Integer foundHazardCount)
    {
        this.foundHazardCount = foundHazardCount;
    }

    public Integer getFoundHazardCount()
    {
        return foundHazardCount;
    }
    public void setRectifiedHazardCount(Integer rectifiedHazardCount)
    {
        this.rectifiedHazardCount = rectifiedHazardCount;
    }

    public Integer getRectifiedHazardCount()
    {
        return rectifiedHazardCount;
    }

    public String getResidentCommunityName() {
        return residentCommunityName;
    }

    public void setResidentCommunityName(String residentCommunityName) {
        this.residentCommunityName = residentCommunityName;
    }

    public String getCommunityName() {
        return communityName;
    }

    public void setCommunityName(String communityName) {
        this.communityName = communityName;
    }

    public String getCommunitySecretary() {
        return communitySecretary;
    }

    public void setCommunitySecretary(String communitySecretary) {
        this.communitySecretary = communitySecretary;
    }

    public String getCommunityHygieneDirector() {
        return communityHygieneDirector;
    }

    public void setCommunityHygieneDirector(String communityHygieneDirector) {
        this.communityHygieneDirector = communityHygieneDirector;
    }

    public BigDecimal getSolveRate() {
        return solveRate;
    }

    public void setSolveRate(BigDecimal solveRate) {
        this.solveRate = solveRate;
    }

    public void setPublishTime(Date publishTime)
    {
        this.publishTime = publishTime;
    }

    public Date getPublishTime()
    {
        return publishTime;
    }

    public void setCaseEndTime(Date caseEndTime)
    {
        this.caseEndTime = caseEndTime;
    }

    public Date getCaseEndTime()
    {
        return caseEndTime;
    }

    public void setCirculationState(String circulationState)
    {
        this.circulationState = circulationState;
    }

    public String getCirculationState()
    {
        return circulationState;
    }

    public void setCaseFinishTime(Date caseFinishTime)
    {
        this.caseFinishTime = caseFinishTime;
    }

    public Date getCaseFinishTime()
    {
        return caseFinishTime;
    }

    public void setDealInTimeState(String dealInTimeState)
    {
        this.dealInTimeState = dealInTimeState;
    }

    public String getDealInTimeState()
    {
        return dealInTimeState;
    }

    public String getEventNo() {
        return eventNo;
    }

    public void setEventNo(String eventNo) {
        this.eventNo = eventNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("checkLocation", getCheckLocation())
            .append("checkPerson", getCheckPerson())
            .append("contactNumber", getContactNumber())
            .append("checkPeople", getCheckPeople())
            .append("checkTime", getCheckTime())
            .append("checkItem", getCheckItem())
            .append("foundHazardCount", getFoundHazardCount())
            .append("rectifiedHazardCount", getRectifiedHazardCount())
            .append("remark", getRemark())
            .append("residentCommunityName", getResidentCommunityName())
            .append("communityName", getCommunityName())
            .append("communitySecretary", getCommunitySecretary())
            .append("communityHygieneDirector", getCommunityHygieneDirector())
            .append("solveRate", getSolveRate())
            .append("publishTime", getPublishTime())
            .append("caseEndTime", getCaseEndTime())
            .append("circulationState", getCirculationState())
            .append("caseFinishTime", getCaseFinishTime())
            .append("dealInTimeState", getDealInTimeState())
            .append("eventNo",getEventNo())
            .toString();
    }
}
