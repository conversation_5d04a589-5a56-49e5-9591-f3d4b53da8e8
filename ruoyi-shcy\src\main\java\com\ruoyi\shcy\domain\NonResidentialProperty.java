package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 物业管理处 shcy_non_residential_property
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public class NonResidentialProperty extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 物业公司名称 */
    @Excel(name = "物业公司名称")
    private String name;



    /** 所属小区id */
    @Excel(name = "所属小区id")
    private String residentialId;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 物业服务地点 */
    @Excel(name = "物业服务地点")
    private String propertyServiceSite;

    /** 居委会 */
    @Excel(name = "居委会")
    private String committee;

    /** 所管小区 */
    @Excel(name = "所管小区")
    private String residential;

    /** 总经理 */
    @Excel(name = "总经理")
    private String generalManage;

    /** 总经理手机 */
    @Excel(name = "总经理手机")
    private String generalPhone;

    /** 小区经理 */
    @Excel(name = "小区经理")
    private String contacts;

    /** 办公电话 */
    @Excel(name = "办公电话")
    private String contactsPhone;

    /** 小区经理手机 */
    @Excel(name = "小区经理手机")
    private String mobilePhone;


    public String getCommittee() {
        return committee;
    }

    public void setCommittee(String committee) {
        this.committee = committee;
    }

    public String getGeneralManage() {
        return generalManage;
    }

    public void setGeneralManage(String generalManage) {
        this.generalManage = generalManage;
    }

    public String getGeneralPhone() {
        return generalPhone;
    }

    public void setGeneralPhone(String generalPhone) {
        this.generalPhone = generalPhone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setResidential(String residential)
    {
        this.residential = residential;
    }

    public String getResidential()
    {
        return residential;
    }

    public String getResidentialId() {
        return residentialId;
    }

    public void setResidentialId(String residentialId) {
        this.residentialId = residentialId;
    }

    public void setLongitude(String longitude)
    {
        this.longitude = longitude;
    }

    public String getLongitude()
    {
        return longitude;
    }
    public void setLatitude(String latitude)
    {
        this.latitude = latitude;
    }

    public String getLatitude()
    {
        return latitude;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setCoordinate(String coordinate)
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate()
    {
        return coordinate;
    }
    public void setPropertyServiceSite(String propertyServiceSite)
    {
        this.propertyServiceSite = propertyServiceSite;
    }

    public String getPropertyServiceSite()
    {
        return propertyServiceSite;
    }
    public void setContacts(String contacts)
    {
        this.contacts = contacts;
    }

    public String getContacts()
    {
        return contacts;
    }
    public void setContactsPhone(String contactsPhone)
    {
        this.contactsPhone = contactsPhone;
    }

    public String getContactsPhone()
    {
        return contactsPhone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("residential", getResidential())
                .append("residentialId", getResidentialId())
                .append("longitude", getLongitude())
                .append("latitude", getLatitude())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("type", getType())
                .append("coordinate", getCoordinate())
                .append("propertyServiceSite", getPropertyServiceSite())
                .append("contacts", getContacts())
                .append("contactsPhone", getContactsPhone())
                .toString();
    }
}
