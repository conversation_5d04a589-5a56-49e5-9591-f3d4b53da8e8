package com.ruoyi.shcy.dto;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.util.Date;
import java.util.List;

/**
 * 网格化案件信息对象(带图片) dbcenter
 *
 * @date 2024-01-26
 */
@Data
public class TaskDbcenterImageDTO {

    @Excel(name = "案件状态")
    private String statusname;

    @Excel(name = "任务号")
    private String taskid;

    @Excel(name = "案件来源")
    private String infosourcename;

    @Excel(name = "发现时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date discovertime;

    @Excel(name = "案件属性")
    private String infotypename;

    @Excel(name = "案件大类")
    private String infobcname;

    @Excel(name = "案件小类")
    private String infoscname;

    @Excel(name = "案件子类")
    private String infozcname;

    @Excel(name = "发生地址", align = HorizontalAlignment.LEFT)
    private String address;

    @Excel(name = "监督员姓名")
    private String reporter;

    @Excel(name = "问题描述", align = HorizontalAlignment.LEFT)
    private String description;

    @Excel(name = "派遣时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date dispatchtime;

    @Excel(name = "主责部门")
    private String executedeptname;

    @Excel(name = "结案时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    private Date endtime;

    @Excel(name = "案件图片1", width = 25, height = 80, cellType = Excel.ColumnType.IMAGE)
    private String images1;

    @Excel(name = "案件图片2", width = 25, height = 80, cellType = Excel.ColumnType.IMAGE)
    private String images2;

    @Excel(name = "案件图片3", width = 25, height = 80, cellType = Excel.ColumnType.IMAGE)
    private String images3;

    @Excel(name = "案件图片4", width = 25, height = 80, cellType = Excel.ColumnType.IMAGE)
    private String images4;

    @Excel(name = "案件图片5", width = 25, height = 80, cellType = Excel.ColumnType.IMAGE)
    private String images5;

    private List<String> imagefilenames;
}
