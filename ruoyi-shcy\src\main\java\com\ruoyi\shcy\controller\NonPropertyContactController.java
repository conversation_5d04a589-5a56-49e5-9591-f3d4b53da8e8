package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.NonPropertyContact;
import com.ruoyi.shcy.service.INonPropertyContactService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 非住宅物业联系表Controller
 * 
 * <AUTHOR>
 * @date 2023-01-31
 */
@RestController
@RequestMapping("/shcy/contact")
public class NonPropertyContactController extends BaseController
{
    @Autowired
    private INonPropertyContactService nonPropertyContactService;

    /**
     * 查询非住宅物业联系表列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:contact:list')")
    @GetMapping("/list")
    public TableDataInfo list(NonPropertyContact nonPropertyContact)
    {
        startPage();
        List<NonPropertyContact> list = nonPropertyContactService.selectNonPropertyContactList(nonPropertyContact);
        return getDataTable(list);
    }

    /**
     * 导出非住宅物业联系表列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:contact:export')")
    @Log(title = "非住宅物业联系表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, NonPropertyContact nonPropertyContact)
    {
        List<NonPropertyContact> list = nonPropertyContactService.selectNonPropertyContactList(nonPropertyContact);
        ExcelUtil<NonPropertyContact> util = new ExcelUtil<NonPropertyContact>(NonPropertyContact.class);
        util.exportExcel(response, list, "非住宅物业联系表数据");
    }

    /**
     * 获取非住宅物业联系表详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:contact:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(nonPropertyContactService.selectNonPropertyContactById(id));
    }

    /**
     * 新增非住宅物业联系表
     */
    @PreAuthorize("@ss.hasPermi('shcy:contact:add')")
    @Log(title = "非住宅物业联系表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody NonPropertyContact nonPropertyContact)
    {
        return toAjax(nonPropertyContactService.insertNonPropertyContact(nonPropertyContact));
    }

    /**
     * 修改非住宅物业联系表
     */
    @PreAuthorize("@ss.hasPermi('shcy:contact:edit')")
    @Log(title = "非住宅物业联系表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody NonPropertyContact nonPropertyContact)
    {
        return toAjax(nonPropertyContactService.updateNonPropertyContact(nonPropertyContact));
    }

    /**
     * 删除非住宅物业联系表
     */
    @PreAuthorize("@ss.hasPermi('shcy:contact:remove')")
    @Log(title = "非住宅物业联系表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(nonPropertyContactService.deleteNonPropertyContactByIds(ids));
    }
}
