package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 小区出入门口对象 shcy_residential_entrance
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public class ResidentialEntrance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 出入口名称 */
    @Excel(name = "出入口名称")
    private String name;

    /** 小区id */
    @Excel(name = "小区id")
    private Long residentialId;

    /** 居委会 */
    @Excel(name = "居委会")
    private String committee;

    /** 居委会id */
    @Excel(name = "居委会id")
    private Long committeeId;

    /** 小区 */
    @Excel(name = "小区")
    private String residential;

    /** 具体位置 */
    @Excel(name = "具体位置")
    private String detailSite;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 是否有门卫 */
    @Excel(name = "是否有门卫")
    private String withGuard;

    /** 机动车通行 */
    @Excel(name = "机动车通行")
    private String motorVehiclePassing;

    /** 出入口类型 */
    @Excel(name = "出入口类型")
    private String entranceType;

    /** 非机动车通行 */
    @Excel(name = "非机动车通行")
    private String nonMotorVehicleTraffic;

    /** 行人 */
    @Excel(name = "行人")
    private String passenger;

    /** 开放时间 */
    @Excel(name = "开放时间")
    private String openTime;

    /** 出入门属性*/
    @Excel(name="属性")
    private String entranceProperty;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setResidentialId(Long residentialId)
    {
        this.residentialId = residentialId;
    }

    public Long getResidentialId()
    {
        return residentialId;
    }
    public void setCommittee(String committee)
    {
        this.committee = committee;
    }

    public String getCommittee()
    {
        return committee;
    }
    public void setCommitteeId(Long committeeId)
    {
        this.committeeId = committeeId;
    }

    public Long getCommitteeId()
    {
        return committeeId;
    }
    public void setResidential(String residential)
    {
        this.residential = residential;
    }

    public String getResidential()
    {
        return residential;
    }
    public void setDetailSite(String detailSite)
    {
        this.detailSite = detailSite;
    }

    public String getDetailSite()
    {
        return detailSite;
    }
    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }
    public void setCoordinate(String coordinate)
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate()
    {
        return coordinate;
    }
    public void setWithGuard(String withGuard)
    {
        this.withGuard = withGuard;
    }

    public String getWithGuard()
    {
        return withGuard;
    }
    public void setMotorVehiclePassing(String motorVehiclePassing)
    {
        this.motorVehiclePassing = motorVehiclePassing;
    }

    public String getMotorVehiclePassing()
    {
        return motorVehiclePassing;
    }
    public void setEntranceType(String entranceType)
    {
        this.entranceType = entranceType;
    }

    public String getEntranceType()
    {
        return entranceType;
    }
    public void setNonMotorVehicleTraffic(String nonMotorVehicleTraffic)
    {
        this.nonMotorVehicleTraffic = nonMotorVehicleTraffic;
    }

    public String getNonMotorVehicleTraffic()
    {
        return nonMotorVehicleTraffic;
    }
    public void setPassenger(String passenger)
    {
        this.passenger = passenger;
    }

    public String getPassenger()
    {
        return passenger;
    }
    public void setOpenTime(String openTime)
    {
        this.openTime = openTime;
    }

    public String getOpenTime()
    {
        return openTime;
    }

    public String getEntranceProperty() {
        return entranceProperty;
    }

    public void setEntranceProperty(String entranceProperty) {
        this.entranceProperty = entranceProperty;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("name", getName())
                .append("residentialId", getResidentialId())
                .append("committee", getCommittee())
                .append("committeeId", getCommitteeId())
                .append("residential", getResidential())
                .append("detailSite", getDetailSite())
                .append("type", getType())
                .append("coordinate", getCoordinate())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .append("withGuard", getWithGuard())
                .append("motorVehiclePassing", getMotorVehiclePassing())
                .append("entranceType", getEntranceType())
                .append("nonMotorVehicleTraffic", getNonMotorVehicleTraffic())
                .append("passenger", getPassenger())
                .append("openTime", getOpenTime())
                .append("remark", getRemark())
                .append("entranceProperty",getEntranceProperty())
                .toString();
    }
}
