package com.ruoyi.quartz.task;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.bigdata.service.BigdataService;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component("dbcenterTask")
public class DbcenterTask {

    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    @Autowired
    private BigdataService bigdataService;

    /**
     * 同步当天的数据
     */
    public void sync() {
        String discoverTime = DateUtil.today() + " 00:00:00";
        init(discoverTime);
    }

    /**
     * 同步昨天的数据
     */
    public void syncUpdate() {
        String discoverTime = DateUtil.format(DateUtil.yesterday(), DatePattern.NORM_DATE_PATTERN) + " 00:00:00";
        init(discoverTime);
    }

    /**
     * 初始化网格化案件信息
     * @param discoverTime 发现时间
     */
    public void init(String discoverTime) {
        List<Long> ids = taskDbcenterService.selectTaskDbcenterIdList();
        String token = bigdataService.getToken();

        int page = 0;
        int size = 500;

        while (true) {
            String res = bigdataService.getGridCaseInfosByDiscoverTime(page, size, discoverTime, token);
            JSONObject responseObj = JSONObject.parseObject(res);
            if (responseObj.getInteger("code") == 1) {
                JSONObject dataObj = responseObj.getJSONObject("data");
                String dataArray = dataObj.getString("content");
                JSONArray jsonArray = JSON.parseArray(dataArray);
                if (jsonArray.size() > 0) {
                    List<TaskDbcenter> responseDataList = jsonArray.toJavaList(TaskDbcenter.class);
                    for (TaskDbcenter dbcenter : responseDataList) {
                        // 判断dbcenter案件来源infosourcename只保留专职监督员上报和12345上报
                        if (!"专职监督员上报".equals(dbcenter.getInfosourcename()) && !"12345上报".equals(dbcenter.getInfosourcename())) {
                            continue;
                        }

                        // 如果是12345上报，判断executedeptname是否是石化街道
                        if ("12345上报".equals(dbcenter.getInfosourcename())) {
                            if (!"石化街道".equals(dbcenter.getExecutedeptname())) {
                                continue;
                            }
                        }

                        // 判断responseData.getId()是否在collect中
                        if (!ids.contains(dbcenter.getId())) {
                            taskDbcenterService.insertTaskDbcenter(dbcenter);
                        } else {
                            taskDbcenterService.updateTaskDbcenter(dbcenter);
                        }
                    }
                    page++;
                } else {
                    break;
                }
            } else {
                break;
            }
        }
        // System.out.println("网格化案件信息初始化完成, 总共" + page + "页数据");
    }

}
