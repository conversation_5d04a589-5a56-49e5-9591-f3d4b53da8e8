package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 雨污管道信息对象 shcy_rainwater_sewage_pipe
 * 
 * <AUTHOR>
 * @date 2022-12-21
 */
public class RainwaterSewagePipe extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 管段编号 */
    @Excel(name = "管段编号")
    private String pipeSectionNo;

    /** 管道性质 */
    @Excel(name = "管道性质")
    private String pipeProperty;

    /** 管道材质 */
    @Excel(name = "管道材质")
    private String pipeMaterial;

    /** 管道内径（mm） */
    @Excel(name = "管道内径", readConverterExp = "m=m")
    private String pipRadius;

    /** 管道长度（m） */
    @Excel(name = "管道长度", readConverterExp = "m=")
    private String pipLength;

    /** 管道权属 */
    @Excel(name = "管道权属")
    private String pipOwnership;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPipeSectionNo(String pipeSectionNo) 
    {
        this.pipeSectionNo = pipeSectionNo;
    }

    public String getPipeSectionNo() 
    {
        return pipeSectionNo;
    }
    public void setPipeProperty(String pipeProperty) 
    {
        this.pipeProperty = pipeProperty;
    }

    public String getPipeProperty() 
    {
        return pipeProperty;
    }
    public void setPipeMaterial(String pipeMaterial) 
    {
        this.pipeMaterial = pipeMaterial;
    }

    public String getPipeMaterial() 
    {
        return pipeMaterial;
    }
    public void setPipRadius(String pipRadius) 
    {
        this.pipRadius = pipRadius;
    }

    public String getPipRadius() 
    {
        return pipRadius;
    }
    public void setPipLength(String pipLength) 
    {
        this.pipLength = pipLength;
    }

    public String getPipLength() 
    {
        return pipLength;
    }
    public void setPipOwnership(String pipOwnership) 
    {
        this.pipOwnership = pipOwnership;
    }

    public String getPipOwnership() 
    {
        return pipOwnership;
    }
    public void setType(String type) 
    {
        this.type = type;
    }

    public String getType() 
    {
        return type;
    }
    public void setCoordinate(String coordinate) 
    {
        this.coordinate = coordinate;
    }

    public String getCoordinate() 
    {
        return coordinate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("pipeSectionNo", getPipeSectionNo())
            .append("pipeProperty", getPipeProperty())
            .append("pipeMaterial", getPipeMaterial())
            .append("pipRadius", getPipRadius())
            .append("pipLength", getPipLength())
            .append("pipOwnership", getPipOwnership())
            .append("type", getType())
            .append("coordinate", getCoordinate())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
