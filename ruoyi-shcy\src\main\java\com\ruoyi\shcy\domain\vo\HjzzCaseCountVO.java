package com.ruoyi.shcy.domain.vo;

import com.ruoyi.shcy.domain.HjzzCase;
import lombok.Data;

import java.util.List;

@Data
public class HjzzCaseCountVO {

    private int currentTotal;
    private int currentDiscarded;
    private int currentInProcess;
    private int currentCompleted;
    private int currentKtjy;
    private int currentLacc;
    private double currentClosureRate;

    private int monthlyTotal;
    private int monthlyDiscarded;
    private int monthlyInProcess;
    private int monthlyCompleted;
    private int monthlyKtjy;
    private int monthlyLacc;
    private double monthlyClosureRate;

    private int lastMonthTotal;
    private int lastMonthDiscarded;
    private int lastMonthInProcess;
    private int lastMonthCompleted;
    private int lastMonthKtjy;
    private int lastMonthLacc;
    private double lastMonthClosureRate;

    private List<HjzzCase> hjzzCaseList;


}
