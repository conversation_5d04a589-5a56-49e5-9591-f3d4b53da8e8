package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.GarbageHouse;

/**
 * 小区垃圾房信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface GarbageHouseMapper 
{
    /**
     * 查询小区垃圾房信息
     * 
     * @param id 小区垃圾房信息主键
     * @return 小区垃圾房信息
     */
    public GarbageHouse selectGarbageHouseById(Long id);

    /**
     * 查询小区垃圾房信息列表
     * 
     * @param garbageHouse 小区垃圾房信息
     * @return 小区垃圾房信息集合
     */
    public List<GarbageHouse> selectGarbageHouseList(GarbageHouse garbageHouse);

    /**
     * 新增小区垃圾房信息
     * 
     * @param garbageHouse 小区垃圾房信息
     * @return 结果
     */
    public int insertGarbageHouse(GarbageHouse garbageHouse);

    /**
     * 修改小区垃圾房信息
     * 
     * @param garbageHouse 小区垃圾房信息
     * @return 结果
     */
    public int updateGarbageHouse(GarbageHouse garbageHouse);

    /**
     * 删除小区垃圾房信息
     * 
     * @param id 小区垃圾房信息主键
     * @return 结果
     */
    public int deleteGarbageHouseById(Long id);

    /**
     * 批量删除小区垃圾房信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGarbageHouseByIds(Long[] ids);
}
