package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.Vehicle;
import com.ruoyi.shcy.mapper.VehicleMapper;
import com.ruoyi.shcy.service.IVehicleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 过车记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-01
 */
@Service
public class VehicleServiceImpl implements IVehicleService 
{
    @Autowired
    private VehicleMapper vehicleMapper;

    /**
     * 查询过车记录
     * 
     * @param id 过车记录主键
     * @return 过车记录
     */
    @Override
    public Vehicle selectVehicleById(Long id)
    {
        return vehicleMapper.selectVehicleById(id);
    }

    /**
     * 查询过车记录列表
     * 
     * @param vehicle 过车记录
     * @return 过车记录
     */
    @Override
    public List<Vehicle> selectVehicleList(Vehicle vehicle)
    {
        return vehicleMapper.selectVehicleList(vehicle);
    }

    /**
     * 新增过车记录
     * 
     * @param vehicle 过车记录
     * @return 结果
     */
    @Override
    public int insertVehicle(Vehicle vehicle)
    {
        return vehicleMapper.insertVehicle(vehicle);
    }

    /**
     * 修改过车记录
     * 
     * @param vehicle 过车记录
     * @return 结果
     */
    @Override
    public int updateVehicle(Vehicle vehicle)
    {
        return vehicleMapper.updateVehicle(vehicle);
    }

    /**
     * 批量删除过车记录
     * 
     * @param ids 需要删除的过车记录主键
     * @return 结果
     */
    @Override
    public int deleteVehicleByIds(Long[] ids)
    {
        return vehicleMapper.deleteVehicleByIds(ids);
    }

    /**
     * 删除过车记录信息
     * 
     * @param id 过车记录主键
     * @return 结果
     */
    @Override
    public int deleteVehicleById(Long id)
    {
        return vehicleMapper.deleteVehicleById(id);
    }
}
