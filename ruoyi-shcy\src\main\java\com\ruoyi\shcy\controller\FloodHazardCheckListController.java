package com.ruoyi.shcy.controller;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.FloodHazardCheckList;
import com.ruoyi.shcy.service.IFloodHazardCheckListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 汛期隐患排查Controller
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
@RestController
@RequestMapping("/shcy/floodHazardCheckList")
public class FloodHazardCheckListController extends BaseController
{
    @Autowired
    private IFloodHazardCheckListService floodHazardCheckListService;

    /**
     * 查询汛期隐患排查列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:list')")
    @GetMapping("/list")
    public TableDataInfo list(FloodHazardCheckList floodHazardCheckList)
    {
        startPage();
        List<FloodHazardCheckList> list = floodHazardCheckListService.selectFloodHazardCheckListList(floodHazardCheckList);
        return getDataTable(list);
    }

    /**
     * 导出汛期隐患排查列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:export')")
    @Log(title = "汛期隐患排查", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FloodHazardCheckList floodHazardCheckList)
    {
        List<FloodHazardCheckList> list = floodHazardCheckListService.selectFloodHazardCheckListList(floodHazardCheckList);
        ExcelUtil<FloodHazardCheckList> util = new ExcelUtil<FloodHazardCheckList>(FloodHazardCheckList.class);
        util.exportExcel(response, list, "汛期隐患排查数据");
    }

    /**
     * 获取汛期隐患排查详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(floodHazardCheckListService.selectFloodHazardCheckListById(id));
    }

    /**
     * 新增汛期隐患排查
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:add')")
    @Log(title = "汛期隐患排查", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FloodHazardCheckList floodHazardCheckList)
    {
        return toAjax(floodHazardCheckListService.insertFloodHazardCheckList(floodHazardCheckList));
    }

    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:add')")
    @Log(title = "汛期隐患排查", businessType = BusinessType.INSERT)
    @PostMapping(value="/publish")
    public AjaxResult publish(@RequestBody FloodHazardCheckList floodHazardCheckList)
    {
        return toAjax(floodHazardCheckListService.publishFloodHazardCheckList(floodHazardCheckList));
    }


    /**
     * 修改汛期隐患排查
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:edit')")
    @Log(title = "汛期隐患排查", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FloodHazardCheckList floodHazardCheckList)
    {
        return toAjax(floodHazardCheckListService.updateFloodHazardCheckList(floodHazardCheckList));
    }

    /**
     * 删除汛期隐患排查
     */
    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:remove')")
    @Log(title = "汛期隐患排查", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(floodHazardCheckListService.deleteFloodHazardCheckListByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:list')")
    @GetMapping("/handle/list")
    public TableDataInfo handleList(FloodHazardCheckList floodHazardCheckList, String keyword)
    {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            floodHazardCheckList.setParams(params);
        }
        startPage();
        List<FloodHazardCheckList> list = floodHazardCheckListService.selectFloodHazardCheckListList(floodHazardCheckList);
        return getDataTable(list);
    }

    @PostMapping("/handle")
    public AjaxResult handle(@RequestBody FloodHazardCheckList floodHazardCheckList)
    {
        return toAjax(floodHazardCheckListService.handleFloodHazardCheckList(floodHazardCheckList));
    }

    @PreAuthorize("@ss.hasPermi('shcy:floodHazardCheckList:list')")
    @GetMapping("/getCaseCount")
    public AjaxResult getCaseCount(FloodHazardCheckList floodHazardCheckList, String keyword) {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            floodHazardCheckList.setParams(params);
        }
        return AjaxResult.success("操作成功", floodHazardCheckListService.getCaseCount(floodHazardCheckList));
    }

}
