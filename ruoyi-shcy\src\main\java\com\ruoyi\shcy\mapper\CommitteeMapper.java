package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.Committee;

import java.util.List;


/**
 * 居委会信息Mapper接口
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface CommitteeMapper
{
    /**
     * 查询居委会信息
     *
     * @param id 居委会信息主键
     * @return 居委会信息
     */
    public Committee selectCommitteeById(Long id);

    /**
     * 查询居委会信息列表
     *
     * @param committee 居委会信息
     * @return 居委会信息集合
     */
    public List<Committee> selectCommitteeList(Committee committee);

    /**
     * 新增居委会信息
     *
     * @param committee 居委会信息
     * @return 结果
     */
    public int insertCommittee(Committee committee);

    /**
     * 修改居委会信息
     *
     * @param committee 居委会信息
     * @return 结果
     */
    public int updateCommittee(Committee committee);

    /**
     * 删除居委会信息
     *
     * @param id 居委会信息主键
     * @return 结果
     */
    public int deleteCommitteeById(Long id);

    /**
     * 批量删除居委会信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommitteeByIds(Long[] ids);
}
