package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ComprehensiveGridMapper;
import com.ruoyi.shcy.domain.ComprehensiveGrid;
import com.ruoyi.shcy.service.IComprehensiveGridService;

/**
 * 综合网格Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-11
 */
@Service
public class ComprehensiveGridServiceImpl implements IComprehensiveGridService 
{
    @Autowired
    private ComprehensiveGridMapper comprehensiveGridMapper;

    /**
     * 查询综合网格
     * 
     * @param id 综合网格主键
     * @return 综合网格
     */
    @Override
    public ComprehensiveGrid selectComprehensiveGridById(Long id)
    {
        return comprehensiveGridMapper.selectComprehensiveGridById(id);
    }

    /**
     * 查询综合网格列表
     * 
     * @param comprehensiveGrid 综合网格
     * @return 综合网格
     */
    @Override
    public List<ComprehensiveGrid> selectComprehensiveGridList(ComprehensiveGrid comprehensiveGrid)
    {
        return comprehensiveGridMapper.selectComprehensiveGridList(comprehensiveGrid);
    }

    /**
     * 新增综合网格
     * 
     * @param comprehensiveGrid 综合网格
     * @return 结果
     */
    @Override
    public int insertComprehensiveGrid(ComprehensiveGrid comprehensiveGrid)
    {
        return comprehensiveGridMapper.insertComprehensiveGrid(comprehensiveGrid);
    }

    /**
     * 修改综合网格
     * 
     * @param comprehensiveGrid 综合网格
     * @return 结果
     */
    @Override
    public int updateComprehensiveGrid(ComprehensiveGrid comprehensiveGrid)
    {
        return comprehensiveGridMapper.updateComprehensiveGrid(comprehensiveGrid);
    }

    /**
     * 批量删除综合网格
     * 
     * @param ids 需要删除的综合网格主键
     * @return 结果
     */
    @Override
    public int deleteComprehensiveGridByIds(Long[] ids)
    {
        return comprehensiveGridMapper.deleteComprehensiveGridByIds(ids);
    }

    /**
     * 删除综合网格信息
     * 
     * @param id 综合网格主键
     * @return 结果
     */
    @Override
    public int deleteComprehensiveGridById(Long id)
    {
        return comprehensiveGridMapper.deleteComprehensiveGridById(id);
    }
}
