package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.EntertainmentPlacesMapper;
import com.ruoyi.shcy.domain.EntertainmentPlaces;
import com.ruoyi.shcy.service.IEntertainmentPlacesService;

/**
 * 娱乐场所Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-01-31
 */
@Service
public class EntertainmentPlacesServiceImpl implements IEntertainmentPlacesService 
{
    @Autowired
    private EntertainmentPlacesMapper entertainmentPlacesMapper;

    /**
     * 查询娱乐场所
     * 
     * @param id 娱乐场所主键
     * @return 娱乐场所
     */
    @Override
    public EntertainmentPlaces selectEntertainmentPlacesById(Long id)
    {
        return entertainmentPlacesMapper.selectEntertainmentPlacesById(id);
    }

    /**
     * 查询娱乐场所列表
     * 
     * @param entertainmentPlaces 娱乐场所
     * @return 娱乐场所
     */
    @Override
    public List<EntertainmentPlaces> selectEntertainmentPlacesList(EntertainmentPlaces entertainmentPlaces)
    {
        return entertainmentPlacesMapper.selectEntertainmentPlacesList(entertainmentPlaces);
    }

    /**
     * 新增娱乐场所
     * 
     * @param entertainmentPlaces 娱乐场所
     * @return 结果
     */
    @Override
    public int insertEntertainmentPlaces(EntertainmentPlaces entertainmentPlaces)
    {
        entertainmentPlaces.setCreateTime(DateUtils.getNowDate());
        return entertainmentPlacesMapper.insertEntertainmentPlaces(entertainmentPlaces);
    }

    /**
     * 修改娱乐场所
     * 
     * @param entertainmentPlaces 娱乐场所
     * @return 结果
     */
    @Override
    public int updateEntertainmentPlaces(EntertainmentPlaces entertainmentPlaces)
    {
        entertainmentPlaces.setUpdateTime(DateUtils.getNowDate());
        return entertainmentPlacesMapper.updateEntertainmentPlaces(entertainmentPlaces);
    }

    /**
     * 批量删除娱乐场所
     * 
     * @param ids 需要删除的娱乐场所主键
     * @return 结果
     */
    @Override
    public int deleteEntertainmentPlacesByIds(Long[] ids)
    {
        return entertainmentPlacesMapper.deleteEntertainmentPlacesByIds(ids);
    }

    /**
     * 删除娱乐场所信息
     * 
     * @param id 娱乐场所主键
     * @return 结果
     */
    @Override
    public int deleteEntertainmentPlacesById(Long id)
    {
        return entertainmentPlacesMapper.deleteEntertainmentPlacesById(id);
    }
}
