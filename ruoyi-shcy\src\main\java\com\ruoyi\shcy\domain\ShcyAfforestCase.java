package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.Date;
import java.util.List;

/**
 * 绿化案事件对象 shcy_afforest_case
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
public class ShcyAfforestCase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 问题名称 */
    @Excel(name = "问题名称")
    private String problemName;

    /** 问题描述 */
    @Excel(name = "问题描述")
    private String problemDescription;

    /** 问题地址 */
    @Excel(name = "问题地址")
    private String problemAddress;

    /** 处理截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dealTimeLimited;

    /** 处置部门 */
    @Excel(name = "处置部门")
    private String dealDept;

    /** 图片信息 */
    @Excel(name = "图片信息")
    private String appendix;

    /** 创建日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fillDate;

    /** 绿化id */
    @Excel(name = "绿化id")
    private Long forestId;

    /** 处理状态*/
    @Excel(name="处理状态")
    private String dealStatus;

    /** 处理时间*/
    @Excel(name="处理时限")
    private String dealDay;

    /** 问题类型 **/
    @Excel(name = "问题类型")
    private String problemType;

    /** 事件完成时间**/
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事件完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date dealFinishTime;

    /** 超时状态 */
    @Excel(name="超时状态")
    private String dealInTimeState;

    /** 处置照片信息 */
    private String dealPhoto;

    /** 处理意见**/
    private String dealDescription;

    /** 上报事件照片 url**/
    private List<String> photoUrls;

    /** 出路事件照片 url **/
    private List<String> dealPhotoUrls;



    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setProblemName(String problemName)
    {
        this.problemName = problemName;
    }

    public String getProblemName()
    {
        return problemName;
    }
    public void setProblemDescription(String problemDescription)
    {
        this.problemDescription = problemDescription;
    }

    public String getProblemDescription()
    {
        return problemDescription;
    }
    public void setDealTimeLimited(Date dealTimeLimited)
    {
        this.dealTimeLimited = dealTimeLimited;
    }

    public String getProblemAddress() {
        return problemAddress;
    }

    public void setProblemAddress(String problemAddress) {
        this.problemAddress = problemAddress;
    }

    public Date getDealTimeLimited()
    {
        return dealTimeLimited;
    }
    public void setDealDept(String dealDept)
    {
        this.dealDept = dealDept;
    }

    public String getDealDept()
    {
        return dealDept;
    }
    public void setAppendix(String appendix)
    {
        this.appendix = appendix;
    }

    public String getAppendix()
    {
        return appendix;
    }
    public void setFillDate(Date fillDate)
    {
        this.fillDate = fillDate;
    }

    public Date getFillDate()
    {
        return fillDate;
    }
    public void setForestId(Long forestId)
    {
        this.forestId = forestId;
    }

    public Long getForestId()
    {
        return forestId;
    }

    public String getDealStatus() {
        return dealStatus;
    }

    public void setDealStatus(String dealStatus) {
        this.dealStatus = dealStatus;
    }

    public List<String> getPhotoUrls() {
        return photoUrls;
    }

    public void setPhotoUrls(List<String> photoUrls) {
        this.photoUrls = photoUrls;
    }

    public String getDealDay() {
        return dealDay;
    }

    public void setDealDay(String dealDay) {
        this.dealDay = dealDay;
    }

    public String getProblemType() {
        return problemType;
    }

    public void setProblemType(String problemType) {
        this.problemType = problemType;
    }


    public Date getDealFinishTime() {
        return dealFinishTime;
    }

    public void setDealFinishTime(Date dealFinishTime) {
        this.dealFinishTime = dealFinishTime;
    }

    public String getDealInTimeState() {
        return dealInTimeState;
    }

    public void setDealInTimeState(String dealInTimeState) {
        this.dealInTimeState = dealInTimeState;
    }

    public String getDealPhoto() {
        return dealPhoto;
    }

    public void setDealPhoto(String dealPhoto) {
        this.dealPhoto = dealPhoto;
    }

    public String getDealDescription() {
        return dealDescription;
    }

    public void setDealDescription(String dealDescription) {
        this.dealDescription = dealDescription;
    }

    public List<String> getDealPhotoUrls() {
        return dealPhotoUrls;
    }

    public void setDealPhotoUrls(List<String> dealPhotoUrls) {
        this.dealPhotoUrls = dealPhotoUrls;
    }

    @Override
    public String toString() {
        return "ShcyAfforestCase{" +
                "id=" + id +
                ", problemName='" + problemName + '\'' +
                ", problemDescription='" + problemDescription + '\'' +
                ", problemAddress='" + problemAddress + '\'' +
                ", dealTimeLimited=" + dealTimeLimited +
                ", dealDept='" + dealDept + '\'' +
                ", appendix='" + appendix + '\'' +
                ", fillDate=" + fillDate +
                ", forestId=" + forestId +
                ", dealStatus='" + dealStatus + '\'' +
                ", dealDay='" + dealDay + '\'' +
                ", problemType='" + problemType + '\'' +
                ", photoUrls=" + photoUrls +
                '}';
    }
}
