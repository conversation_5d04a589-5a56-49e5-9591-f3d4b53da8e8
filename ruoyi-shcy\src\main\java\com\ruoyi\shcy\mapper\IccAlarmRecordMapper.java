package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.IccAlarmRecord;

import java.util.List;

/**
 * ICC报警记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-14
 */
public interface IccAlarmRecordMapper 
{
    /**
     * 查询ICC报警记录
     * 
     * @param id ICC报警记录主键
     * @return ICC报警记录
     */
    public IccAlarmRecord selectIccAlarmRecordById(Long id);

    /**
     * 查询ICC报警记录列表
     * 
     * @param iccAlarmRecord ICC报警记录
     * @return ICC报警记录集合
     */
    public List<IccAlarmRecord> selectIccAlarmRecordList(IccAlarmRecord iccAlarmRecord);

    /**
     * 新增ICC报警记录
     * 
     * @param iccAlarmRecord ICC报警记录
     * @return 结果
     */
    public int insertIccAlarmRecord(IccAlarmRecord iccAlarmRecord);

    /**
     * 修改ICC报警记录
     * 
     * @param iccAlarmRecord ICC报警记录
     * @return 结果
     */
    public int updateIccAlarmRecord(IccAlarmRecord iccAlarmRecord);

    /**
     * 删除ICC报警记录
     * 
     * @param id ICC报警记录主键
     * @return 结果
     */
    public int deleteIccAlarmRecordById(Long id);

    /**
     * 批量删除ICC报警记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteIccAlarmRecordByIds(Long[] ids);

    List<IccAlarmRecord> selectIccAlarmRecordUnprocessedList(IccAlarmRecord iccAlarmRecord);

    List<IccAlarmRecord> selectIccAlarmRecordProcessingList(IccAlarmRecord iccAlarmRecord);

    List<IccAlarmRecord> selectIccAlarmRecordProcessedList(IccAlarmRecord iccAlarmRecord);

    int updateIccAlarmRecordStatus(IccAlarmRecord iccAlarmRecord);

    List<IccAlarmRecord> selectIccAlarmRecordUnprocessedAndProcessingList(IccAlarmRecord iccAlarmRecord);

    IccAlarmRecord selectIccAlarmRecordByCarRecordId(Long carRecordId);

    IccAlarmRecord selectIccAlarmRecordByAlarmRecordId(Long alarmRecordId);

    List<IccAlarmRecord> selectIccAlarmRecordByIds(Long[] alarmRecordIds);
}
