package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.AppealClassify;
import com.ruoyi.shcy.mapper.AppealClassifyMapper;
import com.ruoyi.shcy.service.IAppealClassifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 诉求归类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-03
 */
@Service
public class AppealClassifyServiceImpl implements IAppealClassifyService 
{
    @Autowired
    private AppealClassifyMapper appealClassifyMapper;

    /**
     * 查询诉求归类
     * 
     * @param id 诉求归类主键
     * @return 诉求归类
     */
    @Override
    public AppealClassify selectAppealClassifyById(Long id)
    {
        return appealClassifyMapper.selectAppealClassifyById(id);
    }

    /**
     * 查询诉求归类列表
     * 
     * @param appealClassify 诉求归类
     * @return 诉求归类
     */
    @Override
    public List<AppealClassify> selectAppealClassifyList(AppealClassify appealClassify)
    {
        return appealClassifyMapper.selectAppealClassifyList(appealClassify);
    }

    /**
     * 新增诉求归类
     * 
     * @param appealClassify 诉求归类
     * @return 结果
     */
    @Override
    public int insertAppealClassify(AppealClassify appealClassify)
    {
        return appealClassifyMapper.insertAppealClassify(appealClassify);
    }

    /**
     * 修改诉求归类
     * 
     * @param appealClassify 诉求归类
     * @return 结果
     */
    @Override
    public int updateAppealClassify(AppealClassify appealClassify)
    {
        return appealClassifyMapper.updateAppealClassify(appealClassify);
    }

    /**
     * 批量删除诉求归类
     * 
     * @param ids 需要删除的诉求归类主键
     * @return 结果
     */
    @Override
    public int deleteAppealClassifyByIds(Long[] ids)
    {
        return appealClassifyMapper.deleteAppealClassifyByIds(ids);
    }

    /**
     * 删除诉求归类信息
     * 
     * @param id 诉求归类主键
     * @return 结果
     */
    @Override
    public int deleteAppealClassifyById(Long id)
    {
        return appealClassifyMapper.deleteAppealClassifyById(id);
    }
}
