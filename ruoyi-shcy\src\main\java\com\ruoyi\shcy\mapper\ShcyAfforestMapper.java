package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyAfforest;

/**
 * 绿化信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-05-19
 */
public interface ShcyAfforestMapper 
{
    /**
     * 查询绿化信息
     * 
     * @param id 绿化信息主键
     * @return 绿化信息
     */
    public ShcyAfforest selectShcyAfforestById(Long id);

    /**
     * 查询绿化信息列表
     * 
     * @param shcyAfforest 绿化信息
     * @return 绿化信息集合
     */
    public List<ShcyAfforest> selectShcyAfforestList(ShcyAfforest shcyAfforest);

    /**
     * 新增绿化信息
     * 
     * @param shcyAfforest 绿化信息
     * @return 结果
     */
    public int insertShcyAfforest(ShcyAfforest shcyAfforest);

    /**
     * 修改绿化信息
     * 
     * @param shcyAfforest 绿化信息
     * @return 结果
     */
    public int updateShcyAfforest(ShcyAfforest shcyAfforest);

    /**
     * 删除绿化信息
     * 
     * @param id 绿化信息主键
     * @return 结果
     */
    public int deleteShcyAfforestById(Long id);

    /**
     * 批量删除绿化信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyAfforestByIds(Long[] ids);
}
