package com.ruoyi.shcy.service.impl;

import cn.hutool.core.date.DateTime;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.domain.CheckRecord;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.ShopCheckLog;
import com.ruoyi.shcy.domain.SupervisorLog;
import com.ruoyi.shcy.domain.vo.CategoryDetailVo;
import com.ruoyi.shcy.domain.vo.CommitteeDetailVo;
import com.ruoyi.shcy.domain.vo.ShopDetailVo;
import com.ruoyi.shcy.mapper.ShopMapper;
import com.ruoyi.shcy.mapper.SupervisorLogMapper;
import com.ruoyi.shcy.service.IShopService;
import com.ruoyi.system.service.impl.SysUserServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 店铺信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
@Service
public class ShopServiceImpl implements IShopService
{

    private static final Logger log = LoggerFactory.getLogger(SysUserServiceImpl.class);
    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private SupervisorLogMapper supervisorLogMapper;

    /**
     * 查询店铺信息
     *
     * @param id 店铺信息主键
     * @return 店铺信息
     */
    @Override
    public Shop selectShopById(Long id)
    {
        return shopMapper.selectShopById(id);
    }

    /**
     * 查询店铺信息列表
     *
     * @param shop 店铺信息
     * @return 店铺信息
     */
    @Override
    public List<Shop> selectShopList(Shop shop)
    {
        return shopMapper.selectShopList(shop);
    }

    /**
     * 大屏接口统计店铺数量
     */
    @Override
    public int selectShopCount() {
        return shopMapper.selectShopCount();
    }

    /**
     * 根据统一信用社会信用代码查询
     *
     * @param creditCode
     */
    @Override
    public Shop selectShopByCreditCode(String creditCode) {
        return   shopMapper.selectShopByCreditCode(creditCode);
    }


    /**
     * 根据商铺的统一信用代码修改店铺的监管状态
     *
     * @param shopCreditCode
     **/
    @Override
    public int updateShopSupervisiorStatus(String shopCreditCode) {
        return shopMapper.updateShopSupervisiorStatus(shopCreditCode);
    }

    /**
     * 查询店铺信息列表（正常开业的）
     *
     * @param shop 店铺信息
     * @return 店铺信息集合
     */
    @Override
    public List<Shop> selectShopListWithNoram(Shop shop) {
        return  shopMapper.selectShopListWithNoram(shop);
    }


    /**
     * 城管巡检 已检查列表（是否存在跨门经营，门责制是否落实）
     *
     * @param shopCheckLog
     **/
    @Override
    public List<Shop> selectCheckedShopListWithNormalCg(ShopCheckLog shopCheckLog) {
        return  shopMapper.selectCheckedShopListWithNormalCg(shopCheckLog);
    }

    /**
     * 导入商铺数据
     *
     * @param shopList 商铺数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importShop(List<Shop> shopList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(shopList) || shopList.size() == 0)
        {
            throw new ServiceException("导入商铺数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (Shop shop : shopList)
        {
            try
            {
                // 验证是否存在这个商铺
                Shop u = shopMapper.selectShopByCreditCode(shop.getShopCreditCode());
                if (StringUtils.isNull(u)){
                    this.insertShop(shop);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + shop.getShopName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                   Shop checkShop =  shopMapper.selectShopByCreditCode(shop.getShopCreditCode());
                   if(StringUtils.isNotEmpty(checkShop.getShopCreditCode())){
                       this.updateShopSupervisiorStatus(checkShop.getShopCreditCode());   //更新店铺的监管状态--->重点监管

                       //向重点监管日志表中插入数据
                       SupervisorLog supervisorLog = new SupervisorLog();
                       supervisorLog.setShopId(checkShop.getId());
                       supervisorLog.setSupervisorStatus("重点监管");
                       supervisorLog.setShopName(checkShop.getShopName());
                       supervisorLog.setCreateTime(new DateTime());
                       supervisorLog.setCreateDate(DateUtils.getNowDate());
                       supervisorLogMapper.insertSupervisorLog(supervisorLog);
                   }




                    successNum++;
                    successMsg.append("<br/>" + successNum + "、账号 " + shop.getShopName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、账号 " + shop.getShopName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + shop.getShopName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }


    /**
     * 新增店铺信息
     *
     * @param shop 店铺信息
     * @return 结果
     */
    @Override
    public int insertShop(Shop shop)
    {
        shop.setCreateTime(DateUtils.getNowDate());
        shop.setShopStatus("未检查");
        // shop.setIsOpen("营业");
        shop.setSupervisionStatus("正常监管");
        return shopMapper.insertShop(shop);

    }

    /**
     * 修改店铺信息
     *
     * @param shop 店铺信息
     * @return 结果
     */
    @Override
    public int updateShop(Shop shop)
    {
        shop.setUpdateTime(DateUtils.getNowDate());
        return shopMapper.updateShop(shop);
    }


    /**
     * 每个月修改一次店铺检查状态
     *
     * @param id
     **/
    @Override
    public int updateCheckStatus(Long id) {
        return shopMapper.updateCheckStatus(id);
    }

    /**
     * 批量删除店铺信息
     *
     * @param ids 需要删除的店铺信息主键
     * @return 结果
     */
    @Override
    public int deleteShopByIds(Long[] ids)
    {
        return shopMapper.deleteShopByIds(ids);
    }

    /**
     * 删除店铺信息信息
     *
     * @param id 店铺信息主键
     * @return 结果
     */
    @Override
    public int deleteShopById(Long id)
    {
        return shopMapper.deleteShopById(id);
    }


    /**
     * 查询出来所有的商铺
     *
     * @param shop
     */
    @Override
    public List<Shop> selectAllShops(Shop shop) {
        return  shopMapper.selectAllShops(shop);
    }


    /**
     * 每天夜里12点修改店铺的巡查状态
     *
     * @param shopDeparmentId
     **/
    @Override
    public int updateShopCheckStatus(Long shopDeparmentId) {
        return shopMapper.updateShopCheckStatus(shopDeparmentId);
    }


    /**
     * 查询 居委会下店铺的数量
     *
     * @param shop
     */
    @Override
    public List<Shop> selectCommitteeShopNum(Shop shop) {
        return  shopMapper.selectCommitteeShopNum(shop);
    }


    /**
     * 查询对应大类下管理的商铺信息
     *
     * @param shopCategory
     */
    @Override
    public List<CategoryDetailVo> selectCategoryShopNum(String shopCategory) {
        return  shopMapper.selectCategoryShopNum(shopCategory);
    }

    /**
     * 各个大类下巡查店铺的数量
     *
     * @param shop
     *
     */
    @Override
    public List<CategoryDetailVo> selectCategoryCheckedShopNum(Shop shop) {
        return shopMapper.selectCategoryCheckedShopNum(shop);
    }

    /**
     * 查询店铺总数
     *
     * @param shop
     */
    @Override
    public List<ShopDetailVo> selectShopTotal(Shop shop) {
        return  shopMapper.selectShopTotal(shop);
    }

    /**
     * 查询 今日巡查店铺总数
     *
     * @param shopCheckLog
     */
    @Override
    public List<ShopDetailVo> selectTodayCheckShopTotal(ShopCheckLog shopCheckLog) {
        return  shopMapper.selectTodayCheckShopTotal(shopCheckLog);
    }

    /**
     * 查询今日巡查店铺正常数量
     *
     * @param shopCheckLog
     */
    @Override
    public List<ShopDetailVo> selectTodayCheckShopNomalNum(ShopCheckLog shopCheckLog) {
        return shopMapper.selectTodayCheckShopNomalNum(shopCheckLog);
    }

    /**
     * 查询今日巡查店铺学业数量
     *
     * @param shopCheckLog
     */
    @Override
    public List<ShopDetailVo> selectTodayCheckShopRestNum(ShopCheckLog shopCheckLog) {
        return  shopMapper.selectTodayCheckShopRestNum(shopCheckLog);
    }

    /**
     * 查询今日店铺关停数量
     *
     * @param shop
     */
    @Override
    public List<ShopDetailVo> selectTodayCheckShopCloseNum(Shop shop) {
        return  shopMapper.selectTodayCheckShopCloseNum(shop);
    }


    /**
     * 查询今日店铺重点监管数量
     *
     * @param shop
     **/
    @Override
    public List<ShopDetailVo> selectSupervisorShop(Shop shop) {
        return shopMapper.selectSupervisorShop(shop);
    }

    /**
     * 查询通告类型为 谈话的店铺
     *
     * @param shop
     **/
    @Override
    public List<ShopDetailVo> selectTalkShop(Shop shop) {
        return  shopMapper.selectTalkShop(shop);
    }

    /**
     * 查询通告类型为 督办的店铺
     *
     * @param shop
     **/
    @Override
    public List<ShopDetailVo> selectHandleShop(Shop shop) {
        return shopMapper.selectHandleShop(shop);
    }

    /**
     * 查询通告类型为 黄牌的店铺
     *
     * @param shop
     **/
    @Override
    public List<ShopDetailVo> selectYellowCardShop(Shop shop) {
        return shopMapper.selectYellowCardShop(shop);
    }

    /**
     * 各个居委会管理的店铺数量
     *
     * @param sysDept
     */
    @Override
    public List<CommitteeDetailVo> selectCommitteeCheckShopTotal(SysDept sysDept) {
        return shopMapper.selectCommitteeCheckShopTotal(sysDept);
    }


    /**
     * 居委会巡查商铺数量
     *
     *
     * @param shop
     */
    @Override
    public List<CommitteeDetailVo> selectCommitteeCheckedShopNum(Shop shop) {
        return shopMapper.selectCommitteeCheckedShopNum(shop);
    }



    //*****************************************************************根据店铺开业状态去查询********************************************
    /**
     * 查询开业正常商铺数量
     *
     * @param shop
     */
    @Override
    public List<Shop> selectOpenNormalShopNum(Shop shop) {
        return  shopMapper.selectOpenNormalShopNum(shop);
    }

    /**
     * 大屏查询开业正常商铺数量
     */
    @Override
    public int selectOpenNomalShopCount() {
        return shopMapper.selectOpenNomalShopCount();
    }

    /**
     * 查询歇业商铺数量
     *
     * @param shop
     */
    @Override
    public List<Shop> selectOpenRestShopNum(Shop shop) {
        return  shopMapper.selectOpenRestShopNum(shop);
    }

    /**
     * 大屏查询歇业商铺数量
     */
    @Override
    public int selectOpenRestShopCount() {
        return shopMapper.selectOpenRestShopCount();
    }

    /**
     * 查询关停商铺数量
     *
     * @param shop
     */
    @Override
    public List<Shop> selectOpenStopShopNum(Shop shop) {
        return  shopMapper.selectOpenStopShopNum(shop);
    }

    /**
     * 大屏查询关停商铺数量
     */
    @Override
    public int selectOpenStopShopCount() {
        return shopMapper.selectOpenStopShopCount();
    }

    /**
     * 查询未巡查商铺信息
     *
     * @param checkRecord
     **/
    @Override
    public List<Shop> selectNoCheckedShopList(CheckRecord checkRecord) {
        return shopMapper.selectNoCheckedShopList(checkRecord);
    }

    @Override
    public List<Shop> selectCheckShopList() {
        return shopMapper.selectCheckShopList();
    }

    /**
     * 查询对应街长路段名称的商铺信息
     *
     * @param road
     **/
    @Override
    public List<Shop> selectShopByChiefRoad(String road) {
        return shopMapper.selectShopByChiefRoad(road);
    }

    @Override
    public List<Shop> selectAllYyShops() {
        return shopMapper.selectAllYyShops();
    }

    @Override
    public List<Shop> selectAllShutdownShops() {
        return shopMapper.selectAllShutdownShops();
    }
}
