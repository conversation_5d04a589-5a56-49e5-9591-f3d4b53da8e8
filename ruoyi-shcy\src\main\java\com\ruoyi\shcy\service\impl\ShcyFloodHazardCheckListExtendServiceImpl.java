package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.ShcyFloodHazardType;
import com.ruoyi.shcy.mapper.ShcyFloodHazardTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ShcyFloodHazardCheckListExtendMapper;
import com.ruoyi.shcy.domain.ShcyFloodHazardCheckListExtend;
import com.ruoyi.shcy.service.IShcyFloodHazardCheckListExtendService;

/**
 * 隐患排查子表Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-26
 */
@Service
public class ShcyFloodHazardCheckListExtendServiceImpl implements IShcyFloodHazardCheckListExtendService
{
    @Autowired
    private ShcyFloodHazardCheckListExtendMapper shcyFloodHazardCheckListExtendMapper;

    @Autowired
    private ShcyFloodHazardTypeMapper shcyFloodHazardTypeMapper;

    /**
     * 查询隐患排查子表
     *
     * @param id 隐患排查子表主键
     * @return 隐患排查子表
     */
    @Override
    public ShcyFloodHazardCheckListExtend selectShcyFloodHazardCheckListExtendById(Long id)
    {
        ShcyFloodHazardCheckListExtend theShcyFloodHazardCheckListExtend=shcyFloodHazardCheckListExtendMapper.selectShcyFloodHazardCheckListExtendById(id);
        if(theShcyFloodHazardCheckListExtend.getCheckItemParent() != null)
        {
            ShcyFloodHazardType theType=shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeById(theShcyFloodHazardCheckListExtend.getCheckItemParent());
            theShcyFloodHazardCheckListExtend.setCheckItemParentName(theType.getName());
        }
        if(theShcyFloodHazardCheckListExtend.getCheckItemChild() != null)
        {
            ShcyFloodHazardType theType=shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeById(theShcyFloodHazardCheckListExtend.getCheckItemChild());
            theShcyFloodHazardCheckListExtend.setCheckItemChildName(theType.getName());
        }
        return  theShcyFloodHazardCheckListExtend;
    }

    /**
     * 查询隐患排查子表列表
     *
     * @param shcyFloodHazardCheckListExtend 隐患排查子表
     * @return 隐患排查子表
     */
    @Override
    public List<ShcyFloodHazardCheckListExtend> selectShcyFloodHazardCheckListExtendList(ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend)
    {
        List<ShcyFloodHazardCheckListExtend> list= shcyFloodHazardCheckListExtendMapper.selectShcyFloodHazardCheckListExtendList(shcyFloodHazardCheckListExtend);
        for(ShcyFloodHazardCheckListExtend theShcyFloodHazardCheckListExtend:list)
        {
            if(theShcyFloodHazardCheckListExtend.getCheckItemParent() != null)
            {
                ShcyFloodHazardType theType=shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeById(theShcyFloodHazardCheckListExtend.getCheckItemParent());
                theShcyFloodHazardCheckListExtend.setCheckItemParentName(theType.getName());
            }
            if(theShcyFloodHazardCheckListExtend.getCheckItemChild() != null)
            {
                ShcyFloodHazardType theType=shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeById(theShcyFloodHazardCheckListExtend.getCheckItemChild());
                theShcyFloodHazardCheckListExtend.setCheckItemChildName(theType.getName());
            }
        }
        return list;
    }

    /**
     * 新增隐患排查子表
     *
     * @param shcyFloodHazardCheckListExtend 隐患排查子表
     * @return 结果
     */
    @Override
    public int insertShcyFloodHazardCheckListExtend(ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend)
    {
        shcyFloodHazardCheckListExtend.setCreateTime(DateUtils.getNowDate());
        return shcyFloodHazardCheckListExtendMapper.insertShcyFloodHazardCheckListExtend(shcyFloodHazardCheckListExtend);
    }

    /**
     * 修改隐患排查子表
     *
     * @param shcyFloodHazardCheckListExtend 隐患排查子表
     * @return 结果
     */
    @Override
    public int updateShcyFloodHazardCheckListExtend(ShcyFloodHazardCheckListExtend shcyFloodHazardCheckListExtend)
    {
        shcyFloodHazardCheckListExtend.setUpdateTime(DateUtils.getNowDate());
        return shcyFloodHazardCheckListExtendMapper.updateShcyFloodHazardCheckListExtend(shcyFloodHazardCheckListExtend);
    }

    /**
     * 批量删除隐患排查子表
     *
     * @param ids 需要删除的隐患排查子表主键
     * @return 结果
     */
    @Override
    public int deleteShcyFloodHazardCheckListExtendByIds(Long[] ids)
    {
        return shcyFloodHazardCheckListExtendMapper.deleteShcyFloodHazardCheckListExtendByIds(ids);
    }

    /**
     * 删除隐患排查子表信息
     *
     * @param id 隐患排查子表主键
     * @return 结果
     */
    @Override
    public int deleteShcyFloodHazardCheckListExtendById(Long id)
    {
        return shcyFloodHazardCheckListExtendMapper.deleteShcyFloodHazardCheckListExtendById(id);
    }
}
