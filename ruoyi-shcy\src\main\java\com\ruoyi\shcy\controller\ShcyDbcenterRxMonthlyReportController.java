package com.ruoyi.shcy.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthly;
import com.ruoyi.shcy.domain.ShcyDbcenterRxMonthlyExtend;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.domain.vo.DbCenterRxJmqVO;
import com.ruoyi.shcy.domain.vo.RxReportCfgdVO;
import com.ruoyi.shcy.domain.vo.RxReportVO;
import com.ruoyi.shcy.service.IShcyDbcenterRxMonthlyExtendService;
import com.ruoyi.shcy.service.IShcyDbcenterRxMonthlyService;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 12345热线分析Controller
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
@RestController
@RequestMapping("/shcy/dbcenterRxMonthlyReport")
public class ShcyDbcenterRxMonthlyReportController extends BaseController
{
    @Autowired
    private ITaskDbcenterService iTaskDbcenterService;

    @GetMapping("/getZlList")
    public  RxReportVO getZlList(TaskDbcenter taskDbcenter)
    {
        String dateTimeString2 = taskDbcenter.getParams().get("beginTime").toString();
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDate date2 = LocalDate.parse(dateTimeString2, dateFormatter);
        int month = date2.getMonthValue();
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<TaskDbcenter> list=iTaskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        RxReportVO theR=new RxReportVO();
        theR.setNum(month+"-"+list.size());

        //上个月数量

        String dateTimeString = taskDbcenter.getParams().get("beginTime").toString();
        LocalDate date = LocalDate.parse(dateTimeString, dateFormatter);
        LocalDate firstDayOfLastMonth = getFirstDayOfLastMonth(date);
        LocalDate lastDayOfLastMonth = getLastDayOfLastMonth(date);
        taskDbcenter.getParams().put("beginTime",firstDayOfLastMonth+" 00:00:00");
        taskDbcenter.getParams().put("endTime",lastDayOfLastMonth+" 23:59:59");
        List<TaskDbcenter> list1=iTaskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        int month1 = firstDayOfLastMonth.getMonthValue();
        theR.setLastNum(month1+"-"+list1.size());

        //上上个月数量
        String dateTimeString1 = taskDbcenter.getParams().get("beginTime").toString();
        LocalDate date1 = LocalDate.parse(dateTimeString1, dateFormatter);
        LocalDate firstDayOfLastMonth1 = getFirstDayOfLastMonth(date1);
        LocalDate lastDayOfLastMonth1 = getLastDayOfLastMonth(date1);
        taskDbcenter.getParams().put("beginTime",firstDayOfLastMonth1+" 00:00:00");
        taskDbcenter.getParams().put("endTime",lastDayOfLastMonth1+" 23:59:59");
        List<TaskDbcenter> list2=iTaskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        int month2 = firstDayOfLastMonth1.getMonthValue();
        theR.setLalastNum(month2+"-"+list2.size());
        return theR;
    }

    @GetMapping("/getDlList")
    public  List<RxReportVO> getDlList(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<RxReportVO> list=iTaskDbcenterService.selectListByParentappealclassification(taskDbcenter);
        return list.subList(0,5);
    }


    @GetMapping("/getZdwtlList")
    public  List<RxReportVO> getZdwtlList(TaskDbcenter taskDbcenter)
    {
        List<RxReportVO> list1=new ArrayList<>();

        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        String[] parentappealclassifications={"消防安全","违法建筑","求助类","防汛","讨薪","违规生产"};
        taskDbcenter.getParams().put("parentappealclassifications",parentappealclassifications);
        List<RxReportVO> list=iTaskDbcenterService.selectListByParentappealclassification(taskDbcenter);
        for(int i= 0;i<parentappealclassifications.length;i++)
        {
            RxReportVO theRe=new RxReportVO();
            theRe.setParentappealclassification(parentappealclassifications[i]);
            String dept=parentappealclassifications[i];
            List<RxReportVO> th2 = list.stream().filter(item -> dept.equals(item.getParentappealclassification())).collect(Collectors.toList());
            if(th2.size() == 0)
            {
                theRe.setNum("0");
            }
            else
            {
                theRe.setNum(th2.get(0).getNum());
            }
            list1.add(theRe);
        }
        return list1;
    }


    @GetMapping("/getXlList")
    public  List<RxReportVO> getXlList(TaskDbcenter taskDbcenter)
    {
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<RxReportVO> list=iTaskDbcenterService.selectListByParentappealclassification(taskDbcenter);//前五个
        for(RxReportVO theRxReportVO:list.subList(0,5))
        {
            taskDbcenter.setParentappealclassification(theRxReportVO.getParentappealclassification());
            List<RxReportVO> list1=iTaskDbcenterService.selectXlByParentappealclassification(taskDbcenter);//前六个
            theRxReportVO.setXlList(list1);
            for(RxReportVO item:list1)
            {
                taskDbcenter.setAppealclassification(item.getAppealclassification());
                List<RxReportVO> list2=iTaskDbcenterService.selectJmqByParentappealclassification(taskDbcenter);//前三个
                if(list2.size()>3)
                {
                    item.setXlList(list2.subList(0,3));
                }
                else {
                    item.setXlList(list2);
                }
                item.setXlAllList(list2);
            }
        }

        return list.subList(0,5);
    }

    @GetMapping("/getCfList")
    public  List<RxReportVO> getCfList(TaskDbcenter taskDbcenter) {
        List<RxReportVO> list = new ArrayList<RxReportVO>();
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        taskDbcenter.setIsduplicate("是");
        List<TaskDbcenter> list1 = iTaskDbcenterService.selectTaskDbcenterListDesc(taskDbcenter);
        HashMap<String, TaskDbcenter> map = new HashMap<>();
        HashMap<String, TaskDbcenter> map1 = new HashMap<>();
        List<RxReportCfgdVO> voList=new ArrayList<>();
        for(TaskDbcenter theTaskDbcenter : list1)
        {
            TaskDbcenter taskDbcenter1=new TaskDbcenter();
            taskDbcenter1.setRelatedhotlinesn(theTaskDbcenter.getRelatedhotlinesn());
            taskDbcenter1.setInfosourcename("12345上报");
            taskDbcenter1.getParams().put("cfFlag",true);
            List<TaskDbcenter> array= iTaskDbcenterService.selectTaskDbcenterRxList(taskDbcenter1);
            StringBuilder sb=new StringBuilder();
            for(TaskDbcenter dbcenter:array)
            {
                if(sb.length()>0)
                {
                    sb= new StringBuilder(sb + ",");
                }
                sb.append(dbcenter.getHotlinesn());
            }
            RxReportCfgdVO theRxReportCfgdVO=new RxReportCfgdVO();
            theRxReportCfgdVO.setCbbm(array.get(0).getSubexecutedeptnameMh());
            theRxReportCfgdVO.setSqzl(array.get(0).getAppealclassification());
            theRxReportCfgdVO.setCfgdh(sb.toString());
            theRxReportCfgdVO.setBqgdh(theTaskDbcenter.getHotlinesn());
            theRxReportCfgdVO.setAddress(array.get(0).getAddress());
            theRxReportCfgdVO.setSqr(array.get(0).getReporter());
            theRxReportCfgdVO.setSl(String.valueOf(array.size()));
            voList.add(theRxReportCfgdVO);
        }
        /**
         *  相反的排序规则
         */
        Collections.sort(voList, Comparator.comparing(RxReportCfgdVO::getSl).reversed());


        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String dateTimeString = taskDbcenter.getParams().get("beginTime").toString();
        LocalDate date = LocalDate.parse(dateTimeString, dateFormatter);
        LocalDate firstDayOfLastMonth = getFirstDayOfLastMonth(date);
        LocalDate lastDayOfLastMonth = getLastDayOfLastMonth(date);
        taskDbcenter.getParams().put("beginTime",firstDayOfLastMonth+" 00:00:00");
        taskDbcenter.getParams().put("endTime",lastDayOfLastMonth+" 23:59:59");
        List<TaskDbcenter> list2=iTaskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        RxReportVO theRxReportVO=new RxReportVO();
        theRxReportVO.setNum(String.valueOf(list1.size()));
        theRxReportVO.setLastNum(String.valueOf(list2.size()));
        theRxReportVO.setGbflNum(String.valueOf(map.size()));
        theRxReportVO.setCfgdList(voList);
        list.add(theRxReportVO);
        return list;
    }


    @GetMapping("/getBmyList")
    public  List<RxReportVO> getBmyList(TaskDbcenter taskDbcenter) throws ParseException {
        SimpleDateFormat format1 = new SimpleDateFormat("yyyy-MM");
        Date date=format1.parse(taskDbcenter.getParams().get("beginTime").toString());
        taskDbcenter.setEvaluationmonth(format1.format(date));
        taskDbcenter.getParams().put("beginTime",null);
        taskDbcenter.getParams().put("endTime",null);
        taskDbcenter.setInfosourcename("12345上报");
        taskDbcenter.getParams().put("cfFlag",true);
        List<RxReportVO> list = new ArrayList<RxReportVO>();
        taskDbcenter.setSatisfaction("不满意");
        List<TaskDbcenter> allRelateds = iTaskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        List<RxReportVO> li=iTaskDbcenterService.selectTaskDbcenterListGroupByResidentialarea(taskDbcenter);
        for(RxReportVO theRxReportVO:li)
        {
            TaskDbcenter taskDbcenter1=new TaskDbcenter();
            taskDbcenter1.setEvaluationmonth(format1.format(date));
            taskDbcenter1.setSatisfaction("不满意");
            taskDbcenter1.setInfosourcename("12345上报");
            taskDbcenter1.getParams().put("cfFlag",true);
            taskDbcenter1.setParentappealclassification(theRxReportVO.getParentappealclassification());
            taskDbcenter1.setAppealclassification(theRxReportVO.getAppealclassification());
            taskDbcenter1.setResidentialarea(theRxReportVO.getResidentialarea());
            taskDbcenter1.setSubexecutedeptnameMh(theRxReportVO.getSubexecutedeptnameMh());
            List<TaskDbcenter> array= iTaskDbcenterService.selectTaskDbcenterRxList(taskDbcenter1);
            StringBuilder sb=new StringBuilder();
            for(TaskDbcenter theTaskDbcenter:array)
            {
                if(sb.length()>0)
                {
                    sb= new StringBuilder(sb + ",");
                }
                sb.append(theTaskDbcenter.getHotlinesn());
            }
            theRxReportVO.setHotlinesn(sb.toString());
        }
        HashMap<String, TaskDbcenter> map = new HashMap<>();
        for(TaskDbcenter theTaskDbcenter:allRelateds)
        {
           if(StringUtils.isNotEmpty(theTaskDbcenter.getParentappealclassification()))
           {
               map.put(theTaskDbcenter.getParentappealclassification(),theTaskDbcenter);
           }
        }
        StringBuilder sb=new StringBuilder();
        for(String key: map.keySet())
        {
            taskDbcenter.setParentappealclassification(key);
            List<TaskDbcenter> allRelateds1 = iTaskDbcenterService.selectTaskDbcenterList(taskDbcenter);
            if(sb.length()>0)
            {
                sb= new StringBuilder(sb + ",");
            }
            sb.append(key+"-"+allRelateds1.size());
        }

        taskDbcenter.setParentappealclassification(null);
        String curMonthDate =taskDbcenter.getEvaluationmonth() + "-01"; // 假设输入的是月份的第一天
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

        String month = null;
        try {
            Date transferDate = format.parse(curMonthDate); // 解析日期字符串为Date对象
            Calendar calendar = Calendar.getInstance(); // 创建Calendar实例
            calendar.setTime(transferDate); // 设置Calendar的时间为解析后的日期
            calendar.add(Calendar.MONTH, -1); // 减去一个月
            month = format1.format(calendar.getTime()); // 获取上一个月的日期并格式化
        } catch (Exception e) {
            e.printStackTrace(); // 处理异常（在实际应用中应该有更详细的错误处理）
        }
        taskDbcenter.setEvaluationmonth(month);
        List<TaskDbcenter> list2=iTaskDbcenterService.selectTaskDbcenterList(taskDbcenter);
        RxReportVO theRxReportVO=new RxReportVO();
        theRxReportVO.setNum(String.valueOf(allRelateds.size()));
        theRxReportVO.setLastNum(String.valueOf(list2.size()));
        theRxReportVO.setParentappealclassification(sb.toString());
        theRxReportVO.setXlList(li);
        list.add(theRxReportVO);
        return list;
    }

    public static LocalDate getFirstDayOfLastMonth(LocalDate date) {
        return date.with(TemporalAdjusters.firstDayOfMonth()).minusMonths(1);
    }

    public static LocalDate getLastDayOfLastMonth(LocalDate date) {
        return date.with(TemporalAdjusters.firstDayOfMonth()).minusDays(1);
    }

}
