package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.Employees;
import com.ruoyi.shcy.domain.Shop;

/**
 * 从业人员基本信息Service接口
 *
 * <AUTHOR>
 * @date 2022-10-27
 */
public interface IEmployeesService
{
    /**
     * 查询从业人员基本信息
     *
     * @param id 从业人员基本信息主键
     * @return 从业人员基本信息
     */
    public Employees selectEmployeesById(Long id);

    /**
     * 查询从业人员基本信息列表
     *
     * @param employees 从业人员基本信息
     * @return 从业人员基本信息集合
     */
    public List<Employees> selectEmployeesList(Employees employees);



//    /**
//     * 导入用户数据
//     *
//     * @param employeeList 用户数据列表
//     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
//     * @param operName 操作用户
//     * @return 结果
//     */
//    public String importEmployee(List<Employees> employeeList, Boolean isUpdateSupport, String operName);



    /**
     * 新增从业人员基本信息
     *
     * @param employees 从业人员基本信息
     * @return 结果
     */
    public int insertEmployees(Employees employees);

    /**
     * 修改从业人员基本信息
     *
     * @param employees 从业人员基本信息
     * @return 结果
     */
    public int updateEmployees(Employees employees);

    /**
     * 批量删除从业人员基本信息
     *
     * @param ids 需要删除的从业人员基本信息主键集合
     * @return 结果
     */
    public int deleteEmployeesByIds(Long[] ids);

    /**
     * 删除从业人员基本信息信息
     *
     * @param id 从业人员基本信息主键
     * @return 结果
     */
    public int deleteEmployeesById(Long id);
}
