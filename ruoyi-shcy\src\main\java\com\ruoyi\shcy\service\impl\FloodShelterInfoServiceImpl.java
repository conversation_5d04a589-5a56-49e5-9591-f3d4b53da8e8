package com.ruoyi.shcy.service.impl;

import com.ruoyi.shcy.domain.FloodShelterInfo;
import com.ruoyi.shcy.mapper.FloodShelterInfoMapper;
import com.ruoyi.shcy.service.IFloodShelterInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 防汛安置点信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
@Service
public class FloodShelterInfoServiceImpl implements IFloodShelterInfoService
{
    @Autowired
    private FloodShelterInfoMapper floodShelterInfoMapper;

    /**
     * 查询防汛安置点信息
     * 
     * @param id 防汛安置点信息主键
     * @return 防汛安置点信息
     */
    @Override
    public FloodShelterInfo selectFloodShelterInfoById(Long id)
    {
        return floodShelterInfoMapper.selectFloodShelterInfoById(id);
    }

    /**
     * 查询防汛安置点信息列表
     * 
     * @param floodShelterInfo 防汛安置点信息
     * @return 防汛安置点信息
     */
    @Override
    public List<FloodShelterInfo> selectFloodShelterInfoList(FloodShelterInfo floodShelterInfo)
    {
        return floodShelterInfoMapper.selectFloodShelterInfoList(floodShelterInfo);
    }

    /**
     * 新增防汛安置点信息
     * 
     * @param floodShelterInfo 防汛安置点信息
     * @return 结果
     */
    @Override
    public int insertFloodShelterInfo(FloodShelterInfo floodShelterInfo)
    {
        return floodShelterInfoMapper.insertFloodShelterInfo(floodShelterInfo);
    }

    /**
     * 修改防汛安置点信息
     * 
     * @param floodShelterInfo 防汛安置点信息
     * @return 结果
     */
    @Override
    public int updateFloodShelterInfo(FloodShelterInfo floodShelterInfo)
    {
        return floodShelterInfoMapper.updateFloodShelterInfo(floodShelterInfo);
    }

    /**
     * 批量删除防汛安置点信息
     * 
     * @param ids 需要删除的防汛安置点信息主键
     * @return 结果
     */
    @Override
    public int deleteFloodShelterInfoByIds(Long[] ids)
    {
        return floodShelterInfoMapper.deleteFloodShelterInfoByIds(ids);
    }

    /**
     * 删除防汛安置点信息信息
     * 
     * @param id 防汛安置点信息主键
     * @return 结果
     */
    @Override
    public int deleteFloodShelterInfoById(Long id)
    {
        return floodShelterInfoMapper.deleteFloodShelterInfoById(id);
    }
}
