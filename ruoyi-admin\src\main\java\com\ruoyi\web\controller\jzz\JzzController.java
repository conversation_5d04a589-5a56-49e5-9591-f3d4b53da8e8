package com.ruoyi.web.controller.jzz;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.dahuatech.icc.util.CollectionUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.constant.CaseType;
import com.ruoyi.shcy.constant.SmsConstants;
import com.ruoyi.shcy.domain.*;
import com.ruoyi.shcy.service.*;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysDeptService;
import org.dromara.sms4j.api.SmsBlend;
import org.dromara.sms4j.core.factory.SmsFactory;
import org.gavaghan.geodesy.Ellipsoid;
import org.gavaghan.geodesy.GeodeticCalculator;
import org.gavaghan.geodesy.GeodeticCurve;
import org.gavaghan.geodesy.GlobalCoordinates;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商铺巡查Controller
 */
@RestController
@RequestMapping("/shcy/jzz")
public class JzzController extends BaseController {

    @Autowired
    private IShopCheckLogService shopCheckLogService;

    @Autowired
    private INucleicAcidLogService nucleicAcidLogService;

    @Autowired
    private IShopService shopService;

    @Autowired
    private ICheckRecordService checkRecordService;

    @Autowired
    private IShcyCaseService shcyCaseService;

    @Autowired
    private IShcyFileInfoService shcyFileInfoService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysConfigService configService;

    @PostMapping("/check")
    @Log(title = "确认巡检", businessType = BusinessType.INSERT)
    public AjaxResult check(@RequestBody Map<String, Object> map)
    {
        ShopCheckLog shopCheckLog = JSON.parseObject(map.get("shopCheckLog").toString(), ShopCheckLog.class);
        Shop shop = shopService.selectShopById(shopCheckLog.getShopId());

        // 1. 保存商铺巡查记录
        shopCheckLog.setShopStatus("正常");
        if (shopCheckLog.getId() == null) {
            shopCheckLog.setDeptId(shop.getShopDeparmentId());
            shopCheckLog.setCheckDate(new Date());
            shopCheckLog.setCreateBy(getUsername());
            if(!CollectionUtil.isEmpty(shopCheckLog.getFileIds())){
                String result = shopCheckLog.getFileIds().stream().map(s -> s.replaceAll("\"", ""))
                        .collect(Collectors.joining(","));
                shopCheckLog.setJzCheckPhoto(result);
            }
            shopCheckLogService.insertShopCheckLog(shopCheckLog);
        } else {
            shopCheckLog.setUpdateBy(getUsername());
            if(!CollectionUtil.isEmpty(shopCheckLog.getFileIds())){
                String result = shopCheckLog.getFileIds().stream().map(s -> s.replaceAll("\"", ""))
                        .collect(Collectors.joining(","));
                shopCheckLog.setJzCheckPhoto(result);
            }
            shopCheckLogService.updateShopCheckLog(shopCheckLog);
        }

        String today= DateUtil.today();
        Date date = DateUtil.parse(today, "yyyy-MM-dd");
        CheckRecord checkRecord = checkRecordService.selectCheckRecordByShopIdAndCheckDate(shop.getId(), date);
        if (checkRecord == null) {
            CheckRecord cr = new CheckRecord();
            cr.setShopId(shopCheckLog.getShopId());
            cr.setCheckDate(new Date());
            cr.setCreateBy(getUsername());
            cr.setCheckStatus(isCheckPass(shopCheckLog) ? "检查通过" : "检查未通过");
            checkRecordService.insertCheckRecord(cr);
            //生成案事件
            if (!isCheckPass(shopCheckLog)) {
                createCase(shop, shopCheckLog);
            }

            // 是否发送短信
            String sendSms = configService.selectConfigByKey("shcy.shop.sendSms");
            boolean isSendSms = Convert.toBool(sendSms);
            // 发送短信
            if (isSendSms) {
                sendSmsNotification(shop, shopCheckLog);
            }

        } else {
            checkRecord.setCheckStatus(isCheckPass(shopCheckLog) ? "检查通过" : "检查未通过");
            checkRecordService.updateCheckRecord(checkRecord);
        }

        shop.setShopStatus("已检查");
        return toAjax(shopService.updateShop(shop));
    }

    /**
     * 发送短信通知
     * @param shop 店铺信息
     * @param shopCheckLog 店铺巡查记录
     * 1	是否存在“三合一”住人现象	不存在	存在	应急中心 翁诗培 19946006165
     * 2	广告牌存在安全隐患	不存在	存在	综合执法队	沈晓音	13601796988
     * 3	证照公示	公示	未公示	石化市场监管所	刘芳芳(北片)陆卿卿(南片)分开	刘芳芳18301805628，陆卿卿18019207136
     * 4	沿街商铺是否装修	未装修	正在装修	应急中心、综合执法队	陈战强、沈晓音	13661978248、13601796988
     */
    private void sendSmsNotification(Shop shop, ShopCheckLog shopCheckLog) {

        List<String> phoneList = new ArrayList<>();
        // 存在“三合一”住人现象
        if ("存在".equals(shopCheckLog.getCheckThreeOnePlace())) {
            // 翁诗培
            phoneList.add("19946006165");
        }

        // 广告牌存在安全隐患
        if ("存在".equals(shopCheckLog.getCheckCrossDoorOperation())) {
            // 沈晓音
            phoneList.add("13601796988");
        }

        // 证照未公示
        if ("未公示".equals(shopCheckLog.getCheckFfhx())) {
            SysDept sysDept = deptService.selectDeptById(shop.getShopDeparmentId());
            if (StrUtil.isNotEmpty(sysDept.getRegion())) {
                if ("南片".equals(sysDept.getRegion())) {
                    // 陆卿卿
                    phoneList.add("18019207136");
                } else if ("北片".equals(sysDept.getRegion())) {
                    // 刘芳芳
                    phoneList.add("18301805628");
                }
            }
        }

        if ("正在装修".equals(shopCheckLog.getCheckShopDecorate())) {
            phoneList.add("13661978248");
            phoneList.add("13601796988");
        }

        // 判断phoneList是否为空
        if (phoneList.isEmpty()) {
            return;
        }

        SmsBlend smsBlend = SmsFactory.getSmsBlend("ali1");
        phoneList.forEach(phone1 -> {
            smsBlend.sendMessageAsync(phone1, SmsConstants.TEMPLATE_CODE_JZS, new LinkedHashMap<>());
        });

    }

    /**
     * 判断是否检查通过
     */
    private boolean isCheckPass(ShopCheckLog shopCheckLog) {
        return "不存在".equals(shopCheckLog.getCheckCrossDoorOperation())
                && "公示".equals(shopCheckLog.getCheckFfhx())
                && "不存在".equals(shopCheckLog.getCheckThreeOnePlace())
                && "未装修".equals(shopCheckLog.getCheckShopDecorate());
    }

    /**
     * 生成案事件
     */
    private void createCase(Shop shop, ShopCheckLog shopCheckLog) {
        ShcyCase shcyCase = new ShcyCase();
        shcyCase.setShopCheckLogId(shopCheckLog.getId());
        shcyCase.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        shcyCase.setCreateTime(DateUtils.getNowDate());
        shcyCase.setCaseEndTime(DateUtil.offset(DateUtils.getNowDate(), DateField.DAY_OF_MONTH,10));  // 10天
        // 待处理
        shcyCase.setCirculationState("1");
        shcyCase.setShopAddress(shop.getShopAddress());
        shcyCase.setShopName(shop.getShopName());

        if ("存在".equals(shopCheckLog.getCheckThreeOnePlace())) {
            shcyCase.setCaseType(CaseType.TRI_COMBINATION_PHENOMENON.getValue());
            shcyCase.setCaseTypeName(CaseType.TRI_COMBINATION_PHENOMENON.getName());
            shcyCase.setCaseDealBy("应急中心");
            shcyCaseService.insertShcyCase(shcyCase);
        }
        if ("存在".equals(shopCheckLog.getCheckCrossDoorOperation())) {
            shcyCase.setCaseType(CaseType.ADVERTISEMENT_HAZARD.getValue());
            shcyCase.setCaseTypeName(CaseType.ADVERTISEMENT_HAZARD.getName());
            shcyCase.setCaseDealBy("综合执法队");
            shcyCaseService.insertShcyCase(shcyCase);
        }
        if ("未公示".equals(shopCheckLog.getCheckFfhx())) {
            shcyCase.setCaseType(CaseType.NO_LICENSE_DISPLAYED.getValue());
            shcyCase.setCaseTypeName(CaseType.NO_LICENSE_DISPLAYED.getName());
            shcyCase.setCaseDealBy("市场监管所");
            shcyCaseService.insertShcyCase(shcyCase);
        }
        if ("正在装修".equals(shopCheckLog.getCheckShopDecorate())) {
            shcyCase.setCaseType(CaseType.STREET_SHOP_CONSTRUCTION_SAFETY.getValue());
            shcyCase.setCaseTypeName(CaseType.STREET_SHOP_CONSTRUCTION_SAFETY.getName());
            shcyCase.setCaseDealBy("应急中心");
            shcyCaseService.insertShcyCase(shcyCase);
        }
        if ("正在装修".equals(shopCheckLog.getCheckShopDecorate())) {
            shcyCase.setCaseType(CaseType.STREET_SHOP_CIVILIZED_CONSTRUCTION.getValue());
            shcyCase.setCaseTypeName(CaseType.STREET_SHOP_CIVILIZED_CONSTRUCTION.getName());
            shcyCase.setCaseDealBy("综合执法队");
            shcyCaseService.insertShcyCase(shcyCase);
        }
    }

    @PostMapping("/closure/{shopId}")
    @Log(title = "歇业", businessType = BusinessType.INSERT)
    public AjaxResult closure(@PathVariable("shopId") Long shopId)
    {
        String checkDate = DateUtil.formatDate(new Date());
        ShopCheckLog shopCheckLog = shopCheckLogService.selectShopCheckLogByShopIdAndCheckDate(shopId, checkDate);

        String today= DateUtil.today();
        Date date = DateUtil.parse(today, "yyyy-MM-dd");
        CheckRecord checkRecord = checkRecordService.selectCheckRecordByShopIdAndCheckDate(shopId, date);

        if(checkRecord == null){
            CheckRecord cr = new CheckRecord();
            cr.setCheckDate(new Date());
            cr.setShopId(shopId);
            cr.setCheckStatus("未检查");
            cr.setCreateBy(getUsername());
            checkRecordService.insertCheckRecord(cr);
        }else{
            checkRecord.setCheckStatus("未检查");
            checkRecordService.updateCheckRecord(checkRecord);
        }

        if(shopCheckLog == null) {
            ShopCheckLog scl = new ShopCheckLog();
            scl.setShopId(shopId);
            scl.setCheckDate(new Date());
            scl.setShopStatus("歇业");
            scl.setCreateBy(getUsername());
            shopCheckLogService.insertShopCheckLog(scl);
        } else {
            shopCheckLog.setShopStatus("歇业");
            shopCheckLogService.updateShopCheckLog(shopCheckLog);
        }

        Shop shop = shopService.selectShopById(shopId);
        shop.setShopStatus("歇业");
        return AjaxResult.success(shopService.updateShop(shop));
    }

    @GetMapping(value = "/getTodayShopCheckLog/{shopId}")
    public AjaxResult getTodayShopCheckLog(@PathVariable("shopId") Long shopId)
    {
        String checkDate = DateUtil.formatDate(new Date());
        ShopCheckLog shopCheckLog = shopCheckLogService.selectShopCheckLogByShopIdAndCheckDate(shopId, checkDate);
        if(shopCheckLog!=null){
            if(StringUtils.isNotEmpty(shopCheckLog.getCheckPhoto())){
                String[] ids  = shopCheckLog.getCheckPhoto().split(",");
                if(ids.length!=0){
                    List<String> imgurls = new ArrayList<>();
                    for (String id : ids) {
                        ShcyFileInfo shcyFileInfo = shcyFileInfoService.selectShcyFileInfoByFileId(Long.valueOf(id));
                        imgurls.add(shcyFileInfo.getFilePath());
                    }
                    shopCheckLog.setPhotoUrls(imgurls);

                }
            }
            if(StringUtils.isNotEmpty(shopCheckLog.getJzCheckPhoto())){
                String[] ids  = shopCheckLog.getJzCheckPhoto().split(",");
                if(ids.length!=0){
                    List<String> jzimgurls = new ArrayList<>();
                    for (String id : ids) {
                        ShcyFileInfo shcyFileInfo = shcyFileInfoService.selectShcyFileInfoByFileId(Long.valueOf(id));
                        jzimgurls.add(shcyFileInfo.getFilePath());
                    }
                    shopCheckLog.setJzPhotoUrls(jzimgurls);

                }
            }
        }

        return AjaxResult.success(shopCheckLog);
    }

    @GetMapping(value = "/getTodayNucleicAcidLog/{employeeId}")
    public AjaxResult getTodayNucleicAcidLog(@PathVariable("employeeId") Long employeeId)
    {
        String checkDate = DateUtil.formatDate(new Date());
        NucleicAcidLog nucleicAcidLog = nucleicAcidLogService.selectNucleicAcidLogByEmployeeIdAndCheckDate(employeeId, checkDate);
        return AjaxResult.success(nucleicAcidLog);
    }


    //获取两个坐标点的距离
    public double getDistanceMeter(GlobalCoordinates gpsFrom, GlobalCoordinates gpsTo, Ellipsoid ellipsoid) {

        //创建GeodeticCalculator，调用计算方法，传入坐标系、经纬度用于计算距离
        GeodeticCurve geoCurve = new GeodeticCalculator().calculateGeodeticCurve(ellipsoid, gpsFrom, gpsTo);

        return geoCurve.getEllipsoidalDistance();
    }

    /**
     * 获取商铺巡查案件列表
     */
    @GetMapping("/handleCaseList")
    public TableDataInfo handleCaseList(ShcyCase shcyCase, String keyword)
    {
        if (StrUtil.isNotEmpty(keyword)) {
            Map<String, Object> params = new HashMap<String, Object>() {
                {
                    put("keyword", keyword);
                }
            };
            shcyCase.setParams(params);
        }

        // 权限判断 根据用户名判断 管理员或者营商办看全部，其他人根据caseType判断
        String username = getUsername();
        if("admin".equals(username) || "营商办".equals(username)) {
            // all
            shcyCase.getParams().put("permission", 1);
        } else if ("应急中心".equals(username)) {
            // caseType 对应 1、4
            shcyCase.getParams().put("permission", 2);
        } else if ("综合执法队".equals(username)) {
            // caseType 对应 2、5
            shcyCase.getParams().put("permission", 3);
        } else if ("市场监管所".equals(username)) {
            // caseType 对应 3
            shcyCase.getParams().put("permission", 4);
        } else {
            // caseType 对应 9 代表空数据
            shcyCase.getParams().put("permission", 5);
        }
        startPage();
        List<ShcyCase> list = shcyCaseService.selectShcyCaseList(shcyCase);
        return getDataTable(list);
    }

    /**
     * 根据id获取商铺巡查案件
     */
    @GetMapping(value = "/getShcyCase/{id}")
    public AjaxResult getShcyCase(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shcyCaseService.getShcyCase(id));
    }

    /**
     * 处理商铺巡查案件
     */
    @PostMapping("/handleShcyCase")
    public AjaxResult handleShcyCase(@RequestBody ShcyCase shcyCase)
    {
        shcyCase.setCaseDealBy(getUsername());
        return toAjax(shcyCaseService.handleShcyCase(shcyCase));
    }

    /**
     * 获取商铺巡查案件处理记录
     */
    @GetMapping(value = "/getShopCheckLog/{id}")
    public AjaxResult getShopCheckLog(@PathVariable("id") Long id)
    {
        return AjaxResult.success(shopCheckLogService.getShopCheckLog(id));
    }


}
