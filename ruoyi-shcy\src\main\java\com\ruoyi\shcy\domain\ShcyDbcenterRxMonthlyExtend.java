package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 12345热线分析拓展对象 shcy_dbcenter_rx_monthly_extend
 * 
 * <AUTHOR>
 * @date 2024-04-26
 */
public class ShcyDbcenterRxMonthlyExtend extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 单位 */
    @Excel(name = "单位")
    private String dept;

    /** 立案数 */
    @Excel(name = "立案数")
    private String las;

    /** 常住人口数 */
    @Excel(name = "常住人口数")
    private String czrks;

    /** 标准立案数 */
    @Excel(name = "标准立案数")
    private String bzllas;

    /** 标准类常住人口立案数 */
    @Excel(name = "标准类常住人口立案数")
    private String bzlczrklas;

    /** 拓展类立案数 */
    @Excel(name = "拓展类立案数")
    private String tzllas;

    /** 拓展类常住人口立案数 */
    @Excel(name = "拓展类常住人口立案数")
    private String tzlczrklas;

    /** 简易流程立案数 */
    @Excel(name = "简易流程立案数")
    private String jylclas;

    /** 简易流程常住人口立案数 */
    @Excel(name = "简易流程常住人口立案数")
    private String jylcczrklas;

    /** 村居立案数 */
    @Excel(name = "村居立案数")
    private String cjlas;

    /** 村居常住人口立案数 */
    @Excel(name = "村居常住人口立案数")
    private String cjczrklas;

    /** 标准立案数 */
    @Excel(name = "标准立案数")
    private String bzlas;

    /** 结案数 */
    @Excel(name = "结案数")
    private String jas;

    /** 结案率 */
    @Excel(name = "结案率")
    private String jal;

    /** 未结案数 */
    @Excel(name = "未结案数")
    private String wjas;

    /** 父id */
    @Excel(name = "父id")
    private Long pid;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDept(String dept) 
    {
        this.dept = dept;
    }

    public String getDept() 
    {
        return dept;
    }
    public void setLas(String las) 
    {
        this.las = las;
    }

    public String getLas() 
    {
        return las;
    }
    public void setCzrks(String czrks) 
    {
        this.czrks = czrks;
    }

    public String getCzrks() 
    {
        return czrks;
    }
    public void setBzllas(String bzllas) 
    {
        this.bzllas = bzllas;
    }

    public String getBzllas() 
    {
        return bzllas;
    }
    public void setBzlczrklas(String bzlczrklas) 
    {
        this.bzlczrklas = bzlczrklas;
    }

    public String getBzlczrklas() 
    {
        return bzlczrklas;
    }
    public void setTzllas(String tzllas) 
    {
        this.tzllas = tzllas;
    }

    public String getTzllas() 
    {
        return tzllas;
    }
    public void setTzlczrklas(String tzlczrklas) 
    {
        this.tzlczrklas = tzlczrklas;
    }

    public String getTzlczrklas() 
    {
        return tzlczrklas;
    }
    public void setJylclas(String jylclas) 
    {
        this.jylclas = jylclas;
    }

    public String getJylclas() 
    {
        return jylclas;
    }
    public void setJylcczrklas(String jylcczrklas) 
    {
        this.jylcczrklas = jylcczrklas;
    }

    public String getJylcczrklas() 
    {
        return jylcczrklas;
    }
    public void setCjlas(String cjlas) 
    {
        this.cjlas = cjlas;
    }

    public String getCjlas() 
    {
        return cjlas;
    }
    public void setCjczrklas(String cjczrklas) 
    {
        this.cjczrklas = cjczrklas;
    }

    public String getCjczrklas() 
    {
        return cjczrklas;
    }
    public void setBzlas(String bzlas) 
    {
        this.bzlas = bzlas;
    }

    public String getBzlas() 
    {
        return bzlas;
    }
    public void setJas(String jas) 
    {
        this.jas = jas;
    }

    public String getJas() 
    {
        return jas;
    }
    public void setJal(String jal) 
    {
        this.jal = jal;
    }

    public String getJal() 
    {
        return jal;
    }
    public void setWjas(String wjas) 
    {
        this.wjas = wjas;
    }

    public String getWjas() 
    {
        return wjas;
    }
    public void setPid(Long pid) 
    {
        this.pid = pid;
    }

    public Long getPid() 
    {
        return pid;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("dept", getDept())
            .append("las", getLas())
            .append("czrks", getCzrks())
            .append("bzllas", getBzllas())
            .append("bzlczrklas", getBzlczrklas())
            .append("tzllas", getTzllas())
            .append("tzlczrklas", getTzlczrklas())
            .append("jylclas", getJylclas())
            .append("jylcczrklas", getJylcczrklas())
            .append("cjlas", getCjlas())
            .append("cjczrklas", getCjczrklas())
            .append("bzlas", getBzlas())
            .append("jas", getJas())
            .append("jal", getJal())
            .append("wjas", getWjas())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("pid", getPid())
            .toString();
    }
}
