package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ControlMapper;
import com.ruoyi.shcy.domain.Control;
import com.ruoyi.shcy.service.IControlService;

/**
 * 监控点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2022-07-26
 */
@Service
public class ControlServiceImpl implements IControlService 
{
    @Autowired
    private ControlMapper controlMapper;

    /**
     * 查询监控点
     * 
     * @param controlId 监控点主键
     * @return 监控点
     */
    @Override
    public Control selectControlByControlId(Long controlId)
    {
        return controlMapper.selectControlByControlId(controlId);
    }

    /**
     * 查询监控点列表
     * 
     * @param control 监控点
     * @return 监控点
     */
    @Override
    public List<Control> selectControlList(Control control)
    {
        return controlMapper.selectControlList(control);
    }

    /**
     * 新增监控点
     * 
     * @param control 监控点
     * @return 结果
     */
    @Override
    public int insertControl(Control control)
    {
        return controlMapper.insertControl(control);
    }

    /**
     * 修改监控点
     * 
     * @param control 监控点
     * @return 结果
     */
    @Override
    public int updateControl(Control control)
    {
        return controlMapper.updateControl(control);
    }

    /**
     * 批量删除监控点
     * 
     * @param controlIds 需要删除的监控点主键
     * @return 结果
     */
    @Override
    public int deleteControlByControlIds(Long[] controlIds)
    {
        return controlMapper.deleteControlByControlIds(controlIds);
    }

    /**
     * 删除监控点信息
     * 
     * @param controlId 监控点主键
     * @return 结果
     */
    @Override
    public int deleteControlByControlId(Long controlId)
    {
        return controlMapper.deleteControlByControlId(controlId);
    }
}
