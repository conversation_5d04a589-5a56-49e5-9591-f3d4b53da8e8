package com.ruoyi.shcy.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.domain.FxftCase;
import com.ruoyi.shcy.domain.ShcyCase;
import com.ruoyi.shcy.domain.ShcyFileInfo;
import com.ruoyi.shcy.mapper.ShcyCaseMapper;
import com.ruoyi.shcy.mapper.ShcyFileInfoMapper;
import com.ruoyi.shcy.service.IShcyCaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 案事件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
@Service
public class ShcyCaseServiceImpl implements IShcyCaseService
{
    @Autowired
    private ShcyCaseMapper shcyCaseMapper;

    @Autowired
    private ShcyFileInfoMapper shcyFileInfoMapper;

    /**
     * 查询案事件
     *
     * @param  id 案事件主键
     * @return 案事件
     */
    @Override
    public ShcyCase selectShcyCaseById(Long  id)
    {
        return shcyCaseMapper.selectShcyCaseById( id);
    }

    /**
     * 查询案事件列表
     *
     * @param shcyCase 案事件
     * @return 案事件
     */
    @Override
    public List<ShcyCase> selectShcyCaseList(ShcyCase shcyCase)
    {
        return shcyCaseMapper.selectShcyCaseList(shcyCase);
    }

    /**
     * 新增案事件
     *
     * @param shcyCase 案事件
     * @return 结果
     */
    @Override
    public int insertShcyCase(ShcyCase shcyCase)
    {
        return shcyCaseMapper.insertShcyCase(shcyCase);
    }

    /**
     * 修改案事件
     *
     * @param shcyCase 案事件
     * @return 结果
     */
    @Override
    public int updateShcyCase(ShcyCase shcyCase)
    {
        shcyCase.setUpdateTime(DateUtils.getNowDate());

        return shcyCaseMapper.updateShcyCase(shcyCase);
    }

    /**
     * 批量删除案事件
     *
     * @param  ids 需要删除的案事件主键
     * @return 结果
     */
    @Override
    public int deleteShcyCaseByIds(Long[]  ids)
    {
        return shcyCaseMapper.deleteShcyCaseByIds( ids);
    }

    /**
     * 删除案事件信息
     *
     * @param  id 案事件主键
     * @return 结果
     */
    @Override
    public int deleteShcyCaseById(Long  id)
    {
        return shcyCaseMapper.deleteShcyCaseById( id);
    }


    /**
     * 退单指定事件（重新选择操作人）
     *
     * @param id 案事件主键
     **/
    @Override
    public int returnShcyCaseById(Long id) {
        return shcyCaseMapper.returnShcyCaseById(id);
    }

    @Override
    public int handleShcyCase(ShcyCase shcyCase) {
        // 0:已处理
        shcyCase.setCirculationState("0");
        shcyCase.setCaseFinishTime(new Date());
        return shcyCaseMapper.updateShcyCase(shcyCase);
    }

    @Override
    public ShcyCase getShcyCase(Long id) {
        return handlePhoto(shcyCaseMapper.selectShcyCaseById(id));
    }

    @Override
    public List<ShcyCase> selectShcyCaseListByShopCheckLogIds(Long[] shopCheckLogIds) {
        return shcyCaseMapper.selectShcyCaseListByShopCheckLogIds(shopCheckLogIds);
    }

    public ShcyCase handlePhoto(ShcyCase shcyCase) {
        if (StrUtil.isNotEmpty(shcyCase.getCaseDealPhoto())) {
            List<String> photoUrls = new ArrayList<>();
            String[] fileIds = shcyCase.getCaseDealPhoto().split(",");
            for (String fileId : fileIds) {
                ShcyFileInfo shcyFileInfo = shcyFileInfoMapper.selectShcyFileInfoByFileId(Long.valueOf(fileId));
                // 判断shcyFileInfo是否为空
                if (shcyFileInfo != null) {
                    photoUrls.add(shcyFileInfo.getFilePath());
                }
            }
            shcyCase.setPhotoUrls(photoUrls);
        }
        return shcyCase;
    }
}
