package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.ShcyCase;

import java.util.List;

/**
 * 案事件Service接口
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
public interface IShcyCaseService
{
    /**
     * 查询案事件
     *
     * @param  id 案事件主键
     * @return 案事件
     */
    public ShcyCase selectShcyCaseById(Long id);

    /**
     * 查询案事件列表
     *
     * @param shcyCase 案事件
     * @return 案事件集合
     */
    public List<ShcyCase> selectShcyCaseList(ShcyCase shcyCase);

    /**
     * 新增案事件
     *
     * @param shcyCase 案事件
     * @return 结果
     */
    public int insertShcyCase(ShcyCase shcyCase);

    /**
     * 修改案事件
     *
     * @param shcyCase 案事件
     * @return 结果
     */
    public int updateShcyCase(ShcyCase shcyCase);

    /**
     * 批量删除案事件
     *
     * @param  ids 需要删除的案事件主键集合
     * @return 结果
     */
    public int deleteShcyCaseByIds(Long[]  ids);

    /**
     * 删除案事件信息
     *
     * @param  id 案事件主键
     * @return 结果
     */
    public int deleteShcyCaseById(Long  id);

    /**
     * 退单指定事件（重新选择操作人）
     * **/
    public int returnShcyCaseById(Long id);

    /**
     * 处理案事件
     *
     * @param shcyCase 案事件
     * @return 结果
     */
    public int handleShcyCase(ShcyCase shcyCase);

    /**
     * 查询案事件
     *
     * @param  id 案事件主键
     * @return 案事件
     */
    public ShcyCase getShcyCase(Long id);

    List<ShcyCase> selectShcyCaseListByShopCheckLogIds(Long[] shopCheckLogIds);
}
