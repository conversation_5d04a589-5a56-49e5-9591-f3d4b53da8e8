package com.ruoyi.quartz.task;


import com.dahuatech.hutool.core.bean.BeanUtil;
import com.ruoyi.shcy.domain.JsDbcenter;
import com.ruoyi.shcy.domain.ShcyWgh;
import com.ruoyi.shcy.domain.TaskDbcenter;
import com.ruoyi.shcy.service.IJsDbcenterService;
import com.ruoyi.shcy.service.IShcyWghService;
import com.ruoyi.shcy.service.ITaskDbcenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 网格化案件数据同步
 */
@Component("jsDbcenterTask")
public class JsDbcenterTask {

    @Autowired
    private IJsDbcenterService jsDbcenterService;

    @Autowired
    private ITaskDbcenterService taskDbcenterService;

    @Autowired
    private IShcyWghService shcyWghService;

    /**
     * 同步当天12345的数据
     */
    public void sync12345() {
        // String startTime = DateUtil.yesterday().toDateStr() + " 22:00:00";
        // String endTime = DateUtil.tomorrow().toDateStr() + " 00:00:00";

        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDate tomorrow = LocalDate.now().plusDays(1);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String startTime = yesterday.atTime(22, 0).format(formatter);
        String endTime = tomorrow.atTime(0, 0).format(formatter);

        init12345(startTime, endTime);
    }

    /**
     * 同步当天案件的数据
     */
    public void syncCase() {
        // String startTime = DateUtil.yesterday().toDateStr() + " 22:00:00";
        // String endTime = DateUtil.tomorrow().toDateStr() + " 00:00:00";

        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDate tomorrow = LocalDate.now().plusDays(1);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        String startTime = yesterday.atTime(22, 0).format(formatter);
        String endTime = tomorrow.atTime(0, 0).format(formatter);
        initCase(startTime, endTime);
    }

    /**
     * 更新历史数据
     */
    public void updateHistory() {
        List<String> idList = taskDbcenterService.selectTaskDbcenterSyncTaskidList();
        String[] taskids = idList.toArray(new String[0]);
        List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenterSyncListByTaskid(taskids);
        for (JsDbcenter jsDbcenter : jsDbcenters) {
            TaskDbcenter taskDbcenter = new TaskDbcenter();
            BeanUtil.copyProperties(jsDbcenter, taskDbcenter);
            // 如果状态名为“已结案”，则更新ShcyWgh的流转状态
            if ("已结案".equals(taskDbcenter.getStatusname())) {
                ShcyWgh shcyWgh = shcyWghService.selectShcyWghByTaskDbcenterId(taskDbcenter.getTaskid());
                if (shcyWgh != null && !"6".equals(shcyWgh.getCirculationState())) {
                    shcyWgh.setCirculationState("6");
                    shcyWghService.updateShcyWgh(shcyWgh);
                }
            }
            taskDbcenterService.updateTaskDbcenter(taskDbcenter);
        }

        // 删除部门不是石化街道的数据EXECUTEDEPTNAME = '石化街道' and DEPTNAME = '石化街道'
        taskDbcenterService.deleteTaskDbcenterNotShjd();

        // 删除已退回其他平台的数据
        taskDbcenterService.deleteTaskDbcenterReturnOther();

    }

    /**
     * 初始化12345数据
     * @param startTime
     * @param endTime
     */
    public void init12345(String startTime, String endTime) {
        List<Long> ids = taskDbcenterService.selectTaskDbcenterIdList();
        // 查询所有hotlinesnList
        List<String> hotlinesnList = taskDbcenterService.selectTaskDbcenterHotlinesnList();
        List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenter12345List(startTime, endTime);
        //遍历jsDbcenters判断JsDbcenter.getId()是否在ids中，如果不在则插入
        for (JsDbcenter jsDbcenter : jsDbcenters) {
            TaskDbcenter dbcenter = new TaskDbcenter();
            BeanUtil.copyProperties(jsDbcenter, dbcenter);
            if (!ids.contains(jsDbcenter.getId())) {
                // 判断jsDbcenter.getHotlinesn()是否在hotlinesnList中，如果在则设置ischeck为是
                if (hotlinesnList.contains(jsDbcenter.getHotlinesn())) {
                    dbcenter.setIscheck("是");
                }
                taskDbcenterService.insertTaskDbcenter(dbcenter);
            } else {
                taskDbcenterService.updateTaskDbcenter(dbcenter);
            }
        }
    }

    /**
     * 初始化案件数据
     * @param startTime
     * @param endTime
     */
    public void initCase(String startTime, String endTime) {
        List<Long> ids = taskDbcenterService.selectTaskDbcenterIdList();
        List<JsDbcenter> jsDbcenters = jsDbcenterService.selectJsDbcenterCaseList(startTime, endTime);
        //遍历jsDbcenters判断JsDbcenter.getId()是否在ids中，如果不在则插入
        for (JsDbcenter jsDbcenter : jsDbcenters) {
            TaskDbcenter dbcenter = new TaskDbcenter();
            BeanUtil.copyProperties(jsDbcenter, dbcenter);
            if ("已结案".equals(dbcenter.getStatusname())) {
                ShcyWgh shcyWgh = shcyWghService.selectShcyWghByTaskDbcenterId(dbcenter.getTaskid());
                if (shcyWgh != null && !"6".equals(shcyWgh.getCirculationState())) {
                    shcyWgh.setCirculationState("6");
                    shcyWghService.updateShcyWgh(shcyWgh);
                }
            }
            if (!ids.contains(jsDbcenter.getId())) {
                taskDbcenterService.insertTaskDbcenter(dbcenter);
            } else {
                taskDbcenterService.updateTaskDbcenter(dbcenter);
            }
        }
    }
}
