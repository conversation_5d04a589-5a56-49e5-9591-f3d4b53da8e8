package com.ruoyi.shcy.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.shcy.domain.Shop;
import com.ruoyi.shcy.domain.vo.ShopCheckVo;
import com.ruoyi.shcy.service.IShopService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.CheckRecord;
import com.ruoyi.shcy.service.ICheckRecordService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 商铺巡查通过记录Controller
 *
 * <AUTHOR>
 * @date 2023-02-15
 */
@RestController
@RequestMapping("/shcy/record")
public class CheckRecordController extends BaseController
{
    @Autowired
    private ICheckRecordService checkRecordService;

    @Autowired
    private IShopService shopService;

    /**
     * 查询商铺巡查通过记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckRecord checkRecord)
    {
        startPage();
        List<CheckRecord> list = checkRecordService.selectCheckRecordList(checkRecord);
        return getDataTable(list);
    }

    /**
     * 导出商铺巡查通过记录列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:record:export')")
    @Log(title = "商铺巡查通过记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckRecord checkRecord)
    {
        List<CheckRecord> list = checkRecordService.selectCheckRecordList(checkRecord);
        ExcelUtil<CheckRecord> util = new ExcelUtil<CheckRecord>(CheckRecord.class);
        util.exportExcel(response, list, "商铺巡查通过记录数据");
    }

    /**
     * 获取商铺巡查通过记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(checkRecordService.selectCheckRecordById(id));
    }


    @GetMapping(value= "/shopCheck")
    public AjaxResult getShopCheckRecord(CheckRecord checkRecord){

        if(StringUtils.isEmpty(checkRecord.getParams())){
            String today= DateUtil.today();
            Map params = new HashMap<>();
            params.put("beginTime",today);
            params.put("endTime",today);
            checkRecord.setParams(params);
        }
        ShopCheckVo shopCheckVo = new ShopCheckVo();
        List<ShopCheckVo> shopCheckList = new ArrayList<>();

        //商铺总数
        Shop allShop  = new Shop();
        List<Shop>  allShopNum = shopService.selectShopList(allShop);


        if(allShopNum.size()!=0){
            shopCheckVo.setShopTotalNum(String.valueOf(allShopNum.size()));
        }else{
            shopCheckVo.setShopTotalNum("0");
        }

        //查询巡查店铺数量信息
//        CheckRecord allCheckShop  = new CheckRecord();
        List<CheckRecord> allChcekedShop = checkRecordService.selectCheckedShopNum(checkRecord);
        if(allChcekedShop.size()!=0){
            shopCheckVo.setShopCheckedNum(String.valueOf(allChcekedShop.size()));
        }else{
            shopCheckVo.setShopCheckedNum("0");
        }

        //查询出来检查通过的店铺信息
//        CheckRecord checkRecord1 = new CheckRecord();
        List<CheckRecord> checkAndPass = checkRecordService.selectCheckAndPassShop(checkRecord);
        if(checkAndPass.size()!=0){
            shopCheckVo.setShopCheckedPassNum(String.valueOf(checkAndPass.size()));
        }else{
            shopCheckVo.setShopCheckedPassNum("0");
        }

        //查询 检查未通过的店铺信息
//        CheckRecord checkRecord2 = new CheckRecord();
        List<CheckRecord> checkNoPass = checkRecordService.selectCheckAndNoPassShop(checkRecord);
        if(checkNoPass.size()!=0){
            shopCheckVo.setShopCheckedNoPassNum(String.valueOf(checkNoPass.size()));
        }else{
            shopCheckVo.setShopCheckedNoPassNum("0");
        }

        //查询 未检查店铺的信息
//        CheckRecord checkRecord3 = new CheckRecord();
        List<CheckRecord> noCheck = checkRecordService.selectNoCheckShop(checkRecord);
        if(noCheck.size()!=0){
            shopCheckVo.setShopNoCheckNum(String.valueOf(noCheck.size()));
        }else{
            shopCheckVo.setShopNoCheckNum("0");
        }

        shopCheckList.add(shopCheckVo);

        return AjaxResult.success(shopCheckList);
    }

    /**
     * 新增商铺巡查通过记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:record:add')")
    @Log(title = "商铺巡查通过记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckRecord checkRecord)
    {
        return toAjax(checkRecordService.insertCheckRecord(checkRecord));
    }

    /**
     * 修改商铺巡查通过记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:record:edit')")
    @Log(title = "商铺巡查通过记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckRecord checkRecord)
    {
        return toAjax(checkRecordService.updateCheckRecord(checkRecord));
    }

    /**
     * 删除商铺巡查通过记录
     */
    @PreAuthorize("@ss.hasPermi('shcy:record:remove')")
    @Log(title = "商铺巡查通过记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(checkRecordService.deleteCheckRecordByIds(ids));
    }
}
