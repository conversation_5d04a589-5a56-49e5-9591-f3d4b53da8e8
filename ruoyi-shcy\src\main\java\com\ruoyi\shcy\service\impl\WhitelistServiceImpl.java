package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.WhitelistMapper;
import com.ruoyi.shcy.domain.Whitelist;
import com.ruoyi.shcy.service.IWhitelistService;

/**
 * 车牌白名单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-22
 */
@Service
public class WhitelistServiceImpl implements IWhitelistService 
{
    @Autowired
    private WhitelistMapper whitelistMapper;

    /**
     * 查询车牌白名单
     * 
     * @param id 车牌白名单主键
     * @return 车牌白名单
     */
    @Override
    public Whitelist selectWhitelistById(Long id)
    {
        return whitelistMapper.selectWhitelistById(id);
    }

    /**
     * 查询车牌白名单列表
     * 
     * @param whitelist 车牌白名单
     * @return 车牌白名单
     */
    @Override
    public List<Whitelist> selectWhitelistList(Whitelist whitelist)
    {
        return whitelistMapper.selectWhitelistList(whitelist);
    }

    /**
     * 新增车牌白名单
     * 
     * @param whitelist 车牌白名单
     * @return 结果
     */
    @Override
    public int insertWhitelist(Whitelist whitelist)
    {
        return whitelistMapper.insertWhitelist(whitelist);
    }

    /**
     * 修改车牌白名单
     * 
     * @param whitelist 车牌白名单
     * @return 结果
     */
    @Override
    public int updateWhitelist(Whitelist whitelist)
    {
        return whitelistMapper.updateWhitelist(whitelist);
    }

    /**
     * 批量删除车牌白名单
     * 
     * @param ids 需要删除的车牌白名单主键
     * @return 结果
     */
    @Override
    public int deleteWhitelistByIds(Long[] ids)
    {
        return whitelistMapper.deleteWhitelistByIds(ids);
    }

    /**
     * 删除车牌白名单信息
     * 
     * @param id 车牌白名单主键
     * @return 结果
     */
    @Override
    public int deleteWhitelistById(Long id)
    {
        return whitelistMapper.deleteWhitelistById(id);
    }
}
