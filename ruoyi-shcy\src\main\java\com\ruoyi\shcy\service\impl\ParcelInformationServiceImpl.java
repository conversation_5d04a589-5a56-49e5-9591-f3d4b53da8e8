package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.ParcelInformationMapper;
import com.ruoyi.shcy.domain.ParcelInformation;
import com.ruoyi.shcy.service.IParcelInformationService;

/**
 * 宗地信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-01-31
 */
@Service
public class ParcelInformationServiceImpl implements IParcelInformationService 
{
    @Autowired
    private ParcelInformationMapper parcelInformationMapper;

    /**
     * 查询宗地信息
     * 
     * @param id 宗地信息主键
     * @return 宗地信息
     */
    @Override
    public ParcelInformation selectParcelInformationById(Long id)
    {
        return parcelInformationMapper.selectParcelInformationById(id);
    }

    /**
     * 查询宗地信息列表
     * 
     * @param parcelInformation 宗地信息
     * @return 宗地信息
     */
    @Override
    public List<ParcelInformation> selectParcelInformationList(ParcelInformation parcelInformation)
    {
        return parcelInformationMapper.selectParcelInformationList(parcelInformation);
    }

    /**
     * 新增宗地信息
     * 
     * @param parcelInformation 宗地信息
     * @return 结果
     */
    @Override
    public int insertParcelInformation(ParcelInformation parcelInformation)
    {
        parcelInformation.setCreateTime(DateUtils.getNowDate());
        return parcelInformationMapper.insertParcelInformation(parcelInformation);
    }

    /**
     * 修改宗地信息
     * 
     * @param parcelInformation 宗地信息
     * @return 结果
     */
    @Override
    public int updateParcelInformation(ParcelInformation parcelInformation)
    {
        parcelInformation.setUpdateTime(DateUtils.getNowDate());
        return parcelInformationMapper.updateParcelInformation(parcelInformation);
    }

    /**
     * 批量删除宗地信息
     * 
     * @param ids 需要删除的宗地信息主键
     * @return 结果
     */
    @Override
    public int deleteParcelInformationByIds(Long[] ids)
    {
        return parcelInformationMapper.deleteParcelInformationByIds(ids);
    }

    /**
     * 删除宗地信息信息
     * 
     * @param id 宗地信息主键
     * @return 结果
     */
    @Override
    public int deleteParcelInformationById(Long id)
    {
        return parcelInformationMapper.deleteParcelInformationById(id);
    }
}
