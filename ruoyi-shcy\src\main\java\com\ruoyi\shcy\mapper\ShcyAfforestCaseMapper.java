package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyAfforestCase;

/**
 * 绿化案事件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-19
 */
public interface ShcyAfforestCaseMapper
{
    /**
     * 查询绿化案事件
     *
     * @param id 绿化案事件主键
     * @return 绿化案事件
     */
    public ShcyAfforestCase selectShcyAfforestCaseById(Long id);

    /**
     * 查询绿化案事件列表
     *
     * @param shcyAfforestCase 绿化案事件
     * @return 绿化案事件集合
     */
    public List<ShcyAfforestCase> selectShcyAfforestCaseList(ShcyAfforestCase shcyAfforestCase);

    /**
     * 新增绿化案事件
     *
     * @param shcyAfforestCase 绿化案事件
     * @return 结果
     */
    public int insertShcyAfforestCase(ShcyAfforestCase shcyAfforestCase);

    /**
     * 修改绿化案事件
     *
     * @param shcyAfforestCase 绿化案事件
     * @return 结果
     */
    public int updateShcyAfforestCase(ShcyAfforestCase shcyAfforestCase);

    /**
     * 删除绿化案事件
     *
     * @param id 绿化案事件主键
     * @return 结果
     */
    public int deleteShcyAfforestCaseById(Long id);

    /**
     * 批量删除绿化案事件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyAfforestCaseByIds(Long[] ids);


    /**
     * 绿化id查询出当前的案事件
     * */
    public ShcyAfforestCase selectShcyAfforestCaseByForestId(Long forestId);


    /**
     * 退单指定事件（重新选择操作人）
     * **/
    public int returnShcyAffroestCaseById(Long id);
}
