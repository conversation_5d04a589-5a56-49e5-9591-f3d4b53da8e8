<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.shcy.mapper.VehicleMapper">
    
    <resultMap type="Vehicle" id="VehicleResult">
        <result property="id"    column="id"    />
        <result property="channel"    column="channel"    />
        <result property="passTime"    column="pass_time"    />
        <result property="licensePlate"    column="license_plate"    />
        <result property="licensePlateCloseup"    column="license_plate_closeup"    />
        <result property="licensePlateColor"    column="license_plate_color"    />
        <result property="vehicleCutout"    column="vehicle_cutout"    />
        <result property="vehicleType"    column="vehicle_type"    />
        <result property="vehicleColor"    column="vehicle_color"    />
        <result property="vehicleBrand"    column="vehicle_brand"    />
        <result property="seatbelt"    column="seatbelt"    />
        <result property="phoneUsage"    column="phone_usage"    />
        <result property="interiorItems"    column="interior_items"    />
        <result property="panorama"    column="panorama"    />
    </resultMap>

    <sql id="selectVehicleVo">
        select id, channel, pass_time, license_plate, license_plate_closeup, license_plate_color, vehicle_cutout, vehicle_type, vehicle_color, vehicle_brand, seatbelt, phone_usage, interior_items, panorama from shcy_vehicle
    </sql>

    <select id="selectVehicleList" parameterType="Vehicle" resultMap="VehicleResult">
        <include refid="selectVehicleVo"/>
        <where>  
            <if test="passTime != null "> and pass_time = #{passTime}</if>
            <if test="licensePlate != null  and licensePlate != ''"> and license_plate = #{licensePlate}</if>
            <if test="vehicleType != null  and vehicleType != ''"> and vehicle_type = #{vehicleType}</if>
            <if test="interiorItems != null  and interiorItems != ''"> and interior_items = #{interiorItems}</if>
            <if test="panorama != null  and panorama != ''"> and panorama = #{panorama}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(pass_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(pass_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
    </select>
    
    <select id="selectVehicleById" parameterType="Long" resultMap="VehicleResult">
        <include refid="selectVehicleVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertVehicle" parameterType="Vehicle" useGeneratedKeys="true" keyProperty="id">
        insert into shcy_vehicle
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channel != null">channel,</if>
            <if test="passTime != null">pass_time,</if>
            <if test="licensePlate != null">license_plate,</if>
            <if test="licensePlateCloseup != null">license_plate_closeup,</if>
            <if test="licensePlateColor != null">license_plate_color,</if>
            <if test="vehicleCutout != null">vehicle_cutout,</if>
            <if test="vehicleType != null">vehicle_type,</if>
            <if test="vehicleColor != null">vehicle_color,</if>
            <if test="vehicleBrand != null">vehicle_brand,</if>
            <if test="seatbelt != null">seatbelt,</if>
            <if test="phoneUsage != null">phone_usage,</if>
            <if test="interiorItems != null">interior_items,</if>
            <if test="panorama != null">panorama,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channel != null">#{channel},</if>
            <if test="passTime != null">#{passTime},</if>
            <if test="licensePlate != null">#{licensePlate},</if>
            <if test="licensePlateCloseup != null">#{licensePlateCloseup},</if>
            <if test="licensePlateColor != null">#{licensePlateColor},</if>
            <if test="vehicleCutout != null">#{vehicleCutout},</if>
            <if test="vehicleType != null">#{vehicleType},</if>
            <if test="vehicleColor != null">#{vehicleColor},</if>
            <if test="vehicleBrand != null">#{vehicleBrand},</if>
            <if test="seatbelt != null">#{seatbelt},</if>
            <if test="phoneUsage != null">#{phoneUsage},</if>
            <if test="interiorItems != null">#{interiorItems},</if>
            <if test="panorama != null">#{panorama},</if>
         </trim>
    </insert>

    <update id="updateVehicle" parameterType="Vehicle">
        update shcy_vehicle
        <trim prefix="SET" suffixOverrides=",">
            <if test="channel != null">channel = #{channel},</if>
            <if test="passTime != null">pass_time = #{passTime},</if>
            <if test="licensePlate != null">license_plate = #{licensePlate},</if>
            <if test="licensePlateCloseup != null">license_plate_closeup = #{licensePlateCloseup},</if>
            <if test="licensePlateColor != null">license_plate_color = #{licensePlateColor},</if>
            <if test="vehicleCutout != null">vehicle_cutout = #{vehicleCutout},</if>
            <if test="vehicleType != null">vehicle_type = #{vehicleType},</if>
            <if test="vehicleColor != null">vehicle_color = #{vehicleColor},</if>
            <if test="vehicleBrand != null">vehicle_brand = #{vehicleBrand},</if>
            <if test="seatbelt != null">seatbelt = #{seatbelt},</if>
            <if test="phoneUsage != null">phone_usage = #{phoneUsage},</if>
            <if test="interiorItems != null">interior_items = #{interiorItems},</if>
            <if test="panorama != null">panorama = #{panorama},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVehicleById" parameterType="Long">
        delete from shcy_vehicle where id = #{id}
    </delete>

    <delete id="deleteVehicleByIds" parameterType="String">
        delete from shcy_vehicle where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>