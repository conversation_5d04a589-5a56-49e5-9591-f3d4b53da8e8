package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.Draw;

/**
 * 地图绘制Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-10-08
 */
public interface DrawMapper 
{
    /**
     * 查询地图绘制
     * 
     * @param id 地图绘制主键
     * @return 地图绘制
     */
    public Draw selectDrawById(Long id);

    /**
     * 查询地图绘制列表
     * 
     * @param draw 地图绘制
     * @return 地图绘制集合
     */
    public List<Draw> selectDrawList(Draw draw);

    /**
     * 新增地图绘制
     * 
     * @param draw 地图绘制
     * @return 结果
     */
    public int insertDraw(Draw draw);

    /**
     * 修改地图绘制
     * 
     * @param draw 地图绘制
     * @return 结果
     */
    public int updateDraw(Draw draw);

    /**
     * 删除地图绘制
     * 
     * @param id 地图绘制主键
     * @return 结果
     */
    public int deleteDrawById(Long id);

    /**
     * 批量删除地图绘制
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDrawByIds(Long[] ids);
}
