package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyCase;

/**
 * 案事件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-05-16
 */
public interface ShcyCaseMapper
{
    /**
     * 查询案事件
     *
     * @param  id 案事件主键
     * @return 案事件
     */
    public ShcyCase selectShcyCaseById(Long id);

    /**
     * 查询案事件列表
     *
     * @param shcyCase 案事件
     * @return 案事件集合
     */
    public List<ShcyCase> selectShcyCaseList(ShcyCase shcyCase);

    /**
     * 新增案事件
     *
     * @param shcyCase 案事件
     * @return 结果
     */
    public int insertShcyCase(ShcyCase shcyCase);

    /**
     * 修改案事件
     *
     * @param shcyCase 案事件
     * @return 结果
     */
    public int updateShcyCase(ShcyCase shcyCase);

    /**
     * 删除案事件
     *
     * @param  id 案事件主键
     * @return 结果
     */
    public int deleteShcyCaseById(Long id);

    /**
     * 批量删除案事件
     *
     * @param  ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteShcyCaseByIds(Long[] ids);


    /**
     * 退单指定事件（重新选择操作人）
     * **/
    public int returnShcyCaseById(Long id);

    List<ShcyCase> selectShcyCaseListByShopCheckLogIds(Long[] shopCheckLogIds);
}
