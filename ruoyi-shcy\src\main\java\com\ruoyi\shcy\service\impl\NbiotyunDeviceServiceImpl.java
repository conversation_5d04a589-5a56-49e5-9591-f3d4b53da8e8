package com.ruoyi.shcy.service.impl;

import java.util.List;
import com.ruoyi.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.NbiotyunDeviceMapper;
import com.ruoyi.shcy.domain.NbiotyunDevice;
import com.ruoyi.shcy.service.INbiotyunDeviceService;

/**
 * 设备管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-01
 */
@Service
public class NbiotyunDeviceServiceImpl implements INbiotyunDeviceService 
{
    @Autowired
    private NbiotyunDeviceMapper nbiotyunDeviceMapper;

    /**
     * 查询设备管理
     * 
     * @param deviceId 设备管理主键
     * @return 设备管理
     */
    @Override
    public NbiotyunDevice selectNbiotyunDeviceByDeviceId(Long deviceId)
    {
        return nbiotyunDeviceMapper.selectNbiotyunDeviceByDeviceId(deviceId);
    }

    /**
     * 查询设备管理列表
     * 
     * @param nbiotyunDevice 设备管理
     * @return 设备管理
     */
    @Override
    public List<NbiotyunDevice> selectNbiotyunDeviceList(NbiotyunDevice nbiotyunDevice)
    {
        return nbiotyunDeviceMapper.selectNbiotyunDeviceList(nbiotyunDevice);
    }

    /**
     * 新增设备管理
     * 
     * @param nbiotyunDevice 设备管理
     * @return 结果
     */
    @Override
    public int insertNbiotyunDevice(NbiotyunDevice nbiotyunDevice)
    {
        nbiotyunDevice.setCreateTime(DateUtils.getNowDate());
        return nbiotyunDeviceMapper.insertNbiotyunDevice(nbiotyunDevice);
    }

    /**
     * 修改设备管理
     * 
     * @param nbiotyunDevice 设备管理
     * @return 结果
     */
    @Override
    public int updateNbiotyunDevice(NbiotyunDevice nbiotyunDevice)
    {
        nbiotyunDevice.setUpdateTime(DateUtils.getNowDate());
        return nbiotyunDeviceMapper.updateNbiotyunDevice(nbiotyunDevice);
    }

    /**
     * 批量删除设备管理
     * 
     * @param deviceIds 需要删除的设备管理主键
     * @return 结果
     */
    @Override
    public int deleteNbiotyunDeviceByDeviceIds(Long[] deviceIds)
    {
        return nbiotyunDeviceMapper.deleteNbiotyunDeviceByDeviceIds(deviceIds);
    }

    /**
     * 删除设备管理信息
     * 
     * @param deviceId 设备管理主键
     * @return 结果
     */
    @Override
    public int deleteNbiotyunDeviceByDeviceId(Long deviceId)
    {
        return nbiotyunDeviceMapper.deleteNbiotyunDeviceByDeviceId(deviceId);
    }
}
