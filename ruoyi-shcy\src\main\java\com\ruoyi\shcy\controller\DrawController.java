package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.Draw;
import com.ruoyi.shcy.service.IDrawService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 地图绘制Controller
 * 
 * <AUTHOR>
 * @date 2022-10-08
 */
@RestController
@RequestMapping("/shcy/draw")
public class DrawController extends BaseController
{
    @Autowired
    private IDrawService drawService;

    /**
     * 查询地图绘制列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:draw:list')")
    @GetMapping("/list")
    public TableDataInfo list(Draw draw)
    {
        startPage();
        List<Draw> list = drawService.selectDrawList(draw);
        return getDataTable(list);
    }

    /**
     * 导出地图绘制列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:draw:export')")
    @Log(title = "地图绘制", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Draw draw)
    {
        List<Draw> list = drawService.selectDrawList(draw);
        ExcelUtil<Draw> util = new ExcelUtil<Draw>(Draw.class);
        util.exportExcel(response, list, "地图绘制数据");
    }

    /**
     * 获取地图绘制详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:draw:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(drawService.selectDrawById(id));
    }

    /**
     * 新增地图绘制
     */
    @PreAuthorize("@ss.hasPermi('shcy:draw:add')")
    @Log(title = "地图绘制", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Draw draw)
    {
        return toAjax(drawService.insertDraw(draw));
    }

    /**
     * 修改地图绘制
     */
    @PreAuthorize("@ss.hasPermi('shcy:draw:edit')")
    @Log(title = "地图绘制", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Draw draw)
    {
        return toAjax(drawService.updateDraw(draw));
    }

    /**
     * 删除地图绘制
     */
    @PreAuthorize("@ss.hasPermi('shcy:draw:remove')")
    @Log(title = "地图绘制", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(drawService.deleteDrawByIds(ids));
    }
}
