package com.ruoyi.shcy.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2022/11/8 13:43
 * @Version 1.0
 */

//巡查从业人员数据
public class EmployeesDetailVo {

    /** 从业人员总人数*/
    @Excel(name="从业人员总人数")
    private String EmployeesTotal;

    /** 每日巡查从业人员数*/
    @Excel(name="每日巡查从业人员数")
    private String checkEmployeesTotal;

    /** 每日核酸正常人员数*/
    @Excel(name="每日核酸正常人员数")
    private String checkNoramEmployeesNum;

    /** 每日核酸异常人员数*/
    @Excel(name="每日核酸异常人员数")
    private String checkAbnormalEmployeesNum;

    /** 每日离岗人员数*/
    @Excel(name="每日离岗人数")
    private String  checkRetiredEmployeesNum;


    /**巡查日期*/
    @Excel(name = "巡查日期", width = 30, dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Date curDate;


    /** 商铺的员工数量 */
    private String  employeeNum;

    /** 商铺名称数量 */
    private String shopName;


    public String getEmployeesTotal() {
        return EmployeesTotal;
    }

    public void setEmployeesTotal(String employeesTotal) {
        EmployeesTotal = employeesTotal;
    }

    public String getEmployeeNum() {
        return employeeNum;
    }

    public void setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getCheckEmployeesTotal() {
        return checkEmployeesTotal;
    }

    public void setCheckEmployeesTotal(String checkEmployeesTotal) {
        this.checkEmployeesTotal = checkEmployeesTotal;
    }

    public String getCheckNoramEmployeesNum() {
        return checkNoramEmployeesNum;
    }

    public void setCheckNoramEmployeesNum(String checkNoramEmployeesNum) {
        this.checkNoramEmployeesNum = checkNoramEmployeesNum;
    }

    public String getCheckAbnormalEmployeesNum() {
        return checkAbnormalEmployeesNum;
    }

    public void setCheckAbnormalEmployeesNum(String checkAbnormalEmployeesNum) {
        this.checkAbnormalEmployeesNum = checkAbnormalEmployeesNum;
    }

    public String getCheckRetiredEmployeesNum() {
        return checkRetiredEmployeesNum;
    }

    public void setCheckRetiredEmployeesNum(String checkRetiredEmployeesNum) {
        this.checkRetiredEmployeesNum = checkRetiredEmployeesNum;
    }

    public Date getCurDate() {
        return curDate;
    }

    public void setCurDate(Date curDate) {
        this.curDate = curDate;
    }
}
