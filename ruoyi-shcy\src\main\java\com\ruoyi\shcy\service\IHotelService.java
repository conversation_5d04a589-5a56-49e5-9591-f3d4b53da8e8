package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.Hotel;

/**
 * 宾旅馆信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface IHotelService 
{
    /**
     * 查询宾旅馆信息
     * 
     * @param id 宾旅馆信息主键
     * @return 宾旅馆信息
     */
    public Hotel selectHotelById(Long id);

    /**
     * 查询宾旅馆信息列表
     * 
     * @param hotel 宾旅馆信息
     * @return 宾旅馆信息集合
     */
    public List<Hotel> selectHotelList(Hotel hotel);

    /**
     * 新增宾旅馆信息
     * 
     * @param hotel 宾旅馆信息
     * @return 结果
     */
    public int insertHotel(Hotel hotel);

    /**
     * 修改宾旅馆信息
     * 
     * @param hotel 宾旅馆信息
     * @return 结果
     */
    public int updateHotel(Hotel hotel);

    /**
     * 批量删除宾旅馆信息
     * 
     * @param ids 需要删除的宾旅馆信息主键集合
     * @return 结果
     */
    public int deleteHotelByIds(Long[] ids);

    /**
     * 删除宾旅馆信息信息
     * 
     * @param id 宾旅馆信息主键
     * @return 结果
     */
    public int deleteHotelById(Long id);
}
