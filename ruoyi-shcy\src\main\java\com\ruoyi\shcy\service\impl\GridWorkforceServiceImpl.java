package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.GridWorkforceMapper;
import com.ruoyi.shcy.domain.GridWorkforce;
import com.ruoyi.shcy.service.IGridWorkforceService;

/**
 * 网格工作力量Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class GridWorkforceServiceImpl implements IGridWorkforceService 
{
    @Autowired
    private GridWorkforceMapper gridWorkforceMapper;

    /**
     * 查询网格工作力量
     * 
     * @param id 网格工作力量主键
     * @return 网格工作力量
     */
    @Override
    public GridWorkforce selectGridWorkforceById(Long id)
    {
        return gridWorkforceMapper.selectGridWorkforceById(id);
    }

    /**
     * 查询网格工作力量列表
     * 
     * @param gridWorkforce 网格工作力量
     * @return 网格工作力量
     */
    @Override
    public List<GridWorkforce> selectGridWorkforceList(GridWorkforce gridWorkforce)
    {
        return gridWorkforceMapper.selectGridWorkforceList(gridWorkforce);
    }

    /**
     * 新增网格工作力量
     * 
     * @param gridWorkforce 网格工作力量
     * @return 结果
     */
    @Override
    public int insertGridWorkforce(GridWorkforce gridWorkforce)
    {
        return gridWorkforceMapper.insertGridWorkforce(gridWorkforce);
    }

    /**
     * 修改网格工作力量
     * 
     * @param gridWorkforce 网格工作力量
     * @return 结果
     */
    @Override
    public int updateGridWorkforce(GridWorkforce gridWorkforce)
    {
        return gridWorkforceMapper.updateGridWorkforce(gridWorkforce);
    }

    /**
     * 批量删除网格工作力量
     * 
     * @param ids 需要删除的网格工作力量主键
     * @return 结果
     */
    @Override
    public int deleteGridWorkforceByIds(Long[] ids)
    {
        return gridWorkforceMapper.deleteGridWorkforceByIds(ids);
    }

    /**
     * 删除网格工作力量信息
     * 
     * @param id 网格工作力量主键
     * @return 结果
     */
    @Override
    public int deleteGridWorkforceById(Long id)
    {
        return gridWorkforceMapper.deleteGridWorkforceById(id);
    }
}
