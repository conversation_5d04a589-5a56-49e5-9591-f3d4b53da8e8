package com.ruoyi.quartz.task;

import cn.hutool.core.date.DateUtil;
import com.ruoyi.nbiotyun.domain.AlarmRecordDTO;
import com.ruoyi.nbiotyun.domain.DeviceDTO;
import com.ruoyi.nbiotyun.service.NbiotyunService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("nbiotyunTask")
public class NbiotyunTask {

    @Autowired
    private NbiotyunService nbiotyunService;

    /**
     * 同步
     */
    public void sync() {
        String startTime = DateUtil.today() + " 00:00:00";
        // String endTime = DateUtil.formatDate(DateUtil.tomorrow()) + " 00:00:00";
        String endTime = DateUtil.today() + " 23:59:59";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        nbiotyunService.syncNbiotyunAlarmRecord(result);
    }

    /**
     * 08:00-12:00同步
     */
    public void sync1() {
        String startTime = DateUtil.today() + " 08:00:00";
        String endTime = DateUtil.today() + " 12:00:00";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        String date = DateUtil.today();
        String date1 = "08:00-12:00";
        nbiotyunService.syncData(result, date, date1);
    }

    /**
     * 12:00-15:00同步
     */
    public void sync2() {
        String startTime = DateUtil.today() + " 12:00:00";
        String endTime = DateUtil.today() + " 15:00:00";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        String date = DateUtil.today();
        String date1 = "12:00-15:00";
        nbiotyunService.syncData(result, date, date1);
    }

    /**
     * 15:00-18:00同步
     */
    public void sync3() {
        String startTime = DateUtil.today() + " 15:00:00";
        String endTime = DateUtil.today() + " 18:00:00";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        String date = DateUtil.today();
        String date1 = "15:00-18:00";
        nbiotyunService.syncData(result, date, date1);
    }

    /**
     * 18:00-22:00同步
     */
    public void sync4() {
        String startTime = DateUtil.today() + " 18:00:00";
        String endTime = DateUtil.today() + " 22:00:00";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        String date = DateUtil.today();
        String date1 = "18:00-22:00";
        nbiotyunService.syncData(result, date, date1);
    }

    /**
     * 22:00-08:00同步
     */
    public void sync5() {
        String startTime = DateUtil.yesterday().toDateStr() + " 22:00:00";
        String endTime = DateUtil.today() + " 08:00:00";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        String date = DateUtil.today();
        String date1 = "22:00-08:00";
        nbiotyunService.syncData(result, date, date1);
    }

    /**
     * 根据时间段参数同步
     */
    public void syncByTime(String time1, String time2) {
        String startTime = DateUtil.today() + " " + time1 + ":00";
        String endTime = DateUtil.today() + " " + time2 + ":00";
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        String date = DateUtil.today();
        String date1 = time1 + "-" + time2;
        nbiotyunService.syncData(result, date, date1);
    }

    /**
     * 每30分钟同步报警状态
     */
    public void syncAlarmStatus() {
        // endTime为当前时间， startTime为endTime前30分钟
        String startTime = DateUtil.offsetMinute(DateUtil.date(), -30).toString();
        String endTime = DateUtil.now();
        int pageNum = 1;
        List<AlarmRecordDTO> result = nbiotyunService.alarmRecord(pageNum, startTime, endTime);
        nbiotyunService.syncLiquidLevelDeviceAlarmStatus(result);
    }

    /**
     * 每小时同步设备在线情况
     */
    public void syncDeviceStatus() {
        List<DeviceDTO> result = nbiotyunService.getList(1, 100, 1);
        nbiotyunService.syncDeviceStatus(result);
    }

}
