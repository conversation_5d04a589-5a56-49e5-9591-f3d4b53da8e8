package com.ruoyi.shcy.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.shcy.constant.FxftConstants;
import com.ruoyi.shcy.domain.FloodHazardCheckList;
import com.ruoyi.shcy.domain.ShcyFloodHazardCheckListExtend;
import com.ruoyi.shcy.domain.ShcyFloodHazardCommittee;
import com.ruoyi.shcy.domain.ShcyFloodHazardType;
import com.ruoyi.shcy.mapper.FloodHazardCheckListMapper;
import com.ruoyi.shcy.mapper.ShcyFloodHazardCheckListExtendMapper;
import com.ruoyi.shcy.mapper.ShcyFloodHazardCommitteeMapper;
import com.ruoyi.shcy.mapper.ShcyFloodHazardTypeMapper;
import com.ruoyi.shcy.service.IFloodHazardCheckListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 汛期隐患排查Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-09
 */
@Service
public class FloodHazardCheckListServiceImpl implements IFloodHazardCheckListService
{
    @Autowired
    private FloodHazardCheckListMapper floodHazardCheckListMapper;

    @Autowired
    private ShcyFloodHazardCommitteeMapper shcyFloodHazardCommitteeMapper;

    @Autowired
    private ShcyFloodHazardCheckListExtendMapper shcyFloodHazardCheckListExtendMapper;

    @Autowired
    private ShcyFloodHazardTypeMapper shcyFloodHazardTypeMapper;


    /**
     * 查询汛期隐患排查
     *
     * @param id 汛期隐患排查主键
     * @return 汛期隐患排查
     */
    @Override
    public FloodHazardCheckList selectFloodHazardCheckListById(Long id)
    {
        return floodHazardCheckListMapper.selectFloodHazardCheckListById(id);
    }

    /**
     * 查询汛期隐患排查列表
     *
     * @param floodHazardCheckList 汛期隐患排查
     * @return 汛期隐患排查
     */
    @Override
    public List<FloodHazardCheckList> selectFloodHazardCheckListList(FloodHazardCheckList floodHazardCheckList)
    {
        return floodHazardCheckListMapper.selectFloodHazardCheckListList(floodHazardCheckList);
    }

    /**
     * 新增汛期隐患排查
     *
     * @param floodHazardCheckList 汛期隐患排查
     * @return 结果
     */
    @Override
    public int insertFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList)
    {
        return floodHazardCheckListMapper.insertFloodHazardCheckList(floodHazardCheckList);
    }

    /**
     * 修改汛期隐患排查
     *
     * @param floodHazardCheckList 汛期隐患排查
     * @return 结果
     */
    @Override
    public int updateFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList)
    {
        return floodHazardCheckListMapper.updateFloodHazardCheckList(floodHazardCheckList);
    }

    /**
     * 批量删除汛期隐患排查
     *
     * @param ids 需要删除的汛期隐患排查主键
     * @return 结果
     */
    @Override
    public int deleteFloodHazardCheckListByIds(Long[] ids)
    {
        return floodHazardCheckListMapper.deleteFloodHazardCheckListByIds(ids);
    }

    /**
     * 删除汛期隐患排查信息
     *
     * @param id 汛期隐患排查主键
     * @return 结果
     */
    @Override
    public int deleteFloodHazardCheckListById(Long id)
    {
        return floodHazardCheckListMapper.deleteFloodHazardCheckListById(id);
    }

    @Override
    public int handleFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList) {
        ShcyFloodHazardCheckListExtend theShcyFloodHazardCheckListExtend= shcyFloodHazardCheckListExtendMapper.sumCountByCheckId(floodHazardCheckList.getId());
        floodHazardCheckList.setRectifiedHazardCount(theShcyFloodHazardCheckListExtend.getRectifiedHazardCount());
        floodHazardCheckList.setFoundHazardCount(theShcyFloodHazardCheckListExtend.getFoundHazardCount());
        // 更新案件状态
        floodHazardCheckList.setCirculationState(FxftConstants.PROCESSED);
        floodHazardCheckList.setCaseFinishTime(DateUtils.getNowDate());
        // 判断当前时间是否小于fxftCase的caseEndTime
        if (DateUtils.getNowDate().compareTo(floodHazardCheckList.getCaseEndTime()) < 0) {
            floodHazardCheckList.setDealInTimeState(FxftConstants.OVERTIME_STATE_NORMAL);
        } else {
            floodHazardCheckList.setDealInTimeState(FxftConstants.OVERTIME_STATE_OVERTIME);
        }
        floodHazardCheckList.setCheckTime(DateUtils.getNowDate());
        return floodHazardCheckListMapper.updateFloodHazardCheckList(floodHazardCheckList);
    }

    @Override
    public int publishFloodHazardCheckList(FloodHazardCheckList floodHazardCheckList) {
        ShcyFloodHazardCommittee theC = shcyFloodHazardCommitteeMapper.selectShcyFloodHazardCommitteeById(Long.parseLong(floodHazardCheckList.getResidentCommunityName()));
        if (theC != null) {
            floodHazardCheckList.setResidentCommunityName(theC.getCommunityName());
            floodHazardCheckList.setCommunityHygieneDirector(theC.getCommunityHygieneDirector());
            floodHazardCheckList.setCommunitySecretary(theC.getCommunitySecretary());
            floodHazardCheckList.setCheckPerson(theC.getCommunityHygieneDirector() + "," + theC.getCommunitySecretary());
        }
        floodHazardCheckList.setPublishTime(new Date());
        floodHazardCheckList.setCirculationState(FxftConstants.UNPROCESSED);
        floodHazardCheckList.setEventNo(DateUtil.format(new Date(), "yyyyMMdd") + RandomUtil.randomNumbers(10));

        floodHazardCheckListMapper.insertFloodHazardCheckList(floodHazardCheckList);
        //插入子表数据
        ShcyFloodHazardCheckListExtend theExtend = new ShcyFloodHazardCheckListExtend();
        theExtend.setCheckId(floodHazardCheckList.getId());
        String[] ids = floodHazardCheckList.getCheckItem().split(",");
        for (String id : ids) {
            List<ShcyFloodHazardType> theTs = shcyFloodHazardTypeMapper.selectShcyFloodHazardTypeByParentId(Long.parseLong(id));
            theExtend.setCheckItemParent(Long.parseLong(id));
            for (ShcyFloodHazardType theT : theTs) {
                theExtend.setCheckItemChild(theT.getId());
                theExtend.setCreateTime(new Date());
                shcyFloodHazardCheckListExtendMapper.insertShcyFloodHazardCheckListExtend(theExtend);
            }

        }
        return 1;
    }

    @Override
    public long getCaseCount(FloodHazardCheckList floodHazardCheckList) {
        return floodHazardCheckListMapper.getCaseCount(floodHazardCheckList);
    }
}
