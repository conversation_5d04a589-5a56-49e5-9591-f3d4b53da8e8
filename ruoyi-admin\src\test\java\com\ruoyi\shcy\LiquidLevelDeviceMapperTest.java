package com.ruoyi.shcy;

import com.ruoyi.shcy.domain.LiquidLevelDevice;
import com.ruoyi.shcy.mapper.LiquidLevelDeviceMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest
public class LiquidLevelDeviceMapperTest {

    @Autowired
    private LiquidLevelDeviceMapper liquidLevelDeviceMapper;

    @Test
    public void testSelectImeiList() {
        List<String> imeiList = liquidLevelDeviceMapper.selectImeiList();
        imeiList.forEach(System.out::println);
    }

    @Test
    public void testSelectLiquidLevelDeviceByDeviceImei() {
        LiquidLevelDevice liquidLevelDevice = liquidLevelDeviceMapper.selectLiquidLevelDeviceByDeviceImei("863882046591274");
        System.out.println(liquidLevelDevice.getPipelineType());
    }

}
