package com.ruoyi.shcy.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * nbiotyun报警记录dto
 *
 * <AUTHOR>
 * @date 2023/11/02
 */
@Data
public class NbiotyunAlarmRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String alarmTime;

    private String deviceImei;

    private String detailedLocation;

    // json返回结果忽略该字段
    @JsonIgnore
    private String deviceAttrValue;

    private String status;

    private String responsiblePerson;

    private String monitoredWaterBody;

    public String getAlarmReason() {
        // String value= "";
        // JSONArray jsonArray = JSONArray.parseArray(this.deviceAttrValue);
        // // 遍历 JSON 数组，找到 attrName 为井盖状态的对象
        // for (int i = 0; i < jsonArray.size(); i++) {
        //     JSONObject jsonObject = jsonArray.getJSONObject(i);
        //     if ("井盖状态".equals(jsonObject.getString("attrName"))) {
        //         // 获取对象的 `value` 属性值
        //         value = jsonObject.getString("value");
        //         // System.out.println(value);
        //     }
        // }
        // return value;

        return "液位超限";
    }


}
