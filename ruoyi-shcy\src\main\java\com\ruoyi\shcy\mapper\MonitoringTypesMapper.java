package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.MonitoringTypes;

import java.util.List;

/**
 * 监控类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-10-11
 */
public interface MonitoringTypesMapper 
{
    /**
     * 查询监控类型
     * 
     * @param monitoringTypeId 监控类型主键
     * @return 监控类型
     */
    public MonitoringTypes selectMonitoringTypesByMonitoringTypeId(Long monitoringTypeId);

    /**
     * 查询监控类型列表
     * 
     * @param monitoringTypes 监控类型
     * @return 监控类型集合
     */
    public List<MonitoringTypes> selectMonitoringTypesList(MonitoringTypes monitoringTypes);

    /**
     * 新增监控类型
     * 
     * @param monitoringTypes 监控类型
     * @return 结果
     */
    public int insertMonitoringTypes(MonitoringTypes monitoringTypes);

    /**
     * 修改监控类型
     * 
     * @param monitoringTypes 监控类型
     * @return 结果
     */
    public int updateMonitoringTypes(MonitoringTypes monitoringTypes);

    /**
     * 删除监控类型
     * 
     * @param monitoringTypeId 监控类型主键
     * @return 结果
     */
    public int deleteMonitoringTypesByMonitoringTypeId(Long monitoringTypeId);

    /**
     * 批量删除监控类型
     * 
     * @param monitoringTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMonitoringTypesByMonitoringTypeIds(Long[] monitoringTypeIds);

    List<MonitoringTypes> selectAllMonitoringTypes();

    List<String> getAllMonitoringTypeName();
}
