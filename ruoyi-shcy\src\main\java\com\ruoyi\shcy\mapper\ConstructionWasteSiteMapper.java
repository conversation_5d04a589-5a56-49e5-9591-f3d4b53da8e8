package com.ruoyi.shcy.mapper;

import java.util.List;
import com.ruoyi.shcy.domain.ConstructionWasteSite;

/**
 * 小区建筑垃圾堆放点Mapper接口
 * 
 * <AUTHOR>
 * @date 2022-12-16
 */
public interface ConstructionWasteSiteMapper 
{
    /**
     * 查询小区建筑垃圾堆放点
     * 
     * @param id 小区建筑垃圾堆放点主键
     * @return 小区建筑垃圾堆放点
     */
    public ConstructionWasteSite selectConstructionWasteSiteById(Long id);

    /**
     * 查询小区建筑垃圾堆放点列表
     * 
     * @param constructionWasteSite 小区建筑垃圾堆放点
     * @return 小区建筑垃圾堆放点集合
     */
    public List<ConstructionWasteSite> selectConstructionWasteSiteList(ConstructionWasteSite constructionWasteSite);

    /**
     * 新增小区建筑垃圾堆放点
     * 
     * @param constructionWasteSite 小区建筑垃圾堆放点
     * @return 结果
     */
    public int insertConstructionWasteSite(ConstructionWasteSite constructionWasteSite);

    /**
     * 修改小区建筑垃圾堆放点
     * 
     * @param constructionWasteSite 小区建筑垃圾堆放点
     * @return 结果
     */
    public int updateConstructionWasteSite(ConstructionWasteSite constructionWasteSite);

    /**
     * 删除小区建筑垃圾堆放点
     * 
     * @param id 小区建筑垃圾堆放点主键
     * @return 结果
     */
    public int deleteConstructionWasteSiteById(Long id);

    /**
     * 批量删除小区建筑垃圾堆放点
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConstructionWasteSiteByIds(Long[] ids);
}
