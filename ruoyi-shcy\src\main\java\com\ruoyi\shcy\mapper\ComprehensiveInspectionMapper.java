package com.ruoyi.shcy.mapper;

import com.ruoyi.shcy.domain.ComprehensiveInspection;

import java.util.List;

/**
 * 综合检查Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
public interface ComprehensiveInspectionMapper 
{
    /**
     * 查询综合检查
     * 
     * @param id 综合检查主键
     * @return 综合检查
     */
    public ComprehensiveInspection selectComprehensiveInspectionById(Long id);

    /**
     * 查询综合检查列表
     * 
     * @param comprehensiveInspection 综合检查
     * @return 综合检查集合
     */
    public List<ComprehensiveInspection> selectComprehensiveInspectionList(ComprehensiveInspection comprehensiveInspection);

    /**
     * 新增综合检查
     * 
     * @param comprehensiveInspection 综合检查
     * @return 结果
     */
    public int insertComprehensiveInspection(ComprehensiveInspection comprehensiveInspection);

    /**
     * 修改综合检查
     * 
     * @param comprehensiveInspection 综合检查
     * @return 结果
     */
    public int updateComprehensiveInspection(ComprehensiveInspection comprehensiveInspection);

    /**
     * 删除综合检查
     * 
     * @param id 综合检查主键
     * @return 结果
     */
    public int deleteComprehensiveInspectionById(Long id);

    /**
     * 批量删除综合检查
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteComprehensiveInspectionByIds(Long[] ids);

    long getCaseCount(ComprehensiveInspection comprehensiveInspection);
}
