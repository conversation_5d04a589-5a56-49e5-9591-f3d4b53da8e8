package com.ruoyi.shcy.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.shcy.mapper.GridPartyMemberMapper;
import com.ruoyi.shcy.domain.GridPartyMember;
import com.ruoyi.shcy.service.IGridPartyMemberService;

/**
 * 网格党支部成员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@Service
public class GridPartyMemberServiceImpl implements IGridPartyMemberService 
{
    @Autowired
    private GridPartyMemberMapper gridPartyMemberMapper;

    /**
     * 查询网格党支部成员
     * 
     * @param id 网格党支部成员主键
     * @return 网格党支部成员
     */
    @Override
    public GridPartyMember selectGridPartyMemberById(Long id)
    {
        return gridPartyMemberMapper.selectGridPartyMemberById(id);
    }

    /**
     * 查询网格党支部成员列表
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 网格党支部成员
     */
    @Override
    public List<GridPartyMember> selectGridPartyMemberList(GridPartyMember gridPartyMember)
    {
        return gridPartyMemberMapper.selectGridPartyMemberList(gridPartyMember);
    }

    /**
     * 新增网格党支部成员
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 结果
     */
    @Override
    public int insertGridPartyMember(GridPartyMember gridPartyMember)
    {
        return gridPartyMemberMapper.insertGridPartyMember(gridPartyMember);
    }

    /**
     * 修改网格党支部成员
     * 
     * @param gridPartyMember 网格党支部成员
     * @return 结果
     */
    @Override
    public int updateGridPartyMember(GridPartyMember gridPartyMember)
    {
        return gridPartyMemberMapper.updateGridPartyMember(gridPartyMember);
    }

    /**
     * 批量删除网格党支部成员
     * 
     * @param ids 需要删除的网格党支部成员主键
     * @return 结果
     */
    @Override
    public int deleteGridPartyMemberByIds(Long[] ids)
    {
        return gridPartyMemberMapper.deleteGridPartyMemberByIds(ids);
    }

    /**
     * 删除网格党支部成员信息
     * 
     * @param id 网格党支部成员主键
     * @return 结果
     */
    @Override
    public int deleteGridPartyMemberById(Long id)
    {
        return gridPartyMemberMapper.deleteGridPartyMemberById(id);
    }
}
