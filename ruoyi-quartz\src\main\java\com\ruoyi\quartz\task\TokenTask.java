package com.ruoyi.quartz.task;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.bigdata.config.BigdataConfig;
import com.ruoyi.common.core.redis.RedisCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component("tokenTask")
public class TokenTask {

    private static final String TOKEN_KEY = "AUTHORIZATION_TOKEN";

    private static final String API_TOKEN_KEY = "api-token";

    // token过期时间 60分钟
    private static final Integer TOKEN_EXPIRE_TIME = 60;

    @Autowired
    private BigdataConfig bigdataConfig;

    @Autowired
    private RedisCache redisCache;

    public void refreshToken() {
        redisCache.deleteObject(TOKEN_KEY);
        redisCache.deleteObject(API_TOKEN_KEY);
        String newToken = fetchNewToken();
        if (StrUtil.isNotEmpty(newToken)) {
            redisCache.setCacheObject(TOKEN_KEY, newToken, TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);
        }
        String newApiToken = fetchNewApiToken(newToken);
        if (StrUtil.isNotEmpty(newApiToken)) {
            redisCache.setCacheObject(API_TOKEN_KEY, newApiToken, TOKEN_EXPIRE_TIME, TimeUnit.MINUTES);
        }
    }
    private String fetchNewToken() {
        Map<String, Object> param = new HashMap<String, Object>() {
            {
                put("appKey", bigdataConfig.getAppKey());
                put("appSecret", bigdataConfig.getAppSecret());
            }
        };
        String response = HttpUtil.get(bigdataConfig.getHost() + "/auth/tokens", param);
        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("code") != 1) {
                return null;
            }
            return jsonObject.getJSONObject("data").getString("token");
        } catch (Exception e) {
            // 日志记录或其他异常处理
            return null;
        }
    }

    private String fetchNewApiToken(String token) {
        String response = HttpRequest.post(bigdataConfig.getHost() + "/api/rest/JSQ00923/JS00003ZYML01818/data")
                .header("APPID-USER", bigdataConfig.getAppidUser())
                .header("USER-SECRET", bigdataConfig.getUserSecret())
                .header("AUTHORIZATION_TOKEN", token)
                .execute()
                .body();
        try {
            JSONObject jsonObject = JSONObject.parseObject(response);
            if (jsonObject.getInteger("code") != 1) {
                return null;
            }
            return jsonObject.getJSONObject("data").getString("data");
        } catch (Exception e) {
            // 日志记录或其他异常处理
            return null;
        }
    }


}
