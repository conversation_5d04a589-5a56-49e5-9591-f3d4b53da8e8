package com.ruoyi.icc;

import com.dahuatech.icc.exception.ClientException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.icc.dto.CarRecordDTO;
import com.ruoyi.icc.service.EvoCarService;
import com.ruoyi.icc.service.IccService;
import com.ruoyi.icc.vo.CarPageVO;
import com.ruoyi.shcy.domain.IccAlarmRecord;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

/**
 * icc试验
 *
 * <AUTHOR>
 * @date 2023/09/14
 */
@SpringBootTest
public class IccTest {

    @Autowired
    private IccService iccService;

    @Autowired
    private EvoCarService evoCarService;

    @Test
    public void contextLoads() {
        // 这里没有任何代码，只是确保应用程序上下文能够成功加载
    }

    /**
     * 测试报警记录分页查询
     *
     * @throws ClientException 客户端异常
     */
    @Test
    public void testAlarmRecordPage() throws ClientException {
        List<IccAlarmRecord> iccAlarmRecords = iccService.alarmRecordPage(1, 1000, "2023-12-08 00:00:00", "2023-12-11 00:00:00", 15591);
        for (IccAlarmRecord iccAlarmRecord : iccAlarmRecords) {
            iccService.syncIccAlarmRecord(iccAlarmRecord);
        }
    }

    @Test
    public void testGetAlarmRecords() throws ClientException, JsonProcessingException {
       iccService.getAlarmRecords("{C7D035FE-4CC7-AC4E-B71D-4DA2FC86CF51}");
    }

    @Test
    public void testReplayByTime() throws ClientException {
        iccService.replayByTime("1000010$1$0$0", "1", "3", "2", "1693384964", "1693384973");
    }

    @Test
    public void testStartVideo() throws ClientException {
        String s = iccService.startVideo("1000010$1$0$0", "1");
        System.out.println(s);
    }

    @Test
    public void testGetPicRecords() throws ClientException {
        CarPageVO carPageVO = new CarPageVO();
        carPageVO.setPage(1);
        carPageVO.setRows(999);
        carPageVO.setAutoCount(1);
        carPageVO.setStartDate("1706457600");
        carPageVO.setEndDate("1706543999");
        carPageVO.setChannelIds(Arrays.asList("1000009$1$0$0"));
        carPageVO.setPlateNo("");
        carPageVO.setMinSpeed(0);
        carPageVO.setMaxSpeed(220);
        List<CarRecordDTO> picRecords = evoCarService.getPicRecords(carPageVO, iccService.getAccessToken());
        System.out.println(picRecords.size());
        System.out.println(picRecords);
    }

    @Test
    public void testGetPicRecordTotal() throws ClientException {
        CarPageVO carPageVO = new CarPageVO();
        carPageVO.setPage(1);
        carPageVO.setRows(10);
        carPageVO.setAutoCount(1);
        carPageVO.setStartDate("1706457600");
        carPageVO.setEndDate("1706543999");
        carPageVO.setChannelIds(Arrays.asList("1000009$1$0$0"));
        carPageVO.setPlateNo("");
        carPageVO.setMinSpeed(0);
        carPageVO.setMaxSpeed(220);
        int total = evoCarService.getPicRecordTotal(carPageVO, iccService.getAccessToken());
        System.out.println(total);
    }

}
