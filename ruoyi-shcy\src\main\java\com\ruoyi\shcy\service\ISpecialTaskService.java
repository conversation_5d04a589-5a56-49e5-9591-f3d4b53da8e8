package com.ruoyi.shcy.service;

import com.ruoyi.shcy.domain.SpecialTask;

import java.util.List;

/**
 * 专项任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-10
 */
public interface ISpecialTaskService 
{
    /**
     * 查询专项任务
     * 
     * @param id 专项任务主键
     * @return 专项任务
     */
    public SpecialTask selectSpecialTaskById(Long id);

    /**
     * 查询专项任务列表
     * 
     * @param specialTask 专项任务
     * @return 专项任务集合
     */
    public List<SpecialTask> selectSpecialTaskList(SpecialTask specialTask);

    /**
     * 新增专项任务
     * 
     * @param specialTask 专项任务
     * @return 结果
     */
    public int insertSpecialTask(SpecialTask specialTask);

    /**
     * 修改专项任务
     * 
     * @param specialTask 专项任务
     * @return 结果
     */
    public int updateSpecialTask(SpecialTask specialTask);

    /**
     * 批量删除专项任务
     * 
     * @param ids 需要删除的专项任务主键集合
     * @return 结果
     */
    public int deleteSpecialTaskByIds(Long[] ids);

    /**
     * 删除专项任务信息
     * 
     * @param id 专项任务主键
     * @return 结果
     */
    public int deleteSpecialTaskById(Long id);

    public long getCaseCount(SpecialTask specialTask);
}
