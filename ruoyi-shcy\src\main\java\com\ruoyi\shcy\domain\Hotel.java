package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 宾旅馆信息对象 shcy_hotel
 *
 * <AUTHOR>
 * @date 2022-12-16
 */
public class Hotel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 所属居委会 */
    @Excel(name = "所属居委会")
    private String committee;

    /** 所属居委会id */
    @Excel(name = "所属居委会id")
    private Long committeeId;

    /** 所属小区 */
    @Excel(name = "所属小区")
    private String residential;

    /** 所属小区id */
    @Excel(name = "所属小区id")
    private Long residentialId;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 类型 */
    @Excel(name = "类型")
    private String type;

    /** 坐标 */
    @Excel(name = "坐标")
    private String coordinate;

    /** 名称 */
    @Excel(name = "营业名称")
    private String name;

    /** 营业执照名称 */
    @Excel(name = "营业执照名称")
    private String licenseName;

    /** 法人 */
    @Excel(name = "法人")
    private String legalPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    public String getLicenseName() {
        return licenseName;
    }

    public void setLicenseName(String licenseName) {
        this.licenseName = licenseName;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCommittee(String committee)
    {
        this.committee = committee;
    }

    public String getCommittee()
    {
        return committee;
    }
    public void setCommitteeId(Long committeeId)
    {
        this.committeeId = committeeId;
    }

    public Long getCommitteeId()
    {
        return committeeId;
    }
    public void setResidential(String residential)
    {
        this.residential = residential;
    }

    public String getResidential()
    {
        return residential;
    }
    public void setResidentialId(Long residentialId)
    {
        this.residentialId = residentialId;
    }

    public Long getResidentialId()
    {
        return residentialId;
    }
    public void setLongitude(String longitude)
    {
        this.longitude = longitude;
    }

    public String getLongitude()
    {
        return longitude;
    }
    public void setLatitude(String latitude)
    {
        this.latitude = latitude;
    }

    public String getLatitude()
    {
        return latitude;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    @Override
    public String toString() {
        return "Hotel{" +
                "id=" + id +
                ", committee='" + committee + '\'' +
                ", committeeId=" + committeeId +
                ", residential='" + residential + '\'' +
                ", residentialId=" + residentialId +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", type='" + type + '\'' +
                ", coordinate='" + coordinate + '\'' +
                ", name='" + name + '\'' +
                ", licenseName='" + licenseName + '\'' +
                ", legalPerson='" + legalPerson + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                '}';
    }

}
