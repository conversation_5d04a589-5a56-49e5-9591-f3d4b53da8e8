package com.ruoyi.icc.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 过车记录请求参数
 *
 * <AUTHOR>
 * @date 2024/01/30
 */
@Data
@NoArgsConstructor
public class CarPageVO {

    /**
     * 分页页数
     */
    private int page;
    /**
     * 分页大小
     */
    private int rows;
    /**
     * 是否记录总数；1记录，0不记录；默认为0
     */
    private int autoCount;
    /**
     * 起始时间戳，单位秒
     */
    private String startDate;
    /**
     * 结束时间戳，单位秒
     */
    private String endDate;
    /**
     * 要查询的通道数组
     */
    private List<String> channelIds;
    /**
     * 车牌号
     */
    private String plateNo;
    /**
     * 最小速度
     */
    private int minSpeed;
    /**
     * 最大速度
     */
    private int maxSpeed;

}
