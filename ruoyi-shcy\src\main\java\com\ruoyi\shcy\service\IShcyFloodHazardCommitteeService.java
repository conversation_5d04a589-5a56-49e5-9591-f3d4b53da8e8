package com.ruoyi.shcy.service;

import java.util.List;
import com.ruoyi.shcy.domain.ShcyFloodHazardCommittee;

/**
 * 隐患排查居委会对应表Service接口
 * 
 * <AUTHOR>
 * @date 2023-12-26
 */
public interface IShcyFloodHazardCommitteeService 
{
    /**
     * 查询隐患排查居委会对应表
     * 
     * @param id 隐患排查居委会对应表主键
     * @return 隐患排查居委会对应表
     */
    public ShcyFloodHazardCommittee selectShcyFloodHazardCommitteeById(Long id);

    /**
     * 查询隐患排查居委会对应表列表
     * 
     * @param shcyFloodHazardCommittee 隐患排查居委会对应表
     * @return 隐患排查居委会对应表集合
     */
    public List<ShcyFloodHazardCommittee> selectShcyFloodHazardCommitteeList(ShcyFloodHazardCommittee shcyFloodHazardCommittee);

    /**
     * 新增隐患排查居委会对应表
     * 
     * @param shcyFloodHazardCommittee 隐患排查居委会对应表
     * @return 结果
     */
    public int insertShcyFloodHazardCommittee(ShcyFloodHazardCommittee shcyFloodHazardCommittee);

    /**
     * 修改隐患排查居委会对应表
     * 
     * @param shcyFloodHazardCommittee 隐患排查居委会对应表
     * @return 结果
     */
    public int updateShcyFloodHazardCommittee(ShcyFloodHazardCommittee shcyFloodHazardCommittee);

    /**
     * 批量删除隐患排查居委会对应表
     * 
     * @param ids 需要删除的隐患排查居委会对应表主键集合
     * @return 结果
     */
    public int deleteShcyFloodHazardCommitteeByIds(Long[] ids);

    /**
     * 删除隐患排查居委会对应表信息
     * 
     * @param id 隐患排查居委会对应表主键
     * @return 结果
     */
    public int deleteShcyFloodHazardCommitteeById(Long id);
}
