package com.ruoyi.shcy.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 专项任务对象 shcy_special_task
 *
 * <AUTHOR>
 * @date 2025-04-10
 */
public class SpecialTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 被检单位
     */
    @Excel(name = "被检单位")
    private String companyName;

    /**
     * 单位地址
     */
    @Excel(name = "单位地址")
    private String companyAddress;

    /**
     * 所属网格
     */
    @Excel(name = "所属网格")
    private String gridArea;

    /**
     * 检查日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "检查日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date inspectionDate;

    /**
     * 问题类型
     */
    @Excel(name = "问题类型")
    private String issueType;

    /**
     * 任务名称
     */
    @Excel(name = "任务名称")
    private String taskName;

    /**
     * 检查结果
     */
    @Excel(name = "检查结果")
    private String inspectionResult;

    /**
     * 现场照片
     */
    @Excel(name = "现场照片")
    private String scenePhoto;

    /**
     * 复查日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "复查日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reviewDate;

    /**
     * 复查结果
     */
    @Excel(name = "复查结果")
    private String reviewResult;

    /**
     * 处置现场照片
     */
    @Excel(name = "处置现场照片")
    private String disposalPhoto;

    /**
     * 流转状态
     */
    @Excel(name = "流转状态")
    private String circulationState;

    /**
     * 现场照片URL
     */
    private List<String> scenePhotoUrls;

    /**
     * 处置现场照片URL
     */
    private List<String> disposalPhotoUrls;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setGridArea(String gridArea) {
        this.gridArea = gridArea;
    }

    public String getGridArea() {
        return gridArea;
    }

    public void setInspectionDate(Date inspectionDate) {
        this.inspectionDate = inspectionDate;
    }

    public Date getInspectionDate() {
        return inspectionDate;
    }

    public void setIssueType(String issueType) {
        this.issueType = issueType;
    }

    public String getIssueType() {
        return issueType;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setInspectionResult(String inspectionResult) {
        this.inspectionResult = inspectionResult;
    }

    public String getInspectionResult() {
        return inspectionResult;
    }

    public void setScenePhoto(String scenePhoto) {
        this.scenePhoto = scenePhoto;
    }

    public String getScenePhoto() {
        return scenePhoto;
    }

    public void setReviewDate(Date reviewDate) {
        this.reviewDate = reviewDate;
    }

    public Date getReviewDate() {
        return reviewDate;
    }

    public void setReviewResult(String reviewResult) {
        this.reviewResult = reviewResult;
    }

    public String getReviewResult() {
        return reviewResult;
    }

    public void setDisposalPhoto(String disposalPhoto) {
        this.disposalPhoto = disposalPhoto;
    }

    public String getDisposalPhoto() {
        return disposalPhoto;
    }

    public void setCirculationState(String circulationState) {
        this.circulationState = circulationState;
    }

    public String getCirculationState() {
        return circulationState;
    }

    public void setScenePhotoUrls(List<String> scenePhotoUrls) {
        this.scenePhotoUrls = scenePhotoUrls;
    }

    public List<String> getScenePhotoUrls() {
        return scenePhotoUrls;
    }

    public void setDisposalPhotoUrls(List<String> disposalPhotoUrls) {
        this.disposalPhotoUrls = disposalPhotoUrls;
    }

    public List<String> getDisposalPhotoUrls() {
        return disposalPhotoUrls;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("companyName", getCompanyName())
                .append("companyAddress", getCompanyAddress())
                .append("gridArea", getGridArea())
                .append("inspectionDate", getInspectionDate())
                .append("issueType", getIssueType())
                .append("taskName", getTaskName())
                .append("inspectionResult", getInspectionResult())
                .append("scenePhoto", getScenePhoto())
                .append("reviewDate", getReviewDate())
                .append("reviewResult", getReviewResult())
                .append("disposalPhoto", getDisposalPhoto())
                .append("circulationState", getCirculationState())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
