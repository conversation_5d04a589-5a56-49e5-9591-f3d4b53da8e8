package com.ruoyi.shcy.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.shcy.domain.PumpStation;
import com.ruoyi.shcy.service.IPumpStationService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 泵站Controller
 * 
 * <AUTHOR>
 * @date 2023-03-28
 */
@RestController
@RequestMapping("/shcy/pumpStation")
public class PumpStationController extends BaseController
{
    @Autowired
    private IPumpStationService pumpStationService;

    /**
     * 查询泵站列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:pumpStation:list')")
    @GetMapping("/list")
    public TableDataInfo list(PumpStation pumpStation)
    {
        startPage();
        List<PumpStation> list = pumpStationService.selectPumpStationList(pumpStation);
        return getDataTable(list);
    }

    /**
     * 导出泵站列表
     */
    @PreAuthorize("@ss.hasPermi('shcy:pumpStation:export')")
    @Log(title = "泵站", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PumpStation pumpStation)
    {
        List<PumpStation> list = pumpStationService.selectPumpStationList(pumpStation);
        ExcelUtil<PumpStation> util = new ExcelUtil<PumpStation>(PumpStation.class);
        util.exportExcel(response, list, "泵站数据");
    }

    /**
     * 获取泵站详细信息
     */
    @PreAuthorize("@ss.hasPermi('shcy:pumpStation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(pumpStationService.selectPumpStationById(id));
    }

    /**
     * 新增泵站
     */
    @PreAuthorize("@ss.hasPermi('shcy:pumpStation:add')")
    @Log(title = "泵站", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PumpStation pumpStation)
    {
        return toAjax(pumpStationService.insertPumpStation(pumpStation));
    }

    /**
     * 修改泵站
     */
    @PreAuthorize("@ss.hasPermi('shcy:pumpStation:edit')")
    @Log(title = "泵站", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PumpStation pumpStation)
    {
        return toAjax(pumpStationService.updatePumpStation(pumpStation));
    }

    /**
     * 删除泵站
     */
    @PreAuthorize("@ss.hasPermi('shcy:pumpStation:remove')")
    @Log(title = "泵站", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(pumpStationService.deletePumpStationByIds(ids));
    }
}
