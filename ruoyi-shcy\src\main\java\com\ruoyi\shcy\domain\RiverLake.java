package com.ruoyi.shcy.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 河湖水体对象 shcy_river_lake
 *
 * <AUTHOR>
 * @date 2023-02-03
 */
public class RiverLake extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 分类 */
    @Excel(name = "分类")
    private String classification;

    /** 等级 */
    @Excel(name = "等级")
    private String level;

    /** 是否跨镇 */
    @Excel(name = "是否跨镇")
    private String isCrossTown;

    /** 流经街镇 */
    @Excel(name = "流经街镇")
    private String flowTown;

    /** 行政区划 */
    @Excel(name = "行政区划")
    private String administrativeDivision;

    /** 所属街镇 */
    @Excel(name = "所属街镇")
    private String streetTown;

    /** 长度（KM） */
    @Excel(name = "长度", readConverterExp = "K=M")
    private String length;

    /** 面积（K㎡） */
    @Excel(name = "面积", readConverterExp = "K=㎡")
    private String square;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setClassification(String classification)
    {
        this.classification = classification;
    }

    public String getClassification()
    {
        return classification;
    }
    public void setLevel(String level)
    {
        this.level = level;
    }

    public String getLevel()
    {
        return level;
    }
    public void setIsCrossTown(String isCrossTown)
    {
        this.isCrossTown = isCrossTown;
    }

    public String getIsCrossTown()
    {
        return isCrossTown;
    }
    public void setFlowTown(String flowTown)
    {
        this.flowTown = flowTown;
    }

    public String getFlowTown()
    {
        return flowTown;
    }
    public void setAdministrativeDivision(String administrativeDivision)
    {
        this.administrativeDivision = administrativeDivision;
    }

    public String getAdministrativeDivision()
    {
        return administrativeDivision;
    }
    public void setStreetTown(String streetTown)
    {
        this.streetTown = streetTown;
    }

    public String getStreetTown()
    {
        return streetTown;
    }
    public void setLength(String length)
    {
        this.length = length;
    }

    public String getLength()
    {
        return length;
    }
    public void setSquare(String square)
    {
        this.square = square;
    }

    public String getSquare()
    {
        return square;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("classification", getClassification())
                .append("level", getLevel())
                .append("isCrossTown", getIsCrossTown())
                .append("flowTown", getFlowTown())
                .append("administrativeDivision", getAdministrativeDivision())
                .append("streetTown", getStreetTown())
                .append("length", getLength())
                .append("square", getSquare())
                .append("remark", getRemark())
                .append("createTime", getCreateTime())
                .append("createBy", getCreateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
